# PollGPT

PollGPT is an intelligent polling application that helps you create polls from various content sources. It can extract text from URLs, files (PDFs, DOCXs, TXTs), and even images to automatically generate relevant poll questions.

This project is built with [Next.js](https://nextjs.org) and uses AI services like Mistral AI for content extraction.

## Features

- Create polls from various content sources
- Extract text from URLs, websites, PDFs, DOCXs, TXTs, and images
- Modern, responsive UI with a clean design
- Dashboard to manage your polls
- Shareable poll links

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn
- API keys for the following services:
  - [Mistral AI](https://mistral.ai/) for advanced content extraction
  - [Supabase](https://supabase.com/) for database and authentication (optional)

### Installation

1. Clone the repository

```bash
git clone https://github.com/yourusername/pollgpt.git
cd pollgpt
```

2. Install dependencies

```bash
npm install
# or
yarn install
```

3. Set up environment variables

Copy the example environment file and update it with your API keys:

```bash
cp .env.example .env.local
```

Edit `.env.local` and add your API keys.

4. Run the development server

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Usage

1. Navigate to the dashboard and click "Create Poll"
2. Fill in the poll title and description
3. Extract content from a URL, file, or website:
   - For URLs: Enter a valid URL and click "Extract Content"
   - For files: Upload a PDF, DOCX, TXT, or image file and click "Extract Content"
   - For websites: Enter a website URL to crawl multiple pages
4. Review the extracted content
5. Click "Next: Create Questions" to generate poll questions
6. Customize your questions and options
7. Publish your poll and share the link

## File Extraction Capabilities

PollGPT supports extracting content from various file types:

- **PDFs**: Both text-based and image-based (scanned) PDFs are supported
- **DOCX**: Microsoft Word documents
- **TXT**: Plain text files
- **Images**: JPG, PNG, and other common image formats

## Deployment

The easiest way to deploy PollGPT is using [Vercel](https://vercel.com):

```bash
npm install -g vercel
vercel
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
# Poll-GPT
