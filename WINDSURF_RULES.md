# Windsurf Rules for PollGPT

This document outlines the development and coding standards for the PollGPT project, following Windsurf best practices.

## 1. Database Access

- Always use Supabase client with appropriate authentication
- Never hard-code database credentials in source files
- Use environment variables for all sensitive information
- Follow Row Level Security (RLS) best practices:
  - Users can only access their own data
  - Public data should be explicitly marked as public
  - Service roles should have necessary but minimal permissions

## 2. TypeScript Standards

- Use explicit types for all variables, parameters, and return values
- Avoid using `any` type when possible
- Properly handle Promises with async/await
- Create interfaces for data structures
- Use optional chaining and nullish coalescing when appropriate

## 3. Component Architecture

- Follow Next.js app directory structure
- Separate UI components from data fetching logic
- Use server components when possible for improved performance
- Client components should be lightweight and focused on interactivity

## 4. API Integration

- Handle errors gracefully from external API calls
- Implement fallback strategies for API failures
- Use proper environment variables for API keys
- Validate responses before using them

## 5. Security Practices

- Never expose sensitive information in client-side code
- Properly authenticate and authorize all data access
- Sanitize user inputs to prevent injection attacks
- Use HTTPS for all external requests

## 6. Performance Considerations

- Implement caching where appropriate
- Minimize unnecessary re-renders
- Optimize database queries
- Use proper indexing in database tables

## 7. Testing

- Write unit tests for critical functionality
- Implement integration tests for key user flows
- Use tools like Puppeteer for end-to-end testing
- Test error handling and edge cases

## 8. Configuration Management

- Use .env.local for local environment variables
- Keep sensitive information out of version control
- Document required environment variables
- Use structured configuration files (.mcp.json, etc.)

Following these guidelines will help maintain a secure, performant, and maintainable codebase for the PollGPT project.
