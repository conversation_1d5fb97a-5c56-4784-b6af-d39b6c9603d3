// Quick localStorage Context Test
// Run this in the browser console to verify context data persistence

console.log('🔍 TESTING LOCALSTORAGE CONTEXT PERSISTENCE');
console.log('===========================================\n');

// Clear any existing data
localStorage.removeItem('pollgpt_pending_message');
console.log('🧹 Cleared existing localStorage data\n');

// Step 1: Set up context data (as URL extraction would)
console.log('📝 Step 1: Setting up context data...');
const contextData = {
  source: 'url',
  resourceName: 'https://www.feedbackgpt.com',
  extractedContent: 'This is a test website about feedback and user experience design. It provides tools and insights for gathering meaningful feedback from users to improve products and services.',
  message: 'Please create a poll about this website: https://www.feedbackgpt.com',
  processed: false
};

localStorage.setItem('pollgpt_pending_message', JSON.stringify(contextData));
console.log('✅ Context data saved to localStorage');
console.log('   Data:', {
  source: contextData.source,
  url: contextData.resourceName,
  contentLength: contextData.extractedContent.length,
  processed: contextData.processed
});

// Step 2: Verify data persistence
console.log('\n🔍 Step 2: Checking data persistence...');
const retrievedData = localStorage.getItem('pollgpt_pending_message');
if (retrievedData) {
  const parsed = JSON.parse(retrievedData);
  console.log('✅ Data successfully retrieved from localStorage');
  console.log('   Retrieved:', {
    source: parsed.source,
    url: parsed.resourceName,
    hasContent: !!parsed.extractedContent,
    processed: parsed.processed
  });
} else {
  console.error('❌ No data found in localStorage!');
}

// Step 3: Simulate ConversationalPollInterface processing (mark as processed but keep data)
console.log('\n⚙️ Step 3: Simulating ConversationalPollInterface processing...');
if (retrievedData) {
  const parsed = JSON.parse(retrievedData);
  if (!parsed.processed) {
    parsed.processed = true;
    localStorage.setItem('pollgpt_pending_message', JSON.stringify(parsed));
    console.log('✅ Marked as processed but kept data in localStorage');
  }
}

// Step 4: Check if data is still available after processing
console.log('\n✨ Step 4: Checking data availability after processing...');
const finalCheck = localStorage.getItem('pollgpt_pending_message');
if (finalCheck) {
  const finalParsed = JSON.parse(finalCheck);
  console.log('✅ Context data still available after processing:');
  console.log('   Source:', finalParsed.source);
  console.log('   URL:', finalParsed.resourceName);
  console.log('   Content available:', !!finalParsed.extractedContent);
  console.log('   Processed:', finalParsed.processed);

  // Step 5: Simulate what handleCreatePoll would see
  console.log('\n🎯 Step 5: What handleCreatePoll would receive:');
  let pollContext = null;
  let pollSourceUrl = null;
  let pollSourceType = null;

  if (finalParsed.source === 'url' && finalParsed.resourceName) {
    const contentToSummarize = finalParsed.extractedContent || finalParsed.resourceName;

    if (contentToSummarize.length > 100) {
      pollContext = 'This website provides feedback tools and UX insights...'; // Simulated summary
    } else {
      pollContext = contentToSummarize;
    }

    pollSourceUrl = finalParsed.resourceName;
    pollSourceType = 'url';

    console.log('✅ Poll would be created with:');
    console.log('   - context:', pollContext);
    console.log('   - source_url:', pollSourceUrl);
    console.log('   - source_type:', pollSourceType);

    console.log('\n🎉 SUCCESS! Context data would be properly saved to database!');
  } else {
    console.error('❌ Context data not in expected format');
  }
} else {
  console.error('❌ No context data available for poll creation!');
}

console.log('\n===========================================');
console.log('Test completed. Check results above. ☝️');
