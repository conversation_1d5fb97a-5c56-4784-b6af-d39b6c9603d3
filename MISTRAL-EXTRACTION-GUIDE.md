# Mistral Document Extraction Guide

This guide explains how to use and test the document extraction functionality in PollGPT, powered by Mistral AI.

## 📄 Overview

PollGPT can extract text content from various document types to help create polls. The extraction functionality is built on top of Mistral AI's document understanding capabilities with added fallbacks.

## 🧠 How It Works

1. **Text Files**: Direct extraction without requiring AI capabilities
2. **Images & PDFs**: Uses Mistral AI's vision capabilities when available
3. **PDFs (Fallback)**: Basic text extraction when AI capabilities aren't available
4. **Other Documents**: Requires paid Mistral AI subscription with vision capabilities

## 📋 Supported File Types

- **Text Files**: .txt (works with any Mistral subscription)
- **Images**: .png, .jpg, .jpeg, .webp (requires vision capabilities)
- **Documents**: .pdf (AI extraction requires vision capabilities, fallback available)
- **Office Documents**: .docx (requires vision capabilities)

## 🔧 Setup

1. Create a `.env.local` file in the project root:
   ```sh
   touch .env.local
   ```

2. Add your Mistral API key:
   ```
   MISTRAL_API_KEY=your-key-here
   ```

## 🧪 Testing Options

We provide multiple test scripts for different scenarios:

```sh
# Complete test suite with improved API handling
node scripts/test-mistral-extraction-fixed.js

# Simple text file extraction test (works with free plan)
node scripts/test-text-extraction.js
```

These scripts perform various checks:
1. **API Connectivity**: Verifies your API key works with Mistral
2. **Model Capability Detection**: Checks if your plan has vision/multimodal features
3. **Document Extraction**: Tests extraction using sample files
4. **Application Flow**: Simulates the full extraction process

## ⚠️ Mistral API Plan Limitations

The document extraction capabilities depend on your Mistral API subscription:

### Free Plan Limitations
- ✅ Text files work without issues (direct processing)
- ✅ Basic PDF text extraction available (limited quality)
- ❌ No vision capabilities for images and complex documents
- ❌ Cannot process images, complex PDFs or DOCX files

### Paid Plans with Vision Capabilities
- ✅ All document types supported
- ✅ Higher quality extraction
- ✅ Better handling of formatting and tables
- ✅ Support for DOCX and complex PDFs

## 🚀 Production Setup

Our improved system has several fallback mechanisms:

1. **Direct Text Processing**: Text files (.txt) are processed without requiring AI
2. **Basic PDF Extraction**: Simple text extraction for PDFs when vision isn't available
3. **Graceful Degradation**: Helpful error messages guide users when extraction isn't possible

## 🔍 Troubleshooting Common Issues

### API Key Issues
- Make sure your Mistral API key is correctly set in the `.env.local` file
- The system checks both `MISTRAL_API_KEY` and `NEXT_PUBLIC_MISTRAL_API_KEY`
- Check if your API key is active and has enough quota

### "Vision Capability" Errors
- This means your Mistral plan doesn't include multimodal features
- Solution: Use text files only or upgrade to a plan with vision capabilities
- Check your subscription at https://console.mistral.ai/

### Document Extraction Issues
- Files must be under 25MB (strict limit)
- Text files work best with any plan
- PDFs should contain real text, not just scanned images
- Complex formatting may impact extraction quality

### Application Flow Issues
- Check browser console logs for client-side errors
- Verify server-side logs for API-related errors
- Try the fallback methods for different file types
- Ensure memory limits are properly set in `vercel.json`

## Testing in the Actual Application

To test document extraction in the actual application:

1. Start the development server:
   ```sh
   npm run dev
   ```

2. Navigate to the conversational poll creation page
3. Upload a document file through the UI
4. Check console for extraction details

## Need Help?

If you continue to experience issues after following this guide, please check:
1. Your Mistral AI account permissions
2. Network connectivity to Mistral API
3. File format compatibility
