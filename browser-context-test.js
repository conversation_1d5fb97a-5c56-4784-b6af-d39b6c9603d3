// Simple browser console test for context workflow
// Copy and paste this into the browser console while on the conversational poll creation page

(async function testContextWorkflow() {
  console.log('🧪 Testing Context Workflow with FeedbackGPT URL...\n');

  const testUrl = 'https://www.feedbackgpt.com';

  try {
    // Test URL extraction
    console.log('1. Testing URL extraction...');
    const extractResponse = await fetch('/api/extract-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: testUrl })
    });

    if (!extractResponse.ok) {
      throw new Error(`URL extraction failed: ${extractResponse.status}`);
    }

    const extractData = await extractResponse.json();
    console.log('✅ URL extraction successful:', extractData);

    // Test summarization
    console.log('\n2. Testing content summarization...');
    const summaryResponse = await fetch('/api/ai/summarize-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: extractData.content,
        sourceType: 'website',
        maxLength: 200
      })
    });

    if (!summaryResponse.ok) {
      throw new Error(`Summarization failed: ${summaryResponse.status}`);
    }

    const summaryData = await summaryResponse.json();
    console.log('✅ Content summarization successful:', summaryData);

    // Step 3: Simulate the context data setup for localStorage
    console.log('\n3. Testing localStorage context setup...');
    const contextMessage = {
      source: 'url',
      resourceName: testUrl,
      extractedContent: extractData.content
    };

    localStorage.setItem('pollgpt_pending_message', JSON.stringify(contextMessage));
    console.log('✅ Context message saved to localStorage:', contextMessage);

    // Step 4: Test if we can retrieve and parse it
    console.log('\n4. Testing localStorage retrieval...');
    const retrievedMessage = localStorage.getItem('pollgpt_pending_message');
    if (retrievedMessage) {
      const parsed = JSON.parse(retrievedMessage);
      console.log('✅ Retrieved and parsed message:', parsed);

      // Test the context data logic
      let contextData = null;
      if (parsed.source === 'url' && parsed.resourceName) {
        contextData = {
          url: parsed.resourceName,
          extractedContent: parsed.extractedContent || '',
          source: 'url'
        };
        console.log('✅ Context data constructed:', contextData);

        // Test generateContextSummary function (simulate it)
        const contentToSummarize = contextData.extractedContent || contextData.url;
        console.log('Content to summarize length:', contentToSummarize.length);

        if (contentToSummarize.length > 100) {
          console.log('✅ Content is long enough for summarization');
          console.log('Summary that would be used:', summaryData.summary);
        }
      }
    }

    console.log('\n🎉 Complete context workflow test completed successfully!');
    console.log('Summary:', summaryData.summary);
    console.log('Source URL:', testUrl);

    // Return the data for further use
    return {
      extracted: extractData,
      summary: summaryData,
      contextMessage,
      contextData
    };

  } catch (error) {
    console.error('❌ Context workflow test failed:', error.message);
    return null;
  }
})();
