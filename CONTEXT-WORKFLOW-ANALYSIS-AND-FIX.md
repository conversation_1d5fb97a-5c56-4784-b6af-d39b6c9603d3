# Context Workflow Analysis & Fix Implementation

## 🔍 Issue Analysis

### Current Workflow Problems
1. **Context Field Confusion**: The `context` field stores raw extracted content instead of a meaningful summary
2. **Missing Source URL**: File URLs are generated but not consistently stored in `source_url` field
3. **Poor User Experience**: Users see raw extracted text instead of a clean summary with document link

### What We Found

#### ✅ Working Correctly
- File upload to Supabase Storage (`context` folder)
- Content extraction from PDFs, documents, and URLs
- Database schema supports both `context` and `source_url` fields
- Frontend retrieves context data from localStorage

#### ❌ Issues Identified
- `context` field stores raw extracted content (can be very long and messy)
- `source_url` field sometimes not populated consistently
- No summary generation for context
- Edit poll page doesn't display source links properly

## 🎯 Proposed Solution

### New Context Workflow
1. **Extract Content**: Get full content from document/URL (unchanged)
2. **Generate Summary**: Create AI-powered summary of the content for `context` field
3. **Store Source URL**: Ensure `source_url` always contains the link to original document
4. **Display Both**: Show summary + link to original source in UI

### Benefits
- **Clean Context**: Users see meaningful summaries instead of raw text
- **Source Preservation**: Original documents remain accessible via links
- **Better UX**: Clear separation between summary and source material
- **Consistent Data**: All polls have properly structured context data

## 🔧 Implementation Plan

### Phase 1: Backend Changes
1. **Update Content Extraction**: Generate summaries using AI
2. **Fix Poll Service**: Ensure `source_url` is always populated
3. **Add Summary Generation**: Create utility for content summarization

### Phase 2: Frontend Changes
1. **Update Conversational Creation**: Store summary in context, URL in source_url
2. **Fix Edit Poll Page**: Display context summary + source link
3. **Improve Context Display**: Better formatting and presentation

### Phase 3: Data Migration
1. **Existing Polls**: Convert raw context to summaries where needed
2. **Missing URLs**: Populate source_url for existing polls with attachments

## 📋 Files to Modify

### Backend
- `src/lib/services/polls.ts` - Fix poll creation logic
- `src/lib/utils/content-summarizer.ts` - New summary generation utility
- `src/app/api/ai/summarize-content/route.ts` - New API endpoint for summarization

### Frontend
- `src/app/(dashboard)/dashboard/create/conversational/page.tsx` - Update context handling
- `src/app/(dashboard)/dashboard/polls/[id]/page.tsx` - Fix context display
- `src/components/ui/context-display.tsx` - New component for context presentation

## 🚀 Expected Outcome

### Before Fix
```
context: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat..." (500+ words of raw text)
source_url: null or inconsistent
```

### After Fix
```
context: "This document discusses customer satisfaction metrics for Q3 2024, highlighting key areas for improvement in product quality and customer service response times."
source_url: "https://[supabase-url]/storage/v1/object/public/context/user123/document.pdf"
```

## ✅ Success Criteria
- Context field contains concise, meaningful summaries (50-150 words)
- Source URL field always populated for attachments
- Edit poll page displays both summary and source link
- Users can access original documents via clickable links
- Existing polls work without breaking changes