# Sitemap Fix Documentation

## Issue
The sitemap.xml file was not being served with the correct content type, causing browsers to display it as plain text instead of formatted XML. This was causing problems for search engines and other tools that rely on properly formatted XML sitemaps.

## Root Causes
1. Conflict between Next.js App Router's built-in sitemap functionality (src/app/sitemap.ts) and the static sitemap.xml file in the public directory
2. Missing proper Content-Type headers when serving the sitemap.xml file
3. Inconsistencies in how the sitemap was generated during build time vs. runtime

## Solutions Implemented
1. **Created a dedicated API route** (`/api/sitemap`) to dynamically generate and serve the sitemap with proper XML content type headers
2. **Added URL rewrites** in Next.js config to ensure requests to `/sitemap.xml` are handled by our API route
3. **Added explicit content-type headers** for all XML files to ensure proper rendering
4. **Disabled conflicting App Router sitemap** by renaming `src/app/sitemap.ts` to `src/app/_sitemap.ts`
5. **Enhanced the next-sitemap configuration** to ensure proper XML formatting during build time
6. **Created a test script** (`scripts/test-sitemap.js`) to verify that the sitemap is being served correctly

## Testing
To test if the sitemap is working correctly:

1. Start the development server:
   ```
   npm run dev
   ```

2. Run the sitemap test script:
   ```
   npm run test:sitemap
   ```

3. Visit `/sitemap.xml` in a browser - it should render as formatted XML, not plain text

## Production Deployment
After deploying to production:
1. Verify that https://pollgpt.com/sitemap.xml displays as formatted XML
2. Confirm that the response includes the correct Content-Type header (`application/xml`)
3. Check that search engine tools like Google Search Console can properly parse the sitemap

## References
- [Next.js Rewrites Documentation](https://nextjs.org/docs/api-reference/next.config.js/rewrites)
- [Next.js Headers Documentation](https://nextjs.org/docs/api-reference/next.config.js/headers)
- [Next-Sitemap Documentation](https://www.npmjs.com/package/next-sitemap)
