# PollGPT Database Structure

## Overview
PollGPT uses Supabase (PostgreSQL) as its database solution. This document outlines the database structure, relationships, and design decisions for the application.

## Tables Structure

### 1. users
Stores user account information and authentication details.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| email | STRING | User's email address, unique |
| name | STRING | User's full name |
| profile_image | STRING | URL to profile image |
| created_at | TIMESTAMP | When the user account was created |
| subscription_tier | STRING | User's subscription level (free, pro, enterprise) |

### 2. polls
Stores information about each poll created in the system.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| creator_id | UUID | Foreign key to users.id |
| title | STRING | Title of the poll |
| description | STRING | Description of the poll (optional) |
| settings | JSON | Poll settings (theme, visibility, etc.) |
| status | STRING | Current status (draft, active, completed) |
| created_at | TIMESTAMP | When the poll was created |
| updated_at | TIMESTAMP | When the poll was last updated |
| expires_at | TIMESTAMP | When the poll expires (optional) |
| response_count | INTEGER | Number of responses received |
| views | INTEGER | Number of times the poll was viewed |

### 3. questions
Stores individual questions within polls.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| poll_id | UUID | Foreign key to polls.id |
| text | STRING | Question text |
| type | STRING | Question type (single, multiple, likert, open) |
| options | JSON | Array of answer options |
| required | BOOLEAN | Whether the question requires an answer |
| order | INTEGER | Order of question in the poll |
| logic_rules | JSON | Logic for conditional questions (optional) |

### 4. responses
Stores individual poll response sessions.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| poll_id | UUID | Foreign key to polls.id |
| respondent_fingerprint | STRING | Anonymous identifier for respondent |
| metadata | JSON | Device type, browser, OS, region, etc. |
| created_at | TIMESTAMP | When the response was submitted |

### 5. answers
Stores individual answers to questions.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| response_id | UUID | Foreign key to responses.id |
| question_id | UUID | Foreign key to questions.id |
| answer_value | TEXT | The answer value |
| created_at | TIMESTAMP | When the answer was submitted |

### 6. ai_prompts
Stores AI generation prompts and templates.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| type | STRING | Type of prompt (poll_generation, analysis, etc.) |
| content | TEXT | The prompt template |
| created_at | TIMESTAMP | When the prompt was created |
| updated_at | TIMESTAMP | When the prompt was last updated |

### 7. poll_templates
Stores pre-defined poll templates.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated |
| name | STRING | Template name |
| description | TEXT | Template description |
| questions | JSON | Pre-defined questions and options |
| category | STRING | Template category |
| created_at | TIMESTAMP | When the template was created |
| updated_at | TIMESTAMP | When the template was last updated |

## Relationships

- A **user** can have many **polls** (one-to-many)
- A **poll** can have many **questions** (one-to-many)
- A **poll** can have many **responses** (one-to-many)
- A **response** can have many **answers** (one-to-many)
- A **question** can have many **answers** (one-to-many)

## Indexes

- users(email)
- polls(creator_id)
- questions(poll_id)
- responses(poll_id)
- answers(response_id, question_id)

## Row-Level Security (RLS)

Supabase provides row-level security policies to restrict access to data:

### polls Table
- Users can read their own polls
- Users can update their own polls
- Users can delete their own polls
- Public access for active polls (read-only)

### questions Table
- Inherits access from parent poll

### responses Table
- Poll creators can read all responses to their polls
- Respondents can create responses for active polls
- Respondents can only read their own responses

### answers Table
- Inherits access from parent response

## Database Functions and Triggers

### Functions
1. `increment_response_count()` - Increases the response count when a new response is added
2. `check_poll_expiration()` - Checks and updates poll status when expiration date is reached

### Triggers
1. `after_insert_on_responses` - Calls `increment_response_count()`
2. `daily_poll_expiration_check` - Calls `check_poll_expiration()`

## Migrations

Database migrations will be managed using Supabase migrations. Each migration will be version-controlled and included in the repository under the `/supabase/migrations` directory.

## Backup Strategy

- Daily automated backups
- Point-in-time recovery enabled
- 30-day retention period for backups

## Performance Considerations

- Normalize data where appropriate (answers in separate table)
- Use JSON for flexible schemas (question options, response metadata)
- Implement appropriate indexes for common query patterns
- Consider partitioning for response data as it grows