# Loading UI Implementation - Quick Reference

## Importing the Loader Component

```tsx
import { Loader } from "@/components/ui/loader";
```

## Common Usage Patterns

### 1. Button Loading States

```tsx
<Button disabled={isLoading} className="gap-2">
  {isLoading ? (
    <>
      <Loader variant="minimal" size="xs" />
      Loading...
    </>
  ) : (
    <>
      <Icon className="h-4 w-4" />
      Button Text
    </>
  )}
</Button>
```

### 2. Page/Section Loading

```tsx
{isLoading ? (
  <div className="p-8">
    <Loader size="lg" text="Loading content..." centered={true} />
  </div>
) : (
  <YourContent />
)}
```

### 3. Inline Loading (small indicators)

```tsx
<div className="flex items-center">
  Status: {isLoading ? <Loader variant="minimal" size="xs" /> : "Complete"}
</div>
```

### 4. Chat Conversation Loading (typing indicator)

Keep the custom three-dot typing indicator for chat conversations:

```tsx
<div className="typing-indicator">
  <span></span>
  <span></span>
  <span></span>
</div>

/* Add CSS for typing indicator animation in your stylesheet */
```

## Configuration Options

### Sizes
- `xs` (very small - 12px)
- `sm` (small - 16px)
- `md` (medium - 20px, default)
- `lg` (large - 32px)
- `xl` (extra large - 48px)

### Variants
- `default` (standard spinner)
- `minimal` (smaller spinner, good for buttons)
- `spinner` (bordered spinner)
- `dots` (three dots animation)

### Other Options
- `text` - Add text next to the spinner
- `centered` - Center in the parent container
- `fullPage` - Show as a full page overlay
- `className` - Add custom classes
