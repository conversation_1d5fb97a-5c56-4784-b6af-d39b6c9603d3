# Loading UI Implementation Guide

This document outlines how to use the new unified loading system in PollGPT.

## Loader Component

The new `Loader` component provides a consistent loading UI throughout the application.

## Usage Examples

### Basic Usage

```tsx
import { Loader } from "@/components/ui/loader";

// Default spinner
<Loader />

// With text
<Loader text="Loading data..." />

// Different sizes
<Loader size="xs" />
<Loader size="sm" />
<Loader size="md" /> // default
<Loader size="lg" />
<Loader size="xl" />

// Different variants
<Loader variant="default" /> // default
<Loader variant="spinner" />
<Loader variant="dots" />
<Loader variant="minimal" />

// Full page loader
<Loader fullPage={true} text="Loading application..." />

// Centered in its container
<Loader centered={true} />
```

### Button Loading State

```tsx
<Button disabled={isLoading}>
  {isLoading ? <Loader variant="minimal" size="sm" /> : "Click me"}
</Button>

// With text inside a button
<Button disabled={isLoading} className="gap-2">
  {isLoading ? (
    <>
      <Loader variant="minimal" size="sm" />
      Saving...
    </>
  ) : (
    <>
      <Save className="h-4 w-4" />
      Save
    </>
  )}
</Button>
```

### Section Loading State

```tsx
{isLoading ? (
  <div className="py-10">
    <Loader text="Loading content..." centered={true} />
  </div>
) : (
  <YourContent />
)}
```

### Inline Loading State

```tsx
<div className="flex items-center gap-2">
  <span>Status:</span>
  {isLoading ? <Loader variant="minimal" size="xs" /> : <span>Complete</span>}
</div>
```

### Chat Loading State (for ConversationInterface)

```tsx
{isTyping && (
  <div className="typing-indicator">
    <span></span>
    <span></span>
    <span></span>
  </div>
)}

/* CSS for typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  margin: 0 2px;
  background-color: #555;
  border-radius: 50%;
  opacity: 0.4;
  display: inline-block;
  animation: typing 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}
```

## Implementation Strategy

1. **Step 1:** Identify components with loading states
2. **Step 2:** Replace existing loading indicators with the new `Loader` component
3. **Step 3:** Test and ensure consistent appearance across the application

## Loading States to Update

The following components have been identified as needing updates:

- ConversationInterface.tsx
- simulation-dashboard.tsx
- simulation-setup.tsx
- poll pages
- results pages
- performance dashboard
- button loading states

## Benefits of Using the Unified Loader

- Consistent user experience
- Accessibility built-in
- Easy to maintain and update across the application
- Responsive design
- Variants for different contexts
