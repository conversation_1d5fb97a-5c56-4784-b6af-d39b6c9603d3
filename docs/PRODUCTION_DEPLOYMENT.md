# Production Deployment Configuration for PollGPT Performance Optimization

This guide provides comprehensive instructions for deploying PollGPT with all performance optimization features in production environments.

## Table of Contents

1. [Infrastructure Requirements](#infrastructure-requirements)
2. [Environment Configuration](#environment-configuration)
3. [Service Deployment](#service-deployment)
4. [Monitoring Setup](#monitoring-setup)
5. [Performance Tuning](#performance-tuning)
6. [Scaling Considerations](#scaling-considerations)
7. [Troubleshooting](#troubleshooting)

## Infrastructure Requirements

### Minimum Production Setup

```yaml
# Infrastructure Components
Database:
  - PostgreSQL 14+ with connection pooling
  - Minimum: 2 CPU cores, 4GB RAM, 50GB SSD
  - Recommended: 4 CPU cores, 8GB RAM, 100GB SSD

Redis Cache:
  - Redis 6+ with persistence enabled
  - Minimum: 1GB RAM
  - Recommended: 2GB RAM with high availability

Application Server:
  - Node.js 18+ with PM2 or similar process manager
  - Minimum: 2 CPU cores, 4GB RAM
  - Recommended: 4 CPU cores, 8GB RAM

Load Balancer:
  - Nginx, HAProxy, or cloud load balancer
  - SSL termination and rate limiting
  - Health check endpoints configured
```

### Cloud Provider Specific

#### Vercel (Recommended)
```bash
# Deploy with Vercel CLI
npm install -g vercel
vercel --prod

# Required Vercel configuration
# vercel.json
{
  "env": {
    "DATABASE_URL": "@database-url",
    "REDIS_URL": "@redis-url",
    "NODE_ENV": "production"
  },
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

#### Docker Deployment
```dockerfile
# Dockerfile.production
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

COPY . .
RUN npm run build

FROM node:18-alpine AS runtime
WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./

EXPOSE 3000
CMD ["npm", "start"]
```

#### Docker Compose Setup
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=pollgpt
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  job-worker:
    build:
      context: .
      dockerfile: Dockerfile.production
    command: ["node", "scripts/job-worker.js"]
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  performance-monitor:
    build:
      context: .
      dockerfile: Dockerfile.production
    command: ["node", "scripts/performance-optimization.js", "start"]
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

## Environment Configuration

### Production Environment Variables

```bash
# .env.production
NODE_ENV=production
PORT=3000

# Database Configuration
DATABASE_URL=***************************************/pollgpt
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=60000
DB_QUERY_TIMEOUT=10000

# Redis Configuration
REDIS_URL=redis://redis-host:6379
# OR for Upstash
UPSTASH_REDIS_REST_URL=https://your-redis-rest-url
UPSTASH_REDIS_REST_TOKEN=your-token

# Performance Optimization
JOB_QUEUE_CONCURRENCY=10
JOB_WORKER_COUNT=6
JOB_RETRY_ATTEMPTS=5
CACHE_TTL_DEFAULT=3600
CACHE_WARMUP_ENABLED=true
CACHE_COMPRESSION_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=500
RATE_LIMIT_WINDOW_MS=900000

# Monitoring
METRICS_RETENTION_DAYS=30
PERFORMANCE_ALERTS_ENABLED=true
SLOW_QUERY_THRESHOLD=1000

# Alert Configuration
ALERT_WEBHOOK_URL=https://your-monitoring-webhook.com/alerts
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>
ALERT_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Security
NEXTAUTH_SECRET=your-super-secret-key
NEXTAUTH_URL=https://yourdomain.com
```

### Secrets Management

#### Using Docker Secrets
```yaml
# docker-compose.secrets.yml
version: '3.8'

services:
  app:
    secrets:
      - database_url
      - redis_url
      - nextauth_secret
    environment:
      - DATABASE_URL_FILE=/run/secrets/database_url
      - REDIS_URL_FILE=/run/secrets/redis_url
      - NEXTAUTH_SECRET_FILE=/run/secrets/nextauth_secret

secrets:
  database_url:
    external: true
  redis_url:
    external: true
  nextauth_secret:
    external: true
```

#### Using Kubernetes Secrets
```yaml
# k8s-secrets.yml
apiVersion: v1
kind: Secret
metadata:
  name: pollgpt-secrets
type: Opaque
stringData:
  DATABASE_URL: "**********************************/pollgpt"
  REDIS_URL: "redis://redis:6379"
  NEXTAUTH_SECRET: "your-super-secret-key"
```

## Service Deployment

### PM2 Process Management

```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'pollgpt-app',
      script: 'npm',
      args: 'start',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/app-error.log',
      out_file: './logs/app-out.log',
      log_file: './logs/app-combined.log',
      time: true
    },
    {
      name: 'pollgpt-jobs',
      script: './scripts/job-worker.js',
      instances: 2,
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/jobs-error.log',
      out_file: './logs/jobs-out.log',
      log_file: './logs/jobs-combined.log',
      time: true
    },
    {
      name: 'pollgpt-performance',
      script: './scripts/performance-optimization.js',
      args: 'start',
      instances: 1,
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/perf-error.log',
      out_file: './logs/perf-out.log',
      log_file: './logs/perf-combined.log',
      time: true
    }
  ]
};

# Deploy with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### Nginx Load Balancer Configuration

```nginx
# /etc/nginx/sites-available/pollgpt
upstream pollgpt_app {
    least_conn;
    server 127.0.0.1:3000 weight=10 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=10 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=200r/m;

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Rate Limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://pollgpt_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location / {
        limit_req zone=general burst=50 nodelay;
        proxy_pass http://pollgpt_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Health Check Endpoint
    location /health {
        access_log off;
        proxy_pass http://pollgpt_app/api/health;
        proxy_set_header Host $host;
    }

    # Static Assets Caching
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://pollgpt_app;
    }

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## Monitoring Setup

### Health Check Endpoint

```typescript
// src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { enhancedDb } from '@/lib/services/connection-pool';
import { redisCacheService } from '@/lib/services/redis-cache';

export async function GET() {
  const checks = {
    database: false,
    cache: false,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  };

  try {
    // Database health check
    await enhancedDb.testConnection();
    checks.database = true;
  } catch (error) {
    console.error('Database health check failed:', error);
  }

  try {
    // Cache health check
    const stats = await redisCacheService.getStats();
    checks.cache = stats.redisAvailable || stats.fallbackCacheSize >= 0;
  } catch (error) {
    console.error('Cache health check failed:', error);
  }

  const healthy = checks.database && checks.cache;

  return NextResponse.json(checks, {
    status: healthy ? 200 : 503,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}
```

### Log Management

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=pollgpt-app"

  redis:
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"
        labels: "service=pollgpt-redis"

  postgres:
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"
        labels: "service=pollgpt-postgres"
```

### Monitoring with Prometheus

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'pollgpt'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['localhost:9187']
```

## Performance Tuning

### Database Optimization

```sql
-- PostgreSQL configuration for production
-- postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

-- Create indexes for performance
CREATE INDEX CONCURRENTLY idx_polls_created_at ON polls(created_at);
CREATE INDEX CONCURRENTLY idx_poll_responses_poll_id ON poll_responses(poll_id);
CREATE INDEX CONCURRENTLY idx_poll_responses_created_at ON poll_responses(created_at);
```

### Redis Configuration

```conf
# redis.conf for production
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
tcp-keepalive 300
timeout 0
```

### Node.js Optimization

```bash
# Environment variables for Node.js optimization
NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"
UV_THREADPOOL_SIZE=16
```

## Scaling Considerations

### Horizontal Scaling

```yaml
# Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pollgpt-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pollgpt-app
  template:
    metadata:
      labels:
        app: pollgpt-app
    spec:
      containers:
      - name: pollgpt
        image: pollgpt:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pollgpt-secrets
              key: DATABASE_URL
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
```

### Auto-scaling

```yaml
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: pollgpt-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pollgpt-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   ```bash
   # Check memory usage
   node --inspect scripts/memory-analysis.js

   # Enable heap snapshots
   NODE_OPTIONS="--max-old-space-size=2048 --heapsnapshot-signal=SIGUSR2"
   ```

2. **Database Connection Issues**
   ```bash
   # Check database connections
   SELECT * FROM pg_stat_activity WHERE state = 'active';

   # Monitor connection pool
   curl http://localhost:3000/api/performance/metrics | jq '.database.connections'
   ```

3. **Redis Connection Issues**
   ```bash
   # Check Redis connectivity
   redis-cli ping

   # Monitor Redis performance
   redis-cli info stats
   ```

### Performance Debugging

```bash
# Enable debug logging
DEBUG=pollgpt:* npm start

# Monitor performance metrics
curl -s http://localhost:3000/api/performance/metrics | jq

# Run performance benchmark
npm run perf:benchmark

# Check health status
curl -s http://localhost:3000/health | jq
```

### Log Analysis

```bash
# Analyze application logs
tail -f logs/app-combined.log | grep ERROR

# Monitor slow queries
tail -f logs/app-combined.log | grep "slow query"

# Check job processing
tail -f logs/jobs-combined.log | grep "Job completed"
```

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup-db.sh
BACKUP_DIR="/backups/postgres"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/pollgpt_backup_$TIMESTAMP.sql"

mkdir -p $BACKUP_DIR
pg_dump $DATABASE_URL > $BACKUP_FILE
gzip $BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

### Redis Backup

```bash
#!/bin/bash
# backup-redis.sh
BACKUP_DIR="/backups/redis"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb $BACKUP_DIR/dump_$TIMESTAMP.rdb
```

This production deployment guide ensures that PollGPT runs efficiently and reliably in production environments with all performance optimization features enabled.
