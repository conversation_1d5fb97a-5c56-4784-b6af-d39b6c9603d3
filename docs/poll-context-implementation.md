# Poll Context Implementation

## Overview

This document outlines the implementation of poll context/attachment functionality in PollGPT. The goal is to save imported content in the database and display it in the edit poll page.

## Database Changes

Add a `context` field to the `polls` table:

```sql
ALTER TABLE polls ADD COLUMN context TEXT;
```

This field will store the extracted content from URLs, files, or websites that users import when creating polls.

## Backend Changes

### Update Poll Service

1. Modify the `createPoll` function to accept and save the `context` field:

```typescript
// In polls.ts
const newPollData = {
  // existing fields
  context: pollData.context || null,
  source_url: pollData.source_url || null,
  source_type: pollData.source_type || null,
  source_filename: pollData.source_filename || null,
  show_source: pollData.show_source !== undefined ? pollData.show_source : false,
};
```

2. Update the `updatePoll` function to handle the `context` field:

```typescript
// In polls.ts
if (pollUpdates.context !== undefined) dbUpdates.context = pollUpdates.context;
```

### Update Poll Type Definitions

Update the Poll interface to include the context field:

```typescript
// In schemas.ts or types.ts
export interface Poll {
  // existing fields
  context?: string;
  source_url?: string;
  source_type?: string;
  source_filename?: string;
  show_source?: boolean;
}

// Also update PollDbUpdatePayload
export interface PollDbUpdatePayload {
  // existing fields
  context?: string;
  source_url?: string;
  source_type?: string;
  source_filename?: string;
  show_source?: boolean;
}
```

## Frontend Changes

### Rename "Import Content" to "Import Context"

Update the UI components to rename "Import Content" to "Import Context":

1. In the create poll page
2. In the conversational creation page

### Save Context When Creating Polls

Update the `handleCreateQuestions` function in the create poll page to save the extracted content as context:

```typescript
const pollData = {
  title: pollTitle,
  description: pollDescription,
  context: extractedContent, // Add this line
  source_attachment: attachSource ? {
    source_type: resourceType,
    source_url: resourceType === 'file' ? null : resourceValue,
    source_filename: file?.name || null,
    show_source: showSource
  } : null
};
```

### Display Context in Edit Poll Page

Add a new section in the edit poll page to display the saved context:

```tsx
{poll.context && (
  <Card className="mb-6">
    <CardHeader>
      <CardTitle>Poll Context</CardTitle>
      <CardDescription>
        This content was imported when creating the poll
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="prose max-w-none">
        <pre className="whitespace-pre-wrap bg-muted p-4 rounded-md overflow-auto max-h-[300px] text-sm border border-muted-foreground/20">
          {poll.context}
        </pre>
      </div>
    </CardContent>
  </Card>
)}
```

## Implementation Steps

1. Add the `context` column to the `polls` table in Supabase
2. Update the Poll type definitions
3. Modify the poll service functions to handle the context field
4. Update the UI to rename "Import Content" to "Import Context"
5. Update the create poll page to save the extracted content as context
6. Update the edit poll page to display the saved context

## Testing

1. Create a new poll with imported content
2. Verify that the content is saved in the database
3. Edit the poll and verify that the context is displayed
4. Test with different types of imports (URL, file, website)
