# Performance Optimization Environment Variables

This document outlines all environment variables used by the PollGPT performance optimization system.

## Redis Configuration

### REDIS_URL
- **Required**: Yes (for Redis-based caching)
- **Description**: Redis connection URL for caching and job queue
- **Format**: `redis://username:password@host:port` or `rediss://` for SSL
- **Example**: `redis://localhost:6379`
- **Alternative**: `UPSTASH_REDIS_REST_URL` for Upstash Redis

### UPSTASH_REDIS_REST_URL
- **Required**: Optional (alternative to REDIS_URL)
- **Description**: Upstash Redis REST URL for serverless environments
- **Format**: `https://your-redis-rest-url`
- **Example**: `https://us1-key-12345.upstash.io`

### UPSTASH_REDIS_REST_TOKEN
- **Required**: Yes (if using UPSTASH_REDIS_REST_URL)
- **Description**: Authentication token for Upstash Redis
- **Format**: String token
- **Example**: `AXXXaGVsbG8gZnJpZW5kIGhvdyBhcmUgeW91IGRvaW5nIHRvZGF5Pz1f`

## Database Configuration

### DATABASE_URL
- **Required**: Yes
- **Description**: PostgreSQL database connection URL
- **Format**: `postgresql://user:password@host:port/database`
- **Example**: `postgresql://postgres:password@localhost:5432/pollgpt`

### DB_POOL_MIN
- **Required**: No
- **Description**: Minimum number of database connections in pool
- **Default**: `2`
- **Range**: `1-10`
- **Example**: `2`

### DB_POOL_MAX
- **Required**: No
- **Description**: Maximum number of database connections in pool
- **Default**: `10`
- **Range**: `5-50`
- **Example**: `20`

### DB_POOL_IDLE_TIMEOUT
- **Required**: No
- **Description**: Idle timeout for database connections (milliseconds)
- **Default**: `30000` (30 seconds)
- **Example**: `60000`

### DB_QUERY_TIMEOUT
- **Required**: No
- **Description**: Query timeout for database operations (milliseconds)
- **Default**: `30000` (30 seconds)
- **Example**: `10000`

## Job Queue Configuration

### JOB_QUEUE_CONCURRENCY
- **Required**: No
- **Description**: Maximum number of concurrent job workers
- **Default**: `5`
- **Range**: `1-20`
- **Example**: `8`

### JOB_WORKER_COUNT
- **Required**: No
- **Description**: Number of background job worker processes
- **Default**: `4`
- **Range**: `1-10`
- **Example**: `6`

### JOB_RETRY_ATTEMPTS
- **Required**: No
- **Description**: Maximum retry attempts for failed jobs
- **Default**: `3`
- **Range**: `1-10`
- **Example**: `5`

### JOB_RETRY_DELAY
- **Required**: No
- **Description**: Base delay between job retries (milliseconds)
- **Default**: `1000` (1 second)
- **Example**: `2000`

## Cache Configuration

### CACHE_TTL_DEFAULT
- **Required**: No
- **Description**: Default TTL for cache entries (seconds)
- **Default**: `86400` (24 hours)
- **Example**: `3600`

### CACHE_WARMUP_ENABLED
- **Required**: No
- **Description**: Enable automatic cache warming
- **Default**: `true`
- **Values**: `true`, `false`
- **Example**: `true`

### CACHE_WARMUP_INTERVAL
- **Required**: No
- **Description**: Cache warmup interval (milliseconds)
- **Default**: `300000` (5 minutes)
- **Example**: `600000`

### CACHE_COMPRESSION_ENABLED
- **Required**: No
- **Description**: Enable cache value compression
- **Default**: `true`
- **Values**: `true`, `false`
- **Example**: `false`

## Rate Limiting Configuration

### RATE_LIMIT_WINDOW_MS
- **Required**: No
- **Description**: Rate limit window duration (milliseconds)
- **Default**: `900000` (15 minutes)
- **Example**: `3600000`

### RATE_LIMIT_MAX_REQUESTS
- **Required**: No
- **Description**: Maximum requests per window
- **Default**: `100`
- **Example**: `200`

### RATE_LIMIT_ENABLED
- **Required**: No
- **Description**: Enable rate limiting
- **Default**: `true`
- **Values**: `true`, `false`
- **Example**: `false`

### RATE_LIMIT_SKIP_ON_ERROR
- **Required**: No
- **Description**: Skip rate limiting when Redis is unavailable
- **Default**: `true`
- **Values**: `true`, `false`
- **Example**: `false`

## Performance Monitoring Configuration

### METRICS_RETENTION_DAYS
- **Required**: No
- **Description**: Number of days to retain performance metrics
- **Default**: `7`
- **Range**: `1-30`
- **Example**: `14`

### METRICS_COLLECTION_INTERVAL
- **Required**: No
- **Description**: Metrics collection interval (milliseconds)
- **Default**: `60000` (1 minute)
- **Example**: `30000`

### PERFORMANCE_ALERTS_ENABLED
- **Required**: No
- **Description**: Enable performance alerting
- **Default**: `true`
- **Values**: `true`, `false`
- **Example**: `false`

### SLOW_QUERY_THRESHOLD
- **Required**: No
- **Description**: Threshold for slow query alerts (milliseconds)
- **Default**: `2000` (2 seconds)
- **Example**: `5000`

## Alert System Configuration

### ALERT_WEBHOOK_URL
- **Required**: No
- **Description**: Webhook URL for alert notifications
- **Format**: `https://your-webhook-url.com/alerts`
- **Example**: `https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK`

### ALERT_EMAIL_FROM
- **Required**: No
- **Description**: From email address for alert notifications
- **Format**: `<EMAIL>`
- **Example**: `<EMAIL>`

### ALERT_EMAIL_TO
- **Required**: No
- **Description**: To email address for alert notifications
- **Format**: `<EMAIL>`
- **Example**: `<EMAIL>`

### ALERT_SMTP_HOST
- **Required**: No (if using email alerts)
- **Description**: SMTP server hostname
- **Example**: `smtp.gmail.com`

### ALERT_SMTP_PORT
- **Required**: No
- **Description**: SMTP server port
- **Default**: `587`
- **Example**: `465`

### ALERT_SMTP_USER
- **Required**: No (if using email alerts)
- **Description**: SMTP authentication username
- **Example**: `<EMAIL>`

### ALERT_SMTP_PASS
- **Required**: No (if using email alerts)
- **Description**: SMTP authentication password
- **Example**: `your-app-password`

### ALERT_SLACK_WEBHOOK
- **Required**: No
- **Description**: Slack webhook URL for notifications
- **Format**: `https://hooks.slack.com/services/...`
- **Example**: `*****************************************************************************`

### ALERT_DISCORD_WEBHOOK
- **Required**: No
- **Description**: Discord webhook URL for notifications
- **Format**: `https://discord.com/api/webhooks/...`
- **Example**: `https://discord.com/api/webhooks/*********/abcdefghijklmnop`

### ALERT_SMS_PROVIDER
- **Required**: No
- **Description**: SMS provider for alert notifications
- **Values**: `twilio`, `aws-sns`
- **Default**: `twilio`
- **Example**: `twilio`

### ALERT_TWILIO_SID
- **Required**: No (if using Twilio SMS)
- **Description**: Twilio Account SID
- **Example**: `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### ALERT_TWILIO_TOKEN
- **Required**: No (if using Twilio SMS)
- **Description**: Twilio Auth Token
- **Example**: `your_auth_token_here`

### ALERT_TWILIO_FROM
- **Required**: No (if using Twilio SMS)
- **Description**: Twilio phone number (from)
- **Format**: `+**********`
- **Example**: `+***********`

### ALERT_SMS_TO
- **Required**: No (if using SMS alerts)
- **Description**: Phone number for SMS alerts
- **Format**: `+**********`
- **Example**: `+***********`

## Benchmarking Configuration

### BENCHMARK_ENABLED
- **Required**: No
- **Description**: Enable performance benchmarking
- **Default**: `false`
- **Values**: `true`, `false`
- **Example**: `true`

### BENCHMARK_INTERVAL
- **Required**: No
- **Description**: Benchmark execution interval (milliseconds)
- **Default**: `3600000` (1 hour)
- **Example**: `7200000`

### BENCHMARK_ITERATIONS
- **Required**: No
- **Description**: Number of iterations for benchmark tests
- **Default**: `100`
- **Range**: `10-1000`
- **Example**: `50`

### BENCHMARK_CONCURRENCY
- **Required**: No
- **Description**: Concurrency level for load testing
- **Default**: `10`
- **Range**: `1-100`
- **Example**: `20`

## Development Configuration

### NODE_ENV
- **Required**: Yes
- **Description**: Node.js environment
- **Values**: `development`, `production`, `test`
- **Default**: `development`
- **Example**: `production`

### DEBUG_PERFORMANCE
- **Required**: No
- **Description**: Enable performance debugging logs
- **Default**: `false`
- **Values**: `true`, `false`
- **Example**: `true`

### LOG_LEVEL
- **Required**: No
- **Description**: Logging level for performance services
- **Values**: `error`, `warn`, `info`, `debug`
- **Default**: `info`
- **Example**: `debug`

## Example .env.local File

```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/pollgpt
DB_POOL_MIN=2
DB_POOL_MAX=10

# Redis Cache
REDIS_URL=redis://localhost:6379
# OR for Upstash
# UPSTASH_REDIS_REST_URL=https://your-redis-rest-url
# UPSTASH_REDIS_REST_TOKEN=your-token

# Job Queue
JOB_QUEUE_CONCURRENCY=5
JOB_WORKER_COUNT=4
JOB_RETRY_ATTEMPTS=3

# Caching
CACHE_TTL_DEFAULT=86400
CACHE_WARMUP_ENABLED=true
CACHE_COMPRESSION_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Performance Monitoring
METRICS_RETENTION_DAYS=7
PERFORMANCE_ALERTS_ENABLED=true
SLOW_QUERY_THRESHOLD=2000

# Alerts (Optional)
ALERT_WEBHOOK_URL=https://your-webhook-url.com/alerts
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# Development
NODE_ENV=development
DEBUG_PERFORMANCE=true
LOG_LEVEL=info
```

## Production Recommendations

### Essential Variables for Production:
1. `DATABASE_URL` - PostgreSQL connection
2. `REDIS_URL` or `UPSTASH_REDIS_REST_URL` - Redis cache
3. `NODE_ENV=production` - Production environment
4. `RATE_LIMIT_ENABLED=true` - Enable rate limiting
5. `PERFORMANCE_ALERTS_ENABLED=true` - Enable monitoring

### Performance Optimization:
1. Set `DB_POOL_MAX=20` for high traffic
2. Set `JOB_WORKER_COUNT=6` for background processing
3. Set `CACHE_TTL_DEFAULT=3600` for frequently updated data
4. Enable `CACHE_COMPRESSION_ENABLED=true` for large objects

### Monitoring & Alerts:
1. Configure `ALERT_WEBHOOK_URL` for instant notifications
2. Set `SLOW_QUERY_THRESHOLD=1000` for strict performance monitoring
3. Set `METRICS_RETENTION_DAYS=30` for historical analysis
4. Enable multiple alert channels (webhook, email, Slack)

### Security Considerations:
1. Use strong, unique values for all tokens and passwords
2. Enable SSL/TLS for Redis (`rediss://`) in production
3. Use environment-specific secrets management
4. Regularly rotate authentication tokens
5. Monitor failed authentication attempts through alerts
