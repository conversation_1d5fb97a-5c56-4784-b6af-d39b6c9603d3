// Complete Context Workflow Test - Browser Console
// Copy and paste this into the browser console while on the conversational poll creation page

(async function testCompleteContextWorkflow() {
  console.log('🧪 Testing Complete Context Workflow...\n');

  const testUrl = 'https://www.feedbackgpt.com';

  try {
    // Step 1: Extract content from URL
    console.log('1. Extracting content from URL...');
    const extractResponse = await fetch('/api/extract-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: testUrl })
    });

    if (!extractResponse.ok) {
      throw new Error(`URL extraction failed: ${extractResponse.status}`);
    }

    const extractData = await extractResponse.json();
    console.log('✅ URL extraction successful. Content length:', extractData.content.length);

    // Step 2: Summarize the content
    console.log('\n2. Summarizing content...');
    const summaryResponse = await fetch('/api/ai/summarize-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: extractData.content,
        sourceType: 'website',
        maxLength: 150
      })
    });

    if (!summaryResponse.ok) {
      throw new Error(`Summarization failed: ${summaryResponse.status}`);
    }

    const summaryData = await summaryResponse.json();
    console.log('✅ Content summarization successful:', summaryData.summary);

    // Step 3: Simulate saving context data to localStorage
    console.log('\n3. Saving context data to localStorage...');
    const contextData = {
      source: 'url',
      resourceName: testUrl,
      extractedContent: extractData.content,
      contextSummary: summaryData.summary
    };

    localStorage.setItem('pollgpt_pending_message', JSON.stringify(contextData));
    console.log('✅ Context data saved to localStorage');

    // Step 4: Check if the context data is properly formatted for database
    console.log('\n4. Validating database format...');
    const pollDataToCreate = {
      title: 'Test Poll with Context',
      description: 'Testing context workflow',
      questions: [
        {
          id: '1',
          type: 'multiple_choice',
          question: 'What do you think?',
          options: ['Option A', 'Option B']
        }
      ],
      context: summaryData.summary,
      source_url: testUrl,
      source_type: 'url',
      source_filename: null
    };

    console.log('✅ Database format validated:');
    console.log('  - context:', pollDataToCreate.context.substring(0, 50) + '...');
    console.log('  - source_url:', pollDataToCreate.source_url);
    console.log('  - source_type:', pollDataToCreate.source_type);

    // Step 5: Test actual poll creation API (read-only check)
    console.log('\n5. Testing poll creation API structure...');
    const pollCreateResponse = await fetch('/api/polls', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(pollDataToCreate)
    });

    console.log('Poll creation response status:', pollCreateResponse.status);

    if (pollCreateResponse.ok) {
      const pollResult = await pollCreateResponse.json();
      console.log('✅ Poll created successfully with ID:', pollResult.id);

      // Check if context was saved
      if (pollResult.context) {
        console.log('✅ Context saved to database:', pollResult.context.substring(0, 50) + '...');
      } else {
        console.log('❌ Context NOT saved to database');
      }

      if (pollResult.source_url) {
        console.log('✅ Source URL saved:', pollResult.source_url);
      } else {
        console.log('❌ Source URL NOT saved');
      }
    } else {
      const error = await pollCreateResponse.text();
      console.log('❌ Poll creation failed:', error);
    }

    console.log('\n🎉 Complete context workflow test finished!');
    return {
      extracted: extractData,
      summary: summaryData,
      contextData,
      pollDataToCreate
    };

  } catch (error) {
    console.error('❌ Context workflow test failed:', error.message);
    return null;
  }
})();
