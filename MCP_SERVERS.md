# PollGPT - MCP Servers, <PERSON><PERSON>peteer, and Playwright

This document explains how to use the Model Context Protocol (MCP) servers, Puppeteer, and Playwright integrations in the PollGPT project.

## Installed Tools

- **Puppeteer**: A Node.js library for controlling headless Chrome/Chromium
- **Playwright**: A framework for web testing and automation
- **MCP Servers**: Various Model Context Protocol servers for integrating AI capabilities

## MCP Servers

The following MCP servers are available in this project:

1. **Brave Search Server**: Provides web search functionality
2. **Sequential Thinking Server**: Helps with complex problem solving
3. **Puppeteer MCP Server**: Provides web automation capabilities
4. **Postgres Server**: Connects to PostgreSQL databases
5. **Custom Poll Scraper Server**: Custom implementation for scraping poll data

## Getting Started

### Starting MCP Servers

To start all MCP servers:

```bash
npm run mcp:start
```

### Environment Variables

Create a `.env` file with the following variables:

```env
# Brave Search API key
BRAVE_SEARCH_API_KEY=your-brave-search-api-key

# Database connection string (for Postgres MCP server)
DATABASE_URL=postgresql://username:password@localhost:5432/database
```

## Using Puppeteer

Puppeteer is already set up with utility functions in `src/lib/utils/puppeteer-utils.ts`:

```typescript
// Example: Scrape content from a webpage
import { scrapeWithPuppeteer } from '@/lib/utils/puppeteer-utils';

const content = await scrapeWithPuppeteer('https://example.com', '.main-content');
console.log(content);

// Example: Generate a PDF of a webpage
import { generatePdf } from '@/lib/utils/puppeteer-utils';

await generatePdf('https://example.com', './example.pdf');
```

## Using Playwright

Playwright is configured for testing and web automation with utilities in `src/lib/utils/scraping.ts`:

```typescript
// Example: Scrape content with Playwright
import { scrapeWithPlaywright } from '@/lib/utils/scraping';

const content = await scrapeWithPlaywright('https://example.com', '.main-content');
console.log(content);
```

### Running Playwright Tests

```bash
# Run all tests
npm test

# Run tests with UI
npm run test:ui

# Run tests in headed browsers
npm run test:headed
```

## MCP Server Examples

Examples of using MCP servers can be found in `src/lib/mcp/examples.ts`:

```typescript
// Example: Initialize MCP services
import { initializeMcpServices } from '@/lib/mcp/examples';

await initializeMcpServices();

// Example: Search with Brave
import { searchWithBrave } from '@/lib/mcp/examples';

const results = await searchWithBrave('latest poll results 2025');
console.log(results);

// Example: Scrape poll data
import { scrapePollData } from '@/lib/mcp/examples';

const pollData = await scrapePollData('https://example.com/poll');
console.log(pollData);
```

## Custom Poll Scraper

A custom MCP server for scraping poll data is available in `src/lib/mcp/poll-scraper.ts`. It can be used to extract poll questions and options from web pages:

```typescript
// Using the Poll Scraper via MCP SDK
import { sdk } from '@/lib/mcp';

// Make sure the poll scraper is registered first
const pollData = await sdk.invoke('poll-scraper', {
  url: 'https://example.com/poll',
  questionSelector: '.poll-question',
  optionsSelector: '.poll-option'
});

console.log(pollData);
```

## Best Practices

1. **Resource Management**: Always close browsers after use to prevent memory leaks
2. **Error Handling**: Implement proper error handling for web scraping operations
3. **Rate Limiting**: Respect website rate limits when scraping
4. **User Agents**: Set appropriate user agents when needed
5. **Headless Mode**: Use headless mode for production and non-headless for debugging