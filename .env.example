# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Perplexity AI API
PERPLEXITY_API_KEY=your-perplexity-api-key
NEXT_PUBLIC_PERPLEXITY_API_KEY=your-perplexity-api-key

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Mistral AI API
MISTRAL_API_KEY=your-mistral-api-key
NEXT_PUBLIC_MISTRAL_API_KEY=your-mistral-api-key

# Google AI (Gemini) API
GOOGLE_API_KEY=your-google-api-key

# OpenAI API (Fallback)
OPENAI_API_KEY=your-openai-api-key

# AI SDK Feature Flags
ENABLE_AI_SDK_STRUCTURED=true
ENABLE_AI_SDK_STREAMING=true
ENABLE_AI_SDK_TOOLS=true
ENABLE_AI_SDK_BATCH=true
ENABLE_AI_SDK_CIRCUIT_BREAKER=true
ENABLE_AI_SDK_CACHING=true
ENABLE_AI_SDK_MULTIMODAL=false
ENABLE_AI_SDK_SIMULATION=true

# Phase 2: Enhanced Content Extraction Feature Flags
ENABLE_AI_SDK_DOCUMENT_PROCESSING=true
ENABLE_AI_SDK_OCR_PROCESSING=true
ENABLE_AI_SDK_SENTIMENT_ANALYSIS=true
ENABLE_AI_SDK_BATCH_DOCUMENT_PROCESSING=true
ENABLE_AI_SDK_WEBSOCKET_PROGRESS=false
ENABLE_AI_SDK_ADVANCED_NLP=true

# Phase 3: Enhanced Simulation Feature Flags
ENABLE_AI_SDK_ENHANCED_SIMULATION=true
ENABLE_AI_SDK_STRUCTURED_SIMULATION_GENERATION=true
ENABLE_AI_SDK_BATCH_SIMULATION_PROCESSING=true
ENABLE_AI_SDK_DEMOGRAPHIC_ANALYSIS=true
ENABLE_AI_SDK_SIMULATION_INSIGHTS=true
ENABLE_AI_SDK_STREAMING_INSIGHTS=true
ENABLE_AI_SDK_SIMULATION_CONFIG_GENERATION=true
ENABLE_AI_SDK_SIMULATION_COMPARISON=true

# Phase 2: Enhanced Content Extraction
REDIS_URL=redis://localhost:6379
ENABLE_AI_SDK_OCR=true
ENABLE_AI_SDK_SENTIMENT=true
ENABLE_AI_SDK_WEBSOCKETS=true
ENABLE_AI_SDK_BATCH_PROCESSING=true
MAX_DOCUMENT_SIZE_MB=50
MAX_BATCH_SIZE=10
OCR_CONFIDENCE_THRESHOLD=0.8
