# Conversational Poll Creation AI SDK Upgrade Guide

## Overview

This document provides a comprehensive guide for upgrading the conversational poll creation page (`/dashboard/create/conversational`) to use the modern AI SDK infrastructure that has already been implemented in the codebase.

## Current State Analysis

### Existing API Endpoints Used by Conversational Page

The conversational page currently uses these **legacy** endpoints:

1. **`/api/gemini`** - Direct Gemini API calls
2. **`/api/mistral`** - Direct Mistral API calls
3. **`/api/chat`** - Generic chat endpoint (likely Perplexity)
4. **`/api/extract`** - URL content extraction
5. **`/api/extract-file`** - File content extraction

### Available AI SDK Endpoints (Already Implemented)

The following AI SDK endpoints are ready for integration:

- **`/api/ai/generate-poll`** - Structured poll generation with fallbacks
- **`/api/ai/analyze-content`** - Content analysis and extraction
- **`/api/ai/process-document`** - Document processing with OCR/NLP
- **`/api/ai/extract-content`** - Streaming content extraction
- **`/api/ai/optimize-poll`** - Poll optimization suggestions
- **`/api/ai/stream-poll-insights`** - Real-time poll insights

## Migration Strategy

### Phase 1: Core Chat Integration (Week 1)

#### 1.1 Replace Direct Model Calls

**Current Implementation:**
```typescript
// In conversational/page.tsx (lines 117-121)
const apiEndpoint = model === 'gemini' ? '/api/gemini' :
                   model === 'mistral' ? '/api/mistral' :
                   '/api/chat';
```

**Upgrade to AI SDK:**
```typescript
// New implementation using AI SDK
const apiEndpoint = '/api/ai/chat-completion';

// Call with model preference
const response = await fetch(apiEndpoint, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [...messages, userMessage],
    preferredModel: model, // 'gemini', 'mistral', or 'openai'
    context: 'poll-creation',
    features: ['structured-output', 'json-extraction']
  })
});
```

#### 1.2 Create New AI SDK Chat Endpoint

Create `/src/app/api/ai/chat-completion/route.ts`:

```typescript
import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { withFallback } from '@/lib/ai/fallback-service';
import { FeatureGate } from '@/lib/ai/feature-flags';

export async function POST(req: NextRequest) {
  try {
    const { messages, preferredModel, context, features } = await req.json();

    // Select provider based on preference with fallbacks
    const provider = aiProviders.getProvider(preferredModel) || aiProviders.conversational;

    return await FeatureGate.whenEnabledAsync(
      'unifiedChatCompletion',
      // AI SDK approach
      async () => {
        return withFallback(
          async () => {
            const result = await streamText({
              model: provider,
              messages: buildConversationalMessages(messages, context),
              temperature: 0.7,
              maxTokens: 2048
            });

            return result.toAIStreamResponse();
          },
          // Fallback to existing endpoints
          async () => {
            return legacyFallback(preferredModel, messages);
          },
          'Unified Chat Completion'
        );
      },
      // Feature disabled: Use existing logic
      async () => {
        return legacyFallback(preferredModel, messages);
      }
    );
  } catch (error) {
    console.error('Chat completion error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
```

### Phase 2: Structured Poll Generation (Week 2)

#### 2.1 Replace JSON Extraction Logic

**Current Implementation:**
```typescript
// In conversational/page.tsx (lines 657-730)
// Complex JSON extraction from message content
const extractedData = extractPollDataFromMessage(lastMessage.content);
```

**Upgrade to AI SDK:**
```typescript
// Use structured generation instead of JSON extraction
const response = await fetch('/api/ai/generate-poll', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    conversationHistory: messages,
    extractStructuredPoll: true,
    preferredModel: selectedModel
  })
});

const pollData = await response.json();
// No JSON parsing needed - already structured
```

#### 2.2 Update Poll Creation Flow

**Current Implementation:**
```typescript
// Manual poll creation with complex formatting
const formattedQuestions: PollQuestion[] = pollSuggestion.questions.map((q, index) => {
  // Complex mapping logic...
});
```

**Upgrade to AI SDK:**
```typescript
// Structured poll creation with validation
const response = await fetch('/api/ai/generate-poll', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    topic: extractedTopic,
    audience: extractedAudience,
    conversationContext: messages,
    outputFormat: 'supabase-ready'
  })
});

const { poll, questions } = await response.json();
// Questions are already formatted for database insertion
```

### Phase 3: Enhanced Content Extraction (Week 3)

#### 3.1 Replace URL/File Extraction

**Current Implementation:**
```typescript
// In conversational/page.tsx (lines 314-400)
const extractionResponse = await fetch('/api/extract', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ url: resource }),
});
```

**Upgrade to AI SDK:**
```typescript
// Streaming content extraction with progress
const response = await fetch('/api/ai/extract-content', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    source: resource,
    type: 'url', // or 'file'
    extractionGoal: 'poll-content',
    streaming: true
  })
});

// Handle streaming response
const reader = response.body?.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const data = JSON.parse(chunk);

  // Update UI with extraction progress
  setExtractionProgress(data.progress);
  if (data.content) {
    setExtractedContent(data.content);
  }
}
```

#### 3.2 Add Real-time Processing Feedback

```typescript
// Enhanced extraction with real-time feedback
const useStreamingExtraction = (source: string, type: 'url' | 'file') => {
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState('');
  const [content, setContent] = useState('');

  const extract = async () => {
    const response = await fetch('/api/ai/extract-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        source,
        type,
        extractionGoal: 'poll-content',
        streaming: true,
        includeProgress: true
      })
    });

    // Process Server-Sent Events
    const eventSource = new EventSource(
      `/api/ai/extract-content/stream?source=${encodeURIComponent(source)}&type=${type}`
    );

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'progress':
          setProgress(data.progress);
          setStage(data.stage);
          break;
        case 'content':
          setContent(data.content);
          break;
        case 'complete':
          eventSource.close();
          break;
      }
    };
  };

  return { progress, stage, content, extract };
};
```

### Phase 4: Advanced Features Integration (Week 4)

#### 4.1 Add Poll Optimization

```typescript
// After poll generation, suggest optimizations
const optimizeGeneratedPoll = async (pollData: PollSuggestion) => {
  const response = await fetch('/api/ai/optimize-poll', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      poll: pollData,
      optimizationGoals: ['response-rate', 'unbiased-questions', 'clarity'],
      targetAudience: extractedAudience
    })
  });

  const { suggestions, optimizedPoll, confidence } = await response.json();

  // Show optimization suggestions to user
  setOptimizationSuggestions(suggestions);

  if (confidence > 0.8) {
    // Auto-apply high-confidence optimizations
    setPollSuggestion(optimizedPoll);
  }
};
```

#### 4.2 Add Real-time Insights

```typescript
// Stream insights as user types
const useRealTimeInsights = (messages: Message[]) => {
  const [insights, setInsights] = useState<string[]>([]);

  useEffect(() => {
    if (messages.length === 0) return;

    const debounceTimer = setTimeout(async () => {
      const response = await fetch('/api/ai/stream-poll-insights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationHistory: messages,
          insightTypes: ['audience-analysis', 'topic-suggestions', 'question-ideas']
        })
      });

      // Process streaming insights
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const insightData = JSON.parse(chunk);

        setInsights(prev => [...prev, insightData.insight]);
      }
    }, 1000);

    return () => clearTimeout(debounceTimer);
  }, [messages]);

  return insights;
};
```

## Implementation Steps

### Step 1: Update Dependencies

Ensure AI SDK dependencies are properly configured:

```json
// package.json updates (if needed)
{
  "dependencies": {
    "ai": "^3.0.0",
    "@ai-sdk/openai": "^0.0.20",
    "@ai-sdk/google": "^0.0.15",
    "@ai-sdk/mistral": "^0.0.10"
  }
}
```

### Step 2: Environment Variables

Add/verify environment variables:

```bash
# .env.local
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_google_key
MISTRAL_API_KEY=your_mistral_key

# Feature flags
FEATURE_UNIFIED_CHAT_COMPLETION=true
FEATURE_STRUCTURED_GENERATION=true
FEATURE_STREAMING_EXTRACTION=true
```

### Step 3: Update Conversational Page Component

**File:** `/src/app/(dashboard)/dashboard/create/conversational/page.tsx`

#### 3.1 Replace API Calls

```typescript
// Replace lines 117-135
const handleSendMessage = async (content: string, model: AIModel = 'gemini') => {
  if (!content.trim()) return;

  const userMessage: Message = { role: 'user', content };
  setMessages(prev => [...prev, userMessage]);
  setIsLoading(true);

  try {
    // Use unified AI SDK endpoint
    const response = await fetch('/api/ai/chat-completion', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [...messages, userMessage],
        preferredModel: model,
        context: 'poll-creation',
        features: ['structured-output', 'json-extraction'],
        extractPollData: true // Signal to extract poll data if present
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    // Handle both streaming and structured responses
    const contentType = response.headers.get('Content-Type') || '';

    if (contentType.includes('text/event-stream')) {
      await handleStreamingResponse(response);
    } else {
      const data = await response.json();

      // Check if response contains structured poll data
      if (data.pollData) {
        handleStructuredPollData(data.pollData);
      } else {
        // Regular chat response
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: data.content || data.message
        }]);
      }
    }
  } catch (error) {
    console.error('Chat API error:', error);
    // Fallback to original implementation
    await handleSendMessageFallback(content, model);
  } finally {
    setIsLoading(false);
  }
};
```

#### 3.2 Handle Structured Poll Data

```typescript
// Add new function to handle structured poll responses
const handleStructuredPollData = (pollData: any) => {
  // Convert AI SDK format to internal format
  const formattedPoll: PollSuggestion = {
    title: pollData.title,
    description: pollData.description,
    questions: pollData.questions.map((q: any) => ({
      text: q.text,
      type: q.type,
      options: q.options?.map((opt: any) => opt.text) || [],
      required: q.required
    }))
  };

  setPollSuggestion(formattedPoll);
  setActiveStep('review');

  // Add confirmation message
  setMessages(prev => [...prev, {
    role: 'assistant',
    content: `I've created a poll draft for you! The poll "${pollData.title}" has ${pollData.questions.length} questions. You can review and edit it now.`,
    processed: true
  }]);

  toast.success('Poll draft created! Review and finalize your poll.');
};
```

#### 3.3 Update Content Extraction

```typescript
// Replace lines 290-420 with AI SDK extraction
const handleExtractResource = async (type: 'url' | 'website' | 'file', resource: string | File) => {
  if (!resource) return;

  setIsExtracting(true);
  const toastId = toast.loading(`Extracting content from ${type}...`);

  try {
    let content = '';

    if (type === 'url' && typeof resource === 'string') {
      // Use AI SDK streaming extraction
      const response = await fetch('/api/ai/extract-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          source: resource,
          type: 'url',
          extractionGoal: 'poll-content',
          streaming: false // For simplicity, use non-streaming first
        })
      });

      if (!response.ok) {
        throw new Error(`Extraction failed: ${response.status}`);
      }

      const data = await response.json();
      content = data.content;

    } else if (type === 'file' && resource instanceof File) {
      // Use AI SDK document processing
      const formData = new FormData();
      formData.append('file', resource);
      formData.append('extractionGoal', 'poll-content');

      const response = await fetch('/api/ai/process-document', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Document processing failed: ${response.status}`);
      }

      const data = await response.json();
      content = data.content;
    }

    if (!content || content.trim() === '') {
      throw new Error('No content could be extracted');
    }

    toast.success('Content extracted successfully!', { id: toastId });

    // Send extracted content to AI chat with context
    const extractMessage = `I want to create a poll based on this content: ${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}`;
    handleSendMessage(extractMessage);
    setActiveTab('chat');

  } catch (error) {
    console.error('Extraction error:', error);
    toast.error(`Failed to extract content: ${error.message}`, { id: toastId });
  } finally {
    setIsExtracting(false);
  }
};
```

### Step 4: Feature Flag Configuration

Update feature flags to enable AI SDK features:

```typescript
// In /src/lib/ai/feature-flags.ts
export const FeatureFlags = {
  conversationalAiSdk: true,
  structuredPollGeneration: true,
  streamingExtraction: true,
  realTimeInsights: false, // Gradually enable
  pollOptimization: false  // Future feature
};
```

### Step 5: Error Handling and Fallbacks

```typescript
// Add comprehensive fallback system
const handleSendMessageFallback = async (content: string, model: AIModel) => {
  // Fall back to original implementation
  const apiEndpoint = model === 'gemini' ? '/api/gemini' :
                     model === 'mistral' ? '/api/mistral' :
                     '/api/chat';

  const response = await fetch(apiEndpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      messages: [...messages, { role: 'user', content }],
      model: model
    })
  });

  // Process response using original logic
  // ... existing implementation
};
```

## Benefits of Upgrade

### 1. **Structured Data Handling**
- No more complex JSON extraction from text
- Type-safe poll data structures
- Automatic validation and error handling

### 2. **Improved Reliability**
- Multi-provider fallbacks
- Circuit breaker patterns
- Graceful degradation

### 3. **Enhanced User Experience**
- Real-time extraction progress
- Streaming responses
- Better error messages

### 4. **Developer Experience**
- Unified API interface
- Feature flags for gradual rollout
- Better debugging and monitoring

### 5. **Performance**
- Caching at provider level
- Optimized token usage
- Reduced API call overhead

## Testing Strategy

### 1. **Gradual Rollout**
```typescript
// Use feature flags for gradual rollout
const shouldUseAiSdk = FeatureGate.isEnabled('conversationalAiSdk') &&
                      Math.random() < 0.1; // 10% of users initially
```

### 2. **A/B Testing**
- Compare response quality between old and new systems
- Monitor error rates and user satisfaction
- Track poll creation success rates

### 3. **Monitoring**
- Add analytics for AI SDK usage
- Monitor API response times
- Track fallback activation rates

## Migration Timeline

### Week 1: Foundation
- [ ] Create new AI SDK chat endpoint
- [ ] Update core chat functionality
- [ ] Implement basic fallback system

### Week 2: Poll Generation
- [ ] Replace JSON extraction with structured generation
- [ ] Update poll creation flow
- [ ] Test with various conversation types

### Week 3: Content Extraction
- [ ] Upgrade URL/file extraction
- [ ] Add streaming progress indicators
- [ ] Implement enhanced error handling

### Week 4: Advanced Features
- [ ] Add poll optimization suggestions
- [ ] Implement real-time insights
- [ ] Performance optimization and testing

### Week 5: Rollout
- [ ] Gradual feature flag rollout
- [ ] Monitor metrics and feedback
- [ ] Full deployment

## Risk Mitigation

### 1. **Fallback Systems**
- Always maintain fallback to existing endpoints
- Graceful degradation when AI SDK fails
- User experience remains consistent

### 2. **Feature Flags**
- Ability to instantly rollback problematic features
- Gradual rollout to minimize impact
- A/B testing for validation

### 3. **Monitoring**
- Real-time error tracking
- Performance monitoring
- User satisfaction metrics

## Success Metrics

1. **Technical Metrics**
   - API response time improvement: Target 20% faster
   - Error rate reduction: Target <2%
   - Successful poll creation rate: Target >95%

2. **User Experience Metrics**
   - Time to create poll: Target 30% reduction
   - User satisfaction scores
   - Feature adoption rates

3. **Business Metrics**
   - Poll quality scores
   - User retention
   - Feature usage analytics

## Conclusion

This upgrade will modernize the conversational poll creation experience while maintaining reliability through comprehensive fallback systems. The phased approach ensures minimal risk while delivering significant improvements in functionality, performance, and user experience.

The existing AI SDK infrastructure provides a solid foundation for this upgrade, with structured generation, multi-provider support, and advanced features like streaming and optimization already implemented and ready for integration.
