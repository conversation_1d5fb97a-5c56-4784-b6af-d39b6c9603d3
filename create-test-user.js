// Development script to create test users directly in Supabase
// Run this with: node create-test-user.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_MCP_KEY; // Service key, not anon key

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestUser() {
  const testEmail = `test+${Date.now()}@example.com`;
  const testPassword = 'testpassword123';
  const testName = 'Test User';

  console.log('Creating test user:', testEmail);

  try {
    // Create user with admin client (bypasses rate limiting)
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      user_metadata: {
        name: testName
      },
      email_confirm: true // Auto-confirm email
    });

    if (authError) {
      console.error('Auth error:', authError);
      return;
    }

    console.log('User created successfully:', authData.user.id);

    // Create profile
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        name: testName,
        email: testEmail,
        created_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('Profile error:', profileError);
      return;
    }

    console.log('Profile created successfully');
    console.log('Test user credentials:');
    console.log('Email:', testEmail);
    console.log('Password:', testPassword);
    console.log('You can now login with these credentials to test the onboarding modal');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTestUser();
