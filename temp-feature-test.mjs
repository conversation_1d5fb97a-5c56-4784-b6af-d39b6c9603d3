
      import { getAISDKFeatures, isFeatureEnabled } from './src/lib/ai/feature-flags.ts';

      const features = getAISDKFeatures();
      console.log('Phase 2 Features:', {
        documentProcessing: features.documentProcessing,
        ocrProcessing: features.ocrProcessing,
        sentimentAnalysis: features.sentimentAnalysis,
        batchDocumentProcessing: features.batchDocumentProcessing,
        websocketProgressTracking: features.websocketProgressTracking,
        advancedNLP: features.advancedNLP
      });

      console.log('Document processing enabled:', isFeatureEnabled('documentProcessing'));
      console.log('OCR processing enabled:', isFeatureEnabled('ocrProcessing'));
      console.log('Sentiment analysis enabled:', isFeatureEnabled('sentimentAnalysis'));
    