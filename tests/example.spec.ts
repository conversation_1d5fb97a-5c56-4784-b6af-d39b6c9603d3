import { test, expect } from '@playwright/test';

test('has title', async ({ page }) => {
  await page.goto('/');

  // Expect a title to contain the poll app name
  await expect(page).toHaveTitle(/PollGPT/);
});

test('can navigate to main page', async ({ page }) => {
  await page.goto('/');

  // Test if the page loads correctly
  await expect(page.getByRole('heading', { name: /PollGPT/i, exact: false })).toBeVisible();
});