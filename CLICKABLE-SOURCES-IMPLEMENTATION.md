# Clickable Sources Implementation for Poll Simulations

## Overview
This document outlines the implementation of clickable sources in poll simulation results, enabling users to access the actual research and data sources that inform the AI's demographic simulations.

## Problem Solved
Previously, simulation results displayed citations as plain text, making it difficult for users to:
- Verify the authenticity of simulation data
- Access original research sources
- Trust the credibility of simulation results
- Follow up on interesting findings

## Solution Implementation

### 1. Enhanced Perplexity API Integration

#### API Response Capture
- **Citations Array**: Captures direct URLs from Perplexity API responses
- **Search Results**: Captures structured data with title, URL, and date
- **Fallback Handling**: Ensures sources are always available, even when API doesn't return structured data

#### Key Files Modified:
- `src/lib/services/perplexity-ai.ts` - Enhanced to capture both `citations` and `search_results` from API
- `src/app/api/perplexity/route.ts` - Updated to pass through citation data from API responses
- `src/lib/types/simulation.ts` - Added `searchResults` field to simulation metadata

### 2. Interactive Sources Component

#### Enhanced UI Features:
- **Clickable Links**: Sources are now buttons that open URLs in new tabs
- **Visual Feedback**: Hover effects with external link icons
- **URL Display**: Shows actual URLs for transparency
- **Structured Data**: Prioritizes search_results over plain citations for better UX
- **Date Information**: Displays publication dates when available

#### Component Updates:
- `src/components/simulation/simulation-results.tsx` - Complete redesign of sources section

### 3. Improved Prompt Engineering

#### Enhanced System Prompt:
- Encourages AI to provide URLs when possible
- Requests credible academic and research sources
- Promotes transparency in source attribution

#### Key Changes:
- `src/lib/utils/prompt-builder.ts` - Updated simulation prompts to encourage URL-based citations

## Technical Implementation Details

### Data Flow
1. **Simulation Request** → Perplexity API with enhanced prompts
2. **API Response** → Captures both `citations` and `search_results`
3. **Data Processing** → Extracts URLs and structured source information
4. **UI Rendering** → Displays clickable sources with hover effects
5. **User Interaction** → Opens sources in new browser tabs

### Source Prioritization
1. **Primary**: `searchResults` - Structured data with title, URL, date
2. **Fallback**: `citations` - URL extraction from citation text
3. **Default**: Generic simulation attribution when no sources available

### URL Extraction Logic
```typescript
function extractUrlFromCitation(citation: string): { url: string; title: string } {
  // Handles various citation formats:
  // - Direct URLs (http/https)
  // - URLs in parentheses (Source Name)[URL]
  // - URLs in brackets [URL]
  // - Mixed text with embedded URLs
}
```

### Security & UX Considerations
- **New Tab Opening**: All external links open in new tabs with `noopener,noreferrer`
- **URL Validation**: Ensures only valid HTTP/HTTPS URLs are clickable
- **Graceful Degradation**: Falls back to text display when URLs unavailable
- **Hover Tooltips**: Shows full URL on hover for transparency

## User Experience Improvements

### Before
- Sources displayed as plain text
- No way to verify simulation accuracy
- Limited trust in AI-generated data
- Difficult to follow up on research

### After
- **Clickable Sources**: Direct access to original research
- **Visual Indicators**: Clear UI showing which sources are clickable
- **Structured Information**: Title, URL, and date display
- **Enhanced Trust**: Users can verify simulation credibility
- **Better Transparency**: Full URL visibility and easy access

## API Response Structure

### Perplexity API Returns:
```json
{
  "choices": [...],
  "citations": [
    "https://example.com/research-paper",
    "https://academic-journal.com/article"
  ],
  "search_results": [
    {
      "title": "Research Paper Title",
      "url": "https://example.com/research-paper",
      "date": "2024-01-15"
    }
  ]
}
```

### Simulation Response Structure:
```typescript
interface SimulationResponse {
  metadata: {
    citations: string[];                    // Array of URLs or citation text
    searchResults?: Array<{                // Structured source data (preferred)
      title: string;
      url: string;
      date?: string;
    }>;
  };
  // ... other fields
}
```

## Future Enhancements

### Possible Improvements:
1. **Source Validation**: Verify URL accessibility before display
2. **Citation Formatting**: Automatic academic citation formatting
3. **Source Categorization**: Group sources by type (academic, government, industry)
4. **DOI Integration**: Direct linking to DOI-based academic papers
5. **Source Preview**: Inline previews or summaries of source content
6. **Bias Detection**: Analyze and flag potential source bias
7. **Source Quality Scoring**: Rate sources by credibility and relevance

### Context7 Integration:
- Leverage Context7 MCP tools for enhanced source research
- Automatic fact-checking against known databases
- Cross-reference sources for consistency

## Testing & Validation

### Test Cases:
1. **With search_results**: Verify structured data display
2. **With citations only**: Test URL extraction logic
3. **No sources available**: Ensure graceful fallback
4. **Mixed source types**: Handle both structured and unstructured data
5. **Invalid URLs**: Prevent clicks on malformed links

### Quality Assurance:
- All external links tested for proper tab opening
- Hover effects verified across different browsers
- Mobile responsiveness confirmed
- Accessibility standards met (keyboard navigation, screen readers)

## Benefits

### For Users:
- **Increased Trust**: Can verify simulation accuracy
- **Educational Value**: Access to primary research sources
- **Transparency**: Clear understanding of data sources
- **Research Capability**: Easy follow-up on interesting findings

### For the Platform:
- **Credibility**: Demonstrates commitment to accurate, source-backed data
- **Differentiation**: Unique feature compared to other polling platforms
- **User Engagement**: Encourages deeper exploration of results
- **Trust Building**: Shows transparency in AI decision-making

## Conclusion

This implementation transforms poll simulation results from black-box AI outputs into transparent, verifiable research backed by real academic sources. Users can now click directly through to the studies and data that inform each simulation, dramatically improving trust and educational value.

The solution leverages Perplexity's real-time web search capabilities while providing a polished, user-friendly interface for accessing source materials. This creates a more credible and educational polling simulation experience.
