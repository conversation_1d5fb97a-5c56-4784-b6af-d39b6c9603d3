const { Pool } = require('pg');

async function main() {
  // Connection string from the MCP config in your VS Code settings.json
  const connectionString = 'postgresql://postgres.sumruaeyfidjlssrmfrm:<EMAIL>:6543/postgres';

  try {
    console.log('Testing connection to Supabase PostgreSQL database...');
    const pool = new Pool({ connectionString });

    console.log('Executing query on polls table...');
    const result = await pool.query('SELECT * FROM polls LIMIT 1');

    if (result.rows && result.rows.length > 0) {
      console.log('✅ Connection successful!');
      console.log(`Found ${result.rows.length} poll(s):`);
      console.log(JSON.stringify(result.rows[0], null, 2));
    } else {
      console.log('✅ Connection successful, but no polls found in the database.');
    }

    await pool.end();

  } catch (error) {
    console.error('❌ Error connecting to database:', error);
  }
}

main();
