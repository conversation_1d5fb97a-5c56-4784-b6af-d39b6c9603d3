#!/usr/bin/env node
"use strict";
/**
 * Performance Optimization Integration Script
 *
 * Integrates all performance optimization services with the Next.js application:
 * - Job queue worker startup
 * - Cache warming initialization
 * - Performance monitoring setup
 * - Rate limiting configuration
 * - Database connection pooling
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceOptimizationManager = void 0;
// Import performance services
var job_queue_1 = require("../src/lib/services/job-queue");
var cache_warmer_1 = require("../src/lib/services/cache-warmer");
var performance_monitor_1 = require("../src/lib/services/performance-monitor");
var connection_pool_1 = require("../src/lib/services/connection-pool");
var rate_limiter_1 = require("../src/lib/services/rate-limiter");
var performance_benchmark_1 = require("../src/lib/services/performance-benchmark");
/**
 * Performance Optimization Manager
 * Orchestrates all performance services
 */
var PerformanceOptimizationManager = /** @class */ (function () {
    function PerformanceOptimizationManager() {
        this.services = new Map();
        this.isInitialized = false;
    }
    PerformanceOptimizationManager.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var jobQueue, cacheWarmer, performanceMonitor, dbClient, rateLimiter, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.isInitialized) {
                            console.log('Performance optimization already initialized');
                            return [2 /*return*/];
                        }
                        console.log('🚀 Initializing Performance Optimization Services...');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 5, , 6]);
                        // 1. Initialize Job Queue
                        console.log('📋 Starting Job Queue...');
                        jobQueue = new job_queue_1.JobQueue();
                        return [4 /*yield*/, jobQueue.startProcessing()];
                    case 2:
                        _a.sent();
                        this.services.set('jobQueue', jobQueue);
                        console.log('✅ Job Queue started');
                        // 2. Initialize Cache Warming
                        console.log('🔥 Starting Cache Warming...');
                        cacheWarmer = new cache_warmer_1.CacheWarmer();
                        return [4 /*yield*/, cacheWarmer.start()];
                    case 3:
                        _a.sent();
                        this.services.set('cacheWarmer', cacheWarmer);
                        console.log('✅ Cache Warming started');
                        // 3. Initialize Performance Monitoring
                        console.log('📊 Starting Performance Monitoring...');
                        performanceMonitor = performance_monitor_1.PerformanceMonitor.getInstance();
                        // Performance monitor doesn't have a start method - it's initialized via getInstance()
                        this.services.set('performanceMonitor', performanceMonitor);
                        console.log('✅ Performance Monitoring started');
                        // 4. Initialize Database Connection Pool
                        console.log('🗄️ Setting up Database Connection Pool...');
                        dbClient = new connection_pool_1.EnhancedDatabaseClient();
                        // Connection pool will initialize automatically
                        this.services.set('dbClient', dbClient);
                        console.log('✅ Database Connection Pool ready');
                        // 5. Configure Rate Limiting
                        console.log('🛡️ Configuring Rate Limiting...');
                        rateLimiter = new rate_limiter_1.RateLimiter();
                        this.services.set('rateLimiter', rateLimiter);
                        console.log('✅ Rate Limiting configured');
                        // 6. Setup graceful shutdown
                        this.setupGracefulShutdown();
                        this.isInitialized = true;
                        console.log('🎉 Performance Optimization Services initialized successfully!');
                        // 7. Run initial health check
                        return [4 /*yield*/, this.runHealthCheck()];
                    case 4:
                        // 7. Run initial health check
                        _a.sent();
                        return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        console.error('❌ Failed to initialize Performance Optimization Services:', error_1);
                        process.exit(1);
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.runHealthCheck = function () {
        return __awaiter(this, void 0, void 0, function () {
            var healthChecks, results, healthyServices;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('🏥 Running health check...');
                        healthChecks = [
                            this.checkJobQueue(),
                            this.checkCacheWarming(),
                            this.checkPerformanceMonitoring(),
                            this.checkDatabaseConnection(),
                            this.checkRateLimiting()
                        ];
                        return [4 /*yield*/, Promise.allSettled(healthChecks)];
                    case 1:
                        results = _a.sent();
                        healthyServices = 0;
                        results.forEach(function (result, index) {
                            var serviceName = ['Job Queue', 'Cache Warming', 'Performance Monitoring', 'Database', 'Rate Limiting'][index];
                            if (result.status === 'fulfilled') {
                                console.log("\u2705 ".concat(serviceName, ": Healthy"));
                                healthyServices++;
                            }
                            else {
                                console.log("\u274C ".concat(serviceName, ": ").concat(result.reason));
                            }
                        });
                        console.log("\uD83D\uDCCA Health Check Complete: ".concat(healthyServices, "/").concat(results.length, " services healthy"));
                        if (healthyServices < results.length) {
                            console.warn('⚠️ Some services are not healthy. Check logs for details.');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.runPerformanceBenchmark = function () {
        return __awaiter(this, void 0, void 0, function () {
            var performanceBenchmark, dbBenchmark, cacheBenchmark, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('🔬 Running performance benchmarks...');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        performanceBenchmark = new performance_benchmark_1.PerformanceBenchmark();
                        return [4 /*yield*/, performanceBenchmark.benchmarkDatabaseQueries({
                                name: 'startup_db_benchmark',
                                description: 'Database performance check on startup',
                                iterations: 100,
                                concurrency: 5,
                                warmupIterations: 10
                            })];
                    case 2:
                        dbBenchmark = _a.sent();
                        console.log("\uD83D\uDCCA Database Benchmark Results:");
                        console.log("   Average Response Time: ".concat(dbBenchmark.metrics.responseTime.mean.toFixed(2), "ms"));
                        console.log("   P95 Response Time: ".concat(dbBenchmark.metrics.responseTime.p95.toFixed(2), "ms"));
                        console.log("   Success Rate: ".concat(((dbBenchmark.successCount / dbBenchmark.iterations) * 100).toFixed(1), "%"));
                        return [4 /*yield*/, performanceBenchmark.benchmarkCachePerformance({
                                name: 'startup_cache_benchmark',
                                description: 'Cache performance check on startup',
                                iterations: 50,
                                concurrency: 3,
                                warmupIterations: 5
                            })];
                    case 3:
                        cacheBenchmark = _a.sent();
                        console.log("\uD83D\uDD25 Cache Benchmark Results:");
                        console.log("   Average Response Time: ".concat(cacheBenchmark.metrics.responseTime.mean.toFixed(2), "ms"));
                        console.log("   P95 Response Time: ".concat(cacheBenchmark.metrics.responseTime.p95.toFixed(2), "ms"));
                        console.log("   Success Rate: ".concat(((cacheBenchmark.successCount / cacheBenchmark.iterations) * 100).toFixed(1), "%"));
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _a.sent();
                        console.error('❌ Benchmark failed:', error_2);
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.warmCriticalCaches = function () {
        return __awaiter(this, void 0, void 0, function () {
            var cacheWarmer, warmer, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('🔥 Warming critical caches...');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 5, , 6]);
                        cacheWarmer = this.services.get('cacheWarmer');
                        if (!cacheWarmer) {
                            throw new Error('Cache warmer service not initialized');
                        }
                        warmer = cacheWarmer;
                        // Warm popular polls
                        return [4 /*yield*/, warmer.warmStrategy('popular_polls')];
                    case 2:
                        // Warm popular polls
                        _a.sent();
                        console.log('✅ Popular polls cache warmed');
                        // Warm API responses
                        return [4 /*yield*/, warmer.warmStrategy('api_responses')];
                    case 3:
                        // Warm API responses
                        _a.sent();
                        console.log('✅ API responses cache warmed');
                        // Trigger predictive content warming
                        return [4 /*yield*/, warmer.warmOnEvent('startup_cache_warm')];
                    case 4:
                        // Trigger predictive content warming
                        _a.sent();
                        console.log('✅ Predictive content warming triggered');
                        return [3 /*break*/, 6];
                    case 5:
                        error_3 = _a.sent();
                        console.error('❌ Cache warming failed:', error_3);
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.setupMetricsCollection = function () {
        return __awaiter(this, void 0, void 0, function () {
            var performanceMonitor, monitor;
            return __generator(this, function (_a) {
                console.log('📊 Setting up metrics collection...');
                performanceMonitor = this.services.get('performanceMonitor');
                if (performanceMonitor) {
                    monitor = performanceMonitor;
                    // Set up custom alerts for production
                    monitor.registerAlert({
                        id: 'startup_high_memory',
                        metric: 'memory_usage',
                        condition: 'above',
                        threshold: 500,
                        message: 'High memory usage detected during startup',
                        active: true
                    });
                    monitor.registerAlert({
                        id: 'startup_slow_queries',
                        metric: 'avg_query_time',
                        condition: 'above',
                        threshold: 2000,
                        message: 'Slow database queries detected during startup',
                        active: true
                    });
                    console.log('✅ Custom alerts configured');
                }
                return [2 /*return*/];
            });
        });
    };
    PerformanceOptimizationManager.prototype.setupGracefulShutdown = function () {
        var _this = this;
        var shutdown = function (signal) { return __awaiter(_this, void 0, void 0, function () {
            var _i, _a, _b, name_1, service, typedService, error_4;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        console.log("\n\uD83D\uDED1 Received ".concat(signal, ". Shutting down gracefully..."));
                        _c.label = 1;
                    case 1:
                        _c.trys.push([1, 8, , 9]);
                        _i = 0, _a = this.services.entries();
                        _c.label = 2;
                    case 2:
                        if (!(_i < _a.length)) return [3 /*break*/, 7];
                        _b = _a[_i], name_1 = _b[0], service = _b[1];
                        console.log("\u23F9\uFE0F Stopping ".concat(name_1, "..."));
                        if (!(service && typeof service === 'object')) return [3 /*break*/, 6];
                        typedService = service;
                        if (!(typeof typedService.stop === 'function')) return [3 /*break*/, 4];
                        return [4 /*yield*/, typedService.stop()];
                    case 3:
                        _c.sent();
                        return [3 /*break*/, 6];
                    case 4:
                        if (!(typeof typedService.close === 'function')) return [3 /*break*/, 6];
                        return [4 /*yield*/, typedService.close()];
                    case 5:
                        _c.sent();
                        _c.label = 6;
                    case 6:
                        _i++;
                        return [3 /*break*/, 2];
                    case 7:
                        console.log('✅ All services stopped gracefully');
                        process.exit(0);
                        return [3 /*break*/, 9];
                    case 8:
                        error_4 = _c.sent();
                        console.error('❌ Error during shutdown:', error_4);
                        process.exit(1);
                        return [3 /*break*/, 9];
                    case 9: return [2 /*return*/];
                }
            });
        }); };
        process.on('SIGINT', function () { return shutdown('SIGINT'); });
        process.on('SIGTERM', function () { return shutdown('SIGTERM'); });
        process.on('SIGUSR2', function () { return shutdown('SIGUSR2'); }); // For nodemon
    };
    // Health check methods
    PerformanceOptimizationManager.prototype.checkJobQueue = function () {
        return __awaiter(this, void 0, void 0, function () {
            var jobQueue, queue;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        jobQueue = this.services.get('jobQueue');
                        if (!jobQueue)
                            throw new Error('Job Queue not initialized');
                        queue = jobQueue;
                        // Test job queue by adding a simple job with correct options
                        return [4 /*yield*/, queue.addJob('cache_warmup', { test: true }, { priority: 'normal' })];
                    case 1:
                        // Test job queue by adding a simple job with correct options
                        _a.sent();
                        return [2 /*return*/, 'OK'];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.checkCacheWarming = function () {
        return __awaiter(this, void 0, void 0, function () {
            var cacheWarmer, warmer, stats;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cacheWarmer = this.services.get('cacheWarmer');
                        if (!cacheWarmer)
                            throw new Error('Cache Warmer not initialized');
                        warmer = cacheWarmer;
                        return [4 /*yield*/, warmer.getStats()];
                    case 1:
                        stats = _a.sent();
                        if (stats.totalJobs < 0)
                            throw new Error('Cache warming stats invalid');
                        return [2 /*return*/, 'OK'];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.checkPerformanceMonitoring = function () {
        return __awaiter(this, void 0, void 0, function () {
            var performanceMonitor, monitor, status;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        performanceMonitor = this.services.get('performanceMonitor');
                        if (!performanceMonitor)
                            throw new Error('Performance Monitor not initialized');
                        monitor = performanceMonitor;
                        return [4 /*yield*/, monitor.getSystemStatus()];
                    case 1:
                        status = _a.sent();
                        if (!status)
                            throw new Error('Performance metrics unavailable');
                        return [2 /*return*/, 'OK'];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.checkDatabaseConnection = function () {
        return __awaiter(this, void 0, void 0, function () {
            var dbClient, client, stats;
            return __generator(this, function (_a) {
                dbClient = this.services.get('dbClient');
                if (!dbClient)
                    throw new Error('Database client not initialized');
                client = dbClient;
                stats = client.getPoolStats();
                if (!stats)
                    throw new Error('Database connection unavailable');
                return [2 /*return*/, 'OK'];
            });
        });
    };
    PerformanceOptimizationManager.prototype.checkRateLimiting = function () {
        return __awaiter(this, void 0, void 0, function () {
            var rateLimiter, limiter, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        rateLimiter = this.services.get('rateLimiter');
                        if (!rateLimiter)
                            throw new Error('Rate Limiter not initialized');
                        limiter = rateLimiter;
                        return [4 /*yield*/, limiter.checkLimit('health_check', 'lenient')];
                    case 1:
                        result = _a.sent();
                        if (!result)
                            throw new Error('Rate limiter not responding');
                        return [2 /*return*/, 'OK'];
                }
            });
        });
    };
    PerformanceOptimizationManager.prototype.getStats = function () {
        return {
            services: Array.from(this.services.keys()),
            initialized: this.isInitialized,
            uptime: process.uptime()
        };
    };
    return PerformanceOptimizationManager;
}());
exports.PerformanceOptimizationManager = PerformanceOptimizationManager;
// CLI Interface
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var args, command, manager, _a, stats;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    args = process.argv.slice(2);
                    command = args[0] || 'start';
                    manager = new PerformanceOptimizationManager();
                    _a = command;
                    switch (_a) {
                        case 'start': return [3 /*break*/, 1];
                        case 'health': return [3 /*break*/, 5];
                        case 'benchmark': return [3 /*break*/, 8];
                        case 'warm-cache': return [3 /*break*/, 11];
                        case 'stats': return [3 /*break*/, 14];
                    }
                    return [3 /*break*/, 16];
                case 1: return [4 /*yield*/, manager.initialize()];
                case 2:
                    _b.sent();
                    return [4 /*yield*/, manager.warmCriticalCaches()];
                case 3:
                    _b.sent();
                    return [4 /*yield*/, manager.setupMetricsCollection()];
                case 4:
                    _b.sent();
                    // Keep the process running
                    console.log('🎯 Performance optimization services are running. Press Ctrl+C to stop.');
                    return [3 /*break*/, 17];
                case 5: return [4 /*yield*/, manager.initialize()];
                case 6:
                    _b.sent();
                    return [4 /*yield*/, manager.runHealthCheck()];
                case 7:
                    _b.sent();
                    process.exit(0);
                    _b.label = 8;
                case 8: return [4 /*yield*/, manager.initialize()];
                case 9:
                    _b.sent();
                    return [4 /*yield*/, manager.runPerformanceBenchmark()];
                case 10:
                    _b.sent();
                    process.exit(0);
                    _b.label = 11;
                case 11: return [4 /*yield*/, manager.initialize()];
                case 12:
                    _b.sent();
                    return [4 /*yield*/, manager.warmCriticalCaches()];
                case 13:
                    _b.sent();
                    console.log('✅ Cache warming completed');
                    process.exit(0);
                    _b.label = 14;
                case 14: return [4 /*yield*/, manager.initialize()];
                case 15:
                    _b.sent();
                    stats = manager.getStats();
                    console.log('📊 Performance Optimization Stats:');
                    console.log(JSON.stringify(stats, null, 2));
                    process.exit(0);
                    _b.label = 16;
                case 16:
                    console.log("\n\uD83C\uDFAF Performance Optimization Manager\n\nUsage: node scripts/performance-optimization.js [command]\n\nCommands:\n  start      Initialize and start all performance services (default)\n  health     Run health check on all services\n  benchmark  Run performance benchmarks\n  warm-cache Warm critical caches\n  stats      Show performance statistics\n\nExamples:\n  node scripts/performance-optimization.js start\n  node scripts/performance-optimization.js health\n  node scripts/performance-optimization.js benchmark\n      ");
                    process.exit(1);
                    _b.label = 17;
                case 17: return [2 /*return*/];
            }
        });
    });
}
// Run CLI if called directly
if (import.meta.url === "file://".concat(process.argv[1])) {
    main().catch(function (error) {
        console.error('❌ Performance optimization failed:', error);
        process.exit(1);
    });
}
