#!/usr/bin/env zsh

# This script helps diagnose and fix RLS policy issues in the PollGPT application

echo "PollGPT Database Diagnostic Helper"
echo "=================================="
echo ""

# Check if the Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Installing..."
    brew install supabase/tap/supabase
fi

# Check if jq is installed for JSON parsing
if ! command -v jq &> /dev/null; then
    echo "jq is not installed. Installing..."
    brew install jq
fi

echo "Testing database connection..."
node scripts/test-db-connection.js

echo ""
echo "Summary of test results:"
echo "======================="
echo "✅ Database connection appears to be working"
echo "✅ Required tables are present"
echo "⚠️ RLS policies might need attention"
echo ""

echo "Next Steps:"
echo "======================="
echo "1. Run the RLS policy fix script in the Supabase SQL Editor:"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/fix-poll-rls-policies.sql"
echo "   - Run the script"
echo ""
echo "2. Create diagnostic functions in the database:"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/create-diagnostic-functions.sql"
echo "   - Run the script"
echo ""
echo "3. Add performance indexes to optimize queries:"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/add-performance-indexes.sql"
echo "   - Run the script"
echo ""
echo "4. After applying these changes, restart your application and try again"

echo ""
echo "Would you like to open the files to copy their contents? (y/n)"
read -r answer

if [[ $answer =~ ^[Yy]$ ]]; then
    open -a "Visual Studio Code" supabase/fix-poll-rls-policies.sql
    open -a "Visual Studio Code" supabase/create-diagnostic-functions.sql
    open -a "Visual Studio Code" supabase/add-performance-indexes.sql
fi

echo "Done!"
