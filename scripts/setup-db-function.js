// <PERSON><PERSON><PERSON> to create the get_polls_with_counts function in Supabase
import { createClient } from '@supabase/supabase-js';

// Supabase connection details from memory
const supabaseUrl = 'https://sumruaeyfidjlssrmfrm.supabase.co';
// You'll need to provide your anon key when running this script
const supabaseKey = process.env.SUPABASE_KEY || '';

if (!supabaseKey) {
  console.error('Please provide your Supabase anon key as SUPABASE_KEY environment variable');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// The SQL function definition
const functionSql = `
CREATE OR REPLACE FUNCTION get_polls_with_counts(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  is_published BOOLEAN,
  is_public BOOLEAN,
  slug TEXT,
  response_count BIGINT,
  view_count BIGINT,
  questions JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.description,
    p.created_at,
    p.updated_at,
    p.user_id,
    p.is_published,
    p.is_public,
    p.slug,
    -- Count responses for each poll
    COALESCE((SELECT COUNT(*) FROM responses r WHERE r.poll_id = p.id), 0) AS response_count,
    -- View count (if you have a views table, otherwise return 0)
    COALESCE(p.views, 0) AS view_count,
    -- Aggregate questions into a JSON array
    COALESCE(
      (SELECT 
        jsonb_agg(
          jsonb_build_object(
            'id', q.id,
            'poll_id', q.poll_id,
            'question_text', q.question_text,
            'question_type', q.question_type,
            'order', q.order
          )
        )
       FROM questions q
       WHERE q.poll_id = p.id
      ),
      '[]'::jsonb
    ) AS questions
  FROM 
    polls p
  WHERE 
    p.user_id = user_id_param
  ORDER BY 
    p.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_polls_with_counts(UUID) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION get_polls_with_counts(UUID) IS 'Gets all polls for a user with response counts and questions in a single optimized query';
`;

async function setupDbFunction() {
  try {
    console.log('Setting up the get_polls_with_counts database function...');
    
    // Execute the SQL to create or replace the function
    const { error } = await supabase.rpc('exec_sql', { sql: functionSql });
    
    if (error) {
      console.error('Error creating function:', error);
      
      // Try an alternative approach if the first one fails
      console.log('Trying alternative approach with direct SQL...');
      
      // Get a direct connection to the database if possible
      const { data, error: sqlError } = await supabase.from('_exec_sql').select('*').eq('sql', functionSql);
      
      if (sqlError) {
        console.error('Alternative approach failed:', sqlError);
        console.log('\nPlease run the SQL manually in the Supabase SQL editor:');
        console.log(functionSql);
      } else {
        console.log('Function created successfully using alternative approach');
      }
    } else {
      console.log('Function created successfully!');
    }
    
    // Test if the function exists
    console.log('\nVerifying function exists...');
    const { data, error: checkError } = await supabase.rpc('function_exists', { function_name: 'get_polls_with_counts' });
    
    if (checkError) {
      console.error('Error checking function:', checkError);
      console.log('Could not verify if function exists');
    } else {
      console.log(`Function exists: ${data ? 'Yes' : 'No'}`);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
    console.log('\nPlease run the SQL manually in the Supabase SQL editor:');
    console.log(functionSql);
  }
}

// Run the setup
setupDbFunction()
  .then(() => console.log('Setup completed'))
  .catch(err => console.error('Setup failed:', err));
