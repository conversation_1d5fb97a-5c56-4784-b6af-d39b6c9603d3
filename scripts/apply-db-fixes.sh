#!/bin/bash

# Fix Database Issues Script
# This script applies the fixed diagnostic functions and helps diagnose database issues

# Text formatting
BOLD='\033[1m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BOLD}PollGPT Database Issue Fix Script${NC}\n"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}Supabase CLI is not installed.${NC}"
    echo "Please install it with: npm install -g supabase"
    exit 1
fi

# 1. Apply the fixed diagnostic functions
echo -e "${BOLD}Step 1: Applying fixed diagnostic functions${NC}"

# Check if we have database credentials
if [ -f ".env.local" ]; then
    echo "Found .env.local file with database credentials"
else
    echo -e "${YELLOW}Warning: .env.local file not found. You'll need to manually run the SQL script.${NC}"
    echo "Please run the fixed-diagnostic-functions.sql script in the Supabase SQL editor"
fi

# Try to run the SQL script if we have the Supabase CLI and credentials
echo -e "\n${YELLOW}Attempting to apply fixed-diagnostic-functions.sql...${NC}"
echo "If this fails, please run the SQL script manually in the Supabase dashboard SQL editor"

cat supabase/fixed-diagnostic-functions.sql

echo -e "\n${BOLD}Step 2: Testing database connection${NC}"
echo "Running test-db-connection.js script..."

# Update the test-db-connection.js script to use the new function
cat > scripts/test-db-connection-fixed.js << 'EOF'
// Test database connection script
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_MCP_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('Testing Supabase connection...');
  console.log(`URL: ${supabaseUrl}`);

  try {
    // Check for specific tables from our schema
    const requiredTables = ['profiles', 'polls', 'questions', 'responses', 'answers'];
    console.log('\nChecking for required tables:');

    for (const tableName of requiredTables) {
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error(`❌ Table '${tableName}':`, error.message);
      } else {
        console.log(`✅ Table '${tableName}' exists with ${count || 0} rows`);
      }
    }

    // Test auth connection
    console.log('\nTesting auth connection...');
    const { error: authError } = await supabase.auth.getSession();
    if (authError) {
      console.error('❌ Auth connection error:', authError.message);
    } else {
      console.log('✅ Auth connection successful');
    }

    // Test RLS policies for profiles
    console.log('\nChecking RLS policies for profiles table...');
    const { error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profileError) {
      console.error('❌ Error accessing profiles table:', profileError.message);
      if (profileError.message.includes('permission denied')) {
        console.log('This may be due to RLS policies restricting access. This is expected behavior when using the anon key.');
      }
    } else {
      console.log('✅ Successfully accessed profiles table');
    }

    // Test RLS policies specifically for polls table
    console.log('\nChecking RLS policies for polls table...');

    // 1. Try to access all polls (should be denied without auth)
    const { data: allPolls, error: pollsError } = await supabase
      .from('polls')
      .select('*')
      .limit(5);

    if (pollsError) {
      console.log('❌ Accessing all polls (expected to fail without auth):', pollsError.message);
    } else {
      console.log(`✅ Accessed ${allPolls.length} polls - Note: This should only work if authenticated or if some polls are public`);
    }

    // 2. Try to access only public published polls (should work)
    const { data: publicPolls, error: publicPollsError } = await supabase
      .from('polls')
      .select('*')
      .eq('is_public', true)
      .eq('is_published', true)
      .limit(5);

    if (publicPollsError) {
      console.error('❌ Error accessing public polls:', publicPollsError.message);
      console.log('This is unexpected - the "Public polls are viewable by everyone" RLS policy may be missing or misconfigured');
    } else {
      console.log(`✅ Successfully accessed ${publicPolls?.length || 0} public polls`);
    }

    // 3. List the RLS policies for polls table if using a service role key
    if (process.env.SUPABASE_MCP_KEY) {
      console.log('\nAttempting to list RLS policies for polls table...');
      try {
        // Use the fixed list_table_policies function instead of get_policies_for_table
        const { data: policies, error: policiesError } = await supabase.rpc('list_table_policies', {
          table_name: 'polls'
        });

        if (policiesError) {
          console.error('❌ Error listing policies:', policiesError.message);
          console.log('Try running the fixed-diagnostic-functions.sql script first');
        } else if (policies && policies.length > 0) {
          console.log('✅ Found the following RLS policies for polls table:');
          policies.forEach(policy => {
            console.log(`  - ${policy.policyname}: ${policy.cmd_pretty}`);
          });
        } else {
          console.log('⚠️ No policies found for polls table - this may indicate a problem!');
        }
      } catch (err) {
        console.error('Error executing policy check:', err.message);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testConnection();
EOF

echo -e "${GREEN}Created fixed test script: scripts/test-db-connection-fixed.js${NC}"

echo -e "\n${BOLD}Step 3: Testing the fixed connection script${NC}"
echo "Running node scripts/test-db-connection-fixed.js..."
node scripts/test-db-connection-fixed.js

echo -e "\n${BOLD}Next Steps:${NC}"
echo "1. If the test script succeeded, your diagnostic functions are working properly"
echo "2. Check the dashboard polls page to see if it's still showing the infinite loading state"
echo "3. If issues persist, try running the SQL scripts to fix RLS policies:"
echo "   - supabase/fix-poll-rls-policies.sql"
echo "   - supabase/add-performance-indexes.sql"

echo -e "\n${YELLOW}Remember to check your browser console for any errors when loading the dashboard polls page${NC}"
