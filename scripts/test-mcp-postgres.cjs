const { SDK } = require('@modelcontextprotocol/sdk');

async function main() {
  const sdk = new SDK();

  try {
    // Test query: get current time from the database
    const result = await sdk.invoke('postgresql-pollgpt', {
      query: 'SELECT NOW() as current_time;'
    });
    console.log('Query result:', result);
  } catch (error) {
    console.error('Error querying MCP Postgres:', error);
  }
}

main();