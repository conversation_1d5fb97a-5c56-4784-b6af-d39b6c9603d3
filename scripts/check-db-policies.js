// Script to check database structure and RLS policies
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_MCP_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  console.log('Checking database structure and RLS policies...');
  console.log(`URL: ${supabaseUrl}`);

  try {
    // Check specific tables that we know should exist
    console.log('\n1. Checking tables structure:');

    const requiredTables = ['profiles', 'polls', 'questions', 'responses', 'answers'];

    for (const tableName of requiredTables) {
      try {
        // Check if table exists and count rows
        const { count, error: tableError } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });

        if (tableError) {
          if (tableError.message.includes('permission denied')) {
            console.log(`✅ Table '${tableName}' exists but access is denied (RLS is working)`);
          } else if (tableError.message.includes('does not exist')) {
            console.error(`❌ Table '${tableName}' does not exist`);
          } else {
            console.error(`❌ Error checking table '${tableName}':`, tableError.message);
          }
        } else {
          console.log(`✅ Table '${tableName}' exists with ${count || 0} rows`);
        }
      } catch (err) {
        console.error(`Error checking table '${tableName}':`, err.message);
      }
    }

    // Test authenticated access
    console.log('\n2. Testing RLS policies with anonymous key:');

    // Try to insert data as anonymous user (should be blocked by RLS)
    const testInserts = [
      {
        table: 'profiles',
        data: { id: '00000000-0000-0000-0000-000000000000', email: '<EMAIL>' }
      },
      {
        table: 'polls',
        data: { title: 'Test Poll', user_id: '00000000-0000-0000-0000-000000000000' }
      },
      {
        table: 'questions',
        data: { poll_id: '00000000-0000-0000-0000-000000000000', question_text: 'Test Question', question_type: 'single', order: 1 }
      }
    ];

    for (const test of testInserts) {
      try {
        const { error } = await supabase
          .from(test.table)
          .insert(test.data);

        if (error) {
          if (error.message.includes('permission denied') ||
              error.message.includes('new row violates row-level')) {
            console.log(`✅ RLS policy for '${test.table}' is working (blocked unauthorized insert)`);
          } else {
            console.error(`❓ Insert to '${test.table}' failed for other reasons:`, error.message);
          }
        } else {
          console.warn(`⚠️ RLS policy for '${test.table}' might not be properly configured (allowed unauthorized insert)`);
          // Try to clean up
          if (test.table === 'profiles') {
            await supabase.from(test.table).delete().eq('id', '00000000-0000-0000-0000-000000000000');
          }
        }
      } catch (err) {
        console.error(`Error testing insert for '${test.table}':`, err.message);
      }
    }

    // Try to read public polls (should be allowed by RLS)
    console.log('\n3. Testing public access policies:');
    try {
      const { data, error } = await supabase
        .from('polls')
        .select('*')
        .eq('is_public', true)
        .eq('is_published', true)
        .limit(1);

      if (error) {
        if (error.message.includes('permission denied')) {
          console.warn(`⚠️ Public polls policy might not be configured correctly (access denied)`);
        } else {
          console.error(`❌ Error checking public polls:`, error.message);
        }
      } else {
        console.log(`✅ Public polls policy is working (${data.length} public polls found)`);
      }
    } catch (err) {
      console.error('Error testing public polls policy:', err.message);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkDatabase();
