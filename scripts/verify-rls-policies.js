// Script to verify RLS policies are working correctly
// Run with: node scripts/verify-rls-policies.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Error: Missing Supabase credentials in environment variables');
  process.exit(1);
}

// Create clients
const anonClient = createClient(supabaseUrl, supabaseAnonKey);
if (supabaseServiceKey) {
  console.warn('Service role key provided but not used in these tests');
} else {
  console.warn('Warning: No service role key provided');
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Utility functions
const log = {
  info: (msg) => console.log(`${colors.blue}INFO:${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}SUCCESS:${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}ERROR:${colors.reset} ${msg}`),
  warn: (msg) => console.log(`${colors.yellow}WARNING:${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.magenta}===== ${msg} =====${colors.reset}\n`),
};

// Main function
async function main() {
  log.header('PollGPT RLS Policy Verification Tool');
  log.info(`Testing connection to Supabase at: ${supabaseUrl}`);

  try {
    // 1. Test authentication
    log.header('Testing Authentication');

    // Test sign-in with email/password
    log.info('Please provide temporary test credentials:');
    // In a real script, you would use readline or similar to get user input
    const testEmail = process.env.TEST_EMAIL || '<EMAIL>';
    const testPassword = process.env.TEST_PASSWORD || 'password123';

    log.info(`Attempting to sign in with: ${testEmail}`);
    const { data: authData, error: authError } = await anonClient.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });

    if (authError) {
      log.error(`Authentication failed: ${authError.message}`);
      log.info('Continuing with remaining tests in unauthenticated state');
    } else {
      log.success(`Successfully authenticated as: ${authData.user.email}`);
    }

    // 2. Test RLS policies on the polls table
    log.header('Testing RLS Policies on Polls Table');

    // Test SELECT permission on polls
    log.info('Testing SELECT permission on polls table...');
    const startTime = Date.now();
    const { data: polls, error: pollsError } = await anonClient.from('polls').select('*').limit(5);
    const responseTime = Date.now() - startTime;

    if (pollsError) {
      if (pollsError.code === 'PGRST116' || pollsError.message.includes('permission denied')) {
        log.error(`SELECT permission denied. This is expected if not authenticated: ${pollsError.message}`);
      } else {
        log.error(`Unexpected error when testing SELECT: ${pollsError.message}`);
      }
    } else {
      log.success(`SELECT permission granted. Retrieved ${polls?.length || 0} polls in ${responseTime}ms`);

      if (polls && polls.length > 0) {
        const samplePoll = polls[0];
        log.info(`Sample poll: "${samplePoll.title}" (ID: ${samplePoll.id})`);
      }
    }

    // Test SELECT with filters that should be allowed by RLS
    if (authData?.user) {
      log.info('Testing SELECT with user_id filter (should be allowed)...');
      const { data: userPolls, error: userPollsError } = await anonClient
        .from('polls')
        .select('*')
        .eq('user_id', authData.user.id)
        .limit(5);

      if (userPollsError) {
        log.error(`User-filtered SELECT failed: ${userPollsError.message}`);
      } else {
        log.success(`User-filtered SELECT succeeded. Found ${userPolls?.length || 0} polls belonging to the current user.`);
      }

      // Test INSERT permission (if authenticated)
      log.info('Testing INSERT permission on polls table (should be allowed for own user)...');

      const testPollTitle = `Test Poll ${new Date().toISOString()}`;
      const { data: insertedPoll, error: insertError } = await anonClient
        .from('polls')
        .insert({
          title: testPollTitle,
          description: 'This is a test poll created by the verify-rls-policies script',
          is_published: false,
          user_id: authData.user.id,
        })
        .select('*')
        .single();

      if (insertError) {
        log.error(`INSERT permission denied: ${insertError.message}`);
      } else {
        log.success(`INSERT permission granted. Created poll with ID: ${insertedPoll.id}`);

        // If insert succeeded, also test UPDATE and DELETE
        if (insertedPoll) {
          // Test UPDATE
          log.info('Testing UPDATE permission on polls table...');
          const { error: updateError } = await anonClient
            .from('polls')
            .update({ title: `${testPollTitle} (Updated)` })
            .eq('id', insertedPoll.id);

          if (updateError) {
            log.error(`UPDATE permission denied: ${updateError.message}`);
          } else {
            log.success('UPDATE permission granted');
          }

          // Test DELETE
          log.info('Testing DELETE permission on polls table...');
          const { error: deleteError } = await anonClient
            .from('polls')
            .delete()
            .eq('id', insertedPoll.id);

          if (deleteError) {
            log.error(`DELETE permission denied: ${deleteError.message}`);
          } else {
            log.success('DELETE permission granted');
          }
        }
      }
    }

    // 3. Test public access policies
    log.header('Testing Public Access Policies');

    // Create a new anonymous client without authentication
    const publicClient = createClient(supabaseUrl, supabaseAnonKey);

    log.info('Testing public access to published polls...');
    const { data: publicPolls, error: publicPollsError } = await publicClient
      .from('polls')
      .select('*')
      .eq('is_public', true)
      .eq('is_published', true)
      .limit(5);

    if (publicPollsError) {
      log.error(`Public polls access failed: ${publicPollsError.message}`);
    } else {
      log.success(`Public polls access succeeded. Found ${publicPolls?.length || 0} public polls.`);
    }

    // Summary of results
    log.header('Summary');
    log.info('RLS policy tests completed. Review the output above to ensure policies are working as expected.');

  } catch (error) {
    log.error(`Unexpected error: ${error.message}`);
  } finally {
    // Sign out at the end
    await anonClient.auth.signOut();
    log.info('Signed out. Test complete.');
  }
}

// Run the main function
main().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});
