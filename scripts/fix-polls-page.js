// Tests specifically for the dashboard polls page loading issue
// Uses ES modules to match the NextJS codebase
// Run with: node --experimental-specifier-resolution=node scripts/fix-polls-page.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { setTimeout } from 'timers/promises';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Styling for console output
const colors = {
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
let supabase;

// Track timing information
const timings = {
  authFetch: 0,
  pollsFetch: 0,
};

async function signIn() {
  console.log('Please enter your login details:');
  // In a real script you would use readline or similar for this
  const email = process.env.TEST_USER_EMAIL || '<YOUR_EMAIL>';
  const password = process.env.TEST_USER_PASSWORD || '<YOUR_PASSWORD>';

  if (email === '<YOUR_EMAIL>' || password === '<YOUR_PASSWORD>') {
    console.log(`${colors.yellow}⚠️  You need to set TEST_USER_EMAIL and TEST_USER_PASSWORD environment variables${colors.reset}`);
    console.log('Edit this file to add your credentials or press Ctrl+C to exit');
    await setTimeout(5000);
    process.exit(1);
  }

  console.log(`Attempting to sign in as: ${email}`);
  const startAuth = Date.now();
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  timings.authFetch = Date.now() - startAuth;

  if (authError) {
    console.log(`${colors.red}❌ Authentication failed: ${authError.message}${colors.reset}`);
    return null;
  }

  console.log(`${colors.green}✅ Authentication successful (${timings.authFetch}ms)${colors.reset}`);
  return authData;
}

// Simulates the getPolls function from our service
async function fetchPolls(user) {
  if (!user) {
    throw new Error("No authenticated user");
  }

  console.log(`\nFetching polls for user: ${user.id}`);
  console.log(`${colors.cyan}→ This simulates the getPolls() function in your polls.ts service${colors.reset}`);

  const startFetch = Date.now();

  // First test - with simple query
  console.log('\n1. Testing basic query:');
  try {
    const { data: polls, error } = await supabase
      .from('polls')
      .select('*')
      .eq('user_id', user.id);

    if (error) {
      console.log(`${colors.red}❌ Error: ${error.message}${colors.reset}`);

      if (error.message.includes('permission denied')) {
        console.log(`${colors.yellow}⚠️  This is likely an RLS policy issue.${colors.reset}`);
        console.log('Ensure "Users can view their own polls" policy exists and is correctly configured.');
      }
    } else {
      console.log(`${colors.green}✅ Success! Found ${polls?.length || 0} polls (${Date.now() - startFetch}ms)${colors.reset}`);
      if (polls?.length > 0) {
        console.log(`${colors.dim}First poll: "${polls[0].title}"${colors.reset}`);
      }
    }
  } catch (err) {
    console.log(`${colors.red}❌ Exception: ${err.message}${colors.reset}`);
  }

  // Second test - with timeout and more complex query
  console.log('\n2. Testing production query (with joins and timeout):');
  const complexStart = Date.now();

  try {
    const { data, error } = await Promise.race([
      supabase
        .from('polls')
        .select('*, questions(*), responses(*)')
        .eq('user_id', user.id),
      new Promise((_, reject) => setTimeout(5000).then(() =>
        reject(new Error("Database query timed out after 5 seconds"))
      ))
    ]);

    timings.pollsFetch = Date.now() - complexStart;

    if (error) {
      console.log(`${colors.red}❌ Error: ${error.message}${colors.reset}`);

      if (error.code === '42P01') {
        console.log(`${colors.yellow}⚠️  Table does not exist. Check your database schema.${colors.reset}`);
      } else if (error.message.includes('permission denied')) {
        console.log(`${colors.yellow}⚠️  RLS policy issue detected.${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}✅ Success! Query completed in ${timings.pollsFetch}ms${colors.reset}`);
      console.log(`   Found ${data?.length || 0} polls with their questions and responses`);

      // Log a sample poll
      if (data?.length > 0) {
        const samplePoll = data[0];
        console.log(`${colors.dim}Sample poll: "${samplePoll.title}" with ${samplePoll.questions?.length || 0} questions${colors.reset}`);
      }
    }
  } catch (err) {
    console.log(`${colors.red}❌ Exception: ${err.message}${colors.reset}`);

    if (err.message.includes('timed out')) {
      console.log(`${colors.yellow}⚠️  Query is taking too long. This matches the timeout issue you're seeing in the app.${colors.reset}`);
      console.log('Possible causes:');
      console.log('1. Complex joins without proper indexes');
      console.log('2. Database is under heavy load or is on a sleep/pause state');
      console.log('3. Network latency between your app and the database');
    }
  }

  // Third test - check the RLS policies directly
  console.log('\n3. Examining RLS policies:');
  try {
    // This requires DB access with elevated permissions
    const { data: policies, error: policiesError } = await supabase.rpc('get_policies_for_table', {
      table_name: 'polls'
    });

    if (policiesError) {
      console.log(`${colors.yellow}⚠️  Cannot fetch policies directly: ${policiesError.message}${colors.reset}`);
      console.log('To check policies, run the fix-poll-rls-policies.sql script in Supabase SQL editor.');
    } else if (policies && policies.length > 0) {
      console.log(`${colors.green}✅ Found ${policies.length} RLS policies for the polls table:${colors.reset}`);
      policies.forEach(policy => {
        console.log(`   - ${policy.policyname}: ${policy.cmd_pretty} (${policy.permissive ? 'PERMISSIVE' : 'RESTRICTIVE'})`);
      });
    } else {
      console.log(`${colors.red}❌ No policies found for polls table!${colors.reset}`);
      console.log('This is a critical issue. Run the fix-poll-rls-policies.sql script.');
    }
  } catch (err) {
    console.log(`${colors.yellow}⚠️  Could not check policies: ${err.message}${colors.reset}`);
  }
}

// Main function
async function main() {
  console.log(`${colors.bold}${colors.blue}===== PollGPT Dashboard Polls Loading Diagnostics =====${colors.reset}\n`);

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error(`${colors.red}Error: Missing Supabase credentials in environment variables${colors.reset}`);
    console.log('Make sure you have a .env.local file with:');
    console.log('NEXT_PUBLIC_SUPABASE_URL=your_url');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key');
    return;
  }

  // Initialize Supabase
  supabase = createClient(supabaseUrl, supabaseAnonKey);
  console.log(`Connected to Supabase: ${supabaseUrl}`);

  // Test for existing session
  const { data: existingSession } = await supabase.auth.getSession();
  let user = existingSession?.session?.user;

  // If no session, try to sign in
  if (!user) {
    const authData = await signIn();
    user = authData?.user;
  } else {
    console.log(`${colors.green}✅ Using existing session for: ${user.email}${colors.reset}`);
  }

  // Exit if still no user
  if (!user) {
    console.log(`${colors.red}❌ Cannot proceed without authentication${colors.reset}`);
    return;
  }

  // Fetch polls with the authenticated user
  await fetchPolls(user);

  // Summary
  console.log(`\n${colors.bold}${colors.blue}===== Results Summary =====${colors.reset}\n`);
  console.log(`Auth request: ${timings.authFetch}ms`);
  console.log(`Polls fetch: ${timings.pollsFetch || 'Failed/Timeout'}ms\n`);

  if (timings.pollsFetch > 2000) {
    console.log(`${colors.yellow}⚠️  Warning: Poll fetching is taking longer than 2 seconds.${colors.reset}`);
    console.log('Consider adding database indexes or optimizing your queries.');
  }

  // Recommendations
  console.log(`${colors.bold}${colors.blue}===== Recommendations =====${colors.reset}\n`);

  console.log('1. Run the RLS policy fix script:');
  console.log('   → Execute /supabase/fix-poll-rls-policies.sql in your Supabase SQL Editor');

  console.log('\n2. Add proper indexes to speed up queries:');
  console.log('   → Add index on user_id in the polls table');
  console.log('   → Add index on poll_id in the questions table');
  console.log('   → Add index on poll_id in the responses table');

  console.log('\n3. Optimize your queries:');
  console.log('   → Consider using pagination instead of loading all polls at once');
  console.log('   → Load questions and responses only when viewing a specific poll');

  console.log('\n4. Check for auth token expiration:');
  console.log('   → Implement proper token refresh in your application');
  console.log('   → Make sure the client auth state is synchronized with Supabase');
}

// Run the main function
main()
  .catch(err => {
    console.error(`Fatal error: ${err.message}`);
  })
  .finally(() => {
    console.log('\nDiagnostic complete.');
  });
