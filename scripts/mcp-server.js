#!/usr/bin/env node

/**
 * This script starts the MCP servers for the PollGPT application.
 * Run with: npm run mcp:start
 */

import { setupMcpServers } from '../src/lib/mcp/index.js';
import { PollScraperServer } from '../src/lib/mcp/poll-scraper.js';

async function main() {
  console.log('Starting MCP servers for PollGPT...');

  try {
    // Initialize all MCP servers
    const sdk = setupMcpServers();

    // Register custom poll scraper
    new PollScraperServer();

    console.log('MCP servers started successfully!');
    console.log('Available tools:');

    // List available tools
    const tools = sdk.getTools();
    tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });

    console.log('\nMCP servers are running. Press Ctrl+C to stop.');

    // Keep the process running
    process.on('SIGINT', () => {
      console.log('\nShutting down MCP servers...');
      process.exit(0);
    });
  } catch (error) {
    console.error('Error starting MCP servers:', error);
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});