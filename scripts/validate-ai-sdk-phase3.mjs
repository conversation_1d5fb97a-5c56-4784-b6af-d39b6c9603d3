#!/usr/bin/env node

/**
 * AI SDK Phase 3 Implementation Validation Script
 * Validates enhanced poll simulation integration with AI SDK
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFile, access, readdir } from 'fs/promises';
import { constants } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// ANSI color codes for terminal output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n${colors.bright}=== ${message} ===${colors.reset}`, colors.cyan);
}

async function fileExists(filePath) {
  try {
    await access(filePath, constants.F_OK);
    return true;
  } catch {
    return false;
  }
}

async function readJsonFile(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    throw new Error(`Failed to read JSON file ${filePath}: ${error.message}`);
  }
}

async function readTextFile(filePath) {
  try {
    return await readFile(filePath, 'utf-8');
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error.message}`);
  }
}

// Validation tests
const validationTests = {
  async validateDependencies() {
    logHeader('Validating Dependencies');

    const packageJsonPath = join(projectRoot, 'package.json');
    const packageJson = await readJsonFile(packageJsonPath);

    const requiredDeps = {
      'ai': '^4.3.15',
      '@ai-sdk/mistral': '^0.1.2',
      '@ai-sdk/google': '^0.1.2',
      '@ai-sdk/openai': '^0.1.2',
      'zod': '^3.23.8'
    };

    let missingDeps = [];
    let outdatedDeps = [];

    for (const [dep, requiredVersion] of Object.entries(requiredDeps)) {
      const installedVersion = packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep];

      if (!installedVersion) {
        missingDeps.push(dep);
      } else if (installedVersion !== requiredVersion) {
        outdatedDeps.push(`${dep}: ${installedVersion} (expected ${requiredVersion})`);
      } else {
        logSuccess(`${dep}: ${installedVersion}`);
      }
    }

    if (missingDeps.length > 0) {
      logError(`Missing dependencies: ${missingDeps.join(', ')}`);
      return false;
    }

    if (outdatedDeps.length > 0) {
      logWarning(`Potentially outdated dependencies: ${outdatedDeps.join(', ')}`);
    }

    return true;
  },

  async validatePhase3Files() {
    logHeader('Validating Phase 3 Files');

    const requiredFiles = [
      'src/lib/ai/simulation-engine.ts',
      'src/lib/hooks/use-enhanced-simulation.ts',
      'src/components/ai/enhanced-simulation-demo.tsx',
      'src/app/api/ai/enhanced-simulation/route.ts'
    ];

    let allFilesExist = true;

    for (const file of requiredFiles) {
      const filePath = join(projectRoot, file);
      const exists = await fileExists(filePath);

      if (exists) {
        logSuccess(`${file}`);
      } else {
        logError(`Missing file: ${file}`);
        allFilesExist = false;
      }
    }

    return allFilesExist;
  },

  async validateSimulationEngine() {
    logHeader('Validating Enhanced Simulation Engine');

    const filePath = join(projectRoot, 'src/lib/ai/simulation-engine.ts');
    const exists = await fileExists(filePath);

    if (!exists) {
      logError('Simulation engine file not found');
      return false;
    }

    const content = await readTextFile(filePath);

    // Check for essential components
    const checks = [
      { pattern: /class EnhancedSimulationEngine/, description: 'EnhancedSimulationEngine class' },
      { pattern: /generateObject.*from 'ai'/, description: 'AI SDK generateObject import' },
      { pattern: /SimulationSchema/, description: 'Simulation schema definition' },
      { pattern: /runPollSimulation/, description: 'runPollSimulation method' },
      { pattern: /runBatchSimulation/, description: 'runBatchSimulation method' },
      { pattern: /fallbackToExistingSimulation/, description: 'Fallback mechanism' },
      { pattern: /aiProviders\.simulation/, description: 'AI provider integration' }
    ];

    let allChecksPass = true;

    for (const check of checks) {
      if (check.pattern.test(content)) {
        logSuccess(check.description);
      } else {
        logError(`Missing: ${check.description}`);
        allChecksPass = false;
      }
    }

    return allChecksPass;
  },

  async validateEnhancedHooks() {
    logHeader('Validating Enhanced Simulation Hooks');

    const filePath = join(projectRoot, 'src/lib/hooks/use-enhanced-simulation.ts');
    const exists = await fileExists(filePath);

    if (!exists) {
      logError('Enhanced simulation hooks file not found');
      return false;
    }

    const content = await readTextFile(filePath);

    const checks = [
      { pattern: /useEnhancedSimulation/, description: 'useEnhancedSimulation hook' },
      { pattern: /useSimulationStreaming/, description: 'useSimulationStreaming hook' },
      { pattern: /useCompletion.*from 'ai\/react'/, description: 'AI SDK React hooks import' },
      { pattern: /useObject.*from 'ai\/react'/, description: 'AI SDK useObject hook' },
      { pattern: /useMutation/, description: 'React Query integration' },
      { pattern: /\/api\/ai\/enhanced-simulation/, description: 'Enhanced simulation API endpoint' }
    ];

    let allChecksPass = true;

    for (const check of checks) {
      if (check.pattern.test(content)) {
        logSuccess(check.description);
      } else {
        logError(`Missing: ${check.description}`);
        allChecksPass = false;
      }
    }

    return allChecksPass;
  },

  async validateAPIRoute() {
    logHeader('Validating Enhanced Simulation API Route');

    const filePath = join(projectRoot, 'src/app/api/ai/enhanced-simulation/route.ts');
    const exists = await fileExists(filePath);

    if (!exists) {
      logError('Enhanced simulation API route not found');
      return false;
    }

    const content = await readTextFile(filePath);

    const checks = [
      { pattern: /export async function POST/, description: 'POST endpoint' },
      { pattern: /export async function PUT/, description: 'PUT endpoint for batch' },
      { pattern: /enhancedSimulationEngine/, description: 'Enhanced engine integration' },
      { pattern: /featureFlags\.enhancedSimulation/, description: 'Feature flag checking' },
      { pattern: /rateLimitMap/, description: 'Rate limiting implementation' },
      { pattern: /useEnhancedEngine/, description: 'Engine selection logic' },
      { pattern: /fallback/, description: 'Fallback mechanism' }
    ];

    let allChecksPass = true;

    for (const check of checks) {
      if (check.pattern.test(content)) {
        logSuccess(check.description);
      } else {
        logError(`Missing: ${check.description}`);
        allChecksPass = false;
      }
    }

    return allChecksPass;
  },

  async validateFeatureFlags() {
    logHeader('Validating Phase 3 Feature Flags');

    const filePath = join(projectRoot, 'src/lib/ai/feature-flags.ts');
    const exists = await fileExists(filePath);

    if (!exists) {
      logError('Feature flags file not found');
      return false;
    }

    const content = await readTextFile(filePath);

    const phase3Flags = [
      'enhancedSimulation',
      'structuredSimulationGeneration',
      'batchSimulationProcessing',
      'demographicAnalysis',
      'simulationInsights'
    ];

    let allFlagsExist = true;

    for (const flag of phase3Flags) {
      const flagPattern = new RegExp(`${flag}:\\s*boolean`);
      const defaultPattern = new RegExp(`${flag}:\\s*true`);

      if (flagPattern.test(content)) {
        logSuccess(`Feature flag interface: ${flag}`);

        if (defaultPattern.test(content)) {
          logSuccess(`Default enabled: ${flag}`);
        } else {
          logWarning(`Default disabled: ${flag}`);
        }
      } else {
        logError(`Missing feature flag: ${flag}`);
        allFlagsExist = false;
      }
    }

    return allFlagsExist;
  },

  async validateEnvironmentVariables() {
    logHeader('Validating Environment Variables');

    const envExamplePath = join(projectRoot, '.env.example');
    const exists = await fileExists(envExamplePath);

    if (!exists) {
      logError('.env.example file not found');
      return false;
    }

    const content = await readTextFile(envExamplePath);

    const phase3EnvVars = [
      'ENABLE_AI_SDK_ENHANCED_SIMULATION',
      'ENABLE_AI_SDK_STRUCTURED_SIMULATION_GENERATION',
      'ENABLE_AI_SDK_BATCH_SIMULATION_PROCESSING',
      'ENABLE_AI_SDK_DEMOGRAPHIC_ANALYSIS',
      'ENABLE_AI_SDK_SIMULATION_INSIGHTS'
    ];

    let allVarsExist = true;

    for (const envVar of phase3EnvVars) {
      if (content.includes(envVar)) {
        logSuccess(`Environment variable: ${envVar}`);
      } else {
        logError(`Missing environment variable: ${envVar}`);
        allVarsExist = false;
      }
    }

    return allVarsExist;
  },

  async validateTypeScript() {
    logHeader('Validating TypeScript Compilation');

    // Check if simulation engine types are properly imported
    const simulationEngineFile = join(projectRoot, 'src/lib/ai/simulation-engine.ts');
    const hooksFile = join(projectRoot, 'src/lib/hooks/use-enhanced-simulation.ts');

    const files = [simulationEngineFile, hooksFile];
    let typesValid = true;

    for (const file of files) {
      const exists = await fileExists(file);
      if (!exists) continue;

      const content = await readTextFile(file);

      // Check for proper imports
      if (content.includes("from '@/lib/types/simulation'")) {
        logSuccess(`Proper type imports in ${file.split('/').pop()}`);
      } else {
        logWarning(`Check type imports in ${file.split('/').pop()}`);
      }

      // Check for proper AI SDK imports
      if (content.includes("from 'ai'") || content.includes("from 'ai/react'")) {
        logSuccess(`AI SDK imports in ${file.split('/').pop()}`);
      } else {
        logError(`Missing AI SDK imports in ${file.split('/').pop()}`);
        typesValid = false;
      }
    }

    return typesValid;
  },

  async validateIntegration() {
    logHeader('Validating Integration Points');

    // Check that enhanced simulation integrates with existing simulation system
    const simulationEngineFile = join(projectRoot, 'src/lib/ai/simulation-engine.ts');
    const exists = await fileExists(simulationEngineFile);

    if (!exists) {
      logError('Cannot validate integration - simulation engine file missing');
      return false;
    }

    const content = await readTextFile(simulationEngineFile);

    const integrationChecks = [
      { pattern: /simulatePoll.*from.*perplexity-ai/, description: 'Fallback to existing simulation' },
      { pattern: /SimulationRequest.*SimulationResponse/, description: 'Compatible with existing types' },
      { pattern: /featureFlags/, description: 'Feature flag integration' },
      { pattern: /fallbackToExistingSimulation/, description: 'Graceful fallback mechanism' }
    ];

    let allChecksPass = true;

    for (const check of integrationChecks) {
      if (check.pattern.test(content)) {
        logSuccess(check.description);
      } else {
        logError(`Missing integration: ${check.description}`);
        allChecksPass = false;
      }
    }

    return allChecksPass;
  }
};

// Main validation function
async function runValidation() {
  log(`${colors.bright}${colors.cyan}AI SDK Phase 3 Enhanced Simulation Validation${colors.reset}`);
  log(`${colors.cyan}Validating enhanced poll simulation with AI SDK structured generation${colors.reset}\n`);

  const results = [];

  try {
    // Run all validation tests
    results.push({ name: 'Dependencies', passed: await validationTests.validateDependencies() });
    results.push({ name: 'Phase 3 Files', passed: await validationTests.validatePhase3Files() });
    results.push({ name: 'Simulation Engine', passed: await validationTests.validateSimulationEngine() });
    results.push({ name: 'Enhanced Hooks', passed: await validationTests.validateEnhancedHooks() });
    results.push({ name: 'API Route', passed: await validationTests.validateAPIRoute() });
    results.push({ name: 'Feature Flags', passed: await validationTests.validateFeatureFlags() });
    results.push({ name: 'Environment Variables', passed: await validationTests.validateEnvironmentVariables() });
    results.push({ name: 'TypeScript', passed: await validationTests.validateTypeScript() });
    results.push({ name: 'Integration', passed: await validationTests.validateIntegration() });

  } catch (error) {
    logError(`Validation failed with error: ${error.message}`);
    process.exit(1);
  }

  // Summary
  logHeader('Phase 3 Validation Summary');

  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  results.forEach(result => {
    if (result.passed) {
      logSuccess(`${result.name}: PASSED`);
    } else {
      logError(`${result.name}: FAILED`);
    }
  });

  log(`\n${colors.bright}Results: ${passed}/${total} tests passed${colors.reset}`);

  if (passed === total) {
    logSuccess('🎉 All Phase 3 Enhanced Simulation validations passed!');
    logInfo('Phase 3 AI SDK Enhanced Simulation implementation is complete and ready for use.');
    logInfo('Features available:');
    logInfo('  • Structured simulation generation with AI SDK');
    logInfo('  • Enhanced batch processing across demographics');
    logInfo('  • Real-time streaming insights');
    logInfo('  • Fallback to existing simulation system');
    logInfo('  • React hooks for frontend integration');
    return true;
  } else {
    logError(`❌ ${total - passed} validation(s) failed. Please fix the issues above.`);
    return false;
  }
}

// Run the validation
runValidation()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    logError(`Validation script failed: ${error.message}`);
    process.exit(1);
  });
