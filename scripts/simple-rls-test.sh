#!/bin/bash

# Simple script to verify all RLS policies are working
# This version avoids type casting errors

# Text formatting
BOLD='\033[1m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BOLD}PollGPT RLS Verification Script${NC}\n"

# Show the simplified SQL commands to run
echo -e "${YELLOW}Please run the following SQL in the Supabase SQL Editor:${NC}\n"

cat << 'EOF'
-- Run these commands in the SQL editor:

-- 1. First verify if the polls table has RLS enabled:
SELECT
  schemaname,
  tablename,
  hasrowsecurity AS "rls_enabled"
FROM
  pg_tables
WHERE
  tablename = 'polls';

-- 2. Check what RLS policies exist for the polls table:
SELECT
  policyname,
  permissive,
  CASE cmd::text
    WHEN 'r' THEN 'SELECT'
    WHEN 'a' THEN 'INSERT'
    WHEN 'w' THEN 'UPDATE'
    WHEN 'd' THEN 'DELETE'
    WHEN '*' THEN 'ALL'
  END AS operation
FROM
  pg_policies
WHERE
  tablename = 'polls';

-- 3. If you need to recreate the policies, run:
DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
DROP POLICY IF EXISTS "Public polls are viewable by everyone" ON polls;
DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;

-- Then create the policies:
CREATE POLICY "Users can view their own polls"
  ON polls
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Public polls are viewable by everyone"
  ON polls
  FOR SELECT
  USING (is_public = true AND is_published = true);

CREATE POLICY "Users can insert their own polls"
  ON polls
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);
EOF

echo -e "\n${YELLOW}After running those commands, check if the dashboard loads properly.${NC}"
echo -e "\n${BOLD}More Debugging Tips:${NC}"
echo "1. Check the browser console for any errors on the dashboard page"
echo "2. Verify your auth token is valid by checking auth.getUser() in the browser console"
echo "3. If you need to reset the database policies completely, run the fix-poll-rls-policies.sql script"
