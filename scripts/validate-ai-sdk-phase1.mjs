#!/usr/bin/env node

/**
 * Simple AI SDK Integration Validation
 * Validates Phase 1 setup without importing TypeScript modules
 */

import fs from 'fs';
import path from 'path';

console.log('🧪 AI SDK Integration Phase 1 Validation\n');

// Check 1: Required files exist
console.log('1️⃣ Checking AI SDK Files...');
const requiredFiles = [
  'src/lib/ai/providers.ts',
  'src/lib/ai/schemas.ts',
  'src/lib/ai/unified-service.ts',
  'src/lib/ai/cache-service.ts',
  'src/lib/ai/fallback-service.ts',
  'src/lib/ai/feature-flags.ts',
  'src/lib/ai/index.ts',
  'src/app/api/ai/generate-poll/route.ts',
  'src/app/api/ai/extract-content/route.ts'
];

let filesOk = true;
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    filesOk = false;
  }
}

// Check 2: Package dependencies
console.log('\n2️⃣ Checking Dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  '@ai-sdk/mistral',
  '@ai-sdk/google',
  '@ai-sdk/perplexity',
  'ai',
  'zod',
  'lru-cache'
];

let depsOk = true;
for (const dep of requiredDeps) {
  if (packageJson.dependencies[dep] || packageJson.devDependencies?.[dep]) {
    console.log(`  ✅ ${dep} - ${packageJson.dependencies[dep] || packageJson.devDependencies[dep]}`);
  } else {
    console.log(`  ❌ ${dep} - MISSING`);
    depsOk = false;
  }
}

// Check 3: Environment configuration
console.log('\n3️⃣ Checking Environment Configuration...');
const envExample = fs.readFileSync('.env.example', 'utf8');
const requiredEnvVars = [
  'MISTRAL_API_KEY',
  'GOOGLE_API_KEY',
  'OPENAI_API_KEY',
  'ENABLE_AI_SDK_STRUCTURED',
  'ENABLE_AI_SDK_STREAMING'
];

let envOk = true;
for (const envVar of requiredEnvVars) {
  if (envExample.includes(envVar)) {
    console.log(`  ✅ ${envVar} configured in .env.example`);
  } else {
    console.log(`  ❌ ${envVar} - MISSING from .env.example`);
    envOk = false;
  }
}

// Check 4: TypeScript compilation
console.log('\n4️⃣ Checking TypeScript Compilation...');
try {
  const { execSync } = await import('child_process');
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
  console.log('  ✅ TypeScript compilation successful');
} catch (error) {
  console.log('  ⚠️ TypeScript compilation has issues (check with: npx tsc --noEmit)');
}

// Check 5: API route structure
console.log('\n5️⃣ Checking API Route Structure...');
const apiDir = 'src/app/api/ai';
if (fs.existsSync(apiDir)) {
  console.log(`  ✅ API directory exists: ${apiDir}`);
  const routes = fs.readdirSync(apiDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);

  console.log(`  📁 Available routes: ${routes.join(', ')}`);
} else {
  console.log(`  ❌ API directory missing: ${apiDir}`);
}

// Final summary
console.log('\n📋 Phase 1 Validation Summary:');
console.log(`Files: ${filesOk ? '✅' : '❌'}`);
console.log(`Dependencies: ${depsOk ? '✅' : '❌'}`);
console.log(`Environment: ${envOk ? '✅' : '❌'}`);

if (filesOk && depsOk && envOk) {
  console.log('\n🎉 Phase 1 Implementation Complete!');
  console.log('\n📝 Next Steps:');
  console.log('1. Copy .env.example to .env.local and add your API keys');
  console.log('2. Start development server: npm run dev');
  console.log('3. Test API endpoints:');
  console.log('   - POST /api/ai/generate-poll');
  console.log('   - POST /api/ai/extract-content');
  console.log('4. Verify existing functionality still works');
  console.log('5. Proceed to Phase 2 when ready');
} else {
  console.log('\n⚠️ Phase 1 validation failed. Please fix the issues above.');
}
