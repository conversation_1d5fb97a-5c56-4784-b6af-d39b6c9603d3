// Simple direct SQL test
const { Pool } = require('pg');

// Connection string directly from the MCP configuration
const connectionString = "postgresql://postgres.sumruaeyfidjlssrmfrm:<EMAIL>:6543/postgres";

async function testConnection() {
  console.log('Testing connection to Supabase PostgreSQL...');

  try {
    // Create connection pool
    const pool = new Pool({ connectionString });

    // Test connection with a simple query
    console.log('Executing query on polls table...');
    const result = await pool.query('SELECT * FROM polls LIMIT 5');

    console.log('Query executed successfully!');
    console.log(`Retrieved ${result.rows.length} polls:`);
    console.log(JSON.stringify(result.rows, null, 2));

    // Close connection
    await pool.end();

    console.log('\nConnection test completed successfully!');
  } catch (error) {
    console.error('Error testing SQL connection:', error);
  }
}

// Run the test
testConnection();
