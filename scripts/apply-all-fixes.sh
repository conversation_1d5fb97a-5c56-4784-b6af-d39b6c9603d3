#!/usr/bin/env zsh
# Apply all database fixes in the correct order

echo "PollGPT Database Fix Helper"
echo "=========================="
echo ""

echo "This script will guide you through applying all the necessary database fixes."
echo ""
echo "1. First, apply the RLS policy fixes for polls table"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/fix-poll-rls-policies.sql"
echo "   - Run the script"
echo ""
echo "2. Then, apply the performance indexes"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/add-performance-indexes.sql"
echo "   - Run the script"
echo ""
echo "3. Finally, add the diagnostic functions"
echo "   - Open your Supabase dashboard"
echo "   - Go to the SQL Editor"
echo "   - Copy and paste the contents of supabase/create-diagnostic-functions.sql"
echo "   - Run the script"
echo ""

# Display the content of the SQL files
echo "Would you like to see the contents of fix-poll-rls-policies.sql? (y/n)"
read -r show_rls

if [[ $show_rls == "y" ]]; then
  echo ""
  echo "===== Contents of fix-poll-rls-policies.sql ====="
  cat "$(dirname "$0")/../supabase/fix-poll-rls-policies.sql"
  echo ""
fi

echo "Would you like to see the contents of add-performance-indexes.sql? (y/n)"
read -r show_indexes

if [[ $show_indexes == "y" ]]; then
  echo ""
  echo "===== Contents of add-performance-indexes.sql ====="
  cat "$(dirname "$0")/../supabase/add-performance-indexes.sql"
  echo ""
fi

echo "Would you like to see the contents of create-diagnostic-functions.sql? (y/n)"
read -r show_diag

if [[ $show_diag == "y" ]]; then
  echo ""
  echo "===== Contents of create-diagnostic-functions.sql ====="
  cat "$(dirname "$0")/../supabase/create-diagnostic-functions.sql"
  echo ""
fi

echo "After applying these fixes:"
echo "1. Restart your application"
echo "2. Try accessing the polls page again"
echo "3. If you still encounter issues, run the database diagnostic script again:"
echo "   ./scripts/fix-database-issues.sh"
echo ""
echo "Good luck!"
