#!/usr/bin/env node

/**
 * Verify Performance Optimization Fix
 *
 * This script tests that the registerAlert method works correctly in the
 * PerformanceOptimizationManager class.
 */

import { PerformanceOptimizationManager } from './performance-optimization.js';

async function verifyFix() {
  console.log('🧪 Verifying performance optimization fix...');

  try {
    // Create manager instance
    console.log('📋 Creating PerformanceOptimizationManager instance...');
    const manager = new PerformanceOptimizationManager();
    console.log('✅ Successfully created instance');

    // Initialize the manager
    console.log('🚀 Initializing services...');
    await manager.initialize();
    console.log('✅ Successfully initialized services');

    // Test setupMetricsCollection method
    console.log('📊 Setting up metrics collection...');
    await manager.setupMetricsCollection();
    console.log('✅ Successfully set up metrics collection');

    console.log('\n🎉 Verification complete! The fix for the performance optimization script works correctly.');
    console.log('The code now properly uses the registerAlert method without trying to access private properties.');
    process.exit(0);
  } catch (error) {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  }
}

// Run the verification
verifyFix().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
