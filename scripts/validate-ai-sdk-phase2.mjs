#!/usr/bin/env node

/**
 * Phase 2 AI SDK Integration Validation Script
 * Tests document processing, OCR, sentiment analysis, and batch processing
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'bold');
  console.log('='.repeat(60));
}

function logSection(message) {
  console.log('\n' + '-'.repeat(40));
  log(message, 'blue');
  console.log('-'.repeat(40));
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      cwd: projectRoot,
      stdio: 'pipe',
      ...options
    });

    let stdout = '';
    let stderr = '';

    proc.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    proc.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    proc.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });
  });
}

async function checkFile(filePath, description) {
  const fullPath = path.join(projectRoot, filePath);
  if (fs.existsSync(fullPath)) {
    log(`✅ ${description}`, 'green');
    return true;
  } else {
    log(`❌ ${description} - File not found: ${filePath}`, 'red');
    return false;
  }
}

async function checkDependencies() {
  logSection('Checking Phase 2 Dependencies');

  const packageJsonPath = path.join(projectRoot, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const requiredDeps = [
    'pdf-parse',
    'mammoth',
    'tesseract.js',
    'natural',
    'bullmq',
    'ioredis',
    'ws',
    '@types/ws'
  ];

  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  let allPresent = true;

  for (const dep of requiredDeps) {
    if (allDeps[dep]) {
      log(`✅ ${dep} - ${allDeps[dep]}`, 'green');
    } else {
      log(`❌ Missing dependency: ${dep}`, 'red');
      allPresent = false;
    }
  }

  return allPresent;
}

async function checkPhase2Files() {
  logSection('Checking Phase 2 Implementation Files');

  const files = [
    { path: 'src/lib/ai/document-processor.ts', desc: 'Document processor service' },
    { path: 'src/lib/ai/nlp-analysis.ts', desc: 'NLP and sentiment analysis service' },
    { path: 'src/lib/ai/batch-processor.ts', desc: 'Batch processing service' },
    { path: 'src/app/api/ai/process-document/route.ts', desc: 'Document processing API' },
    { path: 'src/app/api/ai/batch-process/route.ts', desc: 'Batch processing API' },
    { path: 'src/lib/ai/feature-flags.ts', desc: 'Updated feature flags' }
  ];

  let allPresent = true;
  for (const file of files) {
    const exists = await checkFile(file.path, file.desc);
    if (!exists) allPresent = false;
  }

  return allPresent;
}

async function checkTypeScript() {
  logSection('TypeScript Compilation Check');

  try {
    await runCommand('npx', ['tsc', '--noEmit']);
    log('✅ TypeScript compilation successful', 'green');
    return true;
  } catch (error) {
    log('❌ TypeScript compilation failed:', 'red');
    console.log(error.message);
    return false;
  }
}

async function checkESLint() {
  logSection('ESLint Check');

  try {
    await runCommand('npm', ['run', 'lint']);
    log('✅ ESLint check passed', 'green');
    return true;
  } catch (error) {
    log('❌ ESLint check failed:', 'red');
    console.log(error.message);
    return false;
  }
}

async function testFeatureFlags() {
  logSection('Feature Flags Validation');

  try {
    // Test feature flag loading
    const testScript = `
      import { getAISDKFeatures, isFeatureEnabled } from './src/lib/ai/feature-flags.ts';

      const features = getAISDKFeatures();
      console.log('Phase 2 Features:', {
        documentProcessing: features.documentProcessing,
        ocrProcessing: features.ocrProcessing,
        sentimentAnalysis: features.sentimentAnalysis,
        batchDocumentProcessing: features.batchDocumentProcessing,
        websocketProgressTracking: features.websocketProgressTracking,
        advancedNLP: features.advancedNLP
      });

      console.log('Document processing enabled:', isFeatureEnabled('documentProcessing'));
      console.log('OCR processing enabled:', isFeatureEnabled('ocrProcessing'));
      console.log('Sentiment analysis enabled:', isFeatureEnabled('sentimentAnalysis'));
    `;

    const tempFile = path.join(projectRoot, 'temp-feature-test.mjs');
    fs.writeFileSync(tempFile, testScript);

    await runCommand('node', [tempFile]);
    fs.unlinkSync(tempFile);

    log('✅ Feature flags loading correctly', 'green');
    return true;
  } catch (error) {
    log('❌ Feature flags test failed:', 'red');
    console.log(error.message);
    return false;
  }
}

async function testDocumentProcessorImport() {
  logSection('Document Processor Import Test');

  try {
    const testScript = `
      import { DocumentProcessor } from './src/lib/ai/document-processor.ts';

      const processor = new DocumentProcessor();
      console.log('Document processor initialized successfully');
      console.log('Supported formats:', processor.getSupportedFormats?.() || ['pdf', 'docx', 'txt', 'image']);
    `;

    const tempFile = path.join(projectRoot, 'temp-doc-test.mjs');
    fs.writeFileSync(tempFile, testScript);

    await runCommand('node', [tempFile]);
    fs.unlinkSync(tempFile);

    log('✅ Document processor import successful', 'green');
    return true;
  } catch (error) {
    log('❌ Document processor import failed:', 'red');
    console.log(error.message);
    return false;
  }
}

async function testNLPAnalysisImport() {
  logSection('NLP Analysis Import Test');

  try {
    const testScript = `
      import { NLPAnalysisService } from './src/lib/ai/nlp-analysis.ts';

      const nlpService = new NLPAnalysisService();
      console.log('NLP analysis service initialized successfully');

      // Test basic sentiment analysis
      const testText = "This is a great poll question that makes me happy!";
      const sentiment = await nlpService.analyzeSentiment(testText);
      console.log('Sentiment analysis test:', sentiment);
    `;

    const tempFile = path.join(projectRoot, 'temp-nlp-test.mjs');
    fs.writeFileSync(tempFile, testScript);

    await runCommand('node', [tempFile]);
    fs.unlinkSync(tempFile);

    log('✅ NLP analysis service import successful', 'green');
    return true;
  } catch (error) {
    log('❌ NLP analysis service import failed:', 'red');
    console.log(error.message);
    return false;
  }
}

async function checkEnvironmentVariables() {
  logSection('Environment Variables Check');

  const envExamplePath = path.join(projectRoot, '.env.example');
  const envContent = fs.readFileSync(envExamplePath, 'utf8');

  const requiredPhase2Vars = [
    'ENABLE_AI_SDK_DOCUMENT_PROCESSING',
    'ENABLE_AI_SDK_OCR_PROCESSING',
    'ENABLE_AI_SDK_SENTIMENT_ANALYSIS',
    'ENABLE_AI_SDK_BATCH_DOCUMENT_PROCESSING',
    'ENABLE_AI_SDK_WEBSOCKET_PROGRESS',
    'ENABLE_AI_SDK_ADVANCED_NLP',
    'REDIS_URL',
    'MAX_DOCUMENT_SIZE_MB',
    'MAX_BATCH_SIZE',
    'OCR_CONFIDENCE_THRESHOLD'
  ];

  let allPresent = true;
  for (const varName of requiredPhase2Vars) {
    if (envContent.includes(varName)) {
      log(`✅ ${varName}`, 'green');
    } else {
      log(`❌ Missing environment variable: ${varName}`, 'red');
      allPresent = false;
    }
  }

  return allPresent;
}

async function runValidation() {
  logHeader('🚀 AI SDK Phase 2 Integration Validation');

  const results = {
    dependencies: false,
    files: false,
    typescript: false,
    eslint: false,
    featureFlags: false,
    documentProcessor: false,
    nlpAnalysis: false,
    environment: false
  };

  try {
    results.dependencies = await checkDependencies();
    results.files = await checkPhase2Files();
    results.typescript = await checkTypeScript();
    results.eslint = await checkESLint();
    results.featureFlags = await testFeatureFlags();
    results.documentProcessor = await testDocumentProcessorImport();
    results.nlpAnalysis = await testNLPAnalysisImport();
    results.environment = await checkEnvironmentVariables();

    // Summary
    logHeader('📊 Validation Summary');

    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;

    for (const [test, result] of Object.entries(results)) {
      const status = result ? '✅ PASS' : '❌ FAIL';
      const color = result ? 'green' : 'red';
      log(`${status} ${test}`, color);
    }

    console.log('\n' + '='.repeat(60));
    if (passed === total) {
      log(`🎉 All tests passed! (${passed}/${total})`, 'green');
      log('✅ Phase 2 AI SDK integration is ready!', 'green');
    } else {
      log(`⚠️  ${passed}/${total} tests passed`, 'yellow');
      log('❌ Phase 2 integration needs attention', 'red');
    }
    console.log('='.repeat(60));

    process.exit(passed === total ? 0 : 1);

  } catch (error) {
    log('❌ Validation failed with error:', 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run validation
runValidation().catch(console.error);
