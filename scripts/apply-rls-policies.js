// <PERSON>ript to apply RLS policies to Supabase database
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_MCP_KEY; // Using service role key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyRLSPolicies() {
  console.log('Applying RLS policies to Supabase database...');

  try {
    // Read SQL files
    const profilesPoliciesPath = path.join(__dirname, '../supabase/fix-rls-policies.sql');
    const pollsPoliciesPath = path.join(__dirname, '../supabase/fix-polls-rls-policies.sql');
    const questionsPoliciesPath = path.join(__dirname, '../supabase/fix-questions-responses-rls.sql');

    const profilesPolicies = fs.readFileSync(profilesPoliciesPath, 'utf8');
    const pollsPolicies = fs.readFileSync(pollsPoliciesPath, 'utf8');
    const questionsPolicies = fs.readFileSync(questionsPoliciesPath, 'utf8');

    // Apply profiles RLS policies
    console.log('\nApplying profiles RLS policies...');
    const { error: profilesError } = await supabase.rpc('exec_sql', { sql: profilesPolicies });

    if (profilesError) {
      console.error('Error applying profiles RLS policies:', profilesError.message);
      console.log('You may need to apply these policies manually in the Supabase dashboard SQL editor.');
    } else {
      console.log('✅ Successfully applied profiles RLS policies');
    }

    // Apply polls RLS policies
    console.log('\nApplying polls RLS policies...');
    const { error: pollsError } = await supabase.rpc('exec_sql', { sql: pollsPolicies });

    if (pollsError) {
      console.error('Error applying polls RLS policies:', pollsError.message);
      console.log('You may need to apply these policies manually in the Supabase dashboard SQL editor.');
    } else {
      console.log('✅ Successfully applied polls RLS policies');
    }

    // Apply questions and responses RLS policies
    console.log('\nApplying questions and responses RLS policies...');
    const { error: questionsError } = await supabase.rpc('exec_sql', { sql: questionsPolicies });

    if (questionsError) {
      console.error('Error applying questions and responses RLS policies:', questionsError.message);
      console.log('You may need to apply these policies manually in the Supabase dashboard SQL editor.');
    } else {
      console.log('✅ Successfully applied questions and responses RLS policies');
    }

    console.log('\nRLS policies application complete. If there were any errors, you may need to apply the policies manually.');
    console.log('You can find the SQL files in the supabase directory of your project.');

  } catch (error) {
    console.error('Unexpected error:', error);
    console.log('You may need to apply these policies manually in the Supabase dashboard SQL editor.');
  }
}

applyRLSPolicies();
