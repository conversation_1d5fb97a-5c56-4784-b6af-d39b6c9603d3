[{"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.answers\\` has a row level security policy \\`answers_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "answers", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_answers_answers_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.answers\\` has a row level security policy \\`answers_select_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "answers", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_answers_answers_select_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.responses\\` has a row level security policy \\`responses_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "responses", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_responses_responses_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.responses\\` has a row level security policy \\`responses_select_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "responses", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_responses_responses_select_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.questions\\` has a row level security policy \\`questions_select_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "questions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_questions_questions_select_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.questions\\` has a row level security policy \\`questions_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "questions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_questions_questions_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.questions\\` has a row level security policy \\`questions_update_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "questions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_questions_questions_update_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.questions\\` has a row level security policy \\`questions_delete_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "questions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_questions_questions_delete_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.poll_simulations\\` has a row level security policy \\`poll_simulations_select_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "poll_simulations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_poll_simulations_poll_simulations_select_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.poll_simulations\\` has a row level security policy \\`poll_simulations_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "poll_simulations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_poll_simulations_poll_simulations_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.poll_simulations\\` has a row level security policy \\`poll_simulations_update_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "poll_simulations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_poll_simulations_poll_simulations_update_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.poll_simulations\\` has a row level security policy \\`poll_simulations_delete_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "poll_simulations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_poll_simulations_poll_simulations_delete_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.polls\\` has a row level security policy \\`polls_delete_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "polls", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_polls_polls_delete_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.polls\\` has a row level security policy \\`polls_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "polls", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_polls_polls_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.polls\\` has a row level security policy \\`polls_select_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "polls", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_polls_polls_select_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.polls\\` has a row level security policy \\`polls_update_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "polls", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_polls_polls_update_policy"}]