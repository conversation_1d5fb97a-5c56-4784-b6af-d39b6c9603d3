-- Check auth configuration and settings
-- Run this in your Supabase SQL Editor to diagnose email issues

-- 1. Check if there are any users waiting for email confirmation
SELECT
    id,
    email,
    email_confirmed_at,
    created_at,
    CASE
        WHEN email_confirmed_at IS NULL THEN 'Not Confirmed'
        ELSE 'Confirmed'
    E<PERSON> as confirmation_status
FROM auth.users
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- 2. Check if there are any recent one_time_tokens (email confirmation tokens)
SELECT
    id,
    user_id,
    token_type,
    token_hash,
    relates_to,
    created_at,
    updated_at
FROM auth.one_time_tokens
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- 3. Check for any audit log entries related to auth
SELECT
    instance_id,
    id,
    payload,
    created_at,
    ip_address
FROM auth.audit_log_entries
WHERE created_at > NOW() - INTERVAL '24 hours'
AND payload->>'action' IN ('signup', 'email_confirmation_sent', 'email_confirmed')
ORDER BY created_at DESC;

-- 4. Check auth.instances table for configuration (if it exists)
-- This might give us clues about whether email confirmation is disabled
SELECT * FROM auth.instances LIMIT 1;

-- 5. Show table structure to understand the setup
\d+ auth.users;
