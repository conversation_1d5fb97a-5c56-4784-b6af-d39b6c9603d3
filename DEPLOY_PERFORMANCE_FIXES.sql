-- DEPLOY THIS MIGRATION SCRIPT IN SUPABASE SQL EDITOR
-- This script consolidates all performance fixes into a single deployable migration

-- Step 1: Drop all duplicate and inefficient RLS policies
BEGIN;

-- ANSWERS table cleanup - drop ALL existing policies first
DROP POLICY IF EXISTS "Anyone can insert answers" ON answers;
DROP POLICY IF EXISTS "Users can insert answers to any poll" ON answers;
DROP POLICY IF EXISTS "Answers can be viewed by poll owner" ON answers;
DROP POLICY IF EXISTS "Users can view answers for their polls" ON answers;
DROP POLICY IF EXISTS "Users can view their own answers" ON answers;
DROP POLICY IF EXISTS "consolidated_answers_insert" ON answers;
DROP POLICY IF EXISTS "consolidated_answers_select" ON answers;
DROP POLICY IF EXISTS "answers_insert_policy" ON answers;
DROP POLICY IF EXISTS "answers_select_policy" ON answers;

-- RESPONSES table cleanup - drop ALL existing policies first
DROP POLICY IF EXISTS "Anyone can insert responses" ON responses;
DROP POLICY IF EXISTS "Users can insert responses to any poll" ON responses;
DROP POLICY IF EXISTS "Responses can be viewed by poll owner" ON responses;
DROP POLICY IF EXISTS "Users can view responses for their polls" ON responses;
DROP POLICY IF EXISTS "Users can view their own responses" ON responses;
DROP POLICY IF EXISTS "responses_insert_policy" ON responses;
DROP POLICY IF EXISTS "responses_select_policy" ON responses;

-- QUESTIONS table cleanup - drop ALL existing policies first
DROP POLICY IF EXISTS "Questions can be modified by poll owner" ON questions;
DROP POLICY IF EXISTS "Questions can be viewed by poll owner" ON questions;
DROP POLICY IF EXISTS "Questions for public polls are viewable by everyone" ON questions;
DROP POLICY IF EXISTS "Users can view questions for their polls" ON questions;
DROP POLICY IF EXISTS "Anyone can view questions for public polls" ON questions;
DROP POLICY IF EXISTS "Users can delete questions for their polls" ON questions;
DROP POLICY IF EXISTS "Users can insert questions for their polls" ON questions;
DROP POLICY IF EXISTS "Users can update questions for their polls" ON questions;
DROP POLICY IF EXISTS "questions_select_policy" ON questions;
DROP POLICY IF EXISTS "questions_insert_policy" ON questions;
DROP POLICY IF EXISTS "questions_update_policy" ON questions;
DROP POLICY IF EXISTS "questions_delete_policy" ON questions;

-- POLL_SIMULATIONS table cleanup - drop ALL existing policies first
DROP POLICY IF EXISTS "Users can create simulations for own polls" ON poll_simulations;
DROP POLICY IF EXISTS "Users can view simulations for accessible polls" ON poll_simulations;
DROP POLICY IF EXISTS "Users can view their own simulations" ON poll_simulations;
DROP POLICY IF EXISTS "Users can create their own simulations" ON poll_simulations;
DROP POLICY IF EXISTS "Users can delete own simulations" ON poll_simulations;
DROP POLICY IF EXISTS "Users can update own simulations" ON poll_simulations;
DROP POLICY IF EXISTS "poll_simulations_select_policy" ON poll_simulations;
DROP POLICY IF EXISTS "poll_simulations_insert_policy" ON poll_simulations;
DROP POLICY IF EXISTS "poll_simulations_update_policy" ON poll_simulations;
DROP POLICY IF EXISTS "poll_simulations_delete_policy" ON poll_simulations;

-- POLLS table cleanup - drop ALL existing policies first
DROP POLICY IF EXISTS "Users can delete their own polls" ON polls;
DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;
DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
DROP POLICY IF EXISTS "Users can update their own polls" ON polls;
DROP POLICY IF EXISTS "Anyone can view public polls" ON polls;
DROP POLICY IF EXISTS "polls_delete_policy" ON polls;
DROP POLICY IF EXISTS "polls_insert_policy" ON polls;
DROP POLICY IF EXISTS "polls_select_policy" ON polls;
DROP POLICY IF EXISTS "polls_update_policy" ON polls;

COMMIT;

-- Step 2: Create optimized, consolidated policies using (select auth.uid())
BEGIN;

-- ANSWERS table - consolidated policies
CREATE POLICY "answers_insert_policy" ON answers
  FOR INSERT TO public
  WITH CHECK (
    -- Authenticated users can insert for their own responses
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Anonymous users can insert for public active polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Dashboard users can insert anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "answers_select_policy" ON answers
  FOR SELECT TO public
  USING (
    -- Users can view their own answers
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Poll owners can view answers for their polls
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.user_id = (select auth.uid())
    ))
    OR
    -- Anonymous users can view answers for public polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

-- RESPONSES table - consolidated policies
CREATE POLICY "responses_insert_policy" ON responses
  FOR INSERT TO public
  WITH CHECK (
    -- Must be for active polls
    EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND status = 'active'
    )
    AND
    (
      -- Authenticated users set their own user_id
      (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
      OR
      -- Anonymous users can respond to public polls (user_id can be null)
      (auth.role() = 'anon' AND EXISTS (
        SELECT 1 FROM polls
        WHERE id = poll_id AND is_public = true
      ))
      OR
      -- Dashboard users can insert anywhere
      (auth.role() = 'dashboard_user')
    )
  );

CREATE POLICY "responses_select_policy" ON responses
  FOR SELECT TO public
  USING (
    -- Users can view their own responses
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    -- Poll owners can view responses for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

-- QUESTIONS table - consolidated policies
CREATE POLICY "questions_select_policy" ON questions
  FOR SELECT TO public
  USING (
    -- Anyone can view questions for public polls
    EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND is_public = true
    )
    OR
    -- Poll owners can view questions for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_insert_policy" ON questions
  FOR INSERT TO public
  WITH CHECK (
    -- Only poll owners can insert questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can insert anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_update_policy" ON questions
  FOR UPDATE TO public
  USING (
    -- Only poll owners can update questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can update anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_delete_policy" ON questions
  FOR DELETE TO public
  USING (
    -- Only poll owners can delete questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can delete anywhere
    (auth.role() = 'dashboard_user')
  );

-- POLL_SIMULATIONS table - consolidated policies
CREATE POLICY "poll_simulations_select_policy" ON poll_simulations
  FOR SELECT TO public
  USING (
    -- Users can view simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Poll owners can view simulations for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Users can view simulations for public polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND is_public = true
    ))
    OR
    -- Service role and dashboard users can view all
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_insert_policy" ON poll_simulations
  FOR INSERT TO public
  WITH CHECK (
    -- Users can create simulations for their own polls
    (auth.role() = 'authenticated' AND
     created_by = (select auth.uid()) AND
     EXISTS (
       SELECT 1 FROM polls
       WHERE id = poll_id AND user_id = (select auth.uid())
     ))
    OR
    -- Service role and dashboard users can insert anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_update_policy" ON poll_simulations
  FOR UPDATE TO public
  USING (
    -- Users can update simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Service role and dashboard users can update anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_delete_policy" ON poll_simulations
  FOR DELETE TO public
  USING (
    -- Users can delete simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Service role and dashboard users can delete anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

-- POLLS table - optimized policies
CREATE POLICY "polls_delete_policy" ON polls
  FOR DELETE TO public
  USING (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "polls_insert_policy" ON polls
  FOR INSERT TO public
  WITH CHECK (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "polls_select_policy" ON polls
  FOR SELECT TO public
  USING (
    -- Anyone can view public polls
    is_public = true
    OR
    -- Users can view their own polls
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "polls_update_policy" ON polls
  FOR UPDATE TO public
  USING (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

COMMIT;

-- Step 3: Maintenance and cleanup
-- NOTE: VACUUM commands are moved to a separate file: VACUUM_MAINTENANCE.sql
-- Only running ANALYZE commands here which can be run inside transactions

-- Update table statistics
BEGIN;
ANALYZE questions;
ANALYZE polls;
ANALYZE profiles;
ANALYZE answers;
ANALYZE responses;
ANALYZE poll_simulations;
COMMIT;

-- Create comments documenting the optimization (in transaction for safety)
BEGIN;
COMMENT ON TABLE answers IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE responses IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE questions IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE polls IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE poll_simulations IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMIT;

-- Step 4: Verification queries
-- Run these to verify the migration worked correctly
SELECT
  'After Migration - RLS Policies' as check_type,
  COUNT(*) as new_count
FROM pg_policies
WHERE schemaname = 'public'
  AND tablename IN ('answers', 'responses', 'questions', 'polls', 'poll_simulations');

SELECT
  'After Migration - Policies using auth.uid() directly' as check_type,
  COUNT(*) as problematic_count
FROM pg_policies
WHERE schemaname = 'public'
  AND tablename IN ('answers', 'responses', 'questions', 'polls', 'poll_simulations')
  AND (qual LIKE '%auth.uid()%' OR with_check LIKE '%auth.uid()%')
  AND (qual NOT LIKE '%(select auth.uid())%' AND with_check NOT LIKE '%(select auth.uid())%');

SELECT
  'After Migration - Dead Tuples' as check_type,
  COALESCE(SUM(n_dead_tup), 0) as dead_tuples_count
FROM pg_stat_user_tables
WHERE schemaname = 'public';

-- Success message
SELECT 'Database Performance Optimization Complete!' as status,
       'All duplicate policies removed, auth.uid() optimized, and maintenance completed' as details;
