# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run dev:full` - Run development server with performance monitoring

### Testing
- `npm run test` - Run Playwright tests
- `npm run test:ui` - Run tests with UI
- `npm run test:headed` - Run tests in headed mode
- `npm run test:seo` - Run SEO tests (sitemap + schema)
- `npm run test:ai-sdk` - Validate AI SDK integration

### Performance & Monitoring
- `npm run perf:start` - Start performance monitoring
- `npm run perf:health` - Check system health
- `npm run perf:benchmark` - Run performance benchmarks
- `npm run perf:stats` - View performance statistics

### Background Services
- `npm run jobs:start` - Start background job worker
- `npm run mcp:start` - Start MCP server for web scraping

## Project Architecture

### Core Technology Stack
- **Framework**: Next.js 15 with TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with OAuth (Google)
- **AI Services**: Multiple providers (Mistral, OpenAI, Perplexity, Gemini)
- **Caching**: Redis/Upstash for performance
- **Testing**: Playwright for E2E testing
- **Styling**: Tailwind CSS with Radix UI components

### Application Structure

#### App Router Organization
- `(auth)/` - Authentication routes (login, register, password reset)
- `(dashboard)/` - Protected dashboard routes
- `(public)/` - Public poll viewing routes
- `api/` - API routes for AI processing, data operations, and integrations

#### Key Services (`src/lib/services/`)
- **AI Services**: Multiple AI providers with fallback mechanisms
  - `mistral-service.ts` - Primary AI provider
  - `gemini-ai.ts`, `perplexity-ai.ts` - Alternative providers
  - `unified-service.ts` - Unified AI interface with fallbacks
- **Content Extraction**: Multi-format document processing
  - `content-extractor.ts` - Main extraction service
  - `pdf-text-extractor.ts` - PDF processing
  - `mistral-document-service.ts` - AI-powered document analysis
- **Data Layer**: 
  - `polls.ts` - Poll CRUD operations
  - `optimized-database.ts` - Performance-optimized database queries
  - `cache-service.ts` - Redis caching layer

#### Authentication System
- Custom Supabase client with enhanced session management (`src/lib/supabase.ts`)
- OAuth flow with PKCE support and session restoration
- Multiple auth providers and debugging utilities

#### Data Types & Validation
- Comprehensive Zod schemas in `src/lib/validation/schemas.ts`
- Type definitions in `src/lib/types/poll.ts`
- Database type safety with generated types

### Key Features

#### Poll Creation & Management
- Multi-step poll creation wizard
- Support for various question types (single, multiple, open-ended)
- Content extraction from URLs, PDFs, DOCX, images
- AI-powered question generation from extracted content

#### Content Processing Pipeline
1. **Extraction**: URLs, files, or direct text input
2. **AI Analysis**: Content analysis and question generation
3. **Review**: Manual review and editing of generated questions
4. **Publication**: Public or private poll sharing

#### Performance Optimizations
- Redis caching for expensive operations
- Background job processing with BullMQ
- Database query optimization
- CDN integration for static assets

## Development Guidelines

### Authentication Development
- Use `src/hooks/use-auth.ts` for authentication state
- Always check for session validity before protected operations
- Handle OAuth redirects properly with the enhanced storage adapter

### AI Integration
- Use the unified AI service for consistent fallback behavior
- Implement proper error handling for AI service failures
- Cache AI responses when appropriate to reduce costs

### Database Operations
- Use the optimized database service for complex queries
- Implement proper pagination for large datasets
- Always validate data with Zod schemas before database operations

### Content Extraction
- Handle multiple file formats (PDF, DOCX, images)
- Implement proper error handling for extraction failures
- Use appropriate AI models for different content types

### Performance Considerations
- Monitor performance metrics using the built-in dashboard
- Use Redis caching for expensive operations
- Implement proper loading states and error boundaries

## Environment Configuration

### Required Environment Variables
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `MISTRAL_API_KEY` - Primary AI service
- `REDIS_URL` - Redis connection for caching
- Additional AI service keys as needed

### Development Setup
1. Install dependencies: `npm install`
2. Set up environment variables in `.env.local`
3. Start development server: `npm run dev`
4. Run tests to verify setup: `npm run test`

## Testing Strategy

### E2E Testing with Playwright
- Tests located in `tests/` directory
- Run with `npm run test` or `npm run test:ui`
- Tests cover authentication, poll creation, and public poll access

### Performance Testing
- Use `npm run perf:benchmark` to measure performance
- Monitor metrics through the performance dashboard
- Test under load with the job worker system

## Background Services

### Job Processing
- BullMQ for background task processing
- Start with `npm run jobs:start`
- Used for heavy AI processing and bulk operations

### MCP (Model Context Protocol) Integration
- Web scraping capabilities through MCP servers
- Start with `npm run mcp:start`
- Used for extracting content from web pages

## Troubleshooting

### Common Issues
- **Authentication loops**: Check OAuth redirect configuration
- **AI service failures**: Verify API keys and fallback service configuration
- **Database connection issues**: Ensure Supabase credentials are correct
- **Performance issues**: Check Redis connection and cache hit rates

### Debug Tools
- Use `src/lib/debug-oauth.ts` for OAuth debugging
- Performance dashboard at `/dashboard/analytics`
- Check logs through the job worker system