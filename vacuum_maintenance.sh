#!/bin/bash
# vacuum_maintenance.sh
# This script runs each VACUUM command individually using the Supabase CLI
# Ensure that supabase CLI is installed and configured

# Execute each VACUUM command individually through separate files
echo "Running VACUUM on questions table..."
supabase db execute --file vacuum_questions.sql

echo "Running VACUUM on polls table..."
supabase db execute --file vacuum_polls.sql

echo "Running VACUUM on profiles table..."
supabase db execute --file vacuum_profiles.sql

echo "Running VACUUM on answers table..."
supabase db execute --file vacuum_answers.sql

echo "Running VACUUM on poll_simulations table..."
supabase db execute --file vacuum_poll_simulations.sql

echo "Running VACUUM on responses table..."
supabase db execute --file vacuum_responses.sql

echo "VACUUM maintenance completed successfully!"
