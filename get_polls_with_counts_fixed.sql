-- Function to get polls with counts in a single query with pagination support
-- This optimizes the poll loading by reducing multiple queries to one and adds pagination
CREATE OR REPLACE FUNCTION get_polls_with_counts(
  user_id_param UUID,
  page_number INT DEFAULT 1,
  page_size INT DEFAULT 10,
  fetch_all BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  is_published BOOLEAN,
  is_public BOOLEAN,
  slug TEXT,
  response_count BIGINT,
  view_count BIGINT,
  questions JSONB,
  total_count BIGINT -- Added to return the total count for pagination
) AS $$
DECLARE
  total_polls BIGINT;
  offset_val INT;
BEGIN
  -- Calculate the offset based on page number and size
  offset_val := (page_number - 1) * page_size;
  
  -- Get total count first for pagination info
  SELECT COUNT(*) INTO total_polls FROM polls WHERE polls.user_id = user_id_param;
  
  -- Return query with pagination
  RETURN QUERY
  WITH poll_responses AS (
    -- CTE to count responses for each poll (with index for performance)
    SELECT
      r.poll_id,
      COUNT(r.id)::BIGINT AS num_responses
    FROM responses r
    WHERE EXISTS (SELECT 1 FROM polls p WHERE p.id = r.poll_id AND p.user_id = user_id_param)
    GROUP BY r.poll_id
  ), poll_questions_agg AS (
    -- CTE to aggregate questions for each poll into a JSONB array (with index for performance)
    SELECT
      q.poll_id,
      jsonb_agg(
        jsonb_build_object(
          'id', q.id,
          'poll_id', q.poll_id,
          'question_text', q.question_text,
          'question_type', q.question_type,
          'options', q.options,
          'required', q.required,
          'order', q.order
        ) ORDER BY q.order -- Ensures consistent order of questions in the JSON array
      ) AS aggregated_questions
    FROM questions q
    WHERE EXISTS (SELECT 1 FROM polls p WHERE p.id = q.poll_id AND p.user_id = user_id_param)
    GROUP BY q.poll_id
  )
  SELECT
    p.id,
    p.title,
    p.description,
    p.created_at,
    p.updated_at,
    p.user_id,
    p.is_published,
    p.is_public,
    p.slug,
    COALESCE(pr.num_responses, 0) AS response_count,
    COALESCE(p.views, 0)::BIGINT AS view_count,
    COALESCE(pqa.aggregated_questions, '[]'::jsonb) AS questions,
    total_polls AS total_count -- Include the total count in each row
  FROM
    polls p
  LEFT JOIN
    poll_responses pr ON p.id = pr.poll_id
  LEFT JOIN
    poll_questions_agg pqa ON p.id = pqa.poll_id
  WHERE
    p.user_id = user_id_param
  ORDER BY
    p.updated_at DESC
  -- Apply pagination only if fetch_all is false
  LIMIT CASE WHEN fetch_all THEN NULL ELSE page_size END
  OFFSET CASE WHEN fetch_all THEN 0 ELSE offset_val END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_polls_with_counts(UUID, INT, INT, BOOLEAN) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION get_polls_with_counts(UUID, INT, INT, BOOLEAN) IS 'Gets polls for a user with response counts and questions in a single optimized query with pagination support';

-- Create indexes to improve query performance if they don't exist already
DO $$
BEGIN
  -- Check if index exists on responses.poll_id
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_responses_poll_id') THEN
    CREATE INDEX IF NOT EXISTS idx_responses_poll_id ON responses(poll_id);
  END IF;
  
  -- Check if index exists on questions.poll_id
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_questions_poll_id') THEN
    CREATE INDEX IF NOT EXISTS idx_questions_poll_id ON questions(poll_id);
  END IF;
  
  -- Check if index exists on polls.user_id
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_polls_user_id') THEN
    CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
  END IF;
END;
$$;
