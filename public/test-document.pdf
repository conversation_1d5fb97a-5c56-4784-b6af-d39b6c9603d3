%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/MediaBox[0 0 595 842]/Contents 4 0 R/Resources<<>>>>endobj
4 0 obj<</Length 150>>
stream
BT
/F1 12 Tf
50 750 Td
(This is a test PDF document for PollGPT.) Tj
0 -20 Td
(It contains some sample text for extraction testing.) Tj
0 -20 Td
(Let's see if our basic extractor can handle it.) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000102 00000 n
0000000182 00000 n
trailer<</Size 5/Root 1 0 R>>
startxref
369
%%EOF