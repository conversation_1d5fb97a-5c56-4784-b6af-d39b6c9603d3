<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Model Response Tester</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      margin-bottom: 20px;
    }
    .model-controls {
      margin-bottom: 30px;
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      align-items: center;
    }
    button {
      padding: 8px 16px;
      background: #4a69ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
    }
    button:hover {
      background: #3a59ef;
    }
    .output {
      background: #f5f5f5;
      border-radius: 6px;
      padding: 15px;
      margin-top: 20px;
      white-space: pre-wrap;
      font-family: monospace;
      font-size: 14px;
      max-height: 400px;
      overflow-y: auto;
    }
    .log {
      margin: 5px 0;
    }
    .error { color: #e53935; }
    .success { color: #43a047; }
    .info { color: #1e88e5; }
  </style>
</head>
<body>
  <h1>PollGPT Model Response Tester</h1>

  <div class="model-controls">
    <span>Test model responses:</span>
    <button onclick="testModel('perplexity')">Perplexity</button>
    <button onclick="testModel('mistral')">Mistral</button>
    <button onclick="testModel('gemini')">Gemini</button>
    <button onclick="testModel('chat')">Generic Chat</button>
  </div>

  <div class="output" id="output"></div>

  <script>
    const output = document.getElementById('output');

    function log(message, type = 'info') {
      const logElement = document.createElement('div');
      logElement.className = `log ${type}`;
      logElement.textContent = message;
      output.appendChild(logElement);
      output.scrollTop = output.scrollHeight;
    }

    function clear() {
      output.innerHTML = '';
    }

    async function testModel(model) {
      clear();
      log(`Testing ${model.toUpperCase()} API...`, 'info');

      try {
        const endpoint = `/api/${model.toLowerCase()}`;
        log(`Making request to: ${endpoint}`, 'info');

        const startTime = Date.now();

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: [
              { role: 'system', content: 'You are a helpful assistant.' },
              { role: 'user', content: 'Create a brief poll about customer satisfaction with 3 questions.' }
            ],
            model: model
          })
        });

        const endTime = Date.now();
        log(`Response received in ${endTime - startTime}ms`, 'info');
        log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');

        // Log headers
        log('Response headers:', 'info');
        response.headers.forEach((value, key) => {
          log(`${key}: ${value}`, 'info');
        });

        // Check content type
        const contentType = response.headers.get('Content-Type') || '';
        log(`Content type: ${contentType}`, 'info');

        // Handle response based on content type
        if (contentType.includes('application/json')) {
          const jsonResponse = await response.json();
          log('JSON response structure: ' + JSON.stringify(Object.keys(jsonResponse)), 'success');
          log('Response excerpt: ' + JSON.stringify(jsonResponse).substring(0, 500) + '...', 'success');

          // Extract message content if available
          if (jsonResponse.choices && jsonResponse.choices[0]) {
            if (jsonResponse.choices[0].message) {
              log('Message content: ' + jsonResponse.choices[0].message.content.substring(0, 300) + '...', 'success');
            } else if (jsonResponse.choices[0].text) {
              log('Text content: ' + jsonResponse.choices[0].text.substring(0, 300) + '...', 'success');
            }
          }
        } else {
          const textResponse = await response.text();
          log('Text response: ' + textResponse.substring(0, 500) + '...', 'success');
        }
      } catch (error) {
        log(`Error testing ${model} API: ${error.message}`, 'error');
        console.error('Full error:', error);
      }
    }
  </script>
</body>
</html>
