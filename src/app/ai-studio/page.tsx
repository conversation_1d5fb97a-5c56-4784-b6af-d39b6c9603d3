"use client";

import { useState, Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Wand2,
  BarChart3,
  Sparkles,
  TrendingUp,
  Users,
  Zap,
  ArrowRight,
  CheckCircle,
  Clock
} from 'lucide-react';
import RealTimePollCreation from '@/components/ai/real-time-poll-creation';
import StreamingPollAnalytics from '@/components/ai/streaming-poll-analytics';
import { cn } from '@/lib/utils';

function PollStudioContent() {
  const [activeFeature, setActiveFeature] = useState<'creation' | 'analytics'>('creation');
  const [createdPoll, setCreatedPoll] = useState<{
    id?: string;
    title: string;
    description?: string;
    questions?: Array<{
      text: string;
      type: string;
      options?: Array<{ text: string; value: string }>;
    }>;
  } | null>(null);

  const handlePollCreated = (poll: {
    id?: string;
    title: string;
    description?: string;
    questions?: Array<{
      text: string;
      type: string;
      options?: Array<{ text: string; value: string }>;
    }>;
  }) => {
    setCreatedPoll(poll);
    setActiveFeature('analytics');
  };

  const features = [
    {
      id: 'creation',
      title: 'AI Poll Creation',
      description: 'Real-time poll generation with live AI assistance',
      icon: Wand2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950/30',
      borderColor: 'border-blue-200 dark:border-blue-800',
    },
    {
      id: 'analytics',
      title: 'Streaming Analytics',
      description: 'Live AI-powered insights and real-time analysis',
      icon: BarChart3,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50 dark:bg-emerald-950/30',
      borderColor: 'border-emerald-200 dark:border-emerald-800',
    },
  ];

  const aiCapabilities = [
    {
      icon: Sparkles,
      title: 'Real-time Generation',
      description: 'Watch AI create polls with live streaming feedback',
    },
    {
      icon: TrendingUp,
      title: 'Live Analytics',
      description: 'Get instant insights as responses come in',
    },
    {
      icon: Users,
      title: 'Smart Optimization',
      description: 'AI automatically optimizes questions for your audience',
    },
    {
      icon: Zap,
      title: 'Instant Insights',
      description: 'Real-time recommendations and performance analysis',
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
          <Zap className="w-4 h-4" />
          Phase 4: Real-time AI Integration
        </div>

        <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
          AI Poll Studio
          <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            {" "}Live
          </span>
        </h1>

        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
          Experience the future of poll creation with real-time AI assistance,
          streaming analytics, and live optimization recommendations.
        </p>

        {/* AI Capabilities Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
          {aiCapabilities.map((capability, index) => (
            <Card key={index} className="text-center p-4">
              <CardContent className="p-0">
                <capability.icon className="w-8 h-8 mx-auto mb-3 text-primary" />
                <h3 className="font-semibold mb-2">{capability.title}</h3>
                <p className="text-sm text-muted-foreground">{capability.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Feature Selection */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Choose Your AI Experience</CardTitle>
          <CardDescription>
            Select a feature to explore Phase 4&apos;s real-time AI capabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            {features.map((feature) => (
              <Card
                key={feature.id}
                className={cn(
                  "cursor-pointer transition-all duration-200 hover:shadow-lg",
                  activeFeature === feature.id
                    ? `${feature.bgColor} ${feature.borderColor} border-2`
                    : "hover:bg-muted/50"
                )}
                onClick={() => setActiveFeature(feature.id as 'creation' | 'analytics')}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      "p-3 rounded-lg",
                      activeFeature === feature.id ? feature.bgColor : "bg-muted"
                    )}>
                      <feature.icon className={cn(
                        "w-6 h-6",
                        activeFeature === feature.id ? feature.color : "text-muted-foreground"
                      )} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-1">{feature.title}</h3>
                      <p className="text-muted-foreground">{feature.description}</p>
                    </div>
                    {activeFeature === feature.id && (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content Area */}
      <div className="space-y-8">
        {activeFeature === 'creation' && (
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 dark:bg-blue-950/30 rounded-lg">
                <Wand2 className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Real-time AI Poll Creation</h2>
                <p className="text-muted-foreground">
                  Experience live AI assistance with streaming content analysis and real-time optimization
                </p>
              </div>
            </div>

            <RealTimePollCreation onPollCreated={handlePollCreated} />

            {createdPoll && (
              <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <div>
                      <h3 className="font-semibold">Poll Created Successfully!</h3>
                      <p className="text-sm text-muted-foreground">
                        Your poll &quot;{createdPoll.title}&quot; is ready. Switch to Analytics to see live insights.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveFeature('analytics')}
                      className="ml-auto"
                    >
                      View Analytics
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {activeFeature === 'analytics' && (
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-emerald-50 dark:bg-emerald-950/30 rounded-lg">
                <BarChart3 className="w-5 h-5 text-emerald-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Streaming Poll Analytics</h2>
                <p className="text-muted-foreground">
                  Real-time AI-powered insights and live response analysis
                </p>
              </div>
            </div>

            {createdPoll && createdPoll.id ? (
              <StreamingPollAnalytics
                pollId={createdPoll.id}
                pollData={createdPoll}
              />
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Clock className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Poll Selected</h3>
                  <p className="text-muted-foreground mb-4">
                    Create a poll first to see real-time analytics in action
                  </p>
                  <Button
                    onClick={() => setActiveFeature('creation')}
                    className="gap-2"
                  >
                    <Wand2 className="w-4 h-4" />
                    Create a Poll
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Phase 4 Features Summary */}
      <Card className="mt-12 bg-gradient-to-r from-blue-50 to-emerald-50 dark:from-blue-950/30 dark:to-emerald-950/30">
        <CardContent className="p-8">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold mb-2">Phase 4 Features</h3>
            <p className="text-muted-foreground">
              Real-time AI integration bringing the power of streaming intelligence to poll creation
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <Badge className="mb-3">Real-time Generation</Badge>
              <h4 className="font-semibold mb-2">Streaming AI Creation</h4>
              <p className="text-sm text-muted-foreground">
                Watch AI create polls with live feedback, content analysis, and optimization suggestions
              </p>
            </div>

            <div className="text-center">
              <Badge className="mb-3">Live Analytics</Badge>
              <h4 className="font-semibold mb-2">Streaming Insights</h4>
              <p className="text-sm text-muted-foreground">
                Get real-time analysis as responses come in with AI-powered pattern recognition
              </p>
            </div>

            <div className="text-center">
              <Badge className="mb-3">Smart Optimization</Badge>
              <h4 className="font-semibold mb-2">Continuous Improvement</h4>
              <p className="text-sm text-muted-foreground">
                AI continuously optimizes polls for better engagement and response quality
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function AIStudioPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Clock className="w-8 h-8 mx-auto mb-4 animate-spin" />
            <p>Loading AI Studio...</p>
          </div>
        </div>
      </div>
    }>
      <PollStudioContent />
    </Suspense>
  );
}
