"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { PollCreationDemo } from "@/components/ui/poll-creation-demo";
import { PageMetadata } from "../page-metadata";
import { SEOFaqEnhanced } from "@/components/ui/seo-faq-enhanced";
import { RichKeywords } from "@/components/ui/seo-helpers";
import { SEOConstruct } from "@/components/ui/seo-construct";
import { LinkArchive } from "@/components/ui/link-archive";

export default function FreePollsPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <PageMetadata />

      {/* Hero section */}
      <div className="flex-1">
        <div className="container px-4 md:px-6 flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] py-10">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                Free AI-Powered Poll Creator
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Create intelligent polls with AI assistance at zero cost. The fastest way to gather insights and make data-driven decisions.
              </p>
            </div>
            <div className="space-x-4">
              <Link href="/register">
                <Button size="lg" className="font-medium">Get Started Free</Button>
              </Link>
              <Link href="/">
                <Button variant="outline" size="lg">Learn More</Button>
              </Link>
            </div>
          </div>

          <div className="mx-auto w-full max-w-5xl py-12">
            <PollCreationDemo />
          </div>

          <div className="grid gap-6 md:gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-5xl mx-auto w-full px-4 py-8">
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Forever Free</h3>
              <p className="text-muted-foreground text-center">Create unlimited basic polls with our free tier - no credit card required</p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25" />
                  <line x1="8" x2="8" y1="16" y2="16" />
                  <line x1="8" x2="8" y1="20" y2="20" />
                  <line x1="12" x2="12" y1="18" y2="18" />
                  <line x1="12" x2="12" y1="22" y2="22" />
                  <line x1="16" x2="16" y1="16" y2="16" />
                  <line x1="16" x2="16" y1="20" y2="20" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">AI-Powered</h3>
              <p className="text-muted-foreground text-center">Generate unbiased questions and gain insights with advanced AI assistance</p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M3 3v18h18" />
                  <path d="m19 9-5 5-4-4-3 3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Real-time Insights</h3>
              <p className="text-muted-foreground text-center">Watch responses come in and get automatic analysis of your results</p>
            </div>
          </div>
        </div>
      </div>      {/* Enhanced FAQ section for SEO */}
      <SEOFaqEnhanced />

      {/* SEO Construct for semantic markup */}
      <SEOConstruct />

      {/* Rich keywords section */}
      <RichKeywords />

      {/* Link Archive for internal linking SEO */}
      <LinkArchive />

      {/* Footer */}
      <div className="w-full py-6 bg-muted">
        <div className="container px-4 md:px-6 text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
              <span className="font-bold text-lg text-foreground">PollGPT</span>
            </div>
            <nav className="flex gap-4">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
