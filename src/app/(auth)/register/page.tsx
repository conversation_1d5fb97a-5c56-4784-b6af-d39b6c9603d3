"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as zod from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { DistributionPreview } from "@/components/ui/feature-previews";
import { useSignUp } from "@/hooks/use-auth-enhanced";
import { GoogleAuthButton } from "@/components/auth/google-auth-button";

// Add these animations to globals.css if needed
// @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
// .animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
// .animate-fadeInDelayed { animation: fadeIn 0.5s ease-out 0.2s forwards; opacity: 0; }

const registerSchema = zod.object({
  name: zod.string().min(2, { message: "Name must be at least 2 characters" }),
  email: zod.string().email({ message: "Please enter a valid email address" }),
  password: zod.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: zod.string().min(8, { message: "Password must be at least 8 characters" }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

type RegisterFormValues = zod.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const signUpMutation = useSignUp();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  async function onSubmit(data: RegisterFormValues) {
    setIsLoading(true);

    try {
      // Attempt to sign up
      const result = await signUpMutation.mutateAsync({
        email: data.email,
        password: data.password,
        name: data.name,
      });

      if (result.user) {
        // Show email verification success message
        toast.success(
          "Account created! Please check your email to verify your account before signing in.",
          {
            duration: 6000, // Show for 6 seconds since it's an important message
          }
        );

        // Longer delay before redirecting to give them time to read the message
        setTimeout(() => {
          router.push("/login?verify=true");
        }, 2500);
      }
    } catch (error: Error | unknown) {
      console.error("Registration error:", error);

      let errorMessage = "Failed to create account. Please try again.";

      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes("over_email_send_rate_limit") || error.message.includes("429")) {
          errorMessage = "Too many signup attempts. Please wait a few minutes before trying again.";
        } else if (error.message.includes("User already registered")) {
          errorMessage = "This email is already registered. Try signing in instead.";
        } else if (error.message.includes("Invalid email")) {
          errorMessage = "Please enter a valid email address.";
        } else if (error.message.includes("Password should be at least")) {
          errorMessage = "Password must be at least 6 characters long.";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage, {
        duration: 5000, // Show longer for error messages
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      <div className="container px-4 md:px-6 mx-auto flex flex-col md:flex-row h-screen">
      {/* Left side - Registration form */}
        <div className="w-full md:w-1/2 flex items-center justify-center mt-8 md:mt-0">
        <div className="w-full max-w-md animate-fadeIn">
          <div className="mb-8 flex flex-col md:flex-row items-center md:items-start justify-center md:justify-between">
            <Link href="/dashboard/polls" className="flex items-center">
              <ThemeLogo type="logo" width={180} height={40} className="h-10" />
            </Link>
            <Link
              href="/"
              className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground mt-4 md:mt-0"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
                <path d="m12 19-7-7 7-7"/>
                <path d="M19 12H5"/>
              </svg>
              Back to home
            </Link>
          </div>

          <Card className="border-none shadow-lg">
            <CardHeader className="space-y-1 pb-4">
              <CardTitle className="text-2xl font-bold text-center md:text-left">Create an account</CardTitle>
              <CardDescription className="text-center md:text-left">
                Join PollGPT and start creating powerful polls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Your name"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="<EMAIL>"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    className="w-full h-11 mt-6"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating account...
                      </>
                    ) : (
                      "Create account"
                    )}
                  </Button>
                </form>
              </Form>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">OR SIGN UP WITH</span>
                </div>
              </div>

              {/* Google Sign Up */}
              <GoogleAuthButton
                mode="signup"
                disabled={isLoading}
              />
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-2">
              <div className="text-sm text-center">
                Already have an account?{" "}
                <Link href="/login" className="text-primary font-medium hover:underline">
                  Sign in
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Right side - Preview */}
      <div className="hidden md:flex md:w-1/2 relative">
        {/* Full brand orange background for both light and dark modes */}
        <div className="absolute inset-0 bg-primary"></div>

        <div className="relative z-10 flex items-center justify-center w-full h-full p-8">
          <div className="max-w-lg animate-fadeInDelayed">
            <div className="mb-8 text-center">
              <h2 className="text-3xl font-bold mb-2 text-white">Share your polls easily</h2>
              <p className="text-white/90">Distribute your polls via custom links, QR codes, or embed them directly on your website.</p>
            </div>

            <div className="space-y-6">
              <DistributionPreview />

              <div className="bg-background/95 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-border/50">
                <div className="flex items-start gap-4">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                      <polyline points="7 10 12 15 17 10"/>
                      <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium mb-1 text-foreground">Reach your audience anywhere</h3>
                    <p className="text-sm text-foreground/80">Our flexible distribution options make it easy to get your polls in front of the right people.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}
