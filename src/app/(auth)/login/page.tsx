"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth, useSignIn } from "@/hooks/use-auth-unified";
import { GoogleAuthButton } from "@/components/auth/google-auth-button";
import { debugPKCEStorage, clearAllAuthStorage } from "@/lib/debug-oauth";
import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { AiCreationPreview } from "@/components/ui/feature-previews";
import { SessionRecoveryButton } from "@/components/ui/session-recovery-button";

// Add these animations to globals.css if needed
// @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
// .animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
// .animate-fadeInDelayed { animation: fadeIn 0.5s ease-out 0.2s forwards; opacity: 0; }

const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const { user, isLoading } = useAuth();
  const signInMutation = useSignIn();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showVerificationMessage, setShowVerificationMessage] = useState(false);
  const [showRecoveryMessage, setShowRecoveryMessage] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (!isLoading && user) {
      // Check for redirect parameter and redirect to the intended page
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect');

      if (redirectTo) {
        // Decode the redirect URL and navigate there
        try {
          const decodedRedirect = decodeURIComponent(redirectTo);

          // Enhanced safety checks to prevent redirect loops
          if (
            decodedRedirect.includes('login') ||
            decodedRedirect.includes('register') ||
            decodedRedirect.includes('auth/callback') ||
            decodedRedirect === window.location.pathname
          ) {
            console.warn('Prevented redirect loop, redirecting to dashboard');
            router.replace("/dashboard/polls");
          } else {
            console.log('Redirecting authenticated user to:', decodedRedirect);
            router.replace(decodedRedirect);
          }
        } catch (error) {
          console.error('Error decoding redirect URL:', error);
          router.replace("/dashboard/polls");
        }
      } else {
        router.replace("/dashboard/polls");
      }
    }
  }, [user, isLoading, router]);

  // Check for OAuth errors and other messages
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('verify') === 'true') {
      setShowVerificationMessage(true);
    }
    if (urlParams.get('recovery') === 'true') {
      setShowRecoveryMessage(true);
    }

    // Handle OAuth errors
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('description');

    if (error) {
      let message = 'Authentication failed';

      switch (error) {
        case 'oauth_error':
          message = 'Google sign-in failed. Please try again.';
          break;
        case 'pkce_error':
          message = 'Authentication security check failed. Please try signing in again.';
          break;
        case 'exchange_failed':
          message = 'Failed to complete authentication. Please try again.';
          break;
        case 'missing_code':
          message = 'Authentication was interrupted. Please try again.';
          break;
        case 'no_session':
          message = 'Failed to create session. Please try again.';
          break;
        case 'unexpected_error':
          message = 'An unexpected error occurred. Please try again.';
          break;
        default:
          if (errorDescription) {
            message = decodeURIComponent(errorDescription);
          }
      }

      toast.error(message);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, []);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Handle Google OAuth sign in - removed since we're using GoogleAuthButton component

  async function onSubmit(data: LoginFormValues) {
    setIsSubmitting(true);

    try {
      const result = await signInMutation.mutateAsync({
        email: data.email,
        password: data.password,
      });

      if (result.user) {
        toast.success("Logged in successfully!");
        // The auth provider will handle navigation automatically
      }
    } catch (error: Error | unknown) {
      console.error("Login error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to sign in. Please check your credentials.";
      toast.error(errorMessage);
      setIsSubmitting(false); // Only reset loading state on error
    }
    // Note: We don't call setIsSubmitting(false) on success because the page will be unmounted during navigation
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      <div className="container px-4 md:px-6 mx-auto flex flex-col md:flex-row h-screen">
      {/* Left side - Login form */}
        <div className="w-full md:w-1/2 flex items-center justify-center mt-16 md:mt-0">
        <div className="w-full max-w-md animate-fadeIn">
          <div className="mb-8 flex flex-col md:flex-row items-center md:items-start justify-center md:justify-between">
            <Link href="/dashboard/polls" className="flex items-center">
              <ThemeLogo type="logo" width={180} height={40} className="h-10" />
            </Link>
            <Link
              href="/"
              className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground mt-4 md:mt-0"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
                <path d="m12 19-7-7 7-7"/>
                <path d="M19 12H5"/>
              </svg>
              Back to home
            </Link>
          </div>

          {showVerificationMessage && (
            <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
              <p className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 10.5V12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9h1.5" />
                  <path d="m12 13 5-5" />
                  <path d="M17 8h-5V3" />
                </svg>
                <span className="font-medium">Please check your email</span>
              </p>
              <p className="mt-1 text-sm">
                We&apos;ve sent you a verification email. Please verify your account before signing in.
              </p>
            </div>
          )}

          {showRecoveryMessage && (
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md text-green-800">
              <p className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                  <path d="m9 12 2 2 4-4" />
                </svg>
                <span className="font-medium">Session fixed</span>
              </p>
              <p className="mt-1 text-sm">
                Your login session has been reset. You can now sign in with your credentials.
              </p>
            </div>
          )}

          <Card className="border-none shadow-lg">
            <CardHeader className="space-y-1 pb-4">
              <CardTitle className="text-2xl font-bold text-center md:text-left">Welcome Back</CardTitle>
              <CardDescription className="text-center md:text-left">
                Sign in to your account to continue
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="<EMAIL>"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>Password</FormLabel>
                          <Link href="#" className="text-xs text-primary hover:underline">
                            Forgot password?
                          </Link>
                        </div>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    className="w-full h-11 mt-6"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing in...
                      </>
                    ) : (
                      "Sign in"
                    )}
                  </Button>
                </form>
              </Form>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">OR CONTINUE WITH</span>
                </div>
              </div>

              {/* Google Sign In */}
              <GoogleAuthButton
                mode="login"
                disabled={isSubmitting}
              />
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-2">
              <div className="text-sm text-center">
                Don&apos;t have an account?{" "}
                <Link href="/register" className="text-primary font-medium hover:underline">
                  Sign up
                </Link>
              </div>

              <div className="text-xs text-center text-muted-foreground">
                Having trouble logging in?{" "}
                <SessionRecoveryButton variant="link" className="text-xs p-0 h-auto font-medium text-primary">
                  Fix login issues
                </SessionRecoveryButton>
              </div>

              {/* Debug Panel for Development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 pt-4 border-t border-dashed border-gray-200 dark:border-gray-700">
                  <p className="text-xs font-medium text-amber-600 dark:text-amber-400 mb-2">Debug Panel</p>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full text-xs h-8"
                      onClick={() => debugPKCEStorage()}
                    >
                      Debug PKCE Storage
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full text-xs h-8"
                      onClick={() => {
                        clearAllAuthStorage();
                        window.location.reload();
                      }}
                    >
                      Clear Auth Storage
                    </Button>
                  </div>
                </div>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Right side - Preview */}
      <div className="hidden md:flex md:w-1/2 relative">
        {/* Full brand orange background for both light and dark modes */}
        <div className="absolute inset-0 bg-primary"></div>

        <div className="relative z-10 flex items-center justify-center w-full h-full p-8">
          <div className="max-w-lg animate-fadeInDelayed">
            <div className="mb-8 text-center">
              <h2 className="text-3xl font-bold mb-2 text-white">Create powerful polls with AI</h2>
              <p className="text-white/90">Generate intelligent questions, gather insights, and analyze results - all in one place.</p>
            </div>

            <div className="space-y-6">
              <AiCreationPreview />

              <div className="bg-background/95 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-border/50">
                <div className="flex items-start gap-4">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M17 18a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v12Z"/>
                      <path d="M12 9h.01"/>
                      <path d="M12 13h.01"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium mb-1 text-foreground">Start creating polls in minutes</h3>
                    <p className="text-sm text-foreground/80">Our AI-powered platform makes it easy to create professional polls that get results.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}
