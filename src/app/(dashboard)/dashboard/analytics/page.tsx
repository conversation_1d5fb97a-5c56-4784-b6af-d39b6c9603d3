"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart
} from "recharts";
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  MessageSquare,
  Target,
  Shield,
  Brain,
  Activity
} from "lucide-react";
import Link from "next/link";
import { getAnalyticsData, AnalyticsData } from "@/lib/services/analytics";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function AnalyticsPage() {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  const { 
    data: analyticsData, 
    isLoading, 
    error 
  } = useQuery<AnalyticsData, Error>({
    queryKey: ['analytics', selectedTimeRange],
    queryFn: () => getAnalyticsData(selectedTimeRange),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
  });

  if (isLoading) {
    return (
      <div className="container mx-auto p-3 sm:p-6">
        <div className="flex items-center justify-center min-h-[300px] sm:min-h-[400px]">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full animate-pulse"></div>
            <div className="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-3 sm:p-6">
        <div className="text-center py-8 bg-red-50 border border-red-200 rounded-lg">
          <h2 className="text-xl sm:text-2xl font-bold mb-4 text-red-700">An Error Occurred</h2>
          <p className="text-sm sm:text-base text-red-600">Unable to load analytics data. Please try again later.</p>
          <p className="text-xs text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="container mx-auto p-3 sm:p-6">
        <div className="text-center py-8">
          <h2 className="text-xl sm:text-2xl font-bold mb-4">Analytics Unavailable</h2>
          <p className="text-sm sm:text-base text-muted-foreground">Unable to load analytics data at this time.</p>
        </div>
      </div>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="container mx-auto p-3 sm:p-6 space-y-4 sm:space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-2 sm:space-y-0">
            <Button variant="ghost" size="sm" asChild className="w-fit">
              <Link href="/dashboard">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">Analytics Dashboard</h1>
              <p className="text-sm sm:text-base text-muted-foreground">
                Comprehensive insights into your poll performance and engagement
              </p>
            </div>
          </div>

          {/* Time Range Selector */}
          <div className="flex gap-1 sm:gap-2 overflow-x-auto">
            {[
              { value: '7d', label: '7D' },
              { value: '30d', label: '30D' },
              { value: '90d', label: '90D' },
              { value: '1y', label: '1Y' }
            ].map((range) => (
              <Button
                key={range.value}
                variant={selectedTimeRange === range.value ? "default" : "outline"}
                size="sm"
                className="flex-shrink-0 text-xs sm:text-sm px-2 sm:px-3"
                onClick={() => setSelectedTimeRange(range.value as '7d' | '30d' | '90d' | '1y')}
              >
                <span className="hidden sm:inline">
                  {range.value === '7d' ? '7 Days' :
                   range.value === '30d' ? '30 Days' :
                   range.value === '90d' ? '90 Days' : '1 Year'}
                </span>
                <span className="sm:hidden">{range.label}</span>
              </Button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Key Metrics Cards */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Polls</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{analyticsData.overview.totalPolls}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {analyticsData.overview.pollsGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={analyticsData.overview.pollsGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                {Math.abs(analyticsData.overview.pollsGrowth)}%
              </span>
              <span className="hidden sm:inline">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Responses</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{analyticsData.overview.totalResponses}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {analyticsData.overview.responsesGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={analyticsData.overview.responsesGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                {Math.abs(analyticsData.overview.responsesGrowth)}%
              </span>
              <span className="hidden sm:inline">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{analyticsData.overview.avgResponseRate}%</div>
            <Progress value={analyticsData.overview.avgResponseRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{analyticsData.overview.totalViews.toLocaleString()}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {analyticsData.overview.viewsGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={analyticsData.overview.viewsGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                {Math.abs(analyticsData.overview.viewsGrowth)}%
              </span>
              <span className="hidden sm:inline">vs last period</span>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Analytics Tabs */}
      <motion.div variants={itemVariants}>
        <Tabs defaultValue="engagement" className="space-y-4 sm:space-y-6">
          <div className="overflow-x-auto">
            <TabsList className="grid grid-cols-5 min-w-full">
              <TabsTrigger value="engagement" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm">
                <Activity className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Engagement</span>
                <span className="sm:hidden">Engage</span>
              </TabsTrigger>
              <TabsTrigger value="performance" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Performance</span>
                <span className="sm:hidden">Perf</span>
              </TabsTrigger>
              <TabsTrigger value="audience" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm">
                <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Audience</span>
                <span className="sm:hidden">Aud</span>
              </TabsTrigger>
              <TabsTrigger value="quality" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm">
                <Shield className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Quality</span>
                <span className="sm:hidden">Qual</span>
              </TabsTrigger>
              <TabsTrigger value="intelligence" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm">
                <Brain className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">AI Insights</span>
                <span className="sm:hidden">AI</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="engagement" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Poll Creation Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Poll Creation Trend</CardTitle>
                  <CardDescription className="text-sm">Number of polls created over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-center">
                    <ResponsiveContainer width="100%" height={250}>
                      <AreaChart
                        data={analyticsData.pollTrend}
                        margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="date"
                          fontSize={12}
                          tickMargin={8}
                        />
                        <YAxis fontSize={12} />
                        <Tooltip />
                        <Area type="monotone" dataKey="count" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Response Rate Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Response Rate Trend</CardTitle>
                  <CardDescription className="text-sm">How your response rates are changing over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <LineChart data={analyticsData.responseTrend}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        fontSize={12}
                        tickMargin={8}
                      />
                      <YAxis fontSize={12} />
                      <Tooltip />
                      <Line type="monotone" dataKey="rate" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Top Performing Polls */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Top Performing Polls</CardTitle>
                <CardDescription className="text-sm">Your polls with the highest engagement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 sm:space-y-4">
                  {analyticsData.topPolls.map((poll, index) => (
                    <div key={poll.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-3 sm:p-4 border rounded-lg gap-3 sm:gap-0">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">#{index + 1}</Badge>
                        <div>
                          <h4 className="font-medium">{poll.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {poll.responses} responses • {poll.views} views
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{poll.responseRate}%</div>
                        <div className="text-sm text-muted-foreground">response rate</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Question Type Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Question Type Performance</CardTitle>
                  <CardDescription className="text-sm">Response rates by question type</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={analyticsData.questionTypePerformance}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="type"
                        fontSize={12}
                        tickMargin={8}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis fontSize={12} />
                      <Tooltip />
                      <Bar dataKey="responseRate" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Poll Status Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Poll Status Distribution</CardTitle>
                  <CardDescription className="text-sm">Breakdown of your polls by status</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={analyticsData.pollStatusDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {analyticsData.pollStatusDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Performance Metrics</CardTitle>
                <CardDescription className="text-sm">Key performance indicators for your polls</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-2xl sm:text-3xl font-bold text-green-600">{analyticsData.metrics.avgCompletionTime}s</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Avg Completion Time</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-2xl sm:text-3xl font-bold text-blue-600">{analyticsData.metrics.dropOffRate}%</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Drop-off Rate</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-2xl sm:text-3xl font-bold text-purple-600">{analyticsData.metrics.returningUsers}%</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Returning Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audience" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Geographic Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Geographic Distribution</CardTitle>
                  <CardDescription className="text-sm">Where your responses are coming from</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 sm:space-y-4">
                    {analyticsData.geographic.map((country) => (
                      <div key={country.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <span className="text-lg sm:text-2xl">{country.flag}</span>
                          <span className="font-medium text-sm sm:text-base">{country.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={country.percentage} className="w-16 sm:w-20" />
                          <span className="text-xs sm:text-sm text-muted-foreground w-8 sm:w-12">{country.percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Device Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Device Distribution</CardTitle>
                  <CardDescription className="text-sm">How users access your polls</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={analyticsData.deviceDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {analyticsData.deviceDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Audience Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Audience Insights</CardTitle>
                <CardDescription className="text-sm">Understanding your poll participants</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-lg sm:text-2xl font-bold">{analyticsData.audience.uniqueVisitors}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Unique Visitors</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-lg sm:text-2xl font-bold">{analyticsData.audience.avgSessionDuration}m</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Avg Session</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-lg sm:text-2xl font-bold">{analyticsData.audience.bounceRate}%</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Bounce Rate</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 border rounded-lg">
                    <div className="text-lg sm:text-2xl font-bold">{analyticsData.audience.newVsReturning}%</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">New Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quality" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Response Quality Score */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Response Quality Score</CardTitle>
                  <CardDescription className="text-sm">Overall quality of responses received</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl sm:text-4xl font-bold text-green-600 mb-2">{analyticsData.quality.overallScore}/100</div>
                    <Progress value={analyticsData.quality.overallScore} className="mb-4" />
                    <p className="text-xs sm:text-sm text-muted-foreground">
                      Based on response completeness, time spent, and consistency
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Fraud Detection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Fraud Detection</CardTitle>
                  <CardDescription className="text-sm">Security and quality metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base">Duplicate Responses</span>
                      <Badge variant={analyticsData.quality.duplicateResponses < 5 ? "default" : "destructive"}>
                        {analyticsData.quality.duplicateResponses}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base">Suspicious IPs</span>
                      <Badge variant={analyticsData.quality.suspiciousIPs < 3 ? "default" : "destructive"}>
                        {analyticsData.quality.suspiciousIPs}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base">Bot Detection</span>
                      <Badge variant={analyticsData.quality.botDetection < 2 ? "default" : "destructive"}>
                        {analyticsData.quality.botDetection}%
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quality Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Quality Trends</CardTitle>
                <CardDescription className="text-sm">How response quality is changing over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={analyticsData.qualityTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      fontSize={12}
                      tickMargin={8}
                    />
                    <YAxis fontSize={12} />
                    <Tooltip />
                    <Line type="monotone" dataKey="score" stroke="#82ca9d" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="intelligence" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* AI Simulation Usage */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">AI Simulation Usage</CardTitle>
                  <CardDescription className="text-sm">How often you use poll simulations</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={analyticsData.simulationUsage}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="month"
                        fontSize={12}
                        tickMargin={8}
                      />
                      <YAxis fontSize={12} />
                      <Tooltip />
                      <Bar dataKey="simulations" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Prediction Accuracy */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Prediction Accuracy</CardTitle>
                  <CardDescription className="text-sm">How accurate our AI predictions are</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl sm:text-4xl font-bold text-blue-600 mb-2">{analyticsData.aiInsights.predictionAccuracy}%</div>
                    <Progress value={analyticsData.aiInsights.predictionAccuracy} className="mb-4" />
                    <p className="text-xs sm:text-sm text-muted-foreground">
                      Based on simulation vs actual results comparison
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* AI Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">AI-Generated Insights</CardTitle>
                <CardDescription className="text-sm">Smart recommendations based on your data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 sm:space-y-4">
                  {analyticsData.aiInsights.recommendations.map((insight, index) => (
                    <div key={index} className="flex items-start gap-2 sm:gap-3 p-3 sm:p-4 border rounded-lg">
                      <Brain className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <h4 className="font-medium text-sm sm:text-base">{insight.title}</h4>
                        <p className="text-xs sm:text-sm text-muted-foreground mt-1">{insight.description}</p>
                        {insight.impact && (
                          <Badge variant="outline" className="mt-2 text-xs">
                            Impact: {insight.impact}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  );
}
