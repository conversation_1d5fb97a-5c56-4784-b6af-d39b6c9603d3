// polls-page.tsx
// Refactored version using React Query for data fetching and state management

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { usePolls, PollFilters } from '@/hooks/use-polls';
import { useQueryClient } from '@tanstack/react-query';
import { useSessionRefresh } from '@/lib/hooks/useSessionRefresh';
import { Loader } from '@/components/ui/loader';

const PAGE_SIZE = 10;

export default function PollsPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingTime, setLoadingTime] = useState(0);
  const [showError, setShowError] = useState(true);

  // Create filters for the usePolls hook
  const filters: PollFilters = {
    page: currentPage,
    pageSize: PAGE_SIZE
  };

  // Use the React Query hook to fetch polls
  const {
    data: paginatedPolls,
    isLoading,
    error,
    refetch
  } = usePolls(filters);

  const queryClient = useQueryClient();

  // Use session refresh hook
  const {
    refreshSession,
    refreshing,
    resetRefreshAttempts
  } = useSessionRefresh();

  // Function to handle refresh button click
  const handleRefresh = () => {
    resetRefreshAttempts();
    refetch();
  };

  // Function to handle session refresh
  const handleSessionRefresh = async () => {
    const success = await refreshSession();
    if (success) {
      // Wait a moment for the refresh to propagate
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['polls'] });
      }, 500);
    }
  };

  // Calculate loading time for better user feedback
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    const startTime = Date.now();

    if (isLoading) {
      timer = setInterval(() => {
        setLoadingTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    } else {
      setLoadingTime(0);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLoading]);

  // Generate page navigation controls
  const renderPagination = () => {
    if (!paginatedPolls?.polls?.length || !paginatedPolls?.totalCount) return null;

    const totalPages = Math.ceil(paginatedPolls.totalCount / PAGE_SIZE);
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center space-x-2 mt-6">
        <Button
          variant="outline"
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1 || isLoading}
        >
          Previous
        </Button>
        <span>
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages || isLoading}
        >
          Next
        </Button>
      </div>
    );
  };

  // Generate loading status message
  const getLoadingMessage = () => {
    if (loadingTime < 5) {
      return "Loading your polls...";
    } else if (loadingTime < 10) {
      return `Loading your polls... (${loadingTime}s)`;
    } else {
      return `Loading your polls... (${loadingTime}s) This is taking longer than usual.`;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Your Polls</h1>
        <div className="flex space-x-2">
          <Button onClick={handleRefresh} disabled={isLoading} className="gap-2">
            {isLoading ? (
              <>
                <Loader variant="minimal" size="xs" />
                Refreshing...
              </>
            ) : (
              <>Refresh</>
            )}
          </Button>
          {error instanceof Error && error.message.includes('session') && (
            <Button onClick={handleSessionRefresh} disabled={isLoading || refreshing} className="gap-2">
              {refreshing ? (
                <>
                  <Loader variant="minimal" size="xs" />
                  Refreshing...
                </>
              ) : (
                <>Refresh Session</>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Error display */}
      {error instanceof Error && showError && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 rounded-md flex justify-between items-center">
          <p>{error.message}</p>
          <Button variant="outline" size="sm" onClick={() => setShowError(false)}>Dismiss</Button>
        </div>
      )}

      {/* Long loading time warning */}
      {isLoading && loadingTime >= 10 && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md">
          <p>Loading is taking longer than expected. This could be due to:</p>
          <ul className="list-disc pl-5 mt-2">
            <li>Slow internet connection</li>
            <li>High server load</li>
            <li>Session timeout requiring refresh</li>
          </ul>
          <div className="mt-3 flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleSessionRefresh}>
              Refresh Session
            </Button>
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div className="py-12">
          <Loader size="lg" text={getLoadingMessage()} centered={true} />
        </div>
      )}

      {/* Empty state */}
      {!isLoading && (!paginatedPolls?.polls || paginatedPolls.polls.length === 0) && !error && (
        <div className="text-center py-12 rounded-lg bg-transparent">
          <p className="text-gray-500">You haven&apos;t created any polls yet.</p>
          <Button className="mt-4">Create Your First Poll</Button>
        </div>
      )}

      {/* Polls list */}
      {!isLoading && paginatedPolls?.polls && paginatedPolls.polls.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {paginatedPolls.polls.map(poll => (
            <div key={poll.id} className="border rounded-lg p-5 hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-lg mb-1">{poll.title}</h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{poll.description}</p>
              <div className="flex justify-between text-sm text-gray-500">
                <span>{poll.questions_count} questions</span>
                <span>{poll.responses_count} responses</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {renderPagination()}
    </div>
  );
}
