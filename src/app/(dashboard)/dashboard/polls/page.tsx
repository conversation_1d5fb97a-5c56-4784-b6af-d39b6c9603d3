"use client";

import { useState, useMemo } from "react";
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { motion, type Variants } from "framer-motion";
import { ErrorBoundary } from "@/components/error-boundary";
import { useSessionRefresh } from "@/lib/hooks/useSessionRefresh";

// Import UI components
import { PollCard, SearchAndFilter, LoadingAndError, EmptyState } from "./ui-components";

// Import React Query hooks
import { usePolls, useDeletePoll, useDuplicatePoll, useClosePoll, PollFilters } from "@/hooks/use-polls";

// Since we're now using consistent Zod types, no adaptation needed

// Helper function to handle errors
function handleError(error: unknown): string {
  console.error('Error:', error);
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred';
}

export default function PollsPage() {
  const router = useRouter();
  const { refreshSession } = useSessionRefresh();

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Create filters object for React Query
  const filters: PollFilters = {
    page: currentPage,
    pageSize,
    searchQuery,
    statusFilter
  };

  // Use React Query hooks
  const {
    data: paginatedPolls,
    isLoading,
    error: queryError,
    refetch,
    dataUpdatedAt
  } = usePolls(filters);

  const deletePollMutation = useDeletePoll();
  const duplicatePollMutation = useDuplicatePoll();
  const closePollMutation = useClosePoll();

  // Calculate loading time
  const loadingTime = dataUpdatedAt ? (Date.now() - dataUpdatedAt) : 0;

  // Extract data from the query result
  const polls = useMemo(() => paginatedPolls?.polls || [], [paginatedPolls?.polls]);
  const totalCount = paginatedPolls?.totalCount || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Format error message
  const error = queryError ? handleError(queryError as Error) : null;

  // Check online status
  const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of the page when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle creating a new poll
  const handleCreateNewPoll = () => {
    router.push('/dashboard/create/conversational');
  };

  // Handle deleting a poll
  const handleDeletePoll = async (id: string) => {
    try {
      await deletePollMutation.mutateAsync(id);
      toast.success("Poll deleted successfully");
    } catch (err) {
      console.error("Error deleting poll:", err);
      const errorMessage = handleError(err);

      if (typeof errorMessage === 'string' &&
          (errorMessage.includes("auth") || errorMessage.includes("session"))) {
        handleSessionTimeout(errorMessage);
      } else {
        toast.error(`Failed to delete poll: ${errorMessage}`);
      }
    }
  };

  // Handle duplicating a poll
  const handleDuplicatePoll = async (id: string) => {
    try {
      await duplicatePollMutation.mutateAsync(id);
      toast.success("Poll duplicated successfully");
    } catch (err) {
      console.error("Error duplicating poll:", err);
      const errorMessage = handleError(err);

      if (typeof errorMessage === 'string' &&
          (errorMessage.includes("auth") || errorMessage.includes("session"))) {
        handleSessionTimeout(errorMessage);
      } else {
        toast.error(`Failed to duplicate poll: ${errorMessage}`);
      }
    }
  };

  // Handle closing a poll
  const handleClosePoll = async (id: string) => {
    try {
      await closePollMutation.mutateAsync(id);
      toast.success("Poll closed successfully");
    } catch (err) {
      console.error("Error closing poll:", err);
      const errorMessage = handleError(err);

      if (typeof errorMessage === 'string' &&
          (errorMessage.includes("auth") || errorMessage.includes("session"))) {
        handleSessionTimeout(errorMessage);
      } else {
        toast.error(`Failed to close poll: ${errorMessage}`);
      }
    }
  };

  // Handle retrying after auth refresh
  const refreshAuthAndRetry = async () => {
    try {
      console.log('Attempting to refresh session...');
      const refreshed = await refreshSession();

      if (refreshed) {
        console.log('Session refreshed successfully, retrying...');
        toast.success("Session refreshed successfully");
        // Trigger a refetch of the polls data
        refetch();
      } else {
        console.warn('Session refresh failed');
        toast.error("Failed to refresh session. Please try signing in again.");
        // Redirect to login after a delay
        setTimeout(() => {
          router.push('/login?redirect=' + encodeURIComponent(window.location.pathname));
        }, 2000);
      }
    } catch (error) {
      console.error('Error during session refresh:', error);
      toast.error("Error refreshing session. Please try reloading the page.");
    }
  };

  // Handle session timeout
  function handleSessionTimeout(errorMessage: string): void {
    toast.error(`Authentication error: ${errorMessage}. Please sign in again.`);
    // Redirect to sign-in page after a short delay
    setTimeout(() => {
      router.push('/signin');
    }, 2000);
  }

  // Filter polls based on search query and status filter
  const filteredPolls = useMemo(() => {
    return polls.filter(poll => {
      // Handle potential type mismatches safely
      const title = poll.title || '';

      // Check if poll has status property, otherwise assume it's active
      const status = 'status' in poll ? (poll as Record<string, unknown>).status as string : 'active';

      const matchesSearch = title.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter ? status === statusFilter : true;
      return matchesSearch && matchesStatus;
    });
  }, [polls, searchQuery, statusFilter]);

  // Animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07
      }
    }
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.4, ease: [0.0, 0.0, 0.2, 1] } }
  };

  if (!isOnline && !isLoading && polls.length === 0) {
    return (
        <div className="container mx-auto px-4 py-8 text-center">
            <h2 className="text-2xl font-semibold mb-4">You are offline</h2>
            <p className="mb-4">Polls cannot be loaded. Please check your internet connection.</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
    );
  }

  return (
    <ErrorBoundary>
      <motion.div
        className="space-y-8 pb-10"
        initial="hidden"
        animate="show"
        variants={containerVariants}
      >
      {/* Hero section with gradient background */}
      <motion.div
        className="relative flex flex-col gap-4 p-8 rounded-xl bg-gradient-to-r from-blue-500/5 via-blue-500/10 to-blue-500/5 border border-blue-500/10 shadow-sm overflow-hidden mb-8"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-blue-500/10 to-transparent blur-2xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              My Polls
              <span className="text-blue-500 ml-1">📋</span>
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage and monitor all your polls in one place
            </p>
          </div>
          <Button
            onClick={handleCreateNewPoll}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-500/20 transition-all hover:shadow-xl hover:shadow-blue-500/30"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <line x1="12" y1="5" x2="12" y2="19" />
              <line x1="5" y1="12" x2="19" y2="12" />
            </svg>
            Create New Poll
          </Button>
        </div>
      </motion.div>

      <div className="container mx-auto px-4">
        <SearchAndFilter
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
        />

        <LoadingAndError
          isLoading={isLoading}
          error={error}
          loadingTime={loadingTime}
          refreshAuthAndRetry={refreshAuthAndRetry}
        />

        {!isLoading && !error && polls.length === 0 && paginatedPolls && paginatedPolls.totalCount > 0 && (
           <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No polls match your current search or filter criteria.</p>
           </div>
        )}

        {!isLoading && !error && paginatedPolls && paginatedPolls.totalCount === 0 && (
          // Show EmptyState only if there are truly no polls at all (after initial load)
          <EmptyState createNewPoll={handleCreateNewPoll} />
        )}

        {!isLoading && !error && filteredPolls && filteredPolls.length > 0 && (
          <>
            <div className="space-y-4 md:space-y-6">
              {filteredPolls.map((poll) => (
                <PollCard
                  key={poll.id}
                  poll={poll}
                  onDelete={handleDeletePoll}
                  onDuplicate={handleDuplicatePoll}
                  onClose={handleClosePoll}
                />
              ))}
            </div>
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}


          </>
        )}
      </div>
      </motion.div>
    </ErrorBoundary>
  );
}
