import { useState, useMemo } from 'react';
import { PaginatedPolls } from '@/lib/validation/schemas';
import { debounce } from 'lodash-es';

// State management for the updated polls page
export default function PollsPageState() {
  // State for search and filtering
  const [searchQuery, setSearchQuery] = useState("");

  // Create debounced search setter to prevent too many re-renders
  const debouncedSetSearchQuery = useMemo(
    () => debounce((query: string) => setSearchQuery(query), 300),
    []
  );
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  // State for polls data and pagination
  const [paginatedPolls, setPaginatedPolls] = useState<PaginatedPolls>({
    polls: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 0,
    pageSize: 10
  });

  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingTime, setLoadingTime] = useState(0);
  const [isOnline, setIsOnline] = useState(true);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  return {
    // Search and filtering
    searchQuery,
    setSearchQuery,
    debouncedSetSearchQuery, // Add debounced version
    statusFilter,
    setStatusFilter,

    // Polls data
    paginatedPolls,
    setPaginatedPolls,
    polls: paginatedPolls.polls,

    // Loading and error states
    isLoading,
    setIsLoading,
    error,
    setError,
    loadingTime,
    setLoadingTime,
    isOnline,
    setIsOnline,

    // Pagination
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    totalPages: paginatedPolls.totalPages,
    totalCount: paginatedPolls.totalCount
  };
}
