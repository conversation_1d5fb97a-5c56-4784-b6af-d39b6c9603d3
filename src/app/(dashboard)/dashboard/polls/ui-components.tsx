// UI components for the updated polls page
import { Poll } from "@/lib/validation/schemas";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { motion } from "framer-motion";
import { MoreVertical, FileText, MessageCircle, Eye, Link as LinkIcon, Trash2, Plus, Copy, X, PieChart, Bookmark, RefreshCw, XCircle, FolderPlus } from "lucide-react";

// Poll card component
export const PollCard = ({ poll, onDelete, onDuplicate, onClose }: {
  poll: Poll,
  onDelete: (id: string) => void,
  onDuplicate: (id: string) => void,
  onClose: (id: string) => void
}) => {
  const pollLink = typeof window !== 'undefined' ? `${window.location.origin}/poll/${poll.id}` : `/poll/${poll.id}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="w-full"
    >
      <Card className="overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/20 hover:border-l-primary/60 bg-gradient-to-r from-background to-muted/20 relative">
        <CardContent className="p-4 sm:p-6 md:p-7">
          {/* Enhanced three-dots menu positioned at the top right */}
          <div className="absolute top-4 sm:top-6 right-4 sm:right-6 z-10">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-full hover:bg-muted/60 transition-colors duration-200 opacity-70 hover:opacity-100">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[180px] sm:w-[190px] shadow-lg">
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(pollLink)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <LinkIcon className="w-3.5 h-3.5" />
                  <span className="text-sm">Copy Link</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDuplicate(poll.id)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <Copy className="w-3.5 h-3.5" />
                  <span className="text-sm">Duplicate</span>
                </DropdownMenuItem>
                {poll.status === "active" && (
                  <DropdownMenuItem onClick={() => onClose(poll.id)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                    <X className="w-3.5 h-3.5" />
                    <span className="text-sm">Close Poll</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => onDelete(poll.id)} className="text-red-600 focus:text-red-700 flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <Trash2 className="w-3.5 h-3.5" />
                  <span className="text-sm">Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Main card content with improved mobile spacing */}
          <div className="pl-0"> {/* No need for right padding as three-dots are now on the right */}
            {/* Status and metadata section */}
            <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
              <div className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full shadow-sm ${
                poll.status === 'active' ? 'bg-green-500' :
                poll.status === 'completed' ? 'bg-gray-500' :
                'bg-yellow-500'
              }`}></div>
              <span className={`px-2 py-0.5 sm:px-3 sm:py-1 text-xs font-semibold rounded-full ${
                poll.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                poll.status === 'completed' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
              }`}>
                {poll.status ? poll.status.charAt(0).toUpperCase() + poll.status.slice(1) : 'Draft'}
              </span>
            </div>

            {/* Title section with enhanced mobile layout */}
            <div className="mb-3 sm:mb-4">
              <div className="w-full">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold leading-tight mb-2 sm:mb-3">
                  <Link href={`/dashboard/polls/${poll.id}`} className="hover:text-primary transition-colors duration-200 block">
                    {poll.title}
                  </Link>
                </h3>
                <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed">
                  {poll.description || "No description provided."}
                </p>
              </div>
            </div>

            {/* Enhanced mobile-responsive stats section - center aligned */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center gap-3 sm:gap-6 mb-4 sm:mb-6 py-2.5 sm:py-3 px-3 sm:px-4 bg-muted/30 rounded-lg">
              <div className="flex items-center justify-center sm:justify-start gap-2 text-sm">
                <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-violet-100 dark:bg-violet-900/30 text-violet-600 dark:text-violet-400 flex items-center justify-center">
                  <FileText className="w-3 h-3" />
                </div>
                <div>
                  <span className="font-semibold text-foreground">{poll.questions?.length || poll.questions_count || 0}</span>
                  <span className="text-muted-foreground ml-1">questions</span>
                </div>
              </div>
              <div className="flex items-center justify-center sm:justify-start gap-3 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                <div className="flex items-center gap-1.5">
                  <MessageCircle className="w-3 h-3" />
                  <span className="font-medium">{poll.responses_count || 0}</span>
                  <span className="hidden sm:inline">responses</span>
                  <span className="sm:hidden">resp.</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <Eye className="w-3 h-3" />
                  <span className="font-medium">{poll.views_count || 0}</span>
                  <span>views</span>
                </div>
              </div>
            </div>

            {/* Enhanced responsive button section - stacked on mobile, row on larger screens */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 hover:border-primary/50 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/polls/${poll.id}`} className="flex items-center justify-center gap-2">
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/results/${poll.id}`} className="flex items-center justify-center gap-2">
                  <PieChart className="w-4 h-4" />
                  <span>Results</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 border-green-200 text-green-700 hover:bg-green-50 hover:border-green-400 hover:text-green-800 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/30 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/polls/${poll.id}/simulate`} className="flex items-center justify-center gap-2">
                  <Bookmark className="w-4 h-4" />
                  <span>Simulate</span>
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Search and filter component
export const SearchAndFilter = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter
}: {
  searchQuery: string,
  setSearchQuery: (query: string) => void,
  statusFilter: string | null,
  setStatusFilter: (status: string | null) => void
}) => {
  return (
    <div className="flex flex-col sm:flex-row items-center gap-2 mb-6">
      <div className="relative w-full sm:max-w-[300px]">
        <Input
          placeholder="Search polls..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full h-10 rounded-md border-gray-200"
        />
      </div>
      <div className="flex gap-1 ml-auto">
        <Button
          variant={statusFilter === null ? "default" : "outline"}
          onClick={() => setStatusFilter(null)}
          className="whitespace-nowrap"
        >
          All
        </Button>
        <Button
          variant={statusFilter === 'active' ? "default" : "outline"}
          onClick={() => setStatusFilter('active')}
          className="whitespace-nowrap"
        >
          Active
        </Button>
        <Button
          variant={statusFilter === 'draft' ? "default" : "outline"}
          onClick={() => setStatusFilter('draft')}
          className="whitespace-nowrap"
        >
          Draft
        </Button>
        <Button
          variant={statusFilter === 'completed' ? "default" : "outline"}
          onClick={() => setStatusFilter('completed')}
          className="whitespace-nowrap"
        >
          Completed
        </Button>
      </div>
    </div>
  );
};

// Loading and error states component
export const LoadingAndError = ({
  isLoading,
  error,
  loadingTime,
  refreshAuthAndRetry
}: {
  isLoading: boolean,
  error: string | null,
  loadingTime: number,
  refreshAuthAndRetry: () => void
}) => {
  // Enhanced error detection and flag setting
  if (error && typeof window !== 'undefined') {
    localStorage.setItem('pollgpt_fetch_error', 'true');

    // Check for various types of authentication and session errors
    const isAuthError = error.includes('auth') ||
                       error.includes('JWT') ||
                       error.includes('token') ||
                       error.includes('session') ||
                       error.includes('permission denied') ||
                       error.includes('RLS') ||
                       error.includes('not authenticated') ||
                       error.includes('expired');

    if (isAuthError) {
      localStorage.setItem('pollgpt_auth_error', 'true');
    }
  }
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <p className="text-center text-muted-foreground font-medium">
          Loading your polls...
        </p>
        {loadingTime > 3000 && (
          <p className="text-center text-muted-foreground text-sm mt-2">
            This is taking a moment to load ({Math.floor(loadingTime / 1000)}s)
          </p>
        )}
        {loadingTime > 5000 && (
          <div className="mt-4 text-center">
            <p className="text-sm text-muted-foreground mb-2">
              Taking longer than expected. Checking database connection...
            </p>
            <div className="w-48 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto overflow-hidden">
              <div className="h-full bg-primary rounded-full animate-pulse" style={{ width: `${Math.min(100, (loadingTime / 10000) * 100)}%` }}></div>
            </div>
          </div>
        )}
        {loadingTime > 8000 && (
          <div className="mt-4 text-center">
            <p className="text-sm text-amber-600 dark:text-amber-400 mb-3">
              Still loading... This might be a session or network issue.
            </p>
            <Button
              onClick={refreshAuthAndRetry}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              Refresh Session & Retry
            </Button>
          </div>
        )}
      </div>
    );
  }

  if (error) {
    // Determine error type for better messaging
    const isAuthError = error.includes('auth') ||
                       error.includes('JWT') ||
                       error.includes('token') ||
                       error.includes('session') ||
                       error.includes('permission denied') ||
                       error.includes('RLS') ||
                       error.includes('not authenticated') ||
                       error.includes('expired');

    const isNetworkError = error.includes('timeout') ||
                          error.includes('network') ||
                          error.includes('Failed to fetch') ||
                          error.includes('connection');

    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <XCircle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              {isAuthError ? 'Authentication Error' : isNetworkError ? 'Connection Error' : 'Error loading polls'}
            </h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
              {isAuthError && (
                <p className="mt-1 text-xs">
                  Your session may have expired. Try refreshing your session first.
                </p>
              )}
              {isNetworkError && (
                <p className="mt-1 text-xs">
                  Please check your internet connection and try again.
                </p>
              )}
            </div>
            <div className="mt-4">
              <div className="-mx-2 -my-1.5 flex flex-wrap gap-2">
                {isAuthError && (
                  <Button
                    onClick={refreshAuthAndRetry}
                    variant="outline"
                    size="sm"
                    className="gap-1"
                  >
                    <RefreshCw className="h-3 w-3" />
                    Refresh Session
                  </Button>
                )}
                <Button
                  onClick={() => {
                    // Clear error flags when manually retrying
                    if (typeof window !== 'undefined') {
                      localStorage.removeItem('pollgpt_fetch_error');
                      localStorage.removeItem('pollgpt_auth_error');
                    }
                    window.location.reload();
                  }}
                  variant="outline"
                  size="sm"
                  className="gap-1"
                >
                  <RefreshCw className="h-3 w-3" />
                  Reload Page
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Empty state component
export const EmptyState = ({ createNewPoll }: { createNewPoll: () => void }) => {
  return (
    <div className="text-center py-12">
      <FolderPlus className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No polls</h3>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Get started by creating a new poll.
      </p>
      <div className="mt-6">
        <Button onClick={createNewPoll}>
          <Plus className="w-5 h-5 -ml-1 mr-2" />
          New Poll
        </Button>
      </div>
    </div>
  );
};
