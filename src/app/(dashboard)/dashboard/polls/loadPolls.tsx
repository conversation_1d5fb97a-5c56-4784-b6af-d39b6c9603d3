// Load polls function for the updated polls page
import { Poll } from '@/lib/validation/schemas';
import { supabase } from '@/lib/supabase';

// Track initial load state to avoid unnecessary network checks on subsequent page changes
let initialLoadComplete = false;

// Main function to create a polls loading function
export const createLoadPollsFunction = (
  setPolls: (polls: Poll[]) => void,
  setTotalCount: (count: number) => void,
  setIsLoading: (isLoading: boolean) => void,
  setError: (error: string | null) => void,
  setCurrentPage: (page: number) => void,
  setTotalPages: (pages: number) => void
) => {
  // Keep track of pending requests to avoid duplicates
  const pendingRequests: Record<string, boolean> = {};
  let lastLoadTime = 0;

  // Return the actual function to load polls
  return async (
    currentPage: number = 1,
    pageSize: number = 10,
    fetchAll: boolean = false,
    forceRefresh: boolean = false
  ) => {
    // Generate a unique ID for this request
    const requestId = `${currentPage}-${pageSize}-${fetchAll}-${forceRefresh}-${Date.now()}`;

    // Check if we have a pending request with the same parameters
    if (pendingRequests[requestId]) {
      console.log(`[LoadPolls] Skipping duplicate request ${requestId}`);
      return;
    }

    // Check if we've loaded polls recently (debounce)
    const now = Date.now();
    if (now - lastLoadTime < 500 && !forceRefresh) {
      console.log(`[LoadPolls] Skipping request, loaded polls recently (${now - lastLoadTime}ms ago)`);
      return;
    }

    // Mark this request as pending
    pendingRequests[requestId] = true;

    try {
      setIsLoading(true);
      setError(null);

      // Only check network connectivity on first load or if forced
      // This avoids unnecessary network checks on subsequent page changes
      if (!initialLoadComplete || forceRefresh) {
        // Quick check using navigator.onLine (fast but not always reliable)
        if (typeof navigator !== 'undefined' && !navigator.onLine) {
          console.error('Device appears to be offline, cannot load polls');
          setError("You appear to be offline. Please check your internet connection and try again.");
          setIsLoading(false);
          pendingRequests[requestId] = false;
          return;
        }
      }

      console.log('[LoadPolls] Starting polls load operation...');

      // EMERGENCY FIX: Use direct Supabase queries instead of the problematic getPolls function
      // This bypasses all the complex caching and RPC calls that might be causing timeouts

      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session || !session.user) {
        throw new Error('No authenticated user session found');
      }

      const userId = session.user.id;
      console.log(`[LoadPolls] Got user ID: ${userId.substring(0, 8)}...`);

      // Fetch polls directly with a simple query
      const { data: pollsData, error: pollsError } = await supabase
        .from('polls')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range((currentPage - 1) * pageSize, currentPage * pageSize - 1);

      if (pollsError) {
        throw new Error(`Database error: ${pollsError.message}`);
      }

      // Get total count in a separate query
      const { count, error: countError } = await supabase
        .from('polls')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) {
        throw new Error(`Count error: ${countError.message}`);
      }

      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);

      console.log(`[LoadPolls] Found ${pollsData?.length || 0} polls (page ${currentPage}/${totalPages}, total: ${totalCount})`);

      // Transform the data to match the expected format
      const polls: Poll[] = (pollsData || []).map(poll => ({
        id: poll.id,
        title: poll.title || 'Untitled Poll',
        description: poll.description || '',
        questions: [],
        questions_count: 0, // We'll leave this empty for the list view
        responses_count: 0, // We'll leave this empty for the list view
        views_count: 0,     // We'll leave this empty for the list view
        createdAt: poll.created_at,
        updatedAt: poll.updated_at || poll.created_at,
        expiresAt: null,
        userId: poll.user_id,
        status: poll.status as 'draft'|'active'|'completed',
        is_public: poll.is_public !== undefined ? poll.is_public : true,
        access_code: poll.access_code,
      }));

      // Update the state with the fetched polls
      setPolls(polls);
      setTotalCount(totalCount);
      setCurrentPage(currentPage);
      setTotalPages(totalPages);

      // Mark this request as complete
      pendingRequests[requestId] = false;
      lastLoadTime = Date.now();
      initialLoadComplete = true;

      console.log('[LoadPolls] Successfully loaded polls');
    } catch (error) {
      console.error('[LoadPolls] Error loading polls:', error);

      // Handle different types of errors
      if (error instanceof Error) {
        if (error.message.includes('offline') || error.message.includes('network')) {
          setError('You appear to be offline. Please check your connection and try again.');
        } else if (error.message.includes('timed out')) {
          setError('Loading timed out. Your connection might be slow or the server may be experiencing issues.');
        } else if (error.message.includes('auth') || error.message.includes('session')) {
          setError('Authentication error. Please try refreshing the page or logging in again.');
        } else {
          setError(`Error loading polls: ${error.message}`);
        }
      } else {
        setError('An unknown error occurred while loading polls.');
      }

      // Mark this request as complete
      pendingRequests[requestId] = false;
    } finally {
      setIsLoading(false);
    }
  };
};
