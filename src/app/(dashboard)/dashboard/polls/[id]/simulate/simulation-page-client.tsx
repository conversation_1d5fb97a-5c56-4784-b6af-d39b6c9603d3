"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { createBrowserClient } from '@supabase/ssr';
import { Database } from "@/lib/database.types";
import SimulationDashboard from "@/components/simulation/simulation-dashboard";
import { useAuth } from "@/components/providers/auth-provider-optimized";
import { Skeleton } from "@/components/ui/skeleton";

interface SimulationPageClientProps {
  pollId: string;
}

type PollWithQuestions = Database["public"]["Tables"]["polls"]["Row"] & {
  questions: Database["public"]["Tables"]["questions"]["Row"][]
};

export default function SimulationPageClient({ pollId }: SimulationPageClientProps) {
  const { user, isLoading: isAuthLoading } = useAuth();
  const [poll, setPoll] = useState<PollWithQuestions | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const router = useRouter();
  const redirectedRef = useRef(false);

  // Handle authentication without AuthGuard - INFINITE LOOP FIX
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    // Only redirect when we're sure the user is not authenticated and we haven't redirected yet
    if (!isAuthLoading && !user && !redirectedRef.current) {
      // Add a small delay to prevent immediate redirects during auth state changes
      timeoutId = setTimeout(() => {
        if (!redirectedRef.current && !user) {
          redirectedRef.current = true;
          const returnPath = `/dashboard/polls/${pollId}/simulate`;
          const returnUrl = encodeURIComponent(returnPath);
          console.log("User not authenticated, redirecting to login");
          router.push(`/login?redirect=${returnUrl}`);
        }
      }, 100);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [user, isAuthLoading, pollId, router]);

  // Check for cached poll data in localStorage
  useEffect(() => {
    if (user) {
      // Try to get cached poll data first
      const cachedPollKey = `poll_${pollId}_${user.id}`;
      const cachedPoll = localStorage.getItem(cachedPollKey);

      if (cachedPoll) {
        try {
          const parsedPoll = JSON.parse(cachedPoll);
          const cacheTime = parsedPoll._cacheTime;
          const now = Date.now();

          // Use cached data if it's less than 5 minutes old
          if (cacheTime && now - cacheTime < 5 * 60 * 1000) {
            console.log("Using cached poll data");
            setPoll(parsedPoll);
            setIsLoadingData(false);
            return;
          }
        } catch (e) {
          console.error("Error parsing cached poll data:", e);
          // Continue to fetch fresh data if cache parsing fails
        }
      }

      // Fetch fresh poll data
      const fetchPoll = async () => {
        try {
          setIsLoadingData(true);
          const supabase = createBrowserClient<Database>(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
          );

          // First check if the poll exists and belongs to the user
          const { data: pollData, error: pollError } = await supabase
            .from("polls")
            .select(`*, questions(*)`)
            .eq("id", pollId)
            .eq("user_id", user.id);

          if (pollError) {
            throw pollError;
          }

          if (!pollData || pollData.length === 0) {
            throw new Error("Poll not found or you don't have permission to access it");
          }

          // Use the first poll (should be the only one)
          const data = pollData[0];

          // Add cache timestamp to the data
          const pollWithCacheTime = {
            ...data,
            _cacheTime: Date.now()
          };

          // Cache the poll data in localStorage
          localStorage.setItem(cachedPollKey, JSON.stringify(pollWithCacheTime));

          setPoll(pollWithCacheTime);
        } catch (err) {
          console.error("Error fetching poll:", err);
          setError(err as Error);
        } finally {
          setIsLoadingData(false);
        }
      };

      fetchPoll();
    }
  }, [pollId, user, router]);

  // Don't render anything if not authenticated or still loading authentication
  if (isAuthLoading || !user) {
    return (
      <div className="container max-w-7xl py-6 space-y-8">
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }

  // Render content when authenticated
  return (
    <div className="container max-w-7xl py-6 space-y-8">
      {/* Navigation button */}
      {!isLoadingData && poll && (
        <div className="mb-4">
          <button
            onClick={() => router.push(`/dashboard/polls/${pollId}`)}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
            Back to Poll
          </button>
        </div>
      )}

      {isLoadingData && (
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 font-medium">Error loading poll data</p>
            <p className="text-red-600">{error.message}</p>
            <button
              className="mt-2 text-sm text-red-600 hover:underline"
              onClick={() => router.push('/dashboard/polls')}
            >
              Return to polls
            </button>
          </div>
        )}

        {!isLoadingData && !error && !poll && (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-amber-700 font-medium">Poll not found</p>
            <p className="text-amber-600">The requested poll was not found or you don&apos;t have access to it.</p>
            <button
              className="mt-2 text-sm text-amber-600 hover:underline"
              onClick={() => router.push('/dashboard/polls')}
            >
              Return to polls
            </button>
          </div>
        )}

        {!isLoadingData && poll && (
          <SimulationDashboard poll={poll} />
        )}
    </div>
  );
}
