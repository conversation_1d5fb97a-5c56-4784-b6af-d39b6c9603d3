"use client";

import { useState, useEffect } from "react";
import { renderToStaticMarkup } from "react-dom/server";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { canEditPoll, isPollPublic } from "@/lib/services/polls";
import { Poll, PollQuestion, QuestionOption, QuestionType, validatePollSafe } from "@/lib/validation/schemas";

import { usePoll, useUpdatePoll, useDeletePoll } from "@/hooks/use-poll";
import { DragDropContext, Droppable, Draggable, OnDragEndResponder } from "@hello-pangea/dnd";
import {
  BarChartIcon,
  Copy,
  GripVertical,
  Plus,
  Save,
  Share2,
  Trash2,
  X,
  ExternalLink,
} from "lucide-react";
import { Loader } from "@/components/ui/loader";
import { toast } from "sonner";
import { PollSkeleton } from "@/components/ui/poll-skeleton";
import { QRCodeSVG } from 'qrcode.react';
// Removed SimulationRequest and SimulationResponse imports as they are no longer needed

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

const isQuestionOption = (o: unknown): o is QuestionOption => {
  if (typeof o !== 'object' || o === null) return false;
  const opt = o as { id?: unknown; text?: unknown };
  return typeof opt.id === 'string' && typeof opt.text === 'string';
}

// Main component for the poll editing page
export default function PollEditPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pollId = params.id as string;

  // Get current user for permission checking
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  useEffect(() => {
    import('@/lib/supabase').then(({ supabase }) => {
      supabase.auth.getSession().then(({ data: { session } }) => {
        setCurrentUserId(session?.user?.id || null);
      });
    });
  }, []);

  // React Query hooks for data operations
  const { data: fetchedPoll, isLoading, isPending, error, refetch } = usePoll(pollId);
  const { mutate: updatePollMutation, isPending: isSaving } = useUpdatePoll();
  const { mutate: deletePollMutation } = useDeletePoll();

  // Local state for the poll being edited and UI controls
  const [poll, setPoll] = useState<Poll | null | undefined>(undefined);
  const [showShareDialog, setShowShareDialog] = useState(false);

  // Check if current user can edit this poll
  const canEdit = poll ? canEditPoll(poll, currentUserId) : false;
  const isPublicPoll = poll ? isPollPublic(poll) : false;
  const isOwnPoll = poll ? poll.userId === currentUserId : false;

  // Sync local state with fetched data

  // Define keys of Poll that handlePollUpdate can modify
  type EditablePollKeys = 'title' | 'description' | 'status' | 'is_public' | 'access_code' | 'expiresAt';

  // Automatically refetch if poll data is not available initially
  useEffect(() => {
    // If we have a pollId but no data and no error, try refetching
    if (!isLoading && !fetchedPoll && !error) {
      const timer = setTimeout(() => {
        refetch();
      }, 500); // Small delay before refetching

      return () => clearTimeout(timer);
    }
  }, [pollId, fetchedPoll, isLoading, error, refetch]);

  // For development debugging - detect stale data issues
  useEffect(() => {
    if (!isLoading && fetchedPoll === null && pollId) {
      console.warn(`Poll with ID ${pollId} returned null - this may indicate a data issue`);
    }
  }, [fetchedPoll, isLoading, pollId]);

  // Use Zod validation instead of manual type guards

  useEffect(() => {
    if (fetchedPoll) {
      const validationResult = validatePollSafe(fetchedPoll);
      if (validationResult.success) {
        setPoll(validationResult.data);
        if (searchParams.get('newPoll') === 'true') {
          setShowShareDialog(true);
          const newUrl = window.location.pathname;
          window.history.replaceState({}, '', newUrl);
        }
      } else {
        console.error('Poll validation failed:', validationResult.error.errors);
        // Optionally show user-friendly error message
        console.warn(`Poll with ID ${pollId} failed validation - attempting flexible transformation`);

        // Try the flexible transformation approach as fallback
        try {
          import('@/lib/validation/schemas').then(({ transformFlexiblePoll }) => {
            const flexiblePoll = transformFlexiblePoll(fetchedPoll);
            setPoll(flexiblePoll);
          });
        } catch (transformError) {
          console.error('Flexible transformation also failed:', transformError);
        }
      }
    }
  }, [fetchedPoll, searchParams, pollId]);

  // Overload signatures for handlePollUpdate
  function handlePollUpdate(field: 'title', value: string): void;
  function handlePollUpdate(field: 'description', value: string | null | undefined): void;
  function handlePollUpdate(field: 'status', value: 'draft' | 'active' | 'completed'): void;
  function handlePollUpdate(field: 'is_public', value: boolean): void;
  function handlePollUpdate(field: 'access_code', value: string | undefined): void;
  function handlePollUpdate(field: 'expiresAt', value: string | null): void;
  // Implementation signature
  function handlePollUpdate(field: EditablePollKeys, value: unknown): void {
    if (!poll) return;

    let updatedPollState: Poll | null = null;

    switch (field) {
      case 'title':
        if (typeof value === 'string') {
          updatedPollState = { ...poll, title: value };
        } else {
          console.error(`Invalid type for title: expected string, got ${typeof value}`);
          return;
        }
        break;
      case 'description':
        if (typeof value === 'string' || value === null || value === undefined) {
          updatedPollState = { ...poll, description: value };
        } else {
          console.error(`Invalid type for description: expected string, null, or undefined, got ${typeof value}`);
          return;
        }
        break;
      case 'status':
        if (typeof value === 'string' && ['draft', 'active', 'completed'].includes(value)) {
          updatedPollState = { ...poll, status: value as Poll['status'] };
        } else {
          console.error(`Invalid type for status: expected 'draft'|'active'|'completed', got ${value}`);
          return;
        }
        break;
      case 'is_public':
        if (typeof value === 'boolean') {
          updatedPollState = { ...poll, is_public: value };
        } else {
          console.error(`Invalid type for is_public: expected boolean, got ${typeof value}`);
          return;
        }
        break;
      case 'access_code':
        if (typeof value === 'string' || typeof value === 'undefined') {
          updatedPollState = { ...poll, access_code: value };
        } else {
          console.error(`Invalid type for access_code: expected string or undefined, got ${typeof value}`);
          return;
        }
        break;
      case 'expiresAt':
        if (typeof value === 'string' || value === null) {
          updatedPollState = { ...poll, expiresAt: value as string | null };
        } else {
          console.error(`Invalid type for expiresAt: expected string or null, got ${typeof value}`);
          return;
        }
        break;
      default:
        const _exhaustiveCheck: never = field;
        console.error('Unhandled poll field in handlePollUpdate:', _exhaustiveCheck);
        return;
    }

    if (updatedPollState) {
      setPoll(updatedPollState);
    }
  }


  // Define keys of PollQuestion that handleQuestionUpdate can modify
  type EditableQuestionKeys = 'text' | 'type' | 'required' | 'options';

  // Overload signatures for handleQuestionUpdate
  function handleQuestionUpdate(qIndex: number, field: 'text', value: string): void;
  function handleQuestionUpdate(qIndex: number, field: 'type', value: QuestionType): void;
  function handleQuestionUpdate(qIndex: number, field: 'required', value: boolean): void;
  function handleQuestionUpdate(qIndex: number, field: 'options', value: QuestionOption[]): void;
  // Implementation signature for handleQuestionUpdate
  function handleQuestionUpdate(qIndex: number, field: EditableQuestionKeys, value: unknown): void {
    if (!poll?.questions) return;
    const updatedQuestions = [...poll.questions];
    const questionToUpdate = { ...updatedQuestions[qIndex] };

    let questionUpdated = false;

    switch (field) {
      case 'text':
        if (typeof value === 'string') {
          questionToUpdate.text = value;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question text: expected string, got ${typeof value}`);
          return;
        }
        break;
      case 'type':
        if (typeof value === 'string' && ['single', 'multiple', 'open'].includes(value)) {
          questionToUpdate.type = value as QuestionType;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question type: expected QuestionType, got ${value}`);
          return;
        }
        break;
      case 'required':
        if (typeof value === 'boolean') {
          questionToUpdate.required = value;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question required: expected boolean, got ${typeof value}`);
          return;
        }
        break;
      case 'options':
        if (Array.isArray(value) && value.every(isQuestionOption)) {
          questionToUpdate.options = value as QuestionOption[];
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question options: expected QuestionOption[], got ${value}`);
          return;
        }
        break;
      default:
        const _exhaustiveCheck: never = field;
        console.error('Unhandled question field in handleQuestionUpdate:', _exhaustiveCheck);
        return;
    }

    if (questionUpdated) {
      updatedQuestions[qIndex] = questionToUpdate;
      setPoll({ ...poll, questions: updatedQuestions });
    }
  }

  // Save all changes to the poll
  const handleSave = () => {
    if (!poll) return;

    // Create a clean copy of the poll data to avoid any circular references
    const pollToSave = {
      ...poll,
      questions: poll.questions.map(q => ({
        ...q,
        options: q.options ? [...q.options] : undefined
      }))
    };

    console.log('Saving poll:', pollToSave);

    try {
      updatePollMutation(
        { id: poll.id, updates: pollToSave },
        {
          onSuccess: () => {
            console.log('Save successful');
            toast.success("Changes saved successfully");
          },
          onError: (err: Error) => {
            console.error('Save error:', err);
            toast.error(`Failed to save: ${err.message}`);
          },
        }
      );
    } catch (error) {
      console.error('Exception during save:', error);
      toast.error(`Save operation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Reorder questions on drag-and-drop
  const handleOnDragEnd: OnDragEndResponder = (result) => {
    if (!result.destination || !poll?.questions) return;

    const items = Array.from(poll.questions);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedQuestions = items.map((q, index) => ({ ...q, order: index }));
    setPoll({ ...poll, questions: updatedQuestions });
  };

  // Add a new question
  const handleAddQuestion = () => {
    if (!poll) return;
    const newQuestion: PollQuestion = {
      id: `temp-${Date.now()}`,
      text: "New Question",
      type: "multiple",
      required: true,
      order: poll.questions?.length || 0,
      options: [
        { id: `opt1-${Date.now()}`, text: "Option 1", value: "Option 1" },
        { id: `opt2-${Date.now()}`, text: "Option 2", value: "Option 2" },
      ],
    };
    setPoll({ ...poll, questions: [...(poll.questions || []), newQuestion] });
  };

  // Update a specific question's property


  // Delete a question
  const handleDeleteQuestion = (qIndex: number) => {
    if (!poll?.questions) return;
    const updatedQuestions = poll.questions.filter((_, index) => index !== qIndex);
    setPoll({ ...poll, questions: updatedQuestions });
  };

  // Add a new option to a question
  const handleAddOption = (qIndex: number) => {
    if (!poll?.questions) return;
    const question = poll.questions[qIndex];
    const newOption: QuestionOption = {
      id: `temp-opt-${Date.now()}`,
      text: `Option ${ (question.options?.length || 0) + 1}`,
      value: `Option ${ (question.options?.length || 0) + 1}`,
    };
    const updatedOptions = [...(question.options || []), newOption];
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  // Update a question's option
  const handleOptionUpdate = (qIndex: number, optIndex: number, value: string) => {
    if (!poll?.questions?.[qIndex]?.options) return;
    const updatedOptions = [...poll.questions[qIndex].options!];
    updatedOptions[optIndex] = { ...updatedOptions[optIndex], text: value, value: value };
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  // Delete a question's option
  const handleDeleteOption = (qIndex: number, optIndex: number) => {
    if (!poll?.questions?.[qIndex]?.options) return;
    if (poll.questions[qIndex].options!.length <= 2) {
      toast.error("A question must have at least 2 options.");
      return;
    }
    const updatedOptions = poll.questions[qIndex].options!.filter((_, i) => i !== optIndex);
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  const openSimulationDialog = () => {
    // Redirect to the simulation page using client-side navigation
    router.push(`/dashboard/polls/${pollId}/simulate`);
  };

  // This function has been removed as we now redirect to the simulate page

  // Delete the entire poll
  const handleDeletePoll = () => {
    if (!poll) return;
    if (window.confirm("Are you sure you want to delete this poll? This cannot be undone.")) {
      deletePollMutation(poll.id, {
        onSuccess: () => {
          toast.success("Poll deleted successfully");
          router.push("/dashboard/polls");
        },
        onError: (err: unknown) => {
          const message = err instanceof Error ? err.message : String(err);
          toast.error(`Failed to delete poll: ${message}`);
        },
      });
    }
  };  // Enhanced error and loading state handling
  const isAuthError = error?.message?.includes('Not authenticated') ||
                     error?.message?.includes('session') ||
                     error?.message?.includes('auth');

  const isPermissionError = error?.message?.includes('permission denied') ||
                           error?.message?.includes('RLS') ||
                           error?.message?.includes('access denied');

  // Improved loading state - prevent race condition flash
  const isActuallyLoading = isLoading || isPending;
  const hasNoDataYet = !fetchedPoll && !error;

  // Show appropriate loading states
  if (isActuallyLoading || hasNoDataYet) {
    return <PollSkeleton />;
  }

  // Handle authentication-related errors for public polls
  if (error) {
    if (isAuthError && !fetchedPoll) {
      // For auth errors, show a more helpful message
      return (
        <div className="container mx-auto py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold text-destructive mb-4">Access Issue</h2>
              <p className="text-muted-foreground mb-4">
                {poll?.is_public
                  ? "This is a public poll, but there seems to be an authentication issue. Please try refreshing the page."
                  : "You need to be signed in to view this poll."
                }
              </p>
              <div className="space-x-4">
                <Button onClick={() => window.location.reload()}>
                  Refresh Page
                </Button>
                <Button variant="outline" onClick={() => router.push('/login')}>
                  Sign In
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (isPermissionError) {
      return (
        <div className="container mx-auto py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold text-destructive mb-4">Poll Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The poll you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
              </p>
              <Button onClick={() => window.history.back()}>
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }

    // Generic error fallback
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-destructive mb-4">Error Loading Poll</h2>
            <p className="text-muted-foreground mb-4">
              {error.message || 'An unexpected error occurred while loading this poll.'}
            </p>
            <div className="space-x-4">
              <Button onClick={() => refetch()}>
                Try Again
              </Button>
              <Button variant="outline" onClick={() => window.history.back()}>
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!poll) {
    return (
      <div className="container mx-auto py-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p>Poll not found.</p>
        </div>
      </div>
    );
  }

  // Determine if this should be rendered in edit mode or view mode
  const isEditMode = canEdit;
  const pageTitle = isEditMode ? `Edit Poll: ${poll.title}` : poll.title;
  const showEditControls = isEditMode;

  // Main poll editor/viewer UI
  return (
    <div className="container mx-auto py-6">
      {/* Public poll notice - only show for polls the user doesn't own */}
      {isPublicPoll && !isOwnPoll && (
        <div className="mb-6 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
          <p>📊 This is a public poll. You&apos;re viewing it in read-only mode.</p>
        </div>
      )}
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => router.push("/dashboard/polls")}
          className="flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Back to Polls
        </Button>
      </div>
      {/* Header */}
      <div className="flex flex-wrap justify-between items-start gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">{pageTitle}</h1>
          <p className="text-muted-foreground text-sm mt-1">
            {poll.status} • Last updated: {poll.updatedAt && new Date(poll.updatedAt).getTime() > 0 ? new Date(poll.updatedAt).toLocaleString() : 'Just now'}
            {isPublicPoll && !isOwnPoll && <span> • Public Poll</span>}
            {!isEditMode && <span> • Read Only</span>}
          </p>
        </div>
        <div className="flex gap-2">
          {showEditControls && (
            <Button onClick={handleSave} disabled={isSaving} className="bg-primary text-primary-foreground hover:bg-primary/90">
              {isSaving ? (
                <>
                  <Loader variant="minimal" size="xs" className="mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </>
              )}
            </Button>
          )}
          <Button variant="outline" onClick={() => setShowShareDialog(true)}><Share2 className="w-4 h-4 mr-2" /> Share</Button>
          {showEditControls && (
            <>
              <Button onClick={() => openSimulationDialog()} variant="outline" className="ml-2">
                <BarChartIcon className="mr-2 h-4 w-4" />
                Simulate
              </Button>
              <Button variant="destructive" onClick={handleDeletePoll}><Trash2 className="w-4 h-4 mr-2" /> Delete</Button>
            </>
          )}
        </div>
      </div>

      {/* Poll Details Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Poll Details</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={poll.title || ''}
              onChange={(e) => handlePollUpdate('title', e.target.value)}
              readOnly={!showEditControls}
              className={!showEditControls ? 'bg-muted' : ''}
            />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={poll.description || ''}
              onChange={(e) => handlePollUpdate('description', e.target.value)}
              readOnly={!showEditControls}
              className={!showEditControls ? 'bg-muted' : ''}
            />
          </div>
          {/* Poll Context Section */}
          {(poll.context || poll.source_url) && (
            <div>
              <Label htmlFor="context">Imported Context</Label>
              <div className="mt-1 border rounded-lg p-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 border-blue-200/50 space-y-3">
                {poll.context && (
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="text-sm font-medium text-blue-900">Context Summary</div>
                    </div>
                    <div className="text-sm leading-relaxed text-gray-700 pl-4 border-l-2 border-blue-200">
                      {poll.context}
                    </div>
                  </div>
                )}

                {poll.source_url && (
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <div className="text-sm font-medium text-purple-900">Source Document</div>
                    </div>
                    <div className="flex items-center gap-3 pl-4">
                      <a
                        href={poll.source_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200"
                      >
                        <span className="underline decoration-blue-300 group-hover:decoration-blue-500">
                          {poll.source_filename || 'View Original Document'}
                        </span>
                        <ExternalLink className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
                      </a>
                      {poll.source_type && (
                        <span className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full font-medium">
                          {poll.source_type.toUpperCase()}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={poll.status}
              onValueChange={(value) => handlePollUpdate('status', value as 'draft' | 'active' | 'completed')}
              disabled={!showEditControls}
            >
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_public"
              checked={poll.is_public}
              onCheckedChange={(checked) => handlePollUpdate("is_public", Boolean(checked))}
              disabled={!showEditControls}
            />
            <Label htmlFor="is_public">Make this poll public</Label>
          </div>
        </CardContent>
      </Card>

      {/* Questions Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Questions</CardTitle>
          <CardDescription>
            {showEditControls ? "Drag and drop to reorder questions." : "Questions in this poll."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DragDropContext onDragEnd={showEditControls ? handleOnDragEnd : () => {}}>
            <Droppable droppableId="questions" isDropDisabled={!showEditControls}>
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                  {poll.questions?.map((question, index) => (
                    <Draggable key={question.id} draggableId={question.id} index={index} isDragDisabled={!showEditControls}>
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.draggableProps} className="border rounded-md p-4 bg-background">
                          <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2 gap-2">
                            <div className="flex items-center gap-2">
                              {showEditControls && (
                                <div {...provided.dragHandleProps} className="cursor-move"><GripVertical className="w-5 h-5 text-gray-400" /></div>
                              )}
                              <div className="text-sm font-medium text-muted-foreground">Question {index + 1}</div>
                            </div>
                            {showEditControls && (
                              <div className="flex items-center gap-2 self-end md:self-auto">
                                <Button onClick={() => openSimulationDialog()} size="sm" variant="outline" className="border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-700">
                                  <BarChartIcon className="mr-2 h-4 w-4" />
                                  Simulate
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => handleDeleteQuestion(index)}><Trash2 className="w-4 h-4 text-red-500" /></Button>
                              </div>
                            )}
                          </div>
                          <div className="space-y-3 mt-3 pl-6">
                            <div>
                              <Label htmlFor={`q-text-${index}`}>Question Text</Label>
                              <Input
                                id={`q-text-${index}`}
                                value={question.text}
                                onChange={(e) => handleQuestionUpdate(index, 'text', e.target.value)}
                                readOnly={!showEditControls}
                                className={!showEditControls ? 'bg-muted' : ''}
                              />
                            </div>
                            <div>
                              <Label htmlFor={`q-type-${index}`}>Question Type</Label>
                              <Select
                                value={question.type}
                                onValueChange={(value) => handleQuestionUpdate(index, 'type', value as QuestionType)}
                                disabled={!showEditControls}
                              >
                                <SelectTrigger><SelectValue /></SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="single">Single Choice</SelectItem>
                                  <SelectItem value="multiple">Multiple Choice</SelectItem>
                                  <SelectItem value="single">Single Choice</SelectItem>
                                  <SelectItem value="open">Open-ended</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            {question.type !== 'open' && (
                              <div>
                                <Label>Options</Label>
                                <div className="space-y-2 mt-1">
                                  {question.options?.map((option, optIndex) => (
                                    <div key={option.id || optIndex} className="flex items-center gap-2">
                                      <Input
                                        value={option.text}
                                        onChange={(e) => handleOptionUpdate(index, optIndex, e.target.value)}
                                        readOnly={!showEditControls}
                                        className={!showEditControls ? 'bg-muted' : ''}
                                      />
                                      {showEditControls && (
                                        <Button variant="ghost" size="sm" onClick={() => handleDeleteOption(index, optIndex)} disabled={question.options!.length <= 2}><X className="w-4 h-4" /></Button>
                                      )}
                                    </div>
                                  ))}
                                  {showEditControls && (
                                    <Button variant="outline" size="sm" onClick={() => handleAddOption(index)} className="w-full mt-2"><Plus className="w-4 h-4 mr-2" /> Add Option</Button>
                                  )}
                                </div>
                              </div>
                            )}
                            <div className="flex items-center space-x-2 mt-2">
                              <Checkbox
                                id={`q-req-${index}`}
                                checked={question.required}
                                onCheckedChange={(checked) => handleQuestionUpdate(index, 'required', !!checked)}
                                disabled={!showEditControls}
                              />
                              <Label htmlFor={`q-req-${index}`}>Required</Label>
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
          {showEditControls && (
            <Button onClick={handleAddQuestion} className="w-full mt-4"><Plus className="w-4 h-4 mr-2" /> Add Question</Button>
          )}
        </CardContent>
      </Card>

      {/* Floating Save Button */}
      {showEditControls && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="min-w-[120px] shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
            size="lg"
          >
            {isSaving ? (
              <>
                <Loader variant="minimal" size="xs" className="mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      )}

      {/* Share Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share your poll</DialogTitle>
            <DialogDescription>
              Share your poll with others using these options.
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="link" className="mt-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="link">Link</TabsTrigger>
              <TabsTrigger value="qrcode">QR Code</TabsTrigger>
              <TabsTrigger value="embed">Embed</TabsTrigger>
            </TabsList>
            <TabsContent value="link" className="mt-4">
              <p className="text-sm text-gray-500 mb-2">Anyone with the link can view and respond.</p>
              <div className="flex items-center space-x-2">
                <Input value={`${window.location.origin}/p/${poll.slug || poll.id}`} readOnly />
                <Button onClick={() => { navigator.clipboard.writeText(`${window.location.origin}/p/${poll.slug || poll.id}`); toast.success("Link copied!"); }}><Copy className="w-4 h-4" /></Button>
              </div>
            </TabsContent>
            <TabsContent value="qrcode" className="mt-4 flex flex-col items-center justify-center">
                <div className="p-4 bg-white rounded-md">
                    <QRCodeSVG value={`${window.location.origin}/p/${poll.slug || poll.id}`} size={160} />
                </div>
                <a
                  href={`data:image/svg+xml;utf8,${encodeURIComponent(renderToStaticMarkup(<QRCodeSVG value={`${window.location.origin}/p/${poll.slug || poll.id}`} />))}`}
                  download="qrcode.svg"
                  className="text-sm mt-2 text-blue-500 hover:underline"
                >
                    Download QR Code
                </a>
            </TabsContent>
            <TabsContent value="embed" className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Embed this poll on your website.</p>
                <Textarea
                    readOnly
                    value={`<iframe src="${window.location.origin}/p/${poll.slug || poll.id}/embed" style="width: 100%; height: 600px; border: none;" title="Poll: ${poll.title}"></iframe>`}
                    className="h-24"
                />
                <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => { navigator.clipboard.writeText(`<iframe src="${window.location.origin}/p/${poll.slug || poll.id}/embed" style="width: 100%; height: 600px; border: none;" title="Poll: ${poll.title}"></iframe>`); toast.success("Embed code copied!"); }}>
                    <Copy className="w-4 h-4 mr-2" /> Copy Embed Code
                </Button>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Simulation Dialog removed - now redirects to simulate page */}


    </div>
  );
}
