"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { motion, type Variants } from "framer-motion";
import { getPolls } from "@/lib/services/polls";
import { Poll } from "@/lib/validation/schemas";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";

export default function ResultsPage() {
  const router = useRouter();
  const [selectedPoll, setSelectedPoll] = useState<string | null>(null);
  const [polls, setPolls] = useState<Poll[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingTime, setLoadingTime] = useState(0);

  // Update loading time counter
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isLoading) {
      const startTime = Date.now();
      timer = setInterval(() => {
        setLoadingTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLoading]);

  // Function to refresh auth session and retry loading polls
  const refreshAuthAndRetry = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setLoadingTime(0);

      // Explicitly refresh the session
      const { data, error } = await supabase.auth.refreshSession();

      if (error || !data.session) {
        console.error("Failed to refresh session:", error);
        toast.error("Failed to refresh your session. Please try signing in again.");

        // Redirect to login after short delay
        setTimeout(() => {
          router.push("/auth/login");
        }, 2000);
        return;
      }

      console.log("Authentication refreshed successfully");
      toast.success("Session refreshed successfully");

      // Now load the polls with fresh authentication
      loadPolls();
    } catch (err) {
      console.error("Error during auth refresh:", err);
      setError("An unexpected error occurred while refreshing authentication.");
      setIsLoading(false);
    }
  };

  // Define loadPolls function
  const loadPolls = async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);

      // Set up a timeout for the entire operation
      const loadingTimeout = setTimeout(() => {
        if (isLoading) {
          console.log("Loading polls operation timed out globally");
          setError("Loading timed out. Please check your connection and try again.");
          setIsLoading(false);
        }
      }, 15000); // 15 seconds global timeout

      // Check if Supabase auth session is still valid
      const { data } = await supabase.auth.getSession();

      // Session refresh logic
      if (!data.session || forceRefresh) {
        console.log("Session refresh needed, attempting to refresh...");
        try {
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

          if (refreshError || !refreshData.session) {
            console.error("Session refresh failed:", refreshError);
            clearTimeout(loadingTimeout);
            setError("Your session has expired. Please log in again.");
            setPolls([]);
            setIsLoading(false);
            return;
          }

          console.log("Session refreshed successfully");
        } catch (refreshErr) {
          console.error("Error during session refresh:", refreshErr);
          clearTimeout(loadingTimeout);
          setError("Failed to refresh your session. Please try logging in again.");
          setPolls([]);
          setIsLoading(false);
          return;
        }
      }

      // Get polls from Supabase with a timeout to prevent infinite loading
      const fetchedPolls = await Promise.race([
        getPolls(1, 50), // Get first 50 polls for results page
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("Loading polls timed out")), 12000)
        )
      ]);

      // Clear the global timeout since we got a response
      clearTimeout(loadingTimeout);

      // Update state with fetched polls - filter to get only polls with responses
      if (fetchedPolls && fetchedPolls.polls && Array.isArray(fetchedPolls.polls)) {
        // Only show polls that have at least one response
        const pollsWithResponses = fetchedPolls.polls.filter(poll => poll.responses_count > 0);
        setPolls(pollsWithResponses);
        console.log(`Loaded ${pollsWithResponses.length} polls with responses out of ${fetchedPolls.polls.length} total polls`);

      } else {
        console.warn("Received invalid polls data structure:", fetchedPolls);
        setPolls([]);
      }

      setIsLoading(false);
    } catch (error) {
      const err = error as Error;
      console.error("Error loading polls:", err);

      // Provide more specific error messages
      if (err.message?.includes("session") || err.message?.includes("auth")) {
        setError("Authentication issue detected. Please try refreshing your session.");
      } else if (err.message?.includes("timed out")) {
        setError("Loading timed out. The server might be taking longer than expected to respond.");
      } else if (err.message?.includes("permission denied") || err.message?.includes("RLS")) {
        setError("Permission denied. Please try refreshing your session.");
      } else {
        setError(`Failed to load polls: ${err.message || 'Unknown error'}`);
      }

      setPolls([]);
      setIsLoading(false);
    }
  };

  // Load polls on component mount
  useEffect(() => {
    loadPolls();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mock insights data - we'll keep these for now
  const mockInsights = [
    { id: "1", text: "78% of respondents rated their satisfaction as 'Very Satisfied' or 'Satisfied'" },
    { id: "2", text: "Feature B received the highest usage rating among all features" },
    { id: "3", text: "Sentiment analysis shows mostly positive feedback about the new user interface" },
    { id: "4", text: "Common improvement requests include better documentation and more customization options" },
  ];

  // Mock charts data would typically come from a charting library with real data
  const mockChartTypes = ["pie", "bar", "line", "radar"];

  // Animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07
      }
    }
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.4, ease: [0.0, 0.0, 0.2, 1] } }
  };

  return (
    <motion.div
      className="space-y-8 pb-10"
      initial="hidden"
      animate="show"
      variants={containerVariants}
    >
      {/* Hero section with gradient background */}
      <motion.div
        className="relative flex flex-col gap-4 p-8 rounded-xl bg-gradient-to-r from-blue-500/5 via-blue-500/10 to-blue-500/5 border border-blue-500/10 shadow-sm overflow-hidden mb-8"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-blue-500/10 to-transparent blur-2xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              Poll Results & Analysis
              <span className="text-blue-500 ml-1">📊</span>
            </h2>
            <p className="text-muted-foreground mt-1">
              View detailed analytics and insights from your polls
            </p>
          </div>
          <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-500/20 transition-all hover:shadow-xl hover:shadow-blue-500/30">
            <Link href="/dashboard/create/conversational" className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19" />
                <line x1="5" y1="12" x2="19" y2="12" />
              </svg>
              Create New Poll
            </Link>
          </Button>
        </div>
      </motion.div>

      <motion.div
        className="flex flex-col md:flex-row gap-6"
        variants={containerVariants}
      >
        {/* Polls Selection Panel */}
        <motion.div
          className="md:w-1/3 space-y-4"
          variants={itemVariants}
        >
          <Card className="overflow-hidden border hover:shadow-md transition-all duration-300 hover:shadow-blue-500/5">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10 dark:to-transparent">
              <CardTitle className="text-lg flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                Your Polls
              </CardTitle>
              <CardDescription>Select a poll to view results</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {isLoading ? (
                  <div className="py-6 text-center">
                    <div className="inline-block animate-spin h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading polls...</p>
                  </div>
                ) : error ? (
                  <div className="py-6 text-center">
                    <p className="text-sm text-red-500 mb-2">{error}</p>
                    <Button size="sm" variant="outline" onClick={refreshAuthAndRetry}>
                      Try again
                    </Button>
                  </div>
                ) : polls.length === 0 ? (
                  <div className="py-6 text-center">
                    <p className="text-sm text-muted-foreground">No polls with responses found.</p>
                  </div>
                ) : (
                  polls.map((poll) => (
                    <Button
                      key={poll.id}
                      variant={selectedPoll === poll.id ? "default" : "outline"}
                      className={`w-full justify-start h-auto py-3 text-left transition-all ${
                        selectedPoll === poll.id
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "hover:border-blue-200 hover:bg-blue-50/50 dark:hover:bg-blue-900/20"
                      }`}
                      onClick={() => {
                        setSelectedPoll(poll.id);
                        router.push(`/dashboard/polls/${poll.id}`);
                      }}
                    >
                      <div>
                        <div className="font-medium flex items-center">
                          {poll.title}
                          <span className={`ml-2 text-xs px-2 py-0.5 rounded-full ${
                            poll.status === "active"
                              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                              : poll.status === "draft"
                                ? "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
                                : "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                          }`}>
                            {poll.status === "active" ? "Active" : poll.status === "draft" ? "Draft" : "Completed"}
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 flex items-center gap-2">
                          <span className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                              <circle cx="9" cy="7" r="4"></circle>
                            </svg>
                            <p>{poll.responses_count} responses</p>
                          </span>
                          <span>•</span>
                          <span className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                              <line x1="16" y1="2" x2="16" y2="6"></line>
                              <line x1="8" y1="2" x2="8" y2="6"></line>
                              <line x1="3" y1="10" x2="21" y2="10"></line>
                            </svg>
                            {new Date(poll.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </Button>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="overflow-hidden border hover:shadow-md transition-all duration-300 p-4">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600 dark:text-blue-400">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-sm">Need help analyzing results?</h3>
                <p className="text-xs text-muted-foreground">Our AI can help identify patterns and insights for you.</p>
                <Button variant="link" className="text-xs p-0 h-auto mt-1 text-blue-600">Learn more</Button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Results Display */}
        <motion.div
          className="md:w-2/3 space-y-6"
          variants={containerVariants}
        >
          {isLoading ? (
            <motion.div className="w-full h-[400px] flex items-center justify-center" variants={itemVariants}>
              <div className="text-center">
                <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
                </div>
                <h3 className="text-xl font-semibold mb-2">Loading Results</h3>
                <p className="text-muted-foreground max-w-sm mx-auto">
                  {loadingTime < 3 ? "Fetching your poll data..." : "This may take a moment..."}
                </p>
              </div>
            </motion.div>
          ) : error ? (
            <motion.div className="w-full" variants={itemVariants}>
              <Card className="h-[400px] flex flex-col items-center justify-center border-red-100">
                <div className="text-center p-8">
                  <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-red-50 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-red-500">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Error Loading Results</h3>
                  <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                    {error}
                  </p>
                  <Button onClick={refreshAuthAndRetry} className="mb-2">
                    Try Again
                  </Button>
                </div>
              </Card>
            </motion.div>
          ) : !selectedPoll ? (
            <motion.div variants={itemVariants}>
              <Card className="h-[400px] flex flex-col items-center justify-center bg-gradient-to-br from-background to-muted/20 border-dashed">
                <div className="text-center p-8">
                  <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600 dark:text-blue-400">
                      <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path>
                      <path d="M22 12A10 10 0 0 0 12 2v10z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Select a Poll</h3>
                  <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                    Choose a poll from the left panel to view its results and analysis
                  </p>
                  <Button asChild variant="outline" className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300">
                    <Link href="/dashboard/polls">View All Polls</Link>
                  </Button>
                </div>
              </Card>
            </motion.div>
          ) : (
            <>
              <motion.div variants={itemVariants}>
                <Card className="overflow-hidden border hover:shadow-md transition-all duration-300">              <CardHeader className="pb-3 border-b">
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center">
                    Results Overview
                    <span className="ml-2 text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-0.5 rounded-full font-normal">
                      {polls.find(p => p.id === selectedPoll)?.responses_count} responses
                    </span>
                  </CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Export
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                          <line x1="16" y1="13" x2="8" y2="13"></line>
                          <line x1="16" y1="17" x2="8" y2="17"></line>
                          <line x1="10" y1="9" x2="8" y2="9"></line>
                        </svg>
                        Export as PDF
                      </DropdownMenuItem>
                      <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="17 8 12 3 7 8"></polyline>
                          <line x1="12" y1="3" x2="12" y2="15"></line>
                        </svg>
                        Export as CSV
                      </DropdownMenuItem>
                      <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="18" cy="5" r="3"></circle>
                          <circle cx="6" cy="12" r="3"></circle>
                          <circle cx="18" cy="19" r="3"></circle>
                          <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                          <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                        </svg>
                        Share Results
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <CardDescription>
                  {polls.find(p => p.id === selectedPoll)?.title}
                </CardDescription>
              </CardHeader>
                  <CardContent className="pt-6">
                    <div className="h-[200px] flex items-center justify-center border-2 border-dashed rounded-md border-muted-foreground/20 bg-muted/10 relative overflow-hidden">
                      <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_60%,transparent_100%)] opacity-20"></div>
                      <div className="text-center">
                        <p className="text-muted-foreground mb-2">Response summary visualization</p>
                        <div className="flex justify-center gap-4 mt-4">
                          <div className="w-3 h-20 rounded-full bg-blue-500" style={{ height: '40px' }}></div>
                          <div className="w-3 h-20 rounded-full bg-green-500" style={{ height: '80px' }}></div>
                          <div className="w-3 h-20 rounded-full bg-purple-500" style={{ height: '60px' }}></div>
                          <div className="w-3 h-20 rounded-full bg-yellow-500" style={{ height: '30px' }}></div>
                          <div className="w-3 h-20 rounded-full bg-red-500" style={{ height: '50px' }}></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* AI Insights */}
                <motion.div variants={itemVariants}>
                  <Card className="overflow-hidden border hover:shadow-md transition-all duration-300 hover:border-blue-500/20 h-full">
                    <CardHeader className="pb-3 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10 dark:to-transparent">
                      <CardTitle className="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                          <path d="M12 2a8 8 0 0 0-8 8v12l3-3 2.5 2.5L12 19l2.5 2.5L17 19l3 3V10a8 8 0 0 0-8-8z"></path>
                        </svg>
                        AI Insights
                      </CardTitle>
                      <CardDescription>
                        Key findings detected by AI
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-4">
                        {mockInsights.map((insight) => (
                          <li key={insight.id} className="flex gap-3 group">
                            <div className="h-6 w-6 mt-0.5 rounded-full bg-blue-100 dark:bg-blue-900/30 flex-shrink-0 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-colors">
                              <div className="h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400"></div>
                            </div>
                            <p className="text-sm">{insight.text}</p>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Response Breakdown */}
                <motion.div variants={itemVariants}>
                  <Card className="overflow-hidden border hover:shadow-md transition-all duration-300 hover:border-blue-500/20 h-full">
                    <CardHeader className="pb-3 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10 dark:to-transparent">
                      <CardTitle className="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                          <path d="m3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z"></path>
                          <path d="m13 13 6 6"></path>
                        </svg>
                        Response Breakdown
                      </CardTitle>
                      <CardDescription>
                        Demographic information of respondents
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="group hover:bg-blue-50/50 dark:hover:bg-blue-900/10 rounded-lg p-3 -mx-3 transition-colors">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                                <path d="M2 12h20"></path>
                              </svg>
                              <span>Location</span>
                            </div>
                            <span className="font-medium text-blue-600 dark:text-blue-400">12 countries</span>
                          </div>
                          <div className="mt-2 h-1.5 w-full bg-muted rounded-full overflow-hidden">
                            <div className="bg-blue-500 h-full rounded-full" style={{width: '75%'}}></div>
                          </div>
                        </div>

                        <div className="group hover:bg-blue-50/50 dark:hover:bg-blue-900/10 rounded-lg p-3 -mx-3 transition-colors">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                                <line x1="12" y1="18" x2="12.01" y2="18"></line>
                              </svg>
                              <span>Devices</span>
                            </div>
                            <span className="font-medium text-blue-600 dark:text-blue-400">68% mobile</span>
                          </div>
                          <div className="mt-2 h-1.5 w-full bg-muted rounded-full overflow-hidden">
                            <div className="bg-blue-500 h-full rounded-full" style={{width: '68%'}}></div>
                          </div>
                        </div>

                        <div className="group hover:bg-blue-50/50 dark:hover:bg-blue-900/10 rounded-lg p-3 -mx-3 transition-colors">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                                <path d="m22 2-7 20-4-9-9-4Z"></path>
                                <circle cx="12" cy="12" r="10"></circle>
                              </svg>
                              <span>Completion Rate</span>
                            </div>
                            <span className="font-medium text-blue-600 dark:text-blue-400">92%</span>
                          </div>
                          <div className="mt-2 h-1.5 w-full bg-muted rounded-full overflow-hidden">
                            <div className="bg-blue-500 h-full rounded-full" style={{width: '92%'}}></div>
                          </div>
                        </div>

                        <div className="group hover:bg-blue-50/50 dark:hover:bg-blue-900/10 rounded-lg p-3 -mx-3 transition-colors">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                              </svg>
                              <span>Avg. Time</span>
                            </div>
                            <span className="font-medium text-blue-600 dark:text-blue-400">3m 24s</span>
                          </div>
                          <div className="mt-2 h-1.5 w-full bg-muted rounded-full overflow-hidden">
                            <div className="bg-blue-500 h-full rounded-full" style={{width: '45%'}}></div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              {/* Question-specific Results */}
              <motion.div variants={itemVariants}>
                <Card className="overflow-hidden border hover:shadow-md transition-all duration-300">
                  <CardHeader className="pb-3 border-b">
                    <CardTitle className="flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <path d="M12 17h.01"></path>
                      </svg>
                      Question Results
                    </CardTitle>
                    <CardDescription>
                      Detailed results for each question
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {[1, 2, 3].map((questionIndex) => (
                        <div key={questionIndex} className="pb-6 border-b last:border-0 last:pb-0 group">
                          <div className="flex gap-2 items-center mb-2">
                            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs font-medium">Q{questionIndex}</span>
                            <h4 className="font-medium">Question {questionIndex}</h4>
                          </div>
                          <p className="mb-4 text-sm text-muted-foreground">{
                            questionIndex === 1
                              ? "How satisfied are you with our product?"
                              : questionIndex === 2
                                ? "Which features do you use most frequently?"
                                : "How likely are you to recommend our product to others?"
                          }</p>
                          <div className="h-[180px] flex items-center justify-center border-2 border-dashed rounded-md border-muted-foreground/20 bg-muted/10 relative overflow-hidden group-hover:border-blue-500/20 transition-colors">
                            <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_60%,transparent_100%)] opacity-20"></div>
                            <div className="text-center">
                              <p className="text-muted-foreground mb-1">
                                {mockChartTypes[questionIndex % mockChartTypes.length]} chart for question {questionIndex}
                              </p>
                              <div className="flex justify-center gap-3 mt-3">
                                {questionIndex === 1 && (
                                  <>
                                    <div className="flex flex-col items-center">
                                      <div className="h-24 w-8 bg-blue-500 rounded-t-md"></div>
                                      <span className="text-xs mt-1">Very</span>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="h-24 w-8 bg-blue-400 rounded-t-md" style={{height: '60px'}}></div>
                                      <span className="text-xs mt-1">Somewhat</span>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="h-24 w-8 bg-blue-300 rounded-t-md" style={{height: '24px'}}></div>
                                      <span className="text-xs mt-1">Neutral</span>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="h-24 w-8 bg-blue-200 rounded-t-md" style={{height: '18px'}}></div>
                                      <span className="text-xs mt-1">Not very</span>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="h-24 w-8 bg-blue-100 rounded-t-md" style={{height: '12px'}}></div>
                                      <span className="text-xs mt-1">Not at all</span>
                                    </div>
                                  </>
                                )}
                                {questionIndex === 2 && (
                                  <div className="flex items-center justify-center gap-1 h-24 w-24 relative">
                                    <div className="absolute inset-0 rounded-full border-8 border-blue-500"></div>
                                    <div className="absolute inset-0 rounded-full border-8 border-t-green-500" style={{clipPath: 'polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%)'}}></div>
                                    <div className="absolute inset-0 rounded-full border-8 border-t-yellow-500 border-r-yellow-500" style={{clipPath: 'polygon(50% 50%, 100% 0%, 100% 100%)'}}></div>
                                    <div className="absolute inset-0 rounded-full border-8 border-l-violet-500 border-b-violet-500" style={{clipPath: 'polygon(50% 50%, 0% 100%, 50% 100%)'}}></div>
                                  </div>
                                )}
                                {questionIndex === 3 && (
                                  <div className="relative h-5 bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 rounded-md w-64">
                                    <div className="absolute top-full left-[65%] mt-1 w-0.5 h-3 bg-black"></div>
                                    <div className="absolute top-full left-[65%] mt-4 -translate-x-1/2 text-xs font-medium">6.5</div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </>
          )}
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
