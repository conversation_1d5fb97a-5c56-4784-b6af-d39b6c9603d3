"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
// Removed unused 'use' import
import { getPollById, getPollResponses } from "@/lib/services/polls";
import { Poll, PollQuestion, PollResponse } from "@/lib/validation/schemas";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area
} from "recharts";
import { toast } from "sonner";
import { generatePollInsights } from "@/lib/services/perplexity-ai";
import { EnhancedChart } from "@/components/charts/EnhancedChart";
import ChartAccessibility from "@/components/charts/ChartAccessibility";
import { ExportButton } from "@/components/charts/ExportButton";
import { ChartThemeSelector } from "@/components/charts/ChartThemeSelector";
import { analyzeQuestionData } from "@/lib/utils/data-analysis";

// Page props type is inline with the component

// Define chart data types
interface ChartDataPoint {
  name: string;
  value: number;
  fill?: string;
}

interface OpenQuestionResponse {
  id: number;
  response: string;
}

interface ProcessedQuestion extends Omit<PollQuestion, 'options'> {
  options?: { id: string; text: string; value: string }[];
  results: ChartDataPoint[] | OpenQuestionResponse[];
  npsData?: ChartDataPoint[];
  npsScore?: number;
}

interface ResponseByDate {
  date: string;
  count: number;
}

interface ProcessedPollData {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  completedResponses: number;
  partialResponses: number;
  totalViews: number;
  completionRate: string;
  averageTimeToComplete: string;
  questions: ProcessedQuestion[];
  responsesByDate: ResponseByDate[];
  demographics: {
    devices: ChartDataPoint[];
    regions: ChartDataPoint[];
  };
}



// Let Next.js infer the proper types
export default function PollResultsPage() {
  // Use the useParams and useRouter hooks
  const params = useParams();
  const router = useRouter();
  const pollId = params.id as string;

  const [isLoading, setIsLoading] = useState(true);
  const [insights, setInsights] = useState<string[]>([]);
  const [showFullResponse, setShowFullResponse] = useState<string | null>(null);
  const [pollData, setPollData] = useState<ProcessedPollData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Enhanced color palette for charts - matches the screenshot colors
  const COLORS = ['#4285F4', '#34A853', '#8A4BFF', '#FBBC05', '#EA4335', '#22d3ee', '#0ea5e9', '#4f46e5'];

  // Process raw response data into chart-friendly format
  const processResponseData = (poll: Poll, responses: PollResponse[]): ProcessedPollData => {
    if (!poll || !responses || !Array.isArray(responses) || responses.length === 0) {
      return {
        id: poll?.id || '',
        title: poll?.title || 'Unknown Poll',
        description: poll?.description || '',
        createdAt: poll?.createdAt || new Date().toISOString(),
        completedResponses: 0,
        partialResponses: 0,
        totalViews: poll?.views_count || 0,
        completionRate: "0%",
        averageTimeToComplete: "N/A",
        questions: poll?.questions?.map(q => ({
          ...q,
          results: q.type === 'open' ? [] : []
        })),
        responsesByDate: [],
        demographics: {
          devices: [],
          regions: []
        }
      };
    }

    // Demographic data
    const devices = responses.reduce<Record<string, number>>((acc, response) => {
      if (!response || !response.respondentInfo) return acc;
      const device = response.respondentInfo.deviceType || 'unknown';
      acc[device] = (acc[device] || 0) + 1;
      return acc;
    }, {});

    const regions = responses.reduce<Record<string, number>>((acc, response) => {
      if (!response || !response.respondentInfo) return acc;
      const region = response.respondentInfo.region || 'unknown';
      acc[region] = (acc[region] || 0) + 1;
      return acc;
    }, {});

    // Debug: Log the data structure to understand the mismatch
    console.log('Poll questions:', poll.questions.map(q => ({ id: q.id, text: q.text })));

    // Count responses with valid data
    const responsesWithData = responses.filter(r => r && r.responses && Object.keys(r.responses).length > 0);
    console.log(`Found ${responsesWithData.length} responses with data out of ${responses.length} total responses`);

    console.log('Response data sample:', responses.length > 0 ? {
      responseCount: responses.length,
      firstResponse: responses[0],
      responseKeys: responses[0] ? Object.keys(responses[0].responses || {}) : []
    } : 'No responses');

    // Process question results
    console.log('Processing responses for charts, total responses:', responses.length);
    const questionResults = poll.questions.map((question) => {
      if (question.type === 'open') {
        // For open questions, get raw text responses
        const results = responses
          .filter(r => r && r.responses && r.responses[question.id])
          .map((r, index) => ({
            id: index + 1,
            response: r.responses[question.id] as string
          }));

        return {
          ...question,
          results
        };
      } else if (question.type === 'multiple') {
        // For multiple choice, count occurrences of each option
        const counts: Record<string, number> = {};

        // Debug to find mismatches
        const allValues = new Set<string>();

        responses.forEach(r => {
          if (!r || !r.responses) return;
          const selected = r.responses[question.id] || [];
          if (Array.isArray(selected)) {
            selected.forEach(value => {
              counts[value] = (counts[value] || 0) + 1;
              allValues.add(value);
            });
          }
        });

        // Debug log (reduced for clarity)
        console.log(`Multiple Choice Question ${question.id}: ${question.text}`);
        console.log('Available response values:', Array.from(allValues));
        console.log('Option values:', question.options?.map(o => ({text: o.text, value: o.value})));
        console.log('Raw counts:', counts);

        // Count responses with data for this specific question
        const responsesWithDataForQuestion = responses.filter(r =>
          r && r.responses && r.responses[question.id] !== undefined &&
          (Array.isArray(r.responses[question.id]) ? r.responses[question.id].length > 0 : true)
        );
        console.log(`Responses with data for this question: ${responsesWithDataForQuestion.length}`);

        // Match counts with option text - more flexibly to handle potential mismatches
        const results = question.options?.map(option => {
          // Check if we have counts for this option's value directly
          let count = counts[option.value] || 0;

          // If no direct match found, try alternative matching approaches
          if (count === 0) {
            // Try case-insensitive matching (e.g., 'Yes' vs 'yes')
            const lowerCaseOptionValue = option.value.toLowerCase();
            const matchingResponseKey = Object.keys(counts).find(key =>
              key.toLowerCase() === lowerCaseOptionValue
            );

            if (matchingResponseKey) {
              count = counts[matchingResponseKey];
            }
          }

          return {
            name: option.text,
            value: count
          };
        }) || [];

        return {
          ...question,
          results
        };
      } else { // single choice
        // Count occurrences of each option
        const counts: Record<string, number> = {};

        // Debug to find mismatches
        const allValues = new Set<string>();

        responses.forEach(r => {
          if (!r || !r.responses) return;
          const value = r.responses[question.id] as string;
          if (value) {
            counts[value] = (counts[value] || 0) + 1;
            allValues.add(value);
          }
        });

        // Debug log (reduced for clarity)
        console.log(`Question ${question.id}: ${question.text}`);
        console.log('Available response values:', Array.from(allValues));
        console.log('Option values:', question.options?.map(o => ({text: o.text, value: o.value})));
        console.log('Raw counts:', counts);

        // Count responses with data for this specific question
        const responsesWithDataForQuestion = responses.filter(r =>
          r && r.responses && r.responses[question.id] !== undefined
        );
        console.log(`Responses with data for this question: ${responsesWithDataForQuestion.length}`);

        // Match counts with option text - more flexibly to handle potential mismatches
        const results = question.options?.map(option => {
          // Check if we have counts for this option's value directly
          let count = counts[option.value] || 0;

          // If no direct match found, try alternative matching approaches
          if (count === 0) {
            // Try case-insensitive matching (e.g., 'Yes' vs 'yes')
            const lowerCaseOptionValue = option.value.toLowerCase();
            const matchingResponseKey = Object.keys(counts).find(key =>
              key.toLowerCase() === lowerCaseOptionValue
            );

            if (matchingResponseKey) {
              count = counts[matchingResponseKey];
            }
          }

          return {
            name: option.text,
            value: count
          };
        }) || [];

        // Add NPS calculation for single choice questions that might be rating scale
        let npsData: Array<{ name: string; value: number; fill: string }> | undefined = undefined;
        let npsScore: number | undefined = undefined;

        if (question.type === 'single' && question.text.toLowerCase().includes('recommend')) {
          const detractorsValues = ['1', '2', '3', '4', '5', '6'];
          const passivesValues = ['7', '8'];
          const promotersValues = ['9', '10'];

          // Find options that match these values
          const detractors = results
            .filter(r => {
              const firstChar = r.name.charAt(0);
              return detractorsValues.includes(firstChar);
            })
            .reduce((sum, item) => sum + item.value, 0);

          const passives = results
            .filter(r => {
              const firstChar = r.name.charAt(0);
              return passivesValues.includes(firstChar);
            })
            .reduce((sum, item) => sum + item.value, 0);

          const promoters = results
            .filter(r => {
              const firstChar = r.name.charAt(0);
              return promotersValues.includes(firstChar);
            })
            .reduce((sum, item) => sum + item.value, 0);

          const total = detractors + passives + promoters;

          if (total > 0) {
            npsScore = Math.round((promoters - detractors) / total * 100);
            npsData = [
              { name: "Detractors (1-6)", value: detractors, fill: "#ef4444" },
              { name: "Passives (7-8)", value: passives, fill: "#eab308" },
              { name: "Promoters (9-10)", value: promoters, fill: "#22c55e" }
            ];
          }
        }

        return {
          ...question,
          results,
          ...(npsData && { npsData }),
          ...(npsScore !== undefined && { npsScore })
        };
      }
    });

    // Process responses by date - only count responses with actual data
    const validResponses = responses.filter(r => r && r.responses && Object.keys(r.responses).length > 0);
    const dateMap = validResponses.reduce<Record<string, number>>((acc, response) => {
      if (!response || !response.submittedAt) return acc;
      const date = new Date(response.submittedAt).toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {});

    const responsesByDate = Object.entries(dateMap).map(([date, count]) => ({
      date,
      count
    })).sort((a, b) => a.date.localeCompare(b.date));

    // Convert device and region data to chart format
    const devicesChart = Object.entries(devices).map(([name, value]) => ({ name, value }));
    const regionsChart = Object.entries(regions).map(([name, value]) => ({ name, value }));

    return {
      id: poll.id,
      title: poll.title,
      description: poll.description || '',
      createdAt: poll.createdAt,
      completedResponses: validResponses.length,
      partialResponses: responses.length - validResponses.length, // Count responses without data as partial
      totalViews: poll.views_count,
      completionRate: `${Math.round((validResponses.length / (poll.views_count || 1)) * 100)}%`,
      averageTimeToComplete: "3m 45s", // Mock data for now
      questions: questionResults,
      responsesByDate,
      demographics: {
        devices: devicesChart,
        regions: regionsChart
      }
    };
  };

  useEffect(() => {
    // Track if the component is mounted to prevent state updates after unmount
    let isMounted = true;

    const loadResults = async () => {
      try {
        setIsLoading(true);

        // Fetch poll data from local storage
        const poll = await getPollById(pollId);

        if (!poll) {
          setError("Poll not found");
          return;
        }

        // Fetch responses for this poll
        const responses = await getPollResponses(pollId);

        // Process the data into chart-friendly format
        const processedData = processResponseData(poll, responses || []);

        if (isMounted) {
          setPollData(processedData);

          // Generate AI insights using our mock service - only if we have valid responses
          const validResponses = (responses || []).filter(r => r && r.responses && Object.keys(r.responses).length > 0);
          if (validResponses.length > 0) {
            const aiInsights = await generatePollInsights(pollId, validResponses);
            if (isMounted) {
              setInsights(aiInsights);
            }
          }
        }
      } catch (err) {
        console.error('Error loading poll results:', err);
        if (isMounted) {
          toast.error('Failed to load poll results');
          setError('Failed to load poll results');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadResults();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [pollId]);

  const Settings2 = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
      <path d="M20 7h-9" />
      <path d="M14 17H5" />
      <circle cx="17" cy="17" r="3" />
      <circle cx="7" cy="7" r="3" />
    </svg>
  );

  const RefreshCcw = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
      <path d="M3 2v6h6" />
      <path d="M21 12A9 9 0 0 0 6 5.3L3 8" />
      <path d="M21 22v-6h-6" />
      <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7" />
    </svg>
  );

  const Sparkles = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
      <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
      <path d="M5 3v4" />
      <path d="M19 17v4" />
      <path d="M3 5h4" />
      <path d="M17 19h4" />
    </svg>
  );

  const Palette = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
      <circle cx="13.5" cy="6.5" r=".5" />
      <circle cx="17.5" cy="10.5" r=".5" />
      <circle cx="8.5" cy="7.5" r=".5" />
      <circle cx="6.5" cy="12.5" r=".5" />
      <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z" />
    </svg>
  );

  // Function to refresh data
  const refreshData = async () => {
    try {
      toast.info("Refreshing data...");

      // Re-fetch the poll and its responses
      const poll = await getPollById(pollId);
      const responses = await getPollResponses(pollId);

      if (!poll) {
        toast.error("Poll not found");
        return;
      }

      // Process the data into chart-friendly format
      const processedData = processResponseData(poll, responses || []);
      setPollData(processedData);

      // Generate AI insights using our mock service
      if (responses && responses.length > 0) {
        const aiInsights = await generatePollInsights(pollId, responses);
        setInsights(aiInsights);
      }

      toast.success("Data refreshed successfully!");
    } catch (err) {
      console.error('Error refreshing data:', err);
      toast.error('Failed to refresh data');
    }
  };

  // Generate new insights
  const generateNewInsights = async () => {
    try {
      toast.info("Generating new insights...");

      // Get the current poll responses
      const responses = await getPollResponses(pollId);

      // Generate fresh insights
      if (responses && responses.length > 0) {
        const aiInsights = await generatePollInsights(pollId, responses);
        setInsights(aiInsights);
        toast.success("New insights generated!");
      } else {
        toast.warning("Not enough data to generate insights.");
      }
    } catch (err) {
      console.error('Error generating insights:', err);
      toast.error('Failed to generate insights');
    }
  };

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-6">
        <div className="w-full max-w-md space-y-4 text-center">
          <h2 className="text-2xl font-bold tracking-tight">Error</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button
            onClick={() => router.push("/dashboard/polls")}
            className="mt-4"
          >
            Back to Polls
          </Button>
        </div>
      </div>
    );
  }

  // Show enhanced loading state
  if (isLoading || !pollData) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="space-y-6 text-center w-full max-w-md">
          <div className="relative mx-auto">
            <div className="w-16 h-16 rounded-full border-4 border-blue-100 dark:border-blue-900/30 p-2 mx-auto mb-2 relative overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <svg className="w-10 h-10 text-blue-500 animate-pulse" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <div className="animate-spin absolute inset-0 border-t-4 border-blue-500 rounded-full"></div>
            </div>

            <div className="absolute top-0 left-1/2 w-32 h-32 bg-blue-500/10 rounded-full blur-xl -translate-x-1/2 -translate-y-1/2"></div>
          </div>

          <div>
            <h3 className="text-xl font-semibold">Analyzing Results</h3>
            <p className="text-muted-foreground mt-2 max-w-xs mx-auto">
              We&apos;re processing your poll data and generating insights...
            </p>
          </div>

          <div className="flex justify-center gap-2">
            <span className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></span>
            <span className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></span>
            <span className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce"></span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 md:space-y-8">
      {/* Navigation button */}
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => router.push("/dashboard/polls")}
          className="flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Back to Polls
        </Button>
      </div>

      {/* Header Section - Stacked on mobile, side-by-side on desktop */}
      <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight break-words">{pollData.title}</h2>
          <p className="text-muted-foreground mt-1 text-sm md:text-base">
            {pollData.description}
          </p>
        </div>
        <div className="flex items-center gap-2 md:gap-3 flex-shrink-0">
          <ChartThemeSelector className="hidden lg:flex" />
          <ExportButton pollData={pollData} />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Settings2 className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={refreshData} className="flex items-center gap-2 cursor-pointer">
                <RefreshCcw className="mr-2 h-4 w-4" />
                Refresh Data
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => generateNewInsights()} className="flex items-center gap-2 cursor-pointer">
                <Sparkles className="mr-2 h-4 w-4" />
                Generate New Insights
              </DropdownMenuItem>
              <DropdownMenuItem className="md:hidden" onClick={() => {
                const event = new CustomEvent("chartthemechange");
                window.dispatchEvent(event);
              }}>
                <Palette className="mr-2 h-4 w-4" />
                Chart Theme
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-[#4285F4] shadow-sm hover:shadow transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Responses
            </CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#4285F4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#4285F4]">{pollData.completedResponses}</div>
            <p className="text-xs text-muted-foreground">
              From {pollData.totalViews} views
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-[#34A853] shadow-sm hover:shadow transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#34A853" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <path d="m22 2-7 20-4-9-9-4Z" />
              <path d="M22 2 11 13" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#34A853]">{pollData.completionRate}</div>
            <p className="text-xs text-muted-foreground">
              Of all poll views
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-[#FBBC05] shadow-sm hover:shadow transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Time to Complete
            </CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#FBBC05" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#FBBC05]">{pollData.averageTimeToComplete}</div>
            <p className="text-xs text-muted-foreground">
              Per response
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-[#8A4BFF] shadow-sm hover:shadow transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Created On
            </CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#8A4BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#8A4BFF]">
              {new Date(pollData.createdAt).toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {new Date(pollData.createdAt).toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights & Response Trend */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* AI Insights */}
        <Card className="h-full border-t-4 border-t-[#4285F4] shadow-sm hover:shadow transition-all">
          <CardHeader>
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4285F4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2a8 8 0 0 0-8 8c0 5.4 7 10.8 7.7 11.3a.5.5 0 0 0 .6 0C13 20.8 20 15.4 20 10a8 8 0 0 0-8-8Z" />
                <path d="m15 9-6 6" />
                <path d="m9 9 6 6" />
              </svg>
              <CardTitle>AI Insights</CardTitle>
            </div>
            <CardDescription>
              Key findings detected by AI
            </CardDescription>
          </CardHeader>
          <CardContent>
            {insights.length === 0 ? (
              <div className="h-48 flex items-center justify-center">
                <p className="text-muted-foreground">No insights available</p>
              </div>
            ) : (
              <ul className="space-y-3">
                {insights.map((insight, index) => (
                  <li key={index} className="flex gap-2">
                    <div className="min-w-4 h-4 mt-1 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                    <p>{insight}</p>
                  </li>
                ))}
              </ul>
            )}
          </CardContent>
        </Card>

        {/* Response Trend */}
        <Card className="h-full border-t-4 border-t-[#34A853] shadow-sm hover:shadow transition-all">
          <CardHeader>
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#34A853" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 3v18h18" />
                <path d="m19 9-5 5-4-4-3 3" />
              </svg>
              <CardTitle>Response Trend</CardTitle>
            </div>
            <CardDescription>
              Daily response count
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-72">
              <ResponsiveContainer width="100%" height="100%">
                {pollData.responsesByDate.length > 0 ? (
                  <LineChart
                    data={pollData.responsesByDate}
                    margin={{ top: 5, right: 30, left: 20, bottom: 25 }}
                  >
                    <defs>
                      <linearGradient id="colorResponses" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#34A853" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#34A853" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(158, 158, 158, 0.2)" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12, fill: "var(--foreground)" }}
                      tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      stroke="rgba(158, 158, 158, 0.2)"
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: "var(--foreground)" }}
                      stroke="rgba(158, 158, 158, 0.2)"
                      allowDecimals={false}
                    />
                    <Tooltip
                      formatter={(value) => [`${value} responses`, 'Count']}
                      labelFormatter={(label) => new Date(label).toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}
                      contentStyle={{
                        backgroundColor: 'var(--background)',
                        border: '1px solid var(--border)',
                        borderRadius: '6px',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
                      }}
                      cursor={{ stroke: '#34A853', strokeWidth: 1, strokeDasharray: '3 3' }}
                    />
                    <Area
                      type="monotone"
                      dataKey="count"
                      stroke="#34A853"
                      fillOpacity={1}
                      fill="url(#colorResponses)"
                      animationDuration={1500}
                    />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="#34A853"
                      strokeWidth={3}
                      dot={{ stroke: '#34A853', strokeWidth: 2, r: 4, fill: 'white' }}
                      activeDot={{ r: 8, stroke: "#34A853", strokeWidth: 1, fill: "#34A853" }}
                      animationDuration={1500}
                    />
                  </LineChart>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <p className="text-muted-foreground">No response data available</p>
                  </div>
                )}
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Question-specific Results */}
      <div className="space-y-6">
        <div className="flex flex-col gap-3 mb-2 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4285F4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
              <polyline points="14 2 14 8 20 8"/>
              <path d="M12 18v-6"/>
              <path d="M8 15h8"/>
            </svg>
            <h3 className="text-lg sm:text-xl font-semibold">Question Results</h3>
          </div>
          <ChartThemeSelector />
        </div>

        {pollData.questions.map((question, index) => (
          <Card
            key={question.id}
            className="overflow-hidden shadow-md hover:shadow-lg transition-all border-t-4"
            style={{
              borderTopColor: COLORS[index % COLORS.length]
            }}
          >
            <CardHeader>
              <CardTitle className="text-base sm:text-lg flex items-start gap-2 leading-relaxed">
                <span className="inline-flex items-center justify-center w-7 h-7 rounded-full text-white text-sm font-medium flex-shrink-0 mt-0.5" style={{ backgroundColor: COLORS[index % COLORS.length] }}>
                  {index + 1}
                </span>
                <span className="break-words">{question.text}</span>
              </CardTitle>
              <CardDescription className="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  {question.type === 'open' ? (
                    <path d="M14 3v4a1 1 0 0 0 1 1h4" />
                  ) : question.type === 'multiple' ? (
                    <>
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                      <polyline points="9 11 12 14 22 4" />
                    </>
                  ) : (
                    <circle cx="12" cy="12" r="10" />
                  )}
                </svg>
                {question.type === 'single'
                  ? 'Single choice question'
                  : question.type === 'multiple'
                    ? 'Multiple choice question'
                    : 'Open-ended question'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Use EnhancedChart component for single choice questions */}
              {question.type === 'single' && (
                <div className="space-y-6">
                  <EnhancedChart
                    question={question}
                    totalResponses={pollData.completedResponses}
                  />

                  {/* Add accessibility features */}
                  <ChartAccessibility
                    chartTitle={question.text}
                    chartType={question.type}
                    data={question.results as ChartDataPoint[]}
                    totalResponses={pollData.completedResponses}
                  />

                  {/* Display insights for this question */}
                  {analyzeQuestionData(question, pollData.completedResponses).length > 0 && (
                    <div className="bg-muted/50 p-4 rounded-lg border border-muted">
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2a8 8 0 0 0-8 8v12l3-3 2.5 2.5L12 19l2.5 2.5L17 19l3 3V10a8 8 0 0 0-8-8z"></path>
                        </svg>
                        Key Insights
                      </h4>
                      <ul className="space-y-2">
                        {analyzeQuestionData(question, pollData.completedResponses).map((insight, i) => (
                          <li key={i} className="text-sm flex gap-1.5">
                            <span className="text-primary">•</span>
                            <span>{insight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Use EnhancedChart component for multiple-choice questions */}
              {question.type === 'multiple' && (
                <div className="space-y-6">
                  <EnhancedChart
                    question={question}
                    totalResponses={pollData.completedResponses}
                  />

                  {/* Add accessibility features */}
                  <ChartAccessibility
                    chartTitle={question.text}
                    chartType={question.type}
                    data={question.results as ChartDataPoint[]}
                    totalResponses={pollData.completedResponses}
                  />

                  {/* Display insights for this question */}
                  {analyzeQuestionData(question, pollData.completedResponses).length > 0 && (
                    <div className="bg-muted/50 p-4 rounded-lg border border-muted">
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2a8 8 0 0 0-8 8v12l3-3 2.5 2.5L12 19l2.5 2.5L17 19l3 3V10a8 8 0 0 0-8-8z"></path>
                        </svg>
                        Key Insights
                      </h4>
                      <ul className="space-y-2">
                        {analyzeQuestionData(question, pollData.completedResponses).map((insight, i) => (
                          <li key={i} className="text-sm flex gap-1.5">
                            <span className="text-primary">•</span>
                            <span>{insight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Enhanced visualization for open-ended questions */}
              {question.type === 'open' && (
                <div className="space-y-6">
                  {/* Add word cloud visualization */}
                  {(question.results as OpenQuestionResponse[]).length > 0 && (
                    <EnhancedChart
                      question={question}
                      totalResponses={pollData.completedResponses}
                    />
                  )}

                  {/* Show individual responses */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                      Individual Responses
                    </h4>

                    {(question.results as OpenQuestionResponse[]).length > 0 ? (
                      (question.results as OpenQuestionResponse[]).map((item) => (
                        <div key={item.id} className="p-3 bg-muted rounded-lg">
                          <p className="text-sm">
                            {showFullResponse === `${question.id}-${item.id}`
                              ? item.response
                              : item.response.length > 100
                                ? `${item.response.substring(0, 100)}...`
                                : item.response}
                          </p>
                          {item.response.length > 100 && (
                            <Button
                              variant="link"
                              size="sm"
                              className="mt-1 p-0 h-auto"
                              onClick={() => setShowFullResponse(
                                showFullResponse === `${question.id}-${item.id}` ? null : `${question.id}-${item.id}`
                              )}
                            >
                              {showFullResponse === `${question.id}-${item.id}` ? "Show Less" : "Show More"}
                            </Button>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">No responses for this question yet</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Demographics */}
      <div className="space-y-4 mb-1">
        <div className="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FBBC05" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="sm:w-6 sm:h-6">
            <rect width="20" height="14" x="2" y="5" rx="2" />
            <line x1="2" x2="22" y1="10" y2="10" />
          </svg>
          <h3 className="text-lg sm:text-xl font-semibold">Demographics</h3>
        </div>
      </div>

      <div className="space-y-6">
        {/* Device Types - Enhanced with our smart chart component */}
        <Card className="border-t-4 border-t-[#4285F4] shadow-md hover:shadow-lg transition-all">
          <CardHeader>
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#4285F4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="14" height="20" x="5" y="2" rx="2" ry="2" />
                <path d="M12 18h.01" />
              </svg>
              <CardTitle>Device Breakdown</CardTitle>
            </div>
            <CardDescription>
              Types of devices used by respondents
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-6">
              {pollData.demographics.devices.length > 0 ? (
                <EnhancedChart
                  question={{
                    id: 'deviceBreakdown',
                    text: 'Device Breakdown',
                    type: 'single',
                    required: false,
                    order: 1,
                    results: pollData.demographics.devices,
                  }}
                  totalResponses={pollData.completedResponses}
                />
              ) : (
                <div className="flex items-center justify-center py-8">
                  <p className="text-muted-foreground">No device data available</p>
                </div>
              )}
            </div>

            {/* Accessibility features */}
            {pollData.demographics.devices.length > 0 && (
              <ChartAccessibility
                chartTitle="Device Breakdown"
                chartType="donut"
                data={pollData.demographics.devices}
                totalResponses={pollData.completedResponses}
              />
            )}
          </CardContent>
        </Card>

        {/* Regions - Enhanced with our smart chart component */}
        <Card className="border-t-4 border-t-[#EA4335] shadow-md hover:shadow-lg transition-all">
          <CardHeader>
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#EA4335" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10" />
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                <path d="M2 12h20" />
              </svg>
              <CardTitle>Regional Distribution</CardTitle>
            </div>
            <CardDescription>
              Geographic regions of respondents
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-6">
              {pollData.demographics.regions.length > 0 ? (
                <EnhancedChart
                  question={{
                    id: 'regionBreakdown',
                    text: 'Regional Distribution',
                    type: 'multiple',
                    required: false,
                    order: 2,
                    results: pollData.demographics.regions,
                  }}
                  totalResponses={pollData.completedResponses}
                />
              ) : (
                <div className="flex items-center justify-center py-8">
                  <p className="text-muted-foreground">No region data available</p>
                </div>
              )}
            </div>

            {/* Accessibility features */}
            {pollData.demographics.regions.length > 0 && (
              <ChartAccessibility
                chartTitle="Regional Distribution"
                chartType="horizontalBar"
                data={pollData.demographics.regions}
                totalResponses={pollData.completedResponses}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
