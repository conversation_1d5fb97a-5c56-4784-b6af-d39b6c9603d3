// redirect from /dashboard/create to /dashboard/create/conversational
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Middleware runs on both server and client side navigation
export function middleware(request: NextRequest) {
  // Only redirect if the path is exactly /dashboard/create (not subpaths)
  if (request.nextUrl.pathname === '/dashboard/create') {
    // Create URL for redirect
    return NextResponse.redirect(new URL('/dashboard/create/conversational', request.url));
  }
  return NextResponse.next();
}

// Matcher to run middleware only on specific paths
export const config = {
  matcher: '/dashboard/create',
};
