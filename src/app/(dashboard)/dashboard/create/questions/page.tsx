'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { PlusCircle, MinusCircle, Sparkles } from 'lucide-react';
import { Loader } from '@/components/ui/loader';
import { generatePollQuestions } from '@/lib/services/perplexity-ai';
import { toast } from 'sonner';

type QuestionType = 'single' | 'multiple' | 'text';

interface Question {
  id: string;
  text: string;
  type: QuestionType;
  options: { id: string; text: string }[];
  required: boolean;
}

interface PollData {
  title: string;
  description: string;
  extractedContent: string;
  source_attachment?: {
    source_type: 'url' | 'pdf' | 'website' | null;
    source_url: string | null;
    source_filename: string | null;
    show_source: boolean;
  } | null;
}

export default function QuestionsPage() {
  const router = useRouter();
  const [pollData, setPollData] = useState<PollData | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isGeneratingAI, setIsGeneratingAI] = useState<boolean>(false);
  const [aiError, setAiError] = useState<string | null>(null);

  // Generate questions using Perplexity AI - wrapped with useCallback to avoid infinite loop
  const generateAIQuestions = useCallback(async (data: PollData) => {
    setIsGeneratingAI(true);
    setAiError(null);

    try {
      // Prepare input for AI
      const aiInput = {
        title: data.title,
        topic: data.title,
        audience: 'general audience', // Could be enhanced with user input later
        additionalInfo: data.extractedContent || data.description
      };

      // Call the AI service
      const generatedPoll = await generatePollQuestions(aiInput);

      // Map AI-generated questions to our format
      const mappedQuestions = generatedPoll.questions.map(q => {
        // Convert AI question type to our app's question types
        let questionType: QuestionType = 'text';
        if (q.type === 'single') questionType = 'single';
        else if (q.type === 'multiple') questionType = 'multiple';

        // Create options with unique IDs
        const options = q.options ? q.options.map((opt, idx) => ({
          id: `opt-${Date.now()}-${idx}`,
          text: opt.text
        })) : [];

        // Ensure at least 2 options for choice questions
        if ((questionType === 'single' || questionType === 'multiple') && options.length < 2) {
          options.push(
            { id: `opt-${Date.now()}-default-1`, text: 'Option 1' },
            { id: `opt-${Date.now()}-default-2`, text: 'Option 2' }
          );
        }

        return {
          id: `q-${Date.now()}-${q.order || 0}`,
          text: q.text,
          type: questionType,
          options: options,
          required: q.required || true
        };
      });

      // Update questions state with AI-generated questions
      setQuestions(mappedQuestions);

    } catch (error) {
      console.error('Error generating AI questions:', error);
      setAiError('Failed to generate questions with AI. You can create them manually.');
      // Add a default empty question as fallback
      addQuestion();
    } finally {
      setIsGeneratingAI(false);
    }
  }, []); // Empty dependency array since it doesn't use any values from component scope

  // Handle adding a single AI-generated question
  const addAIQuestion = async () => {
    if (!pollData) return;

    setIsGeneratingAI(true);
    try {
      // Prepare input for AI with a modified prompt requesting just one question
      const aiInput = {
        title: pollData.title,
        topic: pollData.title,
        audience: 'general audience',
        additionalInfo: `Please generate ONE additional question for this poll about "${pollData.title}". ${pollData.extractedContent || pollData.description}`
      };

      // Call the AI service
      const generatedPoll = await generatePollQuestions(aiInput);

      // Get the first (or only) question from the response
      if (generatedPoll.questions.length > 0) {
        const aiQuestion = generatedPoll.questions[0];

        // Convert AI question type to our app's question types
        let questionType: QuestionType = 'text';
        if (aiQuestion.type === 'single') questionType = 'single';
        else if (aiQuestion.type === 'multiple') questionType = 'multiple';

        // Create options with unique IDs
        const options = aiQuestion.options ? aiQuestion.options.map((opt, idx) => ({
          id: `opt-${Date.now()}-${idx}`,
          text: opt.text
        })) : [];

        // Ensure at least 2 options for choice questions
        if ((questionType === 'single' || questionType === 'multiple') && options.length < 2) {
          options.push(
            { id: `opt-${Date.now()}-default-1`, text: 'Option 1' },
            { id: `opt-${Date.now()}-default-2`, text: 'Option 2' }
          );
        }

        // Create the new question object
        const newQuestion: Question = {
          id: `q-${Date.now()}-ai`,
          text: aiQuestion.text,
          type: questionType,
          options: options,
          required: aiQuestion.required || true
        };

        // Add it to the questions array
        setQuestions(prev => [...prev, newQuestion]);
      }
    } catch (error) {
      console.error('Error generating AI question:', error);
      setAiError('Failed to generate an AI question. Please try again or add a question manually.');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Initialize with poll data from localStorage and generate AI questions
  useEffect(() => {
    const storedPollData = localStorage.getItem('currentPollData');
    if (storedPollData) {
      const parsedData = JSON.parse(storedPollData);
      setPollData(parsedData);

      // Only generate AI questions if we have content to work with
      if (parsedData.title) {
        generateAIQuestions(parsedData);
      } else {
        // Add a default empty question if no title is available
        addQuestion();
      }
    } else {
      // If no poll data, add a default question instead of redirecting
      setPollData({
        title: 'New Poll',
        description: '',
        extractedContent: ''
      });
      addQuestion(); // Add default question
    }
  }, [router, generateAIQuestions]);

  // Add a new question
  const addQuestion = () => {
    const newQuestion: Question = {
      id: `q-${Date.now()}`,
      text: '',
      type: 'single',
      options: [
        { id: `opt-${Date.now()}-1`, text: '' },
        { id: `opt-${Date.now()}-2`, text: '' }
      ],
      required: true
    };
    setQuestions((prev) => [...prev, newQuestion]);
  };

  // Remove a question
  const removeQuestion = (id: string) => {
    setQuestions((prev) => prev.filter(q => q.id !== id));
  };

  // Update a question
  const updateQuestion = (id: string, data: Partial<Question>) => {
    setQuestions((prev) =>
      prev.map((q) => (q.id === id ? { ...q, ...data } : q))
    );
  };

  // Add an option to a question
  const addOption = (questionId: string) => {
    setQuestions((prev) =>
      prev.map((q) => {
        if (q.id === questionId) {
          return {
            ...q,
            options: [...q.options, { id: `opt-${Date.now()}`, text: '' }]
          };
        }
        return q;
      })
    );
  };

  // Remove an option from a question
  const removeOption = (questionId: string, optionId: string) => {
    setQuestions((prev) =>
      prev.map((q) => {
        if (q.id === questionId) {
          return {
            ...q,
            options: q.options.filter((opt) => opt.id !== optionId)
          };
        }
        return q;
      })
    );
  };

  // Update an option
  const updateOption = (questionId: string, optionId: string, text: string) => {
    setQuestions((prev) =>
      prev.map((q) => {
        if (q.id === questionId) {
          return {
            ...q,
            options: q.options.map((opt) =>
              opt.id === optionId ? { ...opt, text } : opt
            )
          };
        }
        return q;
      })
    );
  };

  // Handle manual regeneration of AI questions
  const handleRegenerateQuestions = () => {
    if (!pollData) return;
    generateAIQuestions(pollData);
  };

  // Handle form submission to create the poll
  const handleSubmit = async () => {
    // Create a unique ID for this toast so we can reference it later
    const loadingToastId = "creating-poll-toast";

    try {
      // Validate questions to ensure they have proper content
      if (questions.length === 0) {
        toast.error("You need to create at least one question");
        return;
      }

      if (!pollData) {
        toast.error("Poll data is missing. Please go back and fill in poll details.");
        return;
      }

      // Show loading toast with the ID
      toast.loading("Creating poll...", { id: loadingToastId });

      // Import the createPoll function to use the centralized poll service
      const { createPoll } = await import('@/lib/services/polls');

      // Define the valid question types that match the service's QuestionType
      type PollServiceQuestionType = 'single' | 'multiple' | 'open';

      // Format the data to match the Poll interface
      const pollToCreate = {
        title: pollData.title || '',
        description: pollData.description || '',
        questions: questions.map(q => {
          // Convert our QuestionType to the service's QuestionType
          let type: PollServiceQuestionType;
          if (q.type === 'text') {
            type = 'open';
          } else if (q.type === 'single' || q.type === 'multiple') {
            type = q.type;
          } else {
            type = 'single'; // Fallback
          }

          return {
            id: q.id,
            text: q.text,
            type: type,
            options: q.type !== 'text' ? q.options.map(opt => ({
              id: opt.id,
              text: opt.text || '',
              value: (opt.text || '').toLowerCase().replace(/\s+/g, '_')
            })) : undefined,
            required: q.required,
            order: 0 // We'll update this below
          };
        }).map((q, index) => ({ ...q, order: index + 1 })), // Add the order
        expiresAt: null,
        status: 'active' as const, // Explicitly type as literal 'active'
        userId: '', // Add the userId property - it will be populated by the service
        is_public: true, // Default to public polls
        // Add source attachment information if available
        source_url: pollData.source_attachment?.source_url || null,
        source_type: pollData.source_attachment?.source_type || null,
        source_filename: pollData.source_attachment?.source_filename || null,
        show_source: pollData.source_attachment?.show_source ?? true
      };

      console.log('Starting poll creation...', pollToCreate);

      // Use the polls service to create the poll - this will store it in Supabase
      const newPoll = await createPoll(pollToCreate);

      console.log('Poll created successfully:', newPoll);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToastId);
      toast.success("Poll created successfully!");

      // Clear any local storage data to prevent state issues
      localStorage.removeItem('currentPollData');

      // Add a small delay before navigation to ensure toast is visible and state is updated
      setTimeout(() => {
        // Navigate directly to the new poll page
        if (newPoll?.id) {
          console.log('Redirecting to poll page:', `/dashboard/polls/${newPoll.id}?newPoll=true`);
          router.push(`/dashboard/polls/${newPoll.id}?newPoll=true`);
        }
      }, 500);
    } catch (error) {
      // Ensure loading toast is dismissed even if there's an error
      toast.dismiss(loadingToastId);

      console.error('Error creating poll:', error);
      toast.error("Failed to create poll. Please try again.");
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-2">Create Poll Questions</h1>
      {pollData && (
        <p className="text-muted-foreground mb-6">
          for &quot;{pollData.title}&quot;
        </p>
      )}

      {isGeneratingAI && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <Loader
              variant="default"
              size="sm"
              text="Generating AI questions based on your inputs..."
              centered={true}
            />
          </CardContent>
        </Card>
      )}

      {aiError && (
        <Card className="mb-6 bg-red-50 dark:bg-red-900/20">
          <CardContent className="p-4">
            <p className="text-red-600 dark:text-red-400">{aiError}</p>
          </CardContent>
        </Card>
      )}

      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-sm text-muted-foreground">
            {questions.length > 0
              ? `${questions.length} question${questions.length === 1 ? '' : 's'} created`
              : 'No questions yet'}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRegenerateQuestions}
          disabled={isGeneratingAI || !pollData}
          className="flex items-center"
        >
          <Sparkles className="mr-2 h-4 w-4" />
          {isGeneratingAI ? 'Generating...' : 'Generate with AI'}
        </Button>
      </div>

      <div className="space-y-6">
        {questions.map((question, index) => (
          <Card key={question.id}>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Question {index + 1}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeQuestion(question.id)}
                >
                  <MinusCircle className="h-4 w-4 mr-1" /> Remove
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-1">Question Text</label>
                  <Input
                    value={question.text}
                    onChange={(e) => updateQuestion(question.id, { text: e.target.value })}
                    placeholder="Enter your question"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Question Type</label>
                  <RadioGroup
                    value={question.type}
                    onValueChange={(value) => updateQuestion(question.id, { type: value as QuestionType })}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="single" id={`radio-single-${question.id}`} />
                      <Label htmlFor={`radio-single-${question.id}`}>Single Choice</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="multiple" id={`radio-multiple-${question.id}`} />
                      <Label htmlFor={`radio-multiple-${question.id}`}>Multiple Choice</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id={`radio-text-${question.id}`} />
                      <Label htmlFor={`radio-text-${question.id}`}>Text Answer</Label>
                    </div>
                  </RadioGroup>
                </div>

                {(question.type === 'single' || question.type === 'multiple') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Options</label>
                    {question.options.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <Input
                          value={option.text}
                          onChange={(e) => updateOption(question.id, option.id, e.target.value)}
                          placeholder="Enter option text"
                          className="flex-grow"
                        />
                        {question.options.length > 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeOption(question.id, option.id)}
                          >
                            <MinusCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addOption(question.id)}
                    >
                      <PlusCircle className="h-4 w-4 mr-1" /> Add Option
                    </Button>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`required-${question.id}`}
                    checked={question.required}
                    onCheckedChange={(checked) =>
                      updateQuestion(question.id, { required: checked === true })
                    }
                  />
                  <label htmlFor={`required-${question.id}`} className="text-sm font-medium">
                    Required question
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        <div className="flex justify-center space-x-4">
          <Button
            variant="outline"
            onClick={addQuestion}
          >
            <PlusCircle className="h-4 w-4 mr-2" /> Add Question
          </Button>
          <Button
            variant="outline"
            onClick={addAIQuestion}
            disabled={isGeneratingAI || !pollData}
          >
            <Sparkles className="h-4 w-4 mr-2" /> Add Question with AI
          </Button>
        </div>

        <div className="flex justify-between mt-8">
          <Button variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button onClick={handleSubmit} disabled={questions.length === 0}>
            Create Poll
          </Button>
        </div>
      </div>
    </div>
  );
}