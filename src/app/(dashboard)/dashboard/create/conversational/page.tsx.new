// This is a temporary file where I'll create a clean version of the handleCreatePoll function with a timeout mechanism

const handleCreatePoll = async () => {
  if (!pollSuggestion) return;
  
  setIsCreatingPoll(true);
  
  // Set a timeout to prevent infinite loading state
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('Poll creation timed out. Please try again.'));
    }, 15000); // 15 second timeout
  });
  
  try {
    // Race between poll creation and timeout
    await Promise.race([
      (async () => {
        // Format questions for the database
        const formattedQuestions: PollQuestion[] = pollSuggestion.questions.map((q, index) => {
          // Map the question type from the AI suggestion to our system
          let questionType: QuestionType = 'single';
          
          // Convert the question type
          if (q.type === 'single') questionType = 'single';
          else if (q.type === 'multiple') questionType = 'multiple';
          else if (q.type === 'open' || q.type === 'text') questionType = 'open';
          
          // Create question options
          const options: QuestionOption[] = q.options ? q.options.map((opt, i) => ({
            id: `o-${Date.now()}-${index}-${i}`,
            text: opt,
            value: opt.toLowerCase().replace(/\s+/g, '_')
          })) : [];
          
          return {
            id: `q-${Date.now()}-${index}`,
            text: q.text,
            type: questionType,
            options,
            required: q.required,
            order: index + 1
          };
        });
        
        // Create the poll
        const createdPoll = await createPoll({
          title: pollSuggestion.title,
          description: pollSuggestion.description,
          questions: formattedQuestions,
          userId: '', // This will be filled in by the server
          status: 'draft',
          expiresAt: null,
          is_public: true
        });
        
        // Show success toast
        toast.success('Poll created successfully!');
        
        // Navigate to the poll after a short delay
        setTimeout(() => {
          router.push(`/dashboard/polls/${createdPoll.id}`);
        }, 1500);
        
        return createdPoll; // Return poll to signal completion
      })(),
      timeoutPromise
    ]);
  } catch (error) {
    console.error('Error creating poll:', error);
    
    // Enhanced error messaging based on error type
    let errorMessage = "I'm sorry, there was an error creating your poll. Please try again.";
    
    if (error instanceof Error) {
      // Check if it's a timeout error
      if (error.message.includes('timed out')) {
        errorMessage = "Poll creation timed out. The server might be busy. Please try again.";
      }
      // Check if it's a network error
      else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }
      // Check if it's a server error
      else if (error.message.includes('500') || error.message.includes('server')) {
        errorMessage = "Server error. Our team has been notified. Please try again later.";
      }
    }
    
    toast.error(errorMessage);
  } finally {
    setIsCreatingPoll(false);
  }
};
