'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { extractContentFromUrl } from '@/lib/services/content-extractor';
import { createPoll } from '@/lib/services/polls';
import { Poll } from '@/lib/validation/schemas';
import { PollQuestion, QuestionType, QuestionOption } from '@/lib/validation/schemas';
import { ConversationalPollInterface, ConversationalPollInterfaceRef, PollSuggestion as ChatPollSuggestion } from '@/components/conversation/ConversationalPollInterface';
import { ArrowLeft, Sparkles, Link as LinkIcon, Upload, Globe, CheckCircle2, FileText, Info, AlertCircle, RefreshCw } from 'lucide-react';
import { Loader } from '@/components/ui/loader';
import Link from 'next/link';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { motion, Variants } from "framer-motion";
import { withAuthRetry, ensureValidSession } from '@/lib/utils/session-manager';

// Note: The JSON code block styling has been moved to ConversationInterface.tsx
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const jsonCodeStyle = `
  .conversation pre code {
    white-space: pre-wrap !important;
    word-break: break-word !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
`;

interface PollSuggestion {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'open' | 'text';
    options?: string[];
    required: boolean;
  }>;
}

const generateContextSummary = async (content: string, sourceType: string): Promise<string> => {
  try {
    const response = await fetch('/api/ai/summarize-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content,
        sourceType,
        maxLength: 150
      })
    });

    if (!response.ok) {
      throw new Error('Summarization failed');
    }

    const { summary } = await response.json();
    return summary;
  } catch (error) {
    console.error('Failed to generate summary:', error);
    // Fallback: return first 25 words
    const words = content.split(' ').slice(0, 25);
    return words.join(' ') + (words.length < content.split(' ').length ? '...' : '');
  }
};

export default function ConversationalCreatePage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const conversationalInterfaceRef = useRef<ConversationalPollInterfaceRef>(null);

  // Load saved state from localStorage or use default initial values
  const getInitialPollSuggestion = (): PollSuggestion | null => {
    if (typeof window === 'undefined') return null;

    const savedPoll = localStorage.getItem('pollgpt_draft');
    return savedPoll ? JSON.parse(savedPoll) : null;
  };

  const getInitialStep = (): 'chat' | 'review' => {
    if (typeof window === 'undefined') return 'chat';
    return localStorage.getItem('pollgpt_step') === 'review' ? 'review' : 'chat';
  };

  // State for poll confirmation workflow
  const [pollSuggestion, setPollSuggestion] = useState<PollSuggestion | null>(getInitialPollSuggestion());
  const [activeStep, setActiveStep] = useState<'chat' | 'review'>(getInitialStep());
  const [isCreatingPoll, setIsCreatingPoll] = useState(false);
  const isCreatingPollRef = useRef(false);
  const [pollCreationCompleted, setPollCreationCompleted] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [showRetryPrompt, setShowRetryPrompt] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('chat');

  // Ref to track timeout IDs for proper cleanup
  const timeoutRefsRef = useRef<{
    pollCreationTimeout: NodeJS.Timeout | null;
    pollCreationSafetyTimeout: NodeJS.Timeout | null;
  }>({
    pollCreationTimeout: null,
    pollCreationSafetyTimeout: null
  });

  // Keep the ref in sync with the state
  useEffect(() => {
    isCreatingPollRef.current = isCreatingPoll;
  }, [isCreatingPoll]);

  // Send a message to the conversational interface
  const sendMessageToChat = async (content: string) => {
    if (conversationalInterfaceRef.current) {
      await conversationalInterfaceRef.current.appendMessage(content);
      setActiveTab('chat'); // Switch to chat tab to show the conversation
    } else {
      console.error('Conversational interface ref is not available');
      toast.error('Failed to send message to chat. Please try again.');
    }
  };

  // Handle resource extraction
  const handleExtractResource = async (type: 'url' | 'website' | 'file', resource: string | File) => {
    if (!resource) return;

    setIsExtracting(true);

    // Show a toast to indicate the extraction has started
    const toastId = toast.loading(`Extracting content from ${type === 'url' ? 'URL' : type === 'website' ? 'website' : 'file'}...`);

    try {
      let content = '';

      if (type === 'url' && typeof resource === 'string') {
        // Show more detailed progress
        toast.loading(`Analyzing ${resource}...`, { id: toastId });

        try {
          // Use the AI SDK extraction endpoint
          const extractionResponse = await fetch('/api/ai/extract-content', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              content: resource,
              extractionType: 'url',
              analysisType: 'basic'
            }),
          });

          if (!extractionResponse.ok) {
            const errorData = await extractionResponse.json();
            throw new Error(errorData.error || `Extraction failed with status: ${extractionResponse.status}`);
          }

          const data = await extractionResponse.json();
          content = data.extractedText || '';

          // If we have key themes, add them to the content
          if (data.keyThemes && data.keyThemes.length > 0) {
            content += '\n\nKey themes: ' + data.keyThemes.join(', ');
          }

          // If extraction failed with AI SDK, fall back to direct extraction
          if (!content || content.trim() === '') {
            console.log('AI SDK extraction returned empty content, falling back to direct extraction');
            content = await extractContentFromUrl(resource);
          }
        } catch (extractError) {
          console.error('Error using AI SDK extraction:', extractError);
          // Fall back to direct extraction if the AI SDK approach fails
          content = await extractContentFromUrl(resource);
        }
      } else if (type === 'file' && resource instanceof File) {
        // Show more detailed progress for file extraction
        toast.loading(`Processing file: ${resource.name}...`, { id: toastId });
        console.log(`Starting extraction for file: ${resource.name}, type: ${resource.type}, size: ${Math.round(resource.size / 1024)}KB`);

        try {
          // Use the AI SDK document processing endpoint
          const formData = new FormData();
          formData.append('file', resource);

          // Add a longer timeout for file extraction (especially PDFs which can be complex)
          const extractionTimeout = 180000; // 3 minutes for complex PDFs (increased from 2 minutes)
          const controller = new AbortController();

          // Add more robust timeout handling
          const timeoutId = setTimeout(() => {
            console.warn(`File extraction timeout after ${extractionTimeout/1000}s for ${resource.name}`);
            controller.abort('timeout');
          }, extractionTimeout);

          toast.loading(`Extracting content with AI...`, { id: toastId });

          // Try the new AI SDK endpoint first with improved error handling
          const extractionResponse = await fetch('/api/ai/process-document', {
            method: 'POST',
            body: formData,
            signal: controller.signal
          }).catch(async (error) => {
            clearTimeout(timeoutId); // Clear timeout on error

            // Handle AbortError specifically
            if (error.name === 'AbortError') {
              console.log('AI SDK document processing was aborted (likely timeout), falling back to extract-file');
              toast.loading(`Processing taking longer than expected, trying alternative method...`, { id: toastId });
            } else {
              console.log('AI SDK document processing failed, falling back to extract-file:', error);
            }

            // Create new controller for fallback with longer timeout for complex documents
            const fallbackController = new AbortController();
            const fallbackTimeout = 240000; // 4 minutes for fallback
            const fallbackTimeoutId = setTimeout(() => {
              console.warn(`Fallback extraction timeout after ${fallbackTimeout/1000}s for ${resource.name}`);
              fallbackController.abort('fallback-timeout');
            }, fallbackTimeout);

            try {
              const fallbackResponse = await fetch('/api/extract-file', {
                method: 'POST',
                body: formData,
                signal: fallbackController.signal
              });
              clearTimeout(fallbackTimeoutId);
              return fallbackResponse;
            } catch (fallbackError) {
              clearTimeout(fallbackTimeoutId);

              // If fallback also fails, provide more informative error
              if (fallbackError.name === 'AbortError') {
                throw new Error(`Document processing timed out. The file "${resource.name}" might be too complex or large. Please try a smaller file or enter your poll details manually.`);
              }
              throw fallbackError;
            }
          });

          clearTimeout(timeoutId); // Clear timeout on success

          if (!extractionResponse.ok) {
            const errorData = await extractionResponse.json();
            console.error('File extraction error response:', errorData);
            throw new Error(errorData.error || `File extraction failed: ${extractionResponse.status}`);
          }

          const data = await extractionResponse.json();
          content = data.content || data.extractedText || '';
          console.log(`Received extracted content (${content.length} chars)`);

          // Check if we have actual content - sometimes we might get empty or minimal responses
          if (!content || content.trim() === '' || content.length < 50) {
            console.warn('Extracted content was empty or too short');
            content = `Unable to extract meaningful content from ${resource.name}. Please try a different file format or enter your poll details manually.`;
          }
        } catch (fileError) {
          console.error('File extraction API error:', fileError);

          // Show a more detailed error to help with debugging
          const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error';
          toast.error(`File processing error: ${errorMessage}`, { id: toastId });

          // Provide a fallback content
          content = `File: ${resource.name} (Content extraction failed, please describe your poll manually)`;
        }
      }

      // If the content starts with an error message, handle it gracefully
      if (content.toLowerCase().includes('error') || content.toLowerCase().includes('failed to extract')) {
        toast.error(`We had trouble processing that content. Using what we could extract.`, { id: toastId });
      } else {
        toast.success(`Content extracted successfully!`, { id: toastId });
      }

      // Prepare message for the chat
      const extractMessage = `I want to create a poll based on this content: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`;

      // Use the same queueing approach as PDF extraction
      // Store the message in localStorage with a timestamp for the conversational interface to pick up
      try {
        const extractedContentData = {
          message: extractMessage,
          timestamp: Date.now(),
          processed: false,
          source: type === 'url' ? 'url' : 'file',
          resourceName: typeof resource === 'string' ? resource : resource.name,
          extractedContent: content // Store the full extracted content
        };
        localStorage.setItem('pollgpt_pending_message', JSON.stringify(extractedContentData));
        console.log(`Saved extracted ${type} content to localStorage for later processing`);
        toast.success('Content extracted and ready for chat. Switching to chat tab...', { id: toastId });
      } catch (storageError) {
        console.error('Failed to save extracted content to localStorage:', storageError);
        toast.error('Failed to save extracted content. Please try again or copy manually.', { id: toastId });
      }

      // Always switch to chat tab so the user can see the conversation
      setActiveTab('chat');
    } catch (error) {
      console.error('Extraction error:', error);
      toast.error(`Failed to extract content: ${error instanceof Error ? error.message : 'Unknown error'}. Try a different URL or copy the content manually.`, { id: toastId });

      // Still switch to chat tab so user can continue manually
      setActiveTab('chat');
    } finally {
      setIsExtracting(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      try {
        const file = e.target.files[0];
        // Check if it's a PDF file
        if (file.type === 'application/pdf') {
          handleExtractPDF(file);
        } else {
          handleExtractResource('file', file);
        }

        // Store the file information in localStorage for later use in poll creation
        localStorage.setItem('pollgpt_context_file', JSON.stringify({
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified
        }));
      } catch (error) {
        console.error('File extraction error:', error);
        toast.error('Failed to process the file. Please try again.');
      }
    }
  };

  // Handle PDF extraction with Mistral OCR
  const handleExtractPDF = async (file: File) => {
    setIsExtracting(true);
    const toastId = toast.loading(`Extracting content from PDF using Mistral OCR...`);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Call the Mistral OCR endpoint
      toast.loading('Processing PDF with Mistral OCR...', { id: toastId });
      const extractionResponse = await fetch('/api/ai/mistral-ocr', {
        method: 'POST',
        body: formData
      });

      if (!extractionResponse.ok) {
        const errorData = await extractionResponse.json();
        throw new Error(errorData.error || `Extraction failed with status: ${extractionResponse.status}`);
      }

      const data = await extractionResponse.json();
      const content = data.content || '';

      if (!content || content.trim() === '') {
        throw new Error('No content extracted from PDF');
      }

      // Also upload the file to Supabase storage
      let fileUrl = null;
      let filePath = null;
      let fileName = file.name;

      try {
        const fileUploadFormData = new FormData();
        fileUploadFormData.append('file', file);

        const fileUploadResponse = await fetch('/api/extract-file', {
          method: 'POST',
          body: fileUploadFormData
        });

        if (fileUploadResponse.ok) {
          const fileData = await fileUploadResponse.json();
          fileUrl = fileData.fileUrl;
          filePath = fileData.filePath;
          fileName = fileData.fileName || file.name;
          console.log('File uploaded to Supabase storage:', fileUrl);
        }
      } catch (uploadError) {
        console.error('Error uploading file to storage:', uploadError);
        // Continue with content extraction even if storage upload fails
      }

      toast.success(`PDF content extracted successfully!`, { id: toastId });

      // Prepare message for the chat
      const extractMessage = `I want to create a poll based on this content: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`;

      // Instead of trying to directly append to the conversational interface,
      // we'll store the message in localStorage with a timestamp
      // The conversational interface will check for this message on mount and initialization
      try {
        const extractedContentData = {
          message: extractMessage,
          timestamp: Date.now(),
          processed: false,
          source: 'pdf',
          filename: file.name,
          fileUrl: fileUrl,
          filePath: filePath,
          fileName: fileName,
          extractedContent: content // Store the actual extracted content
        };
        localStorage.setItem('pollgpt_pending_message', JSON.stringify(extractedContentData));
        console.log('Saved extracted content to localStorage for later processing');
        toast.success('Content extracted and ready for chat. Switching to chat tab...', { id: toastId });
      } catch (storageError) {
        console.error('Failed to save extracted content to localStorage:', storageError);
        toast.error('Failed to save extracted content. Please try again or copy manually.', { id: toastId });
      }

      // Always switch to chat tab so the user can see the conversation
      setActiveTab('chat');
    } catch (error) {
      console.error('PDF extraction error:', error);
      toast.error(`Failed to extract content from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: toastId });
    } finally {
      setIsExtracting(false);
    }
  };

  // We no longer need the ensureConversationalInterfaceReady function since we're using localStorage for queueing

  // Handle extraction from website
  const handleExtractFromWebsite = async () => {
    const url = prompt('Enter the website URL:');
    if (url) {
      try {
        handleExtractResource('url', url);
      } catch (error) {
        console.error('Website extraction error:', error);
        toast.error('Failed to extract content from the website. Please try again.');
      }
    }
  };

  // Handle extraction from file
  const handleExtractFromFile = () => {
    fileInputRef.current?.click();
  };

  // Create the poll in the database
  const handleCreatePoll = useCallback(async () => {
    console.log('🔍 [DEBUG] handleCreatePoll called, starting poll creation...');
    if (!pollSuggestion) return;

    // Don't do anything if we're already creating a poll
    if (isCreatingPollRef.current) return;

    console.log('🔍 [DEBUG] Poll creation proceeding with suggestion:', pollSuggestion.title);

    setIsCreatingPoll(true);
    setShowRetryPrompt(false);

    // Create a unique toast ID to track and update the toast
    const toastId = toast.loading('Creating your poll...');

    // Set up a timer to show retry prompt if operation takes too long
    const timeoutDelay = 15000; // 15 seconds
    const timeoutId = setTimeout(() => {
      // Only show retry prompt if we're still creating the poll
      if (isCreatingPollRef.current) {
        setShowRetryPrompt(true);
        toast.loading('Still creating your poll... This is taking longer than usual', { id: toastId });
      }
    }, timeoutDelay);

    // Update the shared timeout refs so they can be cleaned up if the component unmounts
    if (timeoutRefsRef.current) {
      timeoutRefsRef.current.pollCreationTimeout = timeoutId;
    }

    // Set up a safety timeout to prevent infinite loading state
    const safetyTimeoutId = setTimeout(() => {
      if (isCreatingPollRef.current) {
        console.warn('Poll creation safety timeout triggered - forcing loading state reset');
        setIsCreatingPoll(false);
        toast.error("Poll creation timed out. Please try again.", { id: toastId });
      }
    }, 45000); // 45 seconds absolute maximum

    // Update safety timeout ref
    if (timeoutRefsRef.current) {
      timeoutRefsRef.current.pollCreationSafetyTimeout = safetyTimeoutId;
    }

    // Make sure we clean up timeouts if the operation completes
    const cleanupTimeouts = () => {
      clearTimeout(timeoutId);
      clearTimeout(safetyTimeoutId);

      // Also clear the refs
      if (timeoutRefsRef.current) {
        timeoutRefsRef.current.pollCreationTimeout = null;
        timeoutRefsRef.current.pollCreationSafetyTimeout = null;
      }
    };

    try {
      // Ensure the session is valid before attempting to create a poll
      // Pass options to prevent automatic redirection and use custom error handling
      const sessionValid = await ensureValidSession({
        showToasts: false,
        redirectOnFailure: false,
        forceRefresh: true // Ensure we have a fresh session
      });

      if (!sessionValid) {
        console.warn('Session invalid before poll creation');
        throw new Error('Authentication session invalid. Please log in again.');
      }

      // Format questions for the database
      const formattedQuestions: PollQuestion[] = pollSuggestion.questions.map((q, index) => {
        // Map the question type from the AI suggestion to our system
        let questionType: QuestionType = 'single';

        // Convert the question type
        if (q.type === 'single') questionType = 'single';
        else if (q.type === 'multiple') questionType = 'multiple';
        else if (q.type === 'open' || q.type === 'text') questionType = 'open';

        // Create question options with timestamp to ensure uniqueness
        const timestamp = Date.now() + index;
        const options: QuestionOption[] = q.options ? q.options.map((opt, i) => ({
          id: `o-${timestamp}-${i}`,
          text: opt,
          value: opt.toLowerCase().replace(/\\s+/g, '_')
        })) : [];

        return {
          id: `q-${timestamp}-${index}`,
          text: q.text,
          type: questionType,
          options,
          required: q.required !== undefined ? q.required : true,
          order: index + 1
        };
      });

      console.log('Creating poll with formatted questions:', formattedQuestions);

      // Update toast to show we're connecting to the database
      toast.loading('Connecting to database...', { id: toastId });

      // Get context data from localStorage if available
      let contextData: {
        fileUrl?: string;
        filePath?: string;
        fileName?: string;
        source?: string;
        url?: string;
        website?: string;
        extractedContent?: string; // Add extracted content to the type
      } | null = null;

      try {
        console.log('🔍 [DEBUG] Starting context data extraction from localStorage...');
        const pendingMessage = localStorage.getItem('pollgpt_pending_message');
        console.log('🔍 [DEBUG] Raw pending message from localStorage:', pendingMessage);

        if (pendingMessage) {
          const parsedMessage = JSON.parse(pendingMessage);
          console.log('🔍 [DEBUG] Parsed pending message:', parsedMessage);

          // Handle all three types of context: file, website, url
          if (parsedMessage.fileUrl) {
            // File upload case
            contextData = {
              fileUrl: parsedMessage.fileUrl,
              filePath: parsedMessage.filePath,
              fileName: parsedMessage.fileName,
              extractedContent: parsedMessage.extractedContent || '', // Include extracted content
              source: 'pdf' // Explicitly set to 'pdf' to match enum
            };
            console.log('File context data extracted:', contextData);
          } else if (parsedMessage.source === 'website' && parsedMessage.resourceName) {
            // Website extraction case
            contextData = {
              website: parsedMessage.resourceName,
              extractedContent: parsedMessage.extractedContent || '', // Include extracted content
              source: 'website'
            };
            console.log('Website context data extracted:', contextData);
          } else if (parsedMessage.source === 'url' && parsedMessage.resourceName) {
            // URL extraction case
            console.log('🔍 [DEBUG] Processing URL context case...');
            contextData = {
              url: parsedMessage.resourceName,
              extractedContent: parsedMessage.extractedContent || '', // Include extracted content
              source: 'url'
            };
            console.log('🔍 [DEBUG] URL context data extracted:', contextData);
          } else {
            console.log('🔍 [DEBUG] No recognized context type in parsed message:', parsedMessage);
          }

          console.log('🔍 [DEBUG] Final context data from localStorage:', contextData);
        } else {
          console.log('🔍 [DEBUG] No pending message found in localStorage');
        }
      } catch (err) {
        console.error('🔍 [DEBUG] Error parsing context data from localStorage:', err);
      }

      // Create the poll with automatic auth retry on failure - increase retries to 3
      const createdPoll = await withAuthRetry(async () => {
        // Create poll data with properly typed fields to match schema requirements
        const pollDataToCreate: Partial<Poll> = {
          title: pollSuggestion.title,
          description: pollSuggestion.description,
          questions: formattedQuestions,
          userId: '', // This will be filled in by the server
          status: 'active', // Create polls as active so they're ready to use immediately
          expiresAt: null,
          is_public: true,
          show_source: true
        };

        // Add context data if available - with proper typing for the enum values
        console.log('🔍 [DEBUG] Setting context data on poll:', contextData);
        if (contextData) {
          if (contextData.source === 'pdf' && contextData.fileUrl) {
            console.log('Setting PDF context data:', {
              fileUrl: contextData.fileUrl,
              fileName: contextData.fileName,
              extractedContent: contextData.extractedContent?.substring(0, 50) + '...' || 'No content'
            });

            // Generate summary from extracted content
            const contentToSummarize = contextData.extractedContent || '';
            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'pdf')
              : contentToSummarize;

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.fileUrl;
            pollDataToCreate.source_type = 'pdf';
            pollDataToCreate.source_filename = contextData.fileName || null;
          } else if (contextData.source === 'website' && contextData.website) {
            console.log('Setting website context data:', contextData.website);

            // For website URLs, use the extracted content if available, otherwise use URL
            const contentToSummarize = contextData.extractedContent || contextData.website;
            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'website')
              : contentToSummarize;

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.website;
            pollDataToCreate.source_type = 'website';
          } else if (contextData.source === 'url' && contextData.url) {
            console.log('🔍 [DEBUG] Setting URL context data:', contextData.url);

            // For URLs, use the extracted content if available, otherwise use URL
            const contentToSummarize = contextData.extractedContent || contextData.url;
            console.log('🔍 [DEBUG] Content to summarize length:', contentToSummarize.length);

            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'url')
              : contentToSummarize;

            console.log('🔍 [DEBUG] Generated context summary:', contextSummary.substring(0, 50) + '...');

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.url;
            pollDataToCreate.source_type = 'url';

            console.log('🔍 [DEBUG] Set poll context fields:', {
              context: pollDataToCreate.context?.substring(0, 50) + '...',
              source_url: pollDataToCreate.source_url,
              source_type: pollDataToCreate.source_type
            });
          } else {
            console.log('Context data available but no matching type found');
          }
        } else {
          console.log('No context data available for poll');
        }

        console.log('Creating poll with data:', pollDataToCreate);

        // Log the poll data we're creating with more detail
        console.log('Creating poll with context data:', {
          context: pollDataToCreate.context,
          source_url: pollDataToCreate.source_url,
          source_type: pollDataToCreate.source_type,
          source_filename: pollDataToCreate.source_filename,
          context_type: typeof pollDataToCreate.context,
          source_url_type: typeof pollDataToCreate.source_url,
          source_type_type: typeof pollDataToCreate.source_type,
          source_filename_type: typeof pollDataToCreate.source_filename
        });

        // Ensure the data is properly structured for the database
        console.log('Final poll data being sent to createPoll:', JSON.stringify(pollDataToCreate, null, 2));

        return await createPoll(pollDataToCreate);
      }, 3); // Allow up to 3 retries for auth issues

      // Clear both timeouts since the operation completed successfully
      cleanupTimeouts();

      // Update the loading toast to success with action options
      toast.success('Poll created successfully! You are now viewing what respondents will see.', {
        id: toastId,
        duration: 10000, // Show for 10 seconds
        action: {
          label: 'Edit Poll',
          onClick: () => {
            if (createdPoll?.id) {
              router.push(`/dashboard/polls/${createdPoll.id}`);
            }
          }
        }
      });

      // Mark creation as completed first
      setPollCreationCompleted(true);
      setShowRetryPrompt(false);

      // Clear localStorage since the poll has been created successfully
      localStorage.removeItem('pollgpt_conversation');
      localStorage.removeItem('pollgpt_draft');
      localStorage.removeItem('pollgpt_step');
      localStorage.removeItem('pollgpt_retry_after_refresh');
      localStorage.removeItem('pollgpt_pending_message'); // Also clear the pending message with file data

      // Set creating state to false before navigation
      setIsCreatingPoll(false);

      // Force navigation with a hard redirect to ensure we get to the poll response page
      // Use router.push for client-side navigation
      if (createdPoll?.id) {
        console.log(`Redirecting to poll response page: /poll/${createdPoll.id}`);

        // Use router.push for faster client-side navigation
        router.push(`/poll/${createdPoll.id}?newPoll=true`);

        // As a fallback, try a harder redirect with a longer delay
        setTimeout(() => {
          if (document.location.pathname.includes('/create/conversational')) {
            console.log('Fallback navigation with forced redirect');
            router.push(`/poll/${createdPoll.id}?newPoll=true`);
          }
        }, 1000);
      }

      // Return early to avoid reaching the finally block too soon after successful navigation
      return;
    } catch (error) {
      console.error('Error creating poll:', error);

      // Clear the timeouts since the operation completed (with an error)
      cleanupTimeouts();

      // Get a more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('Poll creation error details:', { errorMessage, pollData: pollSuggestion });

      // Show appropriate error message based on error type
      if (errorMessage.toLowerCase().includes('authenticated') ||
          errorMessage.toLowerCase().includes('auth') ||
          errorMessage.toLowerCase().includes('session') ||
          errorMessage.toLowerCase().includes('login')) {

        toast.error("Authentication error. Please log in again and try creating your poll.", { id: toastId });

        // Store retry info but don't redirect - wait for the user to refresh
        localStorage.setItem('pollgpt_retry_after_refresh', 'true');

        // Set a timeout to reload the page automatically after a short delay
        setTimeout(() => {
          if (window.confirm('Your session has expired. The page will reload to refresh your session. Is that OK?')) {
            window.location.reload();
          }
        }, 2000);
      } else {
        toast.error(`Failed to create poll: ${errorMessage}. Please try refreshing the page and trying again.`, { id: toastId });
      }
    } finally {
      // Ensure loading state is cleared if not already handled by successful navigation path
      // This will run if an error occurs or if the return statement isn't hit.
      if (isCreatingPollRef.current) { // Check ref to see if it was set to true
         setIsCreatingPoll(false);
      }
      setShowRetryPrompt(false);
    }
  }, [pollSuggestion, router]);

  // Create the poll immediately after AI suggestion confirmation
  const createPollImmediately = useCallback(async (pollData: PollSuggestion) => {
    // Don't do anything if we're already creating a poll
    if (isCreatingPollRef.current) return;

    setIsCreatingPoll(true);
    setShowRetryPrompt(false);

    // Create a unique toast ID to track and update the toast
    const toastId = toast.loading('Creating your poll...');

    try {
      // Set up timeouts similar to handleCreatePoll
      const timeoutDelay = 15000; // 15 seconds
      const timeoutId = setTimeout(() => {
        if (isCreatingPollRef.current) {
          setShowRetryPrompt(true);
          toast.loading('Still creating your poll... This is taking longer than usual', { id: toastId });
        }
      }, timeoutDelay);

      const safetyTimeoutId = setTimeout(() => {
        if (isCreatingPollRef.current) {
          console.warn('Poll creation safety timeout triggered - forcing loading state reset');
          setIsCreatingPoll(false);
          toast.error("Poll creation timed out. Please try again.", { id: toastId });
        }
      }, 45000);

      const cleanupTimeouts = () => {
        clearTimeout(timeoutId);
        clearTimeout(safetyTimeoutId);
      };

      // Mark as currently creating
      isCreatingPollRef.current = true;

      // Format questions for the database
      const formattedQuestions: PollQuestion[] = pollData.questions.map((q, index) => {
        let questionType: QuestionType = 'single';

        if (q.type === 'single') questionType = 'single';
        else if (q.type === 'multiple') questionType = 'multiple';
        else if (q.type === 'open' || q.type === 'text') questionType = 'open';

        const timestamp = Date.now() + index;
        const options: QuestionOption[] = q.options ? q.options.map((opt, i) => ({
          id: `o-${timestamp}-${i}`,
          text: opt,
          value: opt.toLowerCase().replace(/\\s+/g, '_')
        })) : [];

        return {
          id: `q-${timestamp}-${index}`,
          text: q.text,
          type: questionType,
          options,
          required: q.required !== undefined ? q.required : true,
          order: index + 1
        };
      });

      // Update toast to show connection status
      toast.loading('Connecting to database...', { id: toastId });

      // Get context data from localStorage if available (same as handleCreatePoll)
      let contextData: {
        fileUrl?: string;
        filePath?: string;
        fileName?: string;
        source?: string;
        url?: string;
        website?: string;
        extractedContent?: string;
      } | null = null;

      try {
        console.log('🔍 [DEBUG] [createPollImmediately] Starting context data extraction from localStorage...');
        const pendingMessage = localStorage.getItem('pollgpt_pending_message');
        console.log('🔍 [DEBUG] [createPollImmediately] Raw pending message from localStorage:', pendingMessage);

        if (pendingMessage) {
          const parsedMessage = JSON.parse(pendingMessage);
          console.log('🔍 [DEBUG] [createPollImmediately] Parsed pending message:', parsedMessage);

          // Handle all three types of context: file, website, url
          if (parsedMessage.fileUrl) {
            // File upload case
            contextData = {
              fileUrl: parsedMessage.fileUrl,
              filePath: parsedMessage.filePath,
              fileName: parsedMessage.fileName,
              extractedContent: parsedMessage.extractedContent || '',
              source: 'pdf'
            };
            console.log('🔍 [DEBUG] [createPollImmediately] File context data extracted:', contextData);
          } else if (parsedMessage.source === 'website' && parsedMessage.resourceName) {
            // Website extraction case
            contextData = {
              website: parsedMessage.resourceName,
              extractedContent: parsedMessage.extractedContent || '',
              source: 'website'
            };
            console.log('🔍 [DEBUG] [createPollImmediately] Website context data extracted:', contextData);
          } else if (parsedMessage.source === 'url' && parsedMessage.resourceName) {
            // URL extraction case
            console.log('🔍 [DEBUG] [createPollImmediately] Processing URL context case...');
            contextData = {
              url: parsedMessage.resourceName,
              extractedContent: parsedMessage.extractedContent || '',
              source: 'url'
            };
            console.log('🔍 [DEBUG] [createPollImmediately] URL context data extracted:', contextData);
          } else {
            console.log('🔍 [DEBUG] [createPollImmediately] No recognized context type in parsed message:', parsedMessage);
          }

          console.log('🔍 [DEBUG] [createPollImmediately] Final context data from localStorage:', contextData);
        } else {
          console.log('🔍 [DEBUG] [createPollImmediately] No pending message found in localStorage');
        }
      } catch (err) {
        console.error('🔍 [DEBUG] [createPollImmediately] Error parsing context data from localStorage:', err);
      }

      // Create the poll with automatic auth retry
      const createdPoll = await withAuthRetry(async () => {
        // Create poll data with properly typed fields to match schema requirements
        const pollDataToCreate: Partial<Poll> = {
          title: pollData.title,
          description: pollData.description,
          questions: formattedQuestions,
          userId: '',
          status: 'active', // Create as active to skip draft stage
          expiresAt: null,
          is_public: true,
          show_source: true
        };

        // Add context data if available - with proper typing for the enum values
        console.log('🔍 [DEBUG] [createPollImmediately] Setting context data on poll:', contextData);
        if (contextData) {
          if (contextData.source === 'pdf' && contextData.fileUrl) {
            console.log('🔍 [DEBUG] [createPollImmediately] Setting PDF context data:', {
              fileUrl: contextData.fileUrl,
              fileName: contextData.fileName,
              extractedContent: contextData.extractedContent?.substring(0, 50) + '...' || 'No content'
            });

            // Generate summary from extracted content
            const contentToSummarize = contextData.extractedContent || '';
            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'pdf')
              : contentToSummarize;

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.fileUrl;
            pollDataToCreate.source_type = 'pdf';
            pollDataToCreate.source_filename = contextData.fileName || null;
          } else if (contextData.source === 'website' && contextData.website) {
            console.log('🔍 [DEBUG] [createPollImmediately] Setting website context data:', contextData.website);

            // For website URLs, use the extracted content if available, otherwise use URL
            const contentToSummarize = contextData.extractedContent || contextData.website;
            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'website')
              : contentToSummarize;

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.website;
            pollDataToCreate.source_type = 'website';
          } else if (contextData.source === 'url' && contextData.url) {
            console.log('🔍 [DEBUG] [createPollImmediately] Setting URL context data:', contextData.url);

            // For URLs, use the extracted content if available, otherwise use URL
            const contentToSummarize = contextData.extractedContent || contextData.url;
            console.log('🔍 [DEBUG] [createPollImmediately] Content to summarize length:', contentToSummarize.length);

            const contextSummary = contentToSummarize.length > 100
              ? await generateContextSummary(contentToSummarize, 'url')
              : contentToSummarize;

            console.log('🔍 [DEBUG] [createPollImmediately] Generated context summary:', contextSummary.substring(0, 50) + '...');

            pollDataToCreate.context = contextSummary;
            pollDataToCreate.source_url = contextData.url;
            pollDataToCreate.source_type = 'url';

            console.log('🔍 [DEBUG] [createPollImmediately] Set poll context fields:', {
              context: pollDataToCreate.context?.substring(0, 50) + '...',
              source_url: pollDataToCreate.source_url,
              source_type: pollDataToCreate.source_type
            });
          } else {
            console.log('🔍 [DEBUG] [createPollImmediately] Context data available but no matching type found');
          }
        } else {
          console.log('🔍 [DEBUG] [createPollImmediately] No context data available for poll');
        }

        console.log('🔍 [DEBUG] [createPollImmediately] Creating poll with data:', pollDataToCreate);
        console.log('🔍 [DEBUG] [createPollImmediately] Final poll data being sent to createPoll:', JSON.stringify(pollDataToCreate, null, 2));

        return await createPoll(pollDataToCreate);
      }, 3);

      cleanupTimeouts();

      // Mark creation as completed
      setPollCreationCompleted(true);
      setShowRetryPrompt(false);
      isCreatingPollRef.current = false;

      // Update toast to success with action options
      toast.success('Poll created successfully! You are now viewing what respondents will see.', {
        id: toastId,
        duration: 10000, // Show for 10 seconds
        action: {
          label: 'Edit Poll',
          onClick: () => {
            if (createdPoll?.id) {
              router.push(`/dashboard/polls/${createdPoll.id}`);
            }
          }
        }
      });

      // Clear localStorage
      localStorage.removeItem('pollgpt_conversation');
      localStorage.removeItem('pollgpt_draft');
      localStorage.removeItem('pollgpt_step');
      localStorage.removeItem('pollgpt_retry_after_refresh');

      setIsCreatingPoll(false);

      // Navigate directly to the poll response page (what respondents see)
      if (createdPoll?.id) {
        console.log(`Redirecting to poll response page: /poll/${createdPoll.id}`);
        router.push(`/poll/${createdPoll.id}?newPoll=true`);
      }

      return;
    } catch (error) {
      console.error('Error creating poll immediately:', error);
      isCreatingPollRef.current = false;
      setIsCreatingPoll(false);
      setShowRetryPrompt(false);

      let errorMessage = 'Failed to create poll. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('JWT') || error.message.includes('session')) {
          errorMessage = 'Session expired. Please refresh the page and try again.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        }
      }

      toast.error(errorMessage, { id: toastId });
    }
  }, [router, isCreatingPollRef, setIsCreatingPoll, setShowRetryPrompt, setPollCreationCompleted]);

  // Add this effect to handle cleanup of poll creation timeouts
  useEffect(() => {
    // Capture the current value of the ref when the effect runs
    const currentTimeoutRefs = timeoutRefsRef.current;

    // Return cleanup function that will be called when component unmounts
    return () => {
      // Clean up any timeouts using the captured value
      if (currentTimeoutRefs?.pollCreationTimeout) {
        clearTimeout(currentTimeoutRefs.pollCreationTimeout);
      }
      if (currentTimeoutRefs?.pollCreationSafetyTimeout) {
        clearTimeout(currentTimeoutRefs.pollCreationSafetyTimeout);
      }
    };
  }, []);

  // Save poll suggestion and active step to localStorage whenever they change
  useEffect(() => {
    if (pollSuggestion) {
      localStorage.setItem('pollgpt_draft', JSON.stringify(pollSuggestion));
    }
    if (activeStep) {
      localStorage.setItem('pollgpt_step', activeStep);
    }
  }, [pollSuggestion, activeStep]);

  // Handle poll confirmation from the new conversational interface
  const handlePollConfirmed = async (pollData: ChatPollSuggestion) => {
    try {
      // Convert ChatPollSuggestion to PollSuggestion format if needed
      const pollSuggestion: PollSuggestion = {
        title: pollData.title,
        description: pollData.description,
        questions: pollData.questions
      };

      // Set the poll suggestion for internal state
      setPollSuggestion(pollSuggestion);

      // Show immediate feedback
      toast.success('Creating your poll...');

      // Create the poll immediately with the poll data
      // We'll call a modified version that accepts poll data directly
      await createPollImmediately(pollSuggestion);

      // Log that we created the poll
      console.log('Created poll immediately after user confirmation:', pollData);
    } catch (error) {
      console.error('Error creating poll after confirmation:', error);
      toast.error('Failed to create poll. Please try again.');
    }
  };

  // Show a notification if we restored data from localStorage
  // Also handle retry logic after refresh
  useEffect(() => {
    const hadSavedData = localStorage.getItem('pollgpt_draft');
    const shouldRetryAfterRefresh = localStorage.getItem('pollgpt_retry_after_refresh') === 'true';

    // Only show this once when the component first loads and if we had data to restore
    if (hadSavedData && typeof window !== 'undefined') {
      // Use a flag in session storage to ensure this only shows once per session
      const hasShownRestoreMessage = sessionStorage.getItem('pollgpt_restore_message_shown');

      if (!hasShownRestoreMessage) {
        toast.success('Your previous work has been restored!', {
          description: 'Your conversation and draft poll data were recovered.',
          duration: 4000
        });
        sessionStorage.setItem('pollgpt_restore_message_shown', 'true');
      }

      // If we're supposed to retry creating the poll after refresh
      if (shouldRetryAfterRefresh && pollSuggestion && activeStep === 'review') {
        // Clear the retry flag
        localStorage.removeItem('pollgpt_retry_after_refresh');

        // First check if the session is valid before retrying
        const checkSessionAndRetry = async () => {
          try {
            // Show a notification that we're checking authentication
            const toastId = toast.loading('Validating your session before retrying poll creation...');

            // Validate the session without redirecting or showing toasts
            const isValid = await ensureValidSession({
              showToasts: false,
              redirectOnFailure: false
            });

            if (isValid) {
              // Session is valid, now retry poll creation
              toast.success('Session validated, retrying poll creation...', { id: toastId });

              // Wait a moment before retrying to ensure the page is fully loaded
              setTimeout(() => {
                // Make sure we're not already creating a poll
                if (!isCreatingPollRef.current) {
                  handleCreatePoll();
                }
              }, 1000);
            } else {
              // Session is invalid, show user a message with option to login
              toast.error('Your session has expired. Please log in again before creating your poll.', { id: toastId });

              // Ask user if they want to go to login page
              setTimeout(() => {
                if (window.confirm('Would you like to log in again to continue creating your poll?')) {
                  // Save current path to return to after login
                  sessionStorage.setItem('pollgpt_return_path', window.location.pathname);
                  router.push('/login');
                }
              }, 1000);
            }
          } catch (error) {
            console.error('Error checking session before poll creation retry:', error);
            toast.error('Failed to validate your session. Please try creating your poll again manually.');
            setIsCreatingPoll(false);
          }
        };

        // Start the session check and retry process
        checkSessionAndRetry();
      }
    }
  }, [pollSuggestion, activeStep, handleCreatePoll, isCreatingPoll, router]);



  // Reset conversation and start over
  const handleResetConversation = () => {
    // Update state
    setPollSuggestion(null);
    setActiveStep('chat');

    // Clear localStorage
    localStorage.removeItem('pollgpt_draft');
    localStorage.setItem('pollgpt_step', 'chat');

    // Reset the conversational interface
    if (conversationalInterfaceRef.current) {
      conversationalInterfaceRef.current.resetConversation();
    }
  };

  // Discard draft poll and return to chat
  const handleDiscardDraft = () => {
    // Show confirmation toast
    toast.success('Draft poll discarded');

    // Update state
    setPollSuggestion(null);
    setActiveStep('chat');

    // Update localStorage
    localStorage.removeItem('pollgpt_draft');
    localStorage.setItem('pollgpt_step', 'chat');
  };

  // Create a new poll from scratch
  const handleCreateFromScratch = () => {
    // Start over with a new poll
    handleResetConversation();
    // Send a message to start fresh
    sendMessageToChat("I want to create a new poll from scratch");
  };

  // Example poll suggestion to try
  const handleTrySuggestion = () => {
    // Send a predefined message to get started with a customer satisfaction survey
    handleResetConversation();
    sendMessageToChat("I want to create a customer satisfaction survey for my online store");
  };

  // Go back to chat from review
  const handleBackToChat = () => {
    // Send a message asking what changes the user would like to make
    sendMessageToChat("What changes would you like to make to the poll draft? Let me know, and I'll help you refine it.");
    setActiveStep('chat');
  };

  // Handle tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle refreshing the page and retrying poll creation
  const handleRefreshAndRetry = () => {
    // Store information that we want to retry after refresh
    localStorage.setItem('pollgpt_retry_after_refresh', 'true');

    // Reset loading states before refresh to ensure a clean state
    setIsCreatingPoll(false);
    setShowRetryPrompt(false);

    // Add a small delay to ensure state changes are applied before refresh
    setTimeout(() => {
      // Refresh the page
      window.location.reload();
    }, 100);
  };

  // Animation variants with proper TypeScript typing
  // Define animation variants with proper Framer Motion types
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07
      }
    }
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.645, 0.045, 0.355, 1.0] // Using cubic-bezier values for easeInOut
      }
    }
  };

  return (
    <motion.div
      className="space-y-8 pb-10"
      initial="hidden"
      animate="show"
      variants={containerVariants}
    >
      {/* Beautiful hero header section with gradient background */}
      <motion.div
        className="relative flex flex-col gap-4 p-8 rounded-xl bg-gradient-to-r from-blue-500/5 via-blue-500/10 to-blue-500/5 border border-blue-500/10 shadow-sm overflow-hidden mb-8"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-blue-500/10 to-transparent blur-2xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              Create a Poll with AI
              {/* <span className="text-blue-500 ml-1">🤖</span> */}
            </h1>
            <p className="text-muted-foreground mt-1">
              Describe your poll idea and let AI create it for you
            </p>
          </div>
          <Button asChild variant="outline" size="sm" className="gap-1 w-full sm:w-auto shadow-sm hover:shadow-md transition-shadow">
            <Link href="/dashboard/create">
              Try Standard Creation
            </Link>
          </Button>
        </div>
      </motion.div>

      <div className="container mx-auto px-4">
        {/* Progress indicator */}
        <motion.div
          className="pb-4 mx-auto"
          variants={itemVariants}
        >
        <div className="flex items-center justify-between max-w-xs sm:max-w-md mx-auto">
          <div className="flex flex-col items-center">
            <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm ${activeStep === 'chat' ? 'bg-primary text-white' : 'bg-primary text-white'}`}>
              1
            </div>
            <span className="text-xs mt-1 text-center">Describe Poll</span>
          </div>
          <div className={`h-0.5 flex-1 mx-2 sm:mx-4 ${activeStep === 'chat' ? 'bg-muted' : 'bg-primary'}`}></div>
          <div className="flex flex-col items-center">
            <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm ${activeStep === 'review' ? 'bg-primary text-white' : 'bg-muted text-foreground'}`}>
              2
            </div>
            <span className="text-xs mt-1 text-center">Review & Create</span>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="flex flex-1 p-0 pt-0"
        variants={itemVariants}
      >
        {activeStep === 'chat' ? (
          <div className="flex flex-col gap-6 w-full">
            {/* Main area - Tabs for Chat and Import - Full width */}
            <div className="w-full">
              <Tabs defaultValue="chat" value={activeTab} onValueChange={handleTabChange} className="h-full">
                <TabsList className="grid grid-cols-2 w-full">
                  <TabsTrigger value="chat">
                    <Sparkles className="h-4 w-4 mr-2" />
                    AI Chat
                  </TabsTrigger>
                  <TabsTrigger value="import">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Context
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="chat" className="mt-4 h-[60vh] sm:h-[calc(100%-48px)]">
                  <Card className="h-full flex flex-col overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">
                        <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                        Tell me about your poll
                      </CardTitle>
                      <CardDescription>
                        Describe what you want, and I&apos;ll help you create it.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 p-0 overflow-hidden">
                      <ConversationalPollInterface
                        ref={conversationalInterfaceRef}
                        onPollConfirmed={handlePollConfirmed}
                        className="flex-1 h-full"
                        showModelSelector={true}
                        defaultModel="gemini"
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="import" className="mt-4">
                  <Card className="flex flex-col h-[500px]">
                    <CardHeader>
                      <CardTitle className="text-base">
                        <Upload className="h-4 w-4 inline mr-2 text-primary" />
                        Import Context for Your Poll
                      </CardTitle>
                      <CardDescription>
                        Import context from a URL, file or website to use as a base for your poll.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col justify-center items-center gap-6">
                      <div className="w-full max-w-md space-y-6">
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <LinkIcon className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from URL</h3>
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Input
                              placeholder="Enter URL (e.g., blog post, article)"
                              className="flex-1"
                              onChange={(e) => setUrlInput(e.target.value)}
                              value={urlInput}
                              disabled={isExtracting}
                            />
                            <Button
                              variant="default"
                              onClick={() => handleExtractResource('url', urlInput)}
                              disabled={!urlInput || isExtracting}
                              className="w-full sm:w-auto"
                            >
                              {isExtracting ? <><Loader variant="minimal" size="xs" className="mr-1" /> Importing...</> : "Import"}
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Upload Document</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full h-24 border-dashed flex flex-col gap-2"
                            onClick={handleExtractFromFile}
                            disabled={isExtracting}
                          >
                            <Upload className="h-6 w-6" />
                            <span>
                              {isExtracting ? "Processing..." : "Click to upload a document"}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Supports .txt, .pdf, .docx files
                            </span>
                          </Button>
                          <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            className="hidden"
                            accept=".txt,.pdf,.docx,.doc"
                          />
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <Globe className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from Website</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={handleExtractFromWebsite}
                            disabled={isExtracting}
                          >
                            <Globe className="h-4 w-4 mr-2" />
                            {isExtracting ? "Processing website..." : "Extract from Website"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Card row below chat - Responsive cards layout */}
            <div className="w-full">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <Info className="h-4 w-4 inline mr-2 text-primary" />
                      How It Works
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">1</div>
                      <p>Describe your poll topic and audience in the chat, or import existing content as a starting point</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">2</div>
                      <p>The AI will help refine your idea and create a draft poll with appropriate questions</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">3</div>
                      <p>Review the generated poll and create it when you&apos;re ready</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleResetConversation}
                    >
                      Reset Conversation
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleCreateFromScratch}
                    >
                      <Sparkles className="h-3 w-3 mr-2" />
                      Create from Scratch
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleTrySuggestion}
                    >
                      <Sparkles className="h-3 w-3 mr-2" />
                      Try Example Poll
                    </Button>
                  </CardContent>
                </Card>

                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <CheckCircle2 className="h-4 w-4 inline mr-2 text-primary" />
                      Poll Template Ideas
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => sendMessageToChat("I need a customer satisfaction survey")}>Customer Satisfaction</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to measure customer satisfaction</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => sendMessageToChat("I want to create an event feedback poll")}>Event Feedback</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to gather feedback about an event</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => sendMessageToChat("I need a product feedback survey")}>Product Feedback</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to gather feedback about a product</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => sendMessageToChat("Help me create a team satisfaction survey")}>Team Satisfaction</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to measure team satisfaction</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <Card className="w-full">
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                  <CardTitle className="flex items-center">
                    <CheckCircle2 className="h-5 w-5 mr-2 text-green-500" />
                    Poll Draft Ready
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={handleBackToChat} className="w-full sm:w-auto">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Chat
                  </Button>
                </div>
                <CardDescription>
                  Review your poll details below. You can go back to make changes or create the poll now.
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold break-words">{pollSuggestion?.title}</h3>
                    <p className="text-muted-foreground mt-1 break-words">{pollSuggestion?.description}</p>
                  </div>

                  <div>
                    <h3 className="text-md font-semibold mb-2">Questions ({pollSuggestion?.questions.length})</h3>
                    <div className="space-y-4">
                      {pollSuggestion?.questions.map((question, index) => (
                        <div key={index} className="border rounded-md p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="font-medium break-words">{question.text}</div>
                              <div className="flex gap-2 my-2">
                                <Badge variant="outline">{question.type}</Badge>
                                {question.required && <Badge>Required</Badge>}
                              </div>
                            </div>
                          </div>

                          {question.options && question.options.length > 0 && (
                            <div className="mt-2">
                              <div className="text-sm text-muted-foreground mb-1">Options:</div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                {question.options.map((option, i) => {
                                  // Handle both string options and object options with text property
                                  const optionText = typeof option === 'string' ? option :
                                    (option && typeof option === 'object' && 'text' in option) ?
                                    (option as { text: string }).text : String(option);
                                  return (
                                    <div key={i} className="text-sm border rounded px-2 py-1 overflow-hidden text-ellipsis whitespace-nowrap" title={optionText}>
                                      {optionText.length > 50 ? optionText.substring(0, 47) + '...' : optionText}
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex flex-col gap-4">
                {showRetryPrompt && (
                  <div className="w-full bg-amber-50 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 p-3 rounded-md">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-5 w-5" />
                      <p className="font-medium">This is taking longer than expected</p>
                    </div>
                    <p className="mt-1 text-sm">The server might be experiencing delays. You can try one of these options:</p>
                    <div className="flex flex-col sm:flex-row gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2 border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-900/50 w-full sm:w-auto"
                        onClick={() => {
                          // Try to manually refresh auth and retry without page reload
                          toast.loading("Refreshing your session...");
                          ensureValidSession({ forceRefresh: true }).then(valid => {
                            if (valid) {
                              toast.success("Session refreshed successfully");
                              // Reset the creating state and retry
                              setIsCreatingPoll(false);
                              setTimeout(() => handleCreatePoll(), 500);
                            } else {
                              toast.error("Couldn't refresh your session. Try reloading the page.");
                            }
                          });
                        }}
                      >
                        <RefreshCw className="h-3 w-3" />
                        Retry without Reloading
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2 border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-900/50 w-full sm:w-auto"
                        onClick={handleRefreshAndRetry}
                      >
                        <RefreshCw className="h-3 w-3" />
                        Refresh Page & Retry
                      </Button>
                    </div>
                    <p className="text-xs mt-2 text-amber-600 dark:text-amber-400">
                      If the issue persists, you may be experiencing an authentication timeout.
                    </p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row sm:justify-between gap-3 w-full">
                  <Button variant="ghost" onClick={handleDiscardDraft} className="text-destructive hover:text-destructive hover:bg-destructive/10 w-full sm:w-auto">
                    Discard Draft
                  </Button>
                  <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <Button variant="outline" onClick={handleBackToChat} className="w-full sm:w-auto">
                      Make Changes
                    </Button>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="w-full sm:w-auto">
                            <Button
                              onClick={handleCreatePoll}
                              disabled={isCreatingPoll || pollCreationCompleted}
                              className="gap-2 w-full sm:w-auto"
                            >
                              {isCreatingPoll ? (
                                <>
                                  <Loader variant="minimal" size="xs" />
                                  Creating Poll...
                                </>
                              ) : pollCreationCompleted ? (
                                <>
                                  <CheckCircle2 className="h-4 w-4" />
                                  Poll Created!
                                </>
                              ) : (
                                <>
                                  <CheckCircle2 className="h-4 w-4" />
                                  Create Poll
                                </>
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{isCreatingPoll ? "Creating your poll..." : "Create your poll and save it to your dashboard"}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        )}
      </motion.div>
      </div>
    </motion.div>
  );
}
