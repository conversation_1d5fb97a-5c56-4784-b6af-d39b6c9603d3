// Helper functions for detecting and parsing JSON in messages

interface PollData {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'open' | 'text';
    options?: string[];
    required: boolean;
  }>;
}

/**
 * Attempts to extract and parse a poll JSON object from text content
 * @param {string} content - The message content to analyze
 * @returns {Object|null} The parsed poll data object or null if no valid poll data found
 */
export function extractPollDataFromMessage(content: string): { data: PollData; jsonContent: string } | null {
  if (!content) return null;

  console.log("Analyzing message for JSON poll data...");

  // First, look for JSON in code blocks (most common format)
  try {
    // Match content within code blocks (with or without json language specifier)
    const codeBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
    const codeBlockMatch = content.match(codeBlockRegex);

    if (codeBlockMatch && codeBlockMatch[1]) {
      const jsonContent = codeBlockMatch[1].trim();
      console.log("Found potential JSON in code block");

      try {
        // Parse the JSON content
        const pollData = JSON.parse(jsonContent);

        // Validate the poll data structure
        if (isPollData(pollData)) {
          console.log("Successfully extracted valid poll data from code block");
          return {
            data: pollData,
            jsonContent: jsonContent
          };
        } else {
          console.log("Found JSON in code block but it doesn't match poll structure");
        }
      } catch (error) {
        console.log("Failed to parse code block as JSON:", error);
      }
    }
  } catch (error) {
    console.log("Error processing code blocks:", error);
  }

  // Second approach: Look for JSON objects with specific poll fields
  try {
    // Find JSON-like structures with title, description, and questions
    const jsonRegex = /(\{[\s\S]*?"title"[\s\S]*?"description"[\s\S]*?"questions"[\s\S]*?\})/;
    const jsonMatch = content.match(jsonRegex);

    if (jsonMatch && jsonMatch[1]) {
      const jsonContent = jsonMatch[1];
      console.log("Found potential JSON with poll structure");

      try {
        // Clean the JSON string to fix common issues
        const cleanedJson = jsonContent
          .replace(/,\s*(\]|\})/g, '$1') // Remove trailing commas
          .replace(/(?<="[^"]*?)"(?=[^"]*?")/g, '\\"'); // Fix unescaped quotes

        const pollData = JSON.parse(cleanedJson);

        if (isPollData(pollData)) {
          console.log("Successfully extracted valid poll data from content");
          return {
            data: pollData,
            jsonContent: jsonContent
          };
        }
      } catch (error) {
        console.log("Failed to parse matched JSON:", error);
      }
    }
  } catch (error) {
    console.log("Error processing JSON pattern:", error);
  }

  // Last resort: Try to find any valid JSON object in the content
  try {
    // Find all potential JSON objects (starting with { and ending with })
    let braceCount = 0;
    let startIndex = -1;
    const potentialJsons: string[] = [];

    for (let i = 0; i < content.length; i++) {
      if (content[i] === '{') {
        if (braceCount === 0) {
          startIndex = i;
        }
        braceCount++;
      } else if (content[i] === '}') {
        braceCount--;
        if (braceCount === 0 && startIndex !== -1) {
          potentialJsons.push(content.substring(startIndex, i + 1));
          startIndex = -1;
        }
      }
    }

    console.log(`Found ${potentialJsons.length} potential JSON objects`);

    // Try each potential JSON object
    for (const json of potentialJsons) {
      try {
        // Clean the JSON string
        const cleanedJson = json
          .replace(/,\s*(\]|\})/g, '$1')
          .replace(/(?<="[^"]*?)"(?=[^"]*?")/g, '\\"');

        const pollData = JSON.parse(cleanedJson);

        if (isPollData(pollData)) {
          console.log("Successfully extracted valid poll data from potential JSON");
          return {
            data: pollData,
            jsonContent: json
          };
        }
      } catch (error) {
        console.log("Failed to parse potential JSON:", error);
        // Continue to next potential JSON
      }
    }
  } catch (error) {
    console.log("Error in last resort JSON extraction:", error);
  }

  console.log("No valid poll data found in message");
  return null;
}

/**
 * Check if an object has the expected structure of a poll
 * @param {Object} obj - The object to validate
 * @returns {boolean} True if the object matches poll structure
 */
function isPollData(obj: unknown): obj is PollData {
  // Basic structure validation
  if (!obj ||
      typeof obj !== 'object' ||
      !('title' in obj) ||
      !('description' in obj) ||
      !('questions' in obj) ||
      typeof (obj as Record<string, unknown>).title !== 'string' ||
      typeof (obj as Record<string, unknown>).description !== 'string' ||
      !Array.isArray((obj as Record<string, unknown>).questions)) {
    return false;
  }

  const castedObj = obj as PollData;

  // Ensure we have at least one question
  if (castedObj.questions.length === 0) {
    return false;
  }

  // Validate each question has required fields
  for (const question of castedObj.questions) {
    if (typeof question !== 'object' ||
        typeof question.text !== 'string' ||
        !question.text.trim()) {
      return false;
    }

    // Question type validation - normalize missing type to 'text'
    if (!question.type) {
      question.type = 'text';
    }
    if (!['single', 'multiple', 'open', 'text'].includes(question.type)) {
      return false;
    }

    // Required field validation - normalize missing required to false
    if (question.required === undefined) {
      question.required = false;
    }
    if (typeof question.required !== 'boolean') {
      return false;
    }
  }

  return true;
}
