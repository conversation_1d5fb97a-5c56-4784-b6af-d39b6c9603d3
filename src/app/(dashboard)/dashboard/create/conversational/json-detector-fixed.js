// Helper functions for detecting and parsing JSON in messages

/**
 * Attempts to extract and parse a poll JSON object from text content
 * @param {string} content - The message content to analyze
 * @returns {Object|null} The parsed poll data object or null if no valid poll data found
 */
export function extractPollDataFromMessage(content) {
  if (!content) return null;

  // First try with strict pattern containing expected fields
  try {
    const jsonMatch = content.match(/\{[\s\S]*?"title"[\s\S]*?"description"[\s\S]*?"questions"[\s\S]*?\}/);
    if (jsonMatch) {
      const jsonContent = jsonMatch[0];
      const pollData = JSON.parse(jsonContent);
      if (isPollData(pollData)) {
        return {
          data: pollData,
          jsonContent: jsonContent
        };
      }
    }
  } catch (e) {
    console.log("Failed to extract with strict pattern:", e);
  }

  // Try with broader pattern
  try {
    const jsonMatch = content.match(/\{[\s\S]*?\}/);
    if (jsonMatch) {
      const jsonContent = jsonMatch[0];
      const pollData = JSON.parse(jsonContent);
      if (isPollData(pollData)) {
        return {
          data: pollData,
          jsonContent: jsonContent
        };
      }
    }
  } catch (e) {
    console.log("Failed to extract with broad pattern:", e);
  }

  // Last resort - try to find any valid JSON in the content
  try {
    // Find potential JSON objects (starting with { and ending with })
    const potentialJsons = [];
    let startIndex = 0;
    while (startIndex < content.length) {
      const openBraceIndex = content.indexOf('{', startIndex);
      if (openBraceIndex === -1) break;

      let openCount = 1;
      let closeIndex = openBraceIndex + 1;

      while (closeIndex < content.length && openCount > 0) {
        if (content[closeIndex] === '{') openCount++;
        if (content[closeIndex] === '}') openCount--;
        closeIndex++;
      }

      if (openCount === 0) {
        const jsonCandidate = content.substring(openBraceIndex, closeIndex);
        potentialJsons.push(jsonCandidate);
      }

      startIndex = openBraceIndex + 1;
    }

    // Try each potential JSON
    for (const json of potentialJsons) {
      try {
        const pollData = JSON.parse(json);
        if (isPollData(pollData)) {
          return {
            data: pollData,
            jsonContent: json
          };
        }
      } catch {
        // Skip invalid JSON without using unused variable
      }
    }
  } catch (err) {
    console.log("Failed to extract with manual parsing:", err);
  }

  return null;
}

/**
 * Check if an object has the expected structure of a poll
 */
function isPollData(obj) {
  return obj &&
         typeof obj.title === 'string' &&
         typeof obj.description === 'string' &&
         Array.isArray(obj.questions) &&
         obj.questions.length > 0;
}
