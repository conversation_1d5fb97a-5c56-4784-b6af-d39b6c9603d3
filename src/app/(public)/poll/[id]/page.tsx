"use client";

import { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { getPollById, addPollResponse, hasUserRespondedToPoll } from "@/lib/services/polls";
import { Poll } from "@/lib/validation/schemas";


import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";

import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";

export default function PollPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const searchParams = useSearchParams();
  const pollId = params.id as string;
  const isNewPoll = searchParams.get('newPoll') === 'true';

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [showThankYou, setShowThankYou] = useState(false);

  const [pollData, setPollData] = useState<Poll | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch poll data
  useEffect(() => {
    const fetchPoll = async () => {
      try {
        console.log(`[POLL DEBUG] Starting fetchPoll for pollId: ${pollId}`);
        setLoading(true);
        const poll = await getPollById(pollId);
        console.log(`[POLL DEBUG] Poll data fetched:`, poll);

        if (!poll) {
          console.log(`[POLL DEBUG] No poll found for ID: ${pollId}`);
          setError("Poll not found");
          return;
        }

        // Check if user has already responded to this poll
        console.log(`[POLL DEBUG] Checking if user has responded to poll: ${pollId}`);

        // Debug localStorage before check
        if (typeof window !== 'undefined') {
          const storageKey = `poll_response_${pollId}`;
          const storedData = localStorage.getItem(storageKey);
          console.log(`[POLL DEBUG] Before hasUserRespondedToPoll check - localStorage key: ${storageKey}, data:`, storedData);
        }

        const alreadyResponded = await hasUserRespondedToPoll(pollId);
        console.log(`[POLL DEBUG] Poll ${pollId}: alreadyResponded =`, alreadyResponded, typeof alreadyResponded);

        // If they've already responded, show the thank you screen
        if (alreadyResponded) {
          console.log(`[POLL DEBUG] User has already responded - showing thank you screen for poll ${pollId}`);
          setShowThankYou(true);
        } else {
          console.log(`[POLL DEBUG] User has NOT responded - showing poll form for poll ${pollId}`);
        }

        setPollData(poll as Poll); // Type assertion to handle existing type mismatch
      } catch (err) {
        console.error("[POLL DEBUG] Error fetching poll:", err);
        setError("Failed to load poll");
      } finally {
        setLoading(false);
      }
    };

    fetchPoll();
  }, [pollId]);

  // Create a dynamic form schema based on the current question
  const getCurrentQuestionSchema = () => {
    // If poll data or current question is missing, return an empty schema
    if (!pollData || !pollData.questions[currentStep]) return z.object({});

    // Get the current question from poll data
    const question = pollData.questions[currentStep];

    if (question.type === "open") {
      return z.object({
        [question.id]: question.required
          ? z.string().min(1, "This field is required")
          : z.string().optional()
      });
    } else if (question.type === "multiple") {
      return z.object({
        [question.id]: question.required
          ? z.array(z.string()).min(1, "Please select at least one option")
          : z.array(z.string()).optional()
      });
    } else {
      // single choice
      return z.object({
        [question.id]: question.required
          ? z.string().min(1, "This field is required")
          : z.string().optional()
      });
    }
  };

  // Initialize the form with the current question schema
  const form = useForm<Record<string, string | string[]>>({
    resolver: zodResolver(getCurrentQuestionSchema()),
    defaultValues: {
      [pollData?.questions[currentStep]?.id || 'default']:
        pollData?.questions[currentStep]?.type === "multiple" ? [] : ""
    }
  });

  // Track all responses
  const [responses, setResponses] = useState<Record<string, string | string[]>>({});

  // Handle moving to the next question or submitting the poll
  const onSubmit = (data: Record<string, string | string[]>) => {
    console.log(`[POLL DEBUG] onSubmit called with data:`, data);
    console.log(`[POLL DEBUG] Current step: ${currentStep}, Total questions: ${pollData?.questions.length}`);

    // Store the current response
    setResponses({ ...responses, ...data });

    // If there are more questions, go to the next one
    if (pollData && currentStep < pollData.questions.length - 1) {
      console.log(`[POLL DEBUG] Moving to next question (step ${currentStep + 1})`);
      setCurrentStep(currentStep + 1);

      // Reset the form with the schema for the next question
      const nextQuestion = pollData?.questions[currentStep + 1];
      if (nextQuestion) {
        form.reset({
          [nextQuestion.id]: nextQuestion.type === "multiple" ? [] : ""
        });
      }
    } else {
      // All questions answered, submit the poll
      console.log(`[POLL DEBUG] All questions answered, calling handleSubmitPoll`);
      handleSubmitPoll({ ...responses, ...data });
    }
  };

  // Handle the poll submission
  const handleSubmitPoll = async (finalResponses: Record<string, string | string[]>) => {
    setIsSubmitting(true);

    try {
      console.log(`[POLL DEBUG] Starting poll submission for poll ${pollId}`);
      console.log(`[POLL DEBUG] Final responses:`, finalResponses);

      // Save the response to local storage
      const deviceType = /mobile|android|iphone|ipad|ipod/i.test(navigator.userAgent) ? 'mobile' : 'desktop';
      const browser = navigator.userAgent.includes('Firefox') ? 'Firefox' :
                      navigator.userAgent.includes('Chrome') ? 'Chrome' :
                      navigator.userAgent.includes('Safari') ? 'Safari' : 'Other';
      const os = navigator.userAgent.includes('Windows') ? 'Windows' :
                 navigator.userAgent.includes('Mac') ? 'macOS' :
                 navigator.userAgent.includes('Linux') ? 'Linux' : 'Other';

      console.log(`[POLL DEBUG] About to call addPollResponse for poll ${pollId}`);

      // Await the response submission to ensure localStorage is saved
      const responseSuccess = await addPollResponse({
        pollId,
        responses: finalResponses,
        respondentInfo: {
          deviceType,
          browser,
          os
        }
      });

      console.log(`[POLL DEBUG] addPollResponse returned:`, responseSuccess);

      if (responseSuccess) {
        console.log(`[POLL DEBUG] Poll response submitted successfully for poll ${pollId}`);

        // Verify localStorage was set
        if (typeof window !== 'undefined') {
          const storageKey = `poll_response_${pollId}`;
          const storedData = localStorage.getItem(storageKey);
          console.log(`[POLL DEBUG] localStorage verification - key: ${storageKey}, data:`, storedData);
        }

        // Show success state
        setShowThankYou(true);
      } else {
        throw new Error("Failed to save poll response");
      }
    } catch (error) {
      console.error("[POLL DEBUG] Error submitting poll:", error);
      toast.error("Failed to submit responses. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // This code only runs after we've confirmed pollData exists (due to guard clauses above)
  // Current question
  const currentQuestion = pollData?.questions[currentStep];

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-6">
        <div className="w-full max-w-md space-y-4 text-center">
          <h2 className="text-2xl font-bold tracking-tight">Poll Not Found</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button asChild className="mt-4">
            <Link href="/">Return Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (loading || !pollData) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="space-y-4 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto" />
          <p className="text-muted-foreground">Loading poll...</p>
        </div>
      </div>
    );
  }

  if (showThankYou) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-muted/30 py-12 px-4 flex items-center justify-center poll-completed">
        <div className="container max-w-xl mx-auto">
          <Card className="border-t-4 border-green-500 shadow-md overflow-hidden" data-testid="thank-you">
            <div className="absolute top-0 right-0 left-0 h-1.5 bg-gradient-to-r from-green-400 via-green-500 to-green-600"></div>
            <div className="absolute top-0 inset-x-0 flex justify-center">
              <div className="-mt-6 bg-green-500 rounded-full p-3 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
            </div>
            <CardHeader className="pt-12">
              <CardTitle className="text-center text-2xl">Thank You!</CardTitle>
              <CardDescription className="text-center text-lg pt-2">
                Your responses have been submitted successfully.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center pb-8">
              <p className="mb-8 text-muted-foreground">
                We appreciate your time and feedback. Your responses will help us improve our products and services.
              </p>
              <Button asChild variant="default" className="bg-green-500 hover:bg-green-600 shadow-md">
                <Link href="/" className="px-6">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                  Back to Home
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30 py-12 px-4">
      <div className="container max-w-xl mx-auto">
        {/* Preview Banner for newly created polls */}
        {isNewPoll && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg shadow-sm dark:bg-blue-950 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  🎉 Poll Created Successfully!
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  This is what your respondents will see. You can test it out or edit your poll.
                </p>
              </div>
              <div className="ml-4 flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  asChild
                  className="text-blue-700 border-blue-300 hover:bg-blue-100 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900"
                >
                  <Link href={`/dashboard/polls/${pollId}`}>
                    Edit Poll
                  </Link>
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  asChild
                  className="text-blue-700 border-blue-300 hover:bg-blue-100 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900"
                >
                  <Link href="/dashboard/polls">
                    My Polls
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Poll Header with Logo */}
        <div className="mb-6 text-center">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">{pollData.title}</h1>
          <p className="text-muted-foreground max-w-md mx-auto">{pollData.description}</p>
        </div>

        <Card className="border-t-4 border-primary shadow-md overflow-hidden">
          <CardHeader className="bg-muted/30 pb-4">
            {/* Progress Bar */}
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium">Your progress</span>
              <span className="text-sm font-medium">{currentStep + 1}/{pollData.questions.length}</span>
            </div>
            <div className="w-full bg-muted h-3 rounded-full overflow-hidden">
              <div
                className="bg-primary h-3 rounded-full transition-all duration-500 ease-in-out"
                style={{ width: `${((currentStep + 1) / pollData.questions.length) * 100}%` }}
              ></div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
          {currentQuestion && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="text-xl font-semibold mb-6 text-center">{currentQuestion.text}</div>

                {/* Render different input types based on question type */}
                {currentQuestion.type === "open" && (
                  <FormField
                    control={form.control}
                    name={currentQuestion.id}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder="Type your answer here..."
                            className="min-h-32"
                            {...field}
                            value={field.value as string}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {currentQuestion.type === "single" && (
                  <FormField
                    control={form.control}
                    name={currentQuestion.id}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) => field.onChange(value)}
                            value={String(field.value)}
                            className="space-y-3"
                          >
                            {currentQuestion.options?.map((option, index) => (
                              <div key={index} className="relative">
                                <label
                                  htmlFor={`${currentQuestion.id}-${option.value}`}
                                  className="flex items-center p-4 mb-2 rounded-lg border border-muted-foreground/20 hover:border-primary/40 hover:bg-muted/30 transition-all cursor-pointer"
                                >
                                  <RadioGroupItem
                                    value={String(option.value)}
                                    id={`${currentQuestion.id}-${option.value}`}
                                    className="mr-3"
                                  />
                                  <span className="text-base">{option.text}</span>
                                </label>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {currentQuestion.type === "multiple" && (
                  <FormField
                    control={form.control}
                    name={currentQuestion.id}
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-3">
                          {currentQuestion.options?.map((option, index) => (
                            <div key={index} className="relative mb-3">
                              <label
                                htmlFor={`${currentQuestion.id}-${option.value}`}
                                className="flex items-center p-4 rounded-lg border border-muted-foreground/20 hover:border-primary/40 hover:bg-muted/30 transition-all cursor-pointer"
                              >
                                <Checkbox
                                  id={`${currentQuestion.id}-${option.value}`}
                                  checked={(field.value as string[])?.includes(String(option.value))}
                                  onCheckedChange={(checked) => {
                                    const currentValue = field.value as string[] || [];
                                    const stringValue = String(option.value);
                                    const updatedValue = checked
                                      ? [...currentValue, stringValue]
                                      : currentValue.filter((value) => value !== stringValue);
                                    field.onChange(updatedValue);
                                  }}
                                  className="mr-3 h-5 w-5"
                                />
                                <span className="text-base">{option.text}</span>
                              </label>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <div className="flex justify-between pt-6 mt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    className="min-w-[100px] gap-2 transition-all hover:border-primary/70 hover:bg-muted"
                    onClick={() => {
                      if (currentStep > 0) {
                        setCurrentStep(currentStep - 1);
                        form.reset({
                          [pollData.questions[currentStep - 1].id]:
                            pollData.questions[currentStep - 1].type === "multiple"
                              ? responses[pollData.questions[currentStep - 1].id] || []
                              : responses[pollData.questions[currentStep - 1].id] || ""
                        });
                      }
                    }}
                    disabled={currentStep === 0}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="m15 18-6-6 6-6"/>
                    </svg>
                    Previous
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="min-w-[100px] gap-2 bg-primary hover:bg-primary/90 shadow-sm transition-all"
                  >
                    {currentStep === pollData.questions.length - 1
                      ? isSubmitting ? (
                          <>
                            <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Submitting...
                          </>
                        ) : (
                          <>
                            Submit
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M5 12h14"/>
                              <path d="m12 5 7 7-7 7"/>
                            </svg>
                          </>
                        )
                      : (
                        <>
                          Next
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M5 12h14"/>
                            <path d="m12 5 7 7-7 7"/>
                          </svg>
                        </>
                      )}
                  </Button>
                </div>
              </form>
            </Form>
          )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
