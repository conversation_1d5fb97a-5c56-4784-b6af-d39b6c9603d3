import Link from "next/link";

interface PublicLayoutProps {
  children: React.ReactNode;
}

export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link href="/" className="text-xl font-semibold">
            PollGPT
          </Link>
          <div className="flex items-center gap-4">
            <Link href="/login" className="text-sm text-muted-foreground hover:text-foreground">
              Login
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-1">
        {children}
      </main>
      <footer className="border-t py-6 md:py-4">
        <div className="container px-4 text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col space-y-3">
              <p className="text-center">© 2025 PollGPT. All rights reserved.</p>
              <div className="flex flex-col space-y-2">
                <a href="mailto:<EMAIL>" className="flex items-center justify-center gap-2 hover:text-foreground transition-colors" title="Email us">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center justify-center gap-2 not-italic hover:text-foreground text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                    <circle cx="12" cy="10" r="3"/>
                  </svg>
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="#" className="hover:underline">Terms</Link>
              <Link href="#" className="hover:underline">Privacy</Link>
              <Link href="#" className="hover:underline">Help</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4">
              <p>© 2025 PollGPT. All rights reserved.</p>
              <div className="flex flex-col lg:flex-row gap-2 lg:gap-4">
                <a href="mailto:<EMAIL>" className="flex items-center gap-1 hover:text-foreground transition-colors" title="Email us">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center gap-1 not-italic hover:text-foreground">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                    <circle cx="12" cy="10" r="3"/>
                  </svg>
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex gap-4">
              <Link href="#" className="hover:underline">Terms</Link>
              <Link href="#" className="hover:underline">Privacy</Link>
              <Link href="#" className="hover:underline">Help</Link>
            </nav>
          </div>
        </div>
      </footer>
    </div>
  );
}
