@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  /* Logo visibility variables for theme handling without hydration issues */
  --display-light-logo: block;
  --display-dark-logo: none;

  /* Standard Tailwind color palette - this makes bg-orange-600 work! */
  --color-orange-50: #fff7ed;
  --color-orange-100: #ffedd5;
  --color-orange-200: #fed7aa;
  --color-orange-300: #fdba74;
  --color-orange-400: #fb923c;
  --color-orange-500: #f97316;
  --color-orange-600: #ea580c;
  --color-orange-700: #c2410c;
  --color-orange-800: #9a3412;
  --color-orange-900: #7c2d12;

  --color-amber-50: #fffbeb;
  --color-amber-100: #fef3c7;
  --color-amber-200: #fde68a;
  --color-amber-300: #fcd34d;
  --color-amber-400: #fbbf24;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  --color-amber-700: #b45309;
  --color-amber-800: #92400e;
  --color-amber-900: #78350f;

  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-300: #86efac;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-800: #166534;
  --color-green-900: #14532d;

  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;

  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef9c3;
  --color-yellow-200: #fef08a;
  --color-yellow-300: #fde047;
  --color-yellow-400: #facc15;
  --color-yellow-500: #eab308;
  --color-yellow-600: #ca8a04;
  --color-yellow-700: #a16207;
  --color-yellow-800: #854d0e;
  --color-yellow-900: #713f12;

  /* Your custom semantic colors */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  /* Chart colors */
  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  /* Sidebar colors */
  --color-sidebar: hsl(var(--sidebar));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  /* Fonts */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Border radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Warm Modern Color Scheme for PollGPT - 2025 Edition */
:root {
  /* Base */
  --radius: 0.75rem;

  /* Core colors - Light mode (HSL format for Tailwind) */
  --background: 45 50% 97%; /* Cream/Off-white background */
  --foreground: 40 5% 15%; /* Very dark gray text color */

  /* Card & UI elements */
  --card: 0 0% 100%; /* Pure white */
  --card-foreground: 40 5% 15%; /* Very dark gray */
  --popover: 0 0% 100%;
  --popover-foreground: 40 5% 15%;

  /* Primary - Vibrant orange */
  --primary: 16 86% 54%; /* Vibrant orange */
  --primary-foreground: 0 0% 100%;

  /* Secondary - Light beige */
  --secondary: 40 15% 75%; /* Light beige/taupe */
  --secondary-foreground: 40 5% 15%;

  /* Muted & accents */
  --muted: 45 35% 92%; /* Lighter cream */
  --muted-foreground: 40 10% 40%; /* Muted dark beige */
  --accent: 40 5% 25%; /* Dark gray accent */
  --accent-foreground: 45 50% 97%; /* Cream/Off-white */

  /* Functional */
  --destructive: 0 84% 58%; /* Warning red */
  --destructive-foreground: 0 0% 100%;
  --border: 40 20% 87%; /* Very light beige border */
  --input: 40 20% 87%;
  --ring: 16 86% 54%; /* Vibrant orange */

  /* Chart colors for data visualization */
  --chart-1: 16 86% 54%; /* Primary orange */
  --chart-2: 16 77% 50%; /* Darker orange */
  --chart-3: 40 5% 25%; /* Dark gray */
  --chart-4: 90 10% 38%; /* Olive green complement */
  --chart-5: 90 10% 48%; /* Sage green complement */

  /* Sidebar specific */
  --sidebar: 45 50% 97%; /* Cream/Off-white */
  --sidebar-foreground: 40 5% 15%; /* Very dark gray */
  --sidebar-primary: 16 86% 54%; /* Match primary */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 40 5% 25%; /* Match accent */
  --sidebar-accent-foreground: 45 50% 97%;
  --sidebar-border: 40 20% 87%; /* Match border */
  --sidebar-ring: 16 86% 54%; /* Match ring */
}

.dark {
  /* Logo visibility variables for dark mode */
  --display-light-logo: none;
  --display-dark-logo: block;

  /* Core colors - Dark mode (HSL format) */
  --background: 40 5% 15%; /* Very dark gray/almost black */
  --foreground: 45 50% 97%; /* Cream/Off-white text */

  /* Card & UI elements */
  --card: 40 5% 25%; /* Dark gray */
  --card-foreground: 45 50% 97%;
  --popover: 40 5% 25%;
  --popover-foreground: 45 50% 97%;

  /* Primary - More vibrant in dark mode */
  --primary: 16 86% 54%; /* Vibrant orange */
  --primary-foreground: 0 0% 100%;

  /* Secondary - Deeper in dark mode */
  --secondary: 40 10% 33%; /* Darker version of beige */
  --secondary-foreground: 45 50% 97%;

  /* Muted & accents */
  --muted: 40 5% 20%; /* Slightly lighter than background */
  --muted-foreground: 40 15% 75%; /* Light beige */
  --accent: 40 15% 75%; /* Light beige accent */
  --accent-foreground: 40 5% 15%;

  /* Functional */
  --destructive: 0 84% 58%; /* Warning red */
  --destructive-foreground: 0 0% 100%;
  --border: 40 10% 30%; /* Dark beige border */
  --input: 40 10% 30%;
  --ring: 16 86% 54%; /* Vibrant orange */

  /* Chart colors - adjusted for dark mode */
  --chart-1: 16 86% 54%; /* Primary orange */
  --chart-2: 16 86% 64%; /* Lighter orange */
  --chart-3: 40 15% 75%; /* Light beige */
  --chart-4: 120 20% 55%; /* Sage green */
  --chart-5: 120 20% 65%; /* Light sage */

  /* Sidebar specific */
  --sidebar: 40 5% 17%; /* Slightly lighter than background */
  --sidebar-foreground: 45 50% 97%;
  --sidebar-primary: 16 86% 54%; /* Match primary */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 40 15% 75%; /* Match accent */
  --sidebar-accent-foreground: 40 5% 15%;
  --sidebar-border: 40 10% 30%; /* Match border */
  --sidebar-ring: 16 86% 54%; /* Match ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Dramatically enhanced dynamic background with more prominent animated elements */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 15% 50%,
        rgba(235, 94, 40, 0.25) 0%,
        transparent 35%),
      radial-gradient(circle at 85% 30%,
        rgba(89, 108, 86, 0.18) 0%,
        transparent 40%),
      radial-gradient(circle at 75% 85%,
        rgba(235, 94, 40, 0.22) 0%,
        transparent 45%),
      radial-gradient(circle at 25% 20%,
        rgba(204, 197, 185, 0.2) 0%,
        transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: subtleShift 15s ease-in-out infinite alternate;
  }

  .dark body::before {
    background:
      radial-gradient(circle at 15% 50%,
        rgba(235, 94, 40, 0.28) 0%,
        transparent 35%),
      radial-gradient(circle at 85% 30%,
        rgba(126, 157, 127, 0.2) 0%,
        transparent 40%),
      radial-gradient(circle at 75% 85%,
        rgba(235, 94, 40, 0.25) 0%,
        transparent 45%),
      radial-gradient(circle at 25% 20%,
        rgba(204, 197, 185, 0.15) 0%,
        transparent 50%);
    animation: subtleShift 15s ease-in-out infinite alternate;
  }

  /* More noticeable shifting animation for background gradients */
  @keyframes subtleShift {
    0% {
      background-position: 0% 0%;
    }
    100% {
      background-position: 5% 5%;
    }
  }

  /* Much more visible geometric pattern overlay */
  .geometric-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23eb5e28' fill-opacity='0.18'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.85;
    z-index: -1;
    pointer-events: none;
  }

  .dark .geometric-overlay {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23eb5e28' fill-opacity='0.22'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.9;
  }

  /* Much larger and more visible floating particles */
  .floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
  }

  .particle {
    position: absolute;
    display: block;
    border-radius: 50%;
    background-color: rgba(235, 94, 40, 0.35);
    animation: float 18s infinite ease-in-out;
    filter: blur(12px);
    box-shadow: 0 0 40px 20px rgba(235, 94, 40, 0.15);
  }

  .dark .particle {
    background-color: rgba(235, 94, 40, 0.4);
    filter: blur(15px);
    box-shadow: 0 0 50px 25px rgba(235, 94, 40, 0.2);
  }

  .particle:nth-child(1) {
    width: 300px;
    height: 300px;
    left: 5%;
    top: 15%;
    opacity: 0.3;
    animation-delay: 0s;
  }

  .particle:nth-child(2) {
    width: 350px;
    height: 350px;
    left: 75%;
    top: 65%;
    opacity: 0.25;
    animation-delay: -5s;
    background-color: rgba(204, 197, 185, 0.35);
  }

  .particle:nth-child(3) {
    width: 250px;
    height: 250px;
    left: 35%;
    top: 80%;
    opacity: 0.3;
    animation-delay: -10s;
  }

  .particle:nth-child(4) {
    width: 320px;
    height: 320px;
    left: 70%;
    top: 20%;
    opacity: 0.25;
    animation-delay: -15s;
    background-color: rgba(89, 108, 86, 0.3);
  }

  /* Add a fifth larger particle for even more visibility */
  .particle:nth-child(5) {
    width: 380px;
    height: 380px;
    left: 40%;
    top: 40%;
    opacity: 0.2;
    animation-delay: -8s;
    background-color: rgba(235, 94, 40, 0.25);
    animation-duration: 25s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0) translateX(0) rotate(0);
    }
    33% {
      transform: translateY(-30px) translateX(15px) rotate(8deg);
    }
    66% {
      transform: translateY(15px) translateX(-15px) rotate(-8deg);
    }
  }

  /* CSS Animations to replace framer-motion */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes shine {
    from {
      background-position: -200% 0;
    }
    to {
      background-position: 200% 0;
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-fadeInDelayed {
    opacity: 0;
    animation: fadeIn 0.5s ease-out 0.2s forwards;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  .animate-fadeInUpDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.1s forwards;
  }

  .animate-fadeInUpMoreDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.2s forwards;
  }

  .animate-fadeInUpMostDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.3s forwards;
  }

  .animate-pulse-slow {
    animation: pulse 8s ease-in-out infinite;
  }

  .animate-shine {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    background-size: 200% 100%;
    animation: shine 3s infinite;
  }

  /* Enhance cards with subtle effects */
  .card {
    position: relative;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right,
      #EB5E28,
      #F17A50);
    opacity: 0;
    transition: opacity 0.25s ease;
  }

  .card:hover::after {
    opacity: 1;
  }

  .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .dark .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Enhance buttons with subtle gradient */
  .btn-primary,
  .btn-action,
  button[data-variant="default"],
  .btn-default {
    background-image: linear-gradient(to right,
      #EB5E28,
      #F17A50
    );
    transition: all 0.2s ease;
  }

  /* Add smooth transitions for theme changes */
  html.disable-transitions * {
    transition-duration: 0ms !important;
  }

  html, body {
    transition: background-color 0.3s ease;
  }

  * {
    transition: color 0.2s ease, border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  }

  /* SEO helper classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
}
