"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { PollCreationDemo } from "@/components/ui/poll-creation-demo";
import { AiCreationPreview, DistributionPreview, RealTimeResponsesPreview, AnalyticsPreview } from "@/components/ui/feature-previews";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { NewsletterForm } from "@/components/ui/newsletter-form";
import { AlternativeNames, RichKeywords } from "@/components/ui/seo-helpers";
import { SEOConstruct } from "@/components/ui/seo-construct";
import { AiSimulationShowcase } from "@/components/ui/ai-simulation-showcase";
// Import the useAuth hook from the auth-provider
import { useAuth } from "@/components/providers/auth-provider-optimized";
import { PageMetadata } from "./page-metadata";
import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { SEOFaq } from "@/components/ui/seo-faq";
import { SEOFaqEnhanced } from "@/components/ui/seo-faq-enhanced";
import { LinkArchive } from "@/components/ui/link-archive";
import { BarChart3, Download, Zap, TrendingUp, Users, Mail, MapPin } from "lucide-react";

export default function Home() {
  const [isAuthLoaded, setIsAuthLoaded] = useState(false);
  const [authUser, setAuthUser] = useState<User | null>(null);
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const auth = useAuth();

  useEffect(() => {
    // Handle auth state changes
    if (auth) {
      setIsAuthLoaded(true);
      setAuthUser(auth.user);
      setIsAuthLoading(auth.isLoading);
    }
  }, [auth]);

  // Handle OAuth codes that accidentally end up on the landing page
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    if (code || error) {
      console.log('OAuth code/error detected on landing page, redirecting to callback...');
      // Redirect to the proper auth callback with all parameters preserved
      const callbackUrl = `/auth/callback${window.location.search}`;
      window.location.replace(callbackUrl);
      return;
    }
  }, []);

  // If auth is still loading and we've waited less than 2 seconds, show loading
  // Otherwise proceed with the page even if auth isn't ready
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAuthLoading(false);
    }, 2000); // After 2 seconds show the page regardless of auth state

    return () => clearTimeout(timer);
  }, []);

  // We'll rely on middleware for redirections
  // Show loading state while auth state is being determined
  if (isAuthLoading && !isAuthLoaded) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-pulse-slow flex flex-col items-center">
        <ThemeLogo width={40} height={40} className="h-10 w-auto" type="icon" />
        <p className="mt-4 text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>;
  }

  return (
    <div className="flex min-h-screen flex-col relative overflow-hidden">
      <PageMetadata />
      {/* Background Elements - adjusted for better visibility */}
      <div className="geometric-overlay absolute inset-0 z-0"></div>
      <div className="floating-particles absolute inset-0 z-0">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>

      {/* Header with logo, login/dashboard buttons and theme toggle */}
      <header className="w-full py-4 relative z-10">
        <div className="container px-4 md:px-6 mx-auto flex justify-between items-center">
          <Link href={authUser ? "/dashboard/polls" : "/"} className="flex items-center gap-2">
            <div className="flex items-center" style={{ zIndex: 100 }}>
              <ThemeLogo
                width={24}
                height={24}
                className="h-6 w-auto animate-pulse-slow"
                type="logo"
              />
            </div>
          </Link>
          <div className="flex items-center gap-2">
            {authUser ? (
              <Button asChild variant="default" size="sm">
                <Link href="/dashboard/polls">Go to Dashboard</Link>
              </Button>
            ) : (
              <>
                <Button asChild variant="ghost" size="sm">
                  <Link href="/login">Login</Link>
                </Button>
                <Button asChild variant="default" size="sm">
                  <Link href="/register">Sign Up</Link>
                </Button>
              </>
            )}
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="w-full py-12 md:py-24 lg:py-32 relative z-10">
        <div className="container px-4 md:px-6 mx-auto flex flex-col items-center text-center space-y-8">
          {/* Hidden SEO text for search engines */}
          <AlternativeNames />
          <div className="flex flex-col items-center gap-4">
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl animate-fadeInUp">
              Create intelligent polls with <span className="text-primary relative inline-block">
                AI assistance
                <span className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-40"></span>
              </span>
            </h1>
          </div>
          <p className="max-w-[700px] text-muted-foreground md:text-xl animate-fadeInUpDelayed">
            Streamline your polling process from creation to analysis using PollGPT. Generate unbiased questions, collect responses, and gain actionable insights with minimal effort.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 animate-fadeInUpMoreDelayed">
            <Button asChild size="lg" className="w-full sm:w-auto relative overflow-hidden group">
              <Link href="/register">
                Get Started
                <span className="absolute top-0 left-0 w-full h-full bg-white opacity-0 group-hover:opacity-10 transition-opacity"></span>
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
              <Link href="/#features">Learn More</Link>
            </Button>
          </div>

          {/* Interactive Demo Section */}
          <div className="relative w-full max-w-3xl mt-8 animate-fadeInUpMostDelayed z-20">
            <div className="absolute -top-3 left-1/2 z-31 dark:text-black transform -translate-x-1/2 bg-black dark:bg-white text-primary-foreground px-4 py-1 rounded-full text-sm font-medium shadow-md ">
              Live Demo
            </div>
            <div className="card-glow absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent rounded-xl blur-3xl opacity-30 z-0"></div>
            <div className="relative z-30">
              <PollCreationDemo />
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" className="w-full py-12 md:py-24 bg-muted/50 relative z-10">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Core Features
            </h2>
            <p className="max-w-[600px] text-muted-foreground md:text-lg">
              Everything you need to create, distribute, and analyze polls with AI assistance.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Feature 1: AI-Assisted Creation */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <BarChart3 className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold">AI-Assisted Creation</h3>
              </div>
              <p className="text-muted-foreground">Generate intelligent poll questions based on your topic and audience. Our AI analyzes your input and creates balanced, engaging questions.</p>
              <AiCreationPreview />
            </div>

            {/* Feature 2: Easy Distribution */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <Download className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold">Easy Distribution</h3>
              </div>
              <p className="text-muted-foreground">Share polls with custom links, QR codes, or embed them on your website. Reach your audience wherever they are with just a few clicks.</p>
              <DistributionPreview />
            </div>

            {/* Feature 3: Real-time Responses */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <Zap className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold">Real-time Responses</h3>
              </div>
              <p className="text-muted-foreground">Track responses instantly as they come in with live updates. Watch your data grow in real-time and get immediate feedback from your audience.</p>
              <RealTimeResponsesPreview />
            </div>

            {/* Feature 4: Advanced Analytics */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <TrendingUp className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold">Advanced Analytics</h3>
              </div>
              <p className="text-muted-foreground">Get AI-powered insights and visualizations from your poll results. Uncover patterns, trends, and actionable recommendations automatically.</p>
              <AnalyticsPreview />
            </div>
          </div>
        </div>
      </div>

      {/* AI Simulation Showcase Section */}
      <AiSimulationShowcase />

     {/* Enhanced FAQ section - Visible and optimized for SEO */}
      <SEOFaqEnhanced />
      {/* Subscribe Section */}
      <div className="w-full py-16 md:py-20 bg-gradient-to-br from-slate-50/90 via-white to-orange-50/30 dark:from-slate-950/50 dark:via-slate-900/30 dark:to-slate-950/50 relative z-10 overflow-hidden">
        {/* Background Elements matching FAQ section */}
        {/* <div className="geometric-overlay absolute inset-0 z-0"></div> */}
        <div className="floating-particles absolute inset-0 z-0">
          <div className="particle"></div>
          <div className="particle"></div>
        </div>

        {/* Additional gradient overlays for depth */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-50/20 via-transparent to-amber-50/10 dark:from-orange-950/10 dark:via-transparent dark:to-amber-950/5 z-0"></div>
        <div className="absolute top-0 right-1/4 w-48 h-48 md:w-72 md:h-72 bg-orange-400/6 dark:bg-orange-500/10 rounded-full blur-3xl z-0"></div>

        <div className="container px-4 md:px-6 mx-auto relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-600 to-amber-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                  <Mail className="w-4 h-4" />
                  Newsletter
                </div>
                <h2 className="text-4xl md:text-5xl font-bold tracking-tight">
                  Stay updated with{" "}
                  <span className="bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent relative inline-block">
                    PollGPT
                    <span className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-orange-600 to-amber-600 opacity-40 rounded-full"></span>
                  </span>
                </h2>
                <p className="text-muted-foreground text-xl leading-relaxed">Subscribe to our newsletter for the latest features, tips, and polling best practices. No spam, ever.</p>
                <div className="flex items-center gap-3 mt-6">
                  <div className="p-3 rounded-full bg-gradient-to-r from-orange-600/20 to-amber-600/20 text-orange-600 dark:text-orange-400 border border-orange-200/50 dark:border-orange-800/30">
                    <Users className="w-6 h-6" />
                  </div>
                  <p className="text-base">Join <span className="font-semibold text-orange-600 dark:text-orange-400">2,000+</span> poll creators &amp; researchers</p>
                </div>
              </div>
              <div className="relative">
                {/* Card glow effect */}
                <div className="card-glow absolute inset-0 bg-gradient-to-r from-orange-500/15 via-amber-500/10 to-orange-500/15 rounded-2xl blur-2xl opacity-30 z-0"></div>

                <div className="relative bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl shadow-orange-500/10 dark:shadow-orange-500/5 border border-orange-200/50 dark:border-orange-800/30 md:ml-auto md:max-w-md w-full z-10">
                  <NewsletterForm />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Semantic term definitions for SEO */}
      <SEOConstruct />

      {/* Legacy SEO FAQ - Hidden but still detected by search engines */}
      <SEOFaq />

      {/* Rich Keywords section for SEO */}
      <RichKeywords />

      {/* Link Archive for internal linking SEO */}
      <LinkArchive />

      {/* Footer */}
      <div className="w-full py-6 bg-muted relative z-10">
        <div className="container px-4 md:px-6 mx-auto text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
              <div className="flex flex-col space-y-2">
                <a href="mailto:<EMAIL>" className="flex items-center justify-center gap-2 hover:text-foreground transition-colors" title="Email us">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center justify-center gap-2 not-italic hover:text-foreground text-center">
                  <MapPin className="w-4 h-4" />
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Help</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
              <div className="flex flex-col lg:flex-row gap-2 lg:gap-4">
                <a href="mailto:<EMAIL>" className="flex items-center gap-1 hover:text-foreground transition-colors" title="Email us">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center gap-1 not-italic hover:text-foreground">
                  <MapPin className="w-4 h-4" />
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex gap-4">
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Help</Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
