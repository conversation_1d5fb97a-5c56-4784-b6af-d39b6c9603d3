import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')
  const errorDescription = requestUrl.searchParams.get('error_description')

  // Log for debugging
  console.log('Auth callback received:', {
    code: code ? 'present' : 'missing',
    error: error || 'none',
    origin: requestUrl.origin
  })

  // Handle OAuth errors from the provider
  if (error) {
    console.error(`OAuth provider error: ${error}`, errorDescription)
    return NextResponse.redirect(`${requestUrl.origin}/login?error=oauth_error&description=${encodeURIComponent(errorDescription || 'Unknown error')}`)
  }

  if (!code) {
    console.error('No code parameter found in callback URL')
    return NextResponse.redirect(`${requestUrl.origin}/login?error=missing_code`)
  }

  // Set up Supabase client with enhanced cookie handling for PKCE
  const cookieStore = await cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => cookieStore.getAll(),
        setAll: (cookiesToSet) => {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch (error) {
            console.error('Error setting cookies:', error);
          }
        },
      },
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
      }
    }
  )

  try {
    // Exchange the code for a session (Supabase handles PKCE internally)
    console.log('Exchanging auth code for session...')

    // Debug: Log available cookies to check PKCE verifier
    const allCookies = cookieStore.getAll();
    console.log('Available cookies:', allCookies.map(c => ({ name: c.name, hasValue: !!c.value })));

    // Look for PKCE-related cookies specifically
    const pkceCookies = allCookies.filter(c =>
      c.name.includes('code-verifier') ||
      c.name.includes('pkce') ||
      c.name.includes('auth-token')
    );
    console.log('PKCE-related cookies:', pkceCookies.map(c => ({ name: c.name, hasValue: !!c.value })));

    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    if (error) {
      console.error('Code exchange error:', error)

      // Specific handling for PKCE errors
      if (error.message?.includes('PKCE') || error.message?.includes('code_verifier')) {
        return NextResponse.redirect(
          `${requestUrl.origin}/login?error=pkce_error&description=${encodeURIComponent('PKCE verification failed. Please ensure you\'re using the same browser and try again.')}`
        )
      }

      return NextResponse.redirect(
        `${requestUrl.origin}/login?error=exchange_failed&description=${encodeURIComponent(error.message)}`
      )
    }

    if (!data.session || !data.user) {
      console.error('No session or user data returned from code exchange')
      return NextResponse.redirect(`${requestUrl.origin}/login?error=no_session`)
    }

    console.log('Session successfully created for user:', data.user.id)

    // Handle profile creation for new users
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is 'not found'
        console.error('Error fetching profile:', profileError)
      }

      if (!profile) {
        console.log('Creating new profile for user:', data.user.id)

        // Extract user details from OAuth metadata
        const isOAuthUser = data.user.app_metadata?.provider === 'google'
        const name = isOAuthUser
          ? (data.user.user_metadata?.full_name || data.user.user_metadata?.name)
          : data.user.user_metadata?.name
        const avatarUrl = isOAuthUser ? data.user.user_metadata?.avatar_url : null

        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email,
            name: name || data.user.email?.split('@')[0] || 'User',
            avatar_url: avatarUrl
          })

        if (insertError) {
          console.error('Error creating profile:', insertError)
          // Continue anyway - the user is authenticated
        } else {
          console.log('Profile created successfully for user:', data.user.id)
        }
      }
    } catch (profileError) {
      console.error('Unexpected error handling profile:', profileError)
      // Continue with authentication even if profile handling fails
    }

    // Check for redirect parameter to determine where to send the user
    const redirectTo = requestUrl.searchParams.get('redirect')
    let finalRedirectUrl = `${requestUrl.origin}/dashboard`

    if (redirectTo) {
      try {
        const decodedRedirect = decodeURIComponent(redirectTo)

        // Safety checks to prevent redirect loops
        if (
          !decodedRedirect.includes('login') &&
          !decodedRedirect.includes('register') &&
          !decodedRedirect.includes('auth/callback') &&
          decodedRedirect.startsWith('/')
        ) {
          finalRedirectUrl = `${requestUrl.origin}${decodedRedirect}`
          console.log('Redirecting to preserved destination:', finalRedirectUrl)
        } else {
          console.log('Invalid redirect parameter, defaulting to dashboard')
        }
      } catch (error) {
        console.error('Error decoding redirect parameter:', error)
        // Default to dashboard on error
      }
    }

    // Set completion flag and redirect to final destination
    const response = NextResponse.redirect(finalRedirectUrl)

    // Set a temporary cookie to help client-side auth refresh
    response.cookies.set('oauth_completed', 'true', {
      maxAge: 60, // 1 minute
      httpOnly: false, // Make it accessible to JavaScript
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    })

    return response
  } catch (error) {
    console.error('Unexpected error during auth callback:', error)
    return NextResponse.redirect(
      `${requestUrl.origin}/login?error=unexpected_error&description=${encodeURIComponent('An unexpected error occurred during authentication')}`
    )
  }
}
