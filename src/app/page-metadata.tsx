// src/app/page-metadata.tsx
import React from 'react';

export function PageMetadata() {
  return (
    <>
      {/* Organization Schema */}
      <div itemScope itemType="http://schema.org/Organization" className="hidden">
        <span itemProp="name">PollGPT</span>
        <span itemProp="description">AI-Powered Poll & Survey Creator</span>
        <span itemProp="alternateName">Poll GPT</span>
        <span itemProp="alternateName">PollGPT AI</span>
        <span itemProp="alternateName">Poll GPT AI</span>
        <span itemProp="slogan">Create intelligent polls with AI in seconds</span>
        <div itemProp="address" itemScope itemType="http://schema.org/PostalAddress">
          <span itemProp="streetAddress">5 Parv. Alan Turing</span>
          <span itemProp="addressLocality">Paris</span>
          <span itemProp="postalCode">75013</span>
          <span itemProp="addressCountry">France</span>
        </div>
        <div itemProp="contactPoint" itemScope itemType="http://schema.org/ContactPoint">
          <span itemProp="contactType">Customer Support</span>
          <span itemProp="email"><EMAIL></span>
        </div>
      </div>

      {/* SoftwareApplication Schema */}
      <div itemScope itemType="http://schema.org/SoftwareApplication" className="hidden">
        <span itemProp="name">PollGPT</span>
        <span itemProp="alternateName">Poll GPT</span>
        <span itemProp="description">Free AI-powered poll creator and survey generator. Create, distribute and analyze polls with AI assistance.</span>
        <span itemProp="url">https://pollgpt.com</span>
        <span itemProp="applicationCategory">Survey Tool</span>
        <span itemProp="applicationCategory">Productivity</span>
        <span itemProp="operatingSystem">Web</span>
        <span itemProp="offers" itemScope itemType="http://schema.org/Offer">
          <span itemProp="price">0</span>
          <span itemProp="priceCurrency">USD</span>
          <span itemProp="availability">https://schema.org/InStock</span>
        </span>
        <div itemProp="aggregateRating" itemScope itemType="http://schema.org/AggregateRating">
          <span itemProp="ratingValue">4.8</span>
          <span itemProp="ratingCount">237</span>
          <span itemProp="reviewCount">142</span>
        </div>
        <meta itemProp="applicationCategory" content="BusinessApplication" />
        <meta itemProp="operatingSystem" content="Web, iOS, Android" />
        <div itemProp="offers" itemScope itemType="http://schema.org/Offer">
          <meta itemProp="price" content="0" />
          <meta itemProp="priceCurrency" content="USD" />
        </div>
        <div itemProp="aggregateRating" itemScope itemType="http://schema.org/AggregateRating">
          <meta itemProp="ratingValue" content="4.8" />
          <meta itemProp="ratingCount" content="127" />
          <meta itemProp="reviewCount" content="68" />
        </div>
      </div>
    </>
  );
}
