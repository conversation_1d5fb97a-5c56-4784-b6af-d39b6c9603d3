"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Theme<PERSON>ogo } from "@/components/ui/theme-logo";
import { AiCreationPreview, AnalyticsPreview } from "@/components/ui/feature-previews";
import { PageMetadata } from "../page-metadata";
import { RichKeywords } from "@/components/ui/seo-helpers";
import { SEOConstruct } from "@/components/ui/seo-construct";
import { LinkArchive } from "@/components/ui/link-archive";
import { SEOFaqEnhanced } from "@/components/ui/seo-faq-enhanced";

export default function AiPollGeneratorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <PageMetadata />

      {/* Hero section */}
      <div className="flex-1">
        <div className="container px-4 md:px-6 flex flex-col items-center justify-center min-h-[calc(80vh-4rem)] py-10">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                #1 AI Poll Generator
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Create perfect polls in seconds with artificial intelligence. Generate questions, analyze responses, and gain insights automatically.
              </p>
            </div>
            <div className="space-x-4">
              <Link href="/register">
                <Button size="lg" className="font-medium">Create Your First Poll</Button>
              </Link>
              <Link href="/">
                <Button variant="outline" size="lg">View Demo</Button>
              </Link>
            </div>
          </div>

          {/* Demo section */}
          <div className="mx-auto w-full max-w-5xl py-12">
            <AiCreationPreview />
          </div>

          {/* Features section */}
          <div className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
            <div className="container space-y-12 px-4 md:px-6">
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <div className="space-y-2">
                  <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Generate Any Type of Poll</h2>
                  <p className="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed">
                    Our AI poll generator creates perfect questions for any scenario - from customer satisfaction surveys to market research polls.
                  </p>
                </div>
              </div>
              <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-2 md:gap-12">
                <div className="flex flex-col justify-center space-y-4">
                  <ul className="grid gap-6">
                    <li>
                      <div className="grid gap-1">
                        <h3 className="text-xl font-bold">Multiple Poll Types</h3>
                        <p className="text-muted-foreground">Create everything from simple multiple choice polls to complex surveys</p>
                      </div>
                    </li>
                    <li>
                      <div className="grid gap-1">
                        <h3 className="text-xl font-bold">NLP-Enhanced Questions</h3>
                        <p className="text-muted-foreground">AI-crafted questions that eliminate bias and maximize response quality</p>
                      </div>
                    </li>
                    <li>
                      <div className="grid gap-1">
                        <h3 className="text-xl font-bold">Document Extraction</h3>
                        <p className="text-muted-foreground">Upload documents or text to automatically generate relevant polls</p>
                      </div>
                    </li>
                  </ul>
                </div>
                <div className="flex justify-center">
                  <AnalyticsPreview />
                </div>
              </div>
            </div>
          </div>

          {/* Testimonial section */}
          <div className="w-full py-12 md:py-24 lg:py-32">
            <div className="container px-4 md:px-6">
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <div className="space-y-2">
                  <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Trusted by Professionals</h2>
                  <p className="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed">
                    See why researchers, marketers, and educators choose PollGPT for intelligent polling.
                  </p>
                </div>
              </div>
              <div className="mx-auto grid max-w-5xl grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
                <div className="flex flex-col gap-2 p-6 border rounded-xl">
                  <p className="text-muted-foreground">&quot;PollGPT saved me hours of work by generating the perfect customer satisfaction survey in seconds.&quot;</p>
                  <p className="font-medium">- Marketing Director</p>
                </div>
                <div className="flex flex-col gap-2 p-6 border rounded-xl">
                  <p className="text-muted-foreground">&quot;The AI analysis of our survey results identified patterns we would have missed otherwise.&quot;</p>
                  <p className="font-medium">- Research Analyst</p>
                </div>
                <div className="flex flex-col gap-2 p-6 border rounded-xl">
                  <p className="text-muted-foreground">&quot;My students love creating polls with PollGPT for their research projects. It&apos;s intuitive and powerful.&quot;</p>
                  <p className="font-medium">- University Professor</p>
                </div>
              </div>
            </div>
          </div>

          {/* CTA section */}
          <div className="w-full py-12 md:py-24 lg:py-32 bg-primary/5">
            <div className="container px-4 md:px-6">
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <div className="space-y-2">
                  <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Ready to Create Your First AI Poll?</h2>
                  <p className="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed">
                    Join thousands of professionals using PollGPT to create intelligent surveys and polls.
                  </p>
                </div>
                <div className="w-full max-w-sm space-y-2">
                  <Link href="/register">
                    <Button className="w-full" size="lg">Get Started Free</Button>
                  </Link>
                  <p className="text-xs text-muted-foreground">No credit card required</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced FAQ section for SEO */}
      <SEOFaqEnhanced />

      {/* SEO Construct for semantic markup */}
      <SEOConstruct />

      {/* Rich keywords section */}
      <RichKeywords />

      {/* Link Archive for internal linking SEO */}
      <LinkArchive />

      {/* Footer */}
      <div className="w-full py-6 bg-muted">
        <div className="container px-4 md:px-6 text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
              <span className="font-bold text-lg text-foreground">PollGPT</span>
            </div>
            <nav className="flex gap-4">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
