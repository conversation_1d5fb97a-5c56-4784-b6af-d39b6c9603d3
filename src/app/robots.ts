export default async function robots() {
  // Get the base URL from environment or default to production URL
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://pollgpt.com';

  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/dashboard/',
          '/api/',
          '/_*',         // Block any paths starting with underscore
          '/private/',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/pollgpt',
          '/poll-gpt',
          '/free-ai-polls',
          '/ai-poll-generator',
          '/poll-gpt-login',
          '/login',
          '/register',
        ],
        disallow: ['/dashboard/', '/api/'],
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: ['/dashboard/', '/api/'],
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
