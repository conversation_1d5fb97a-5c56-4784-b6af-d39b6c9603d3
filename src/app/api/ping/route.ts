import { NextResponse } from 'next/server';

/**
 * Simple API route to check if the server is reachable
 * Used for network connectivity detection
 */
export async function GET() {
  return NextResponse.json({ status: 'ok', timestamp: Date.now() });
}

export async function HEAD() {
  // Lightweight HEAD response for ping checks
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    }
  });
}
