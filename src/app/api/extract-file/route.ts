import { NextRequest, NextResponse } from 'next/server';
import { extractContentFromFile } from '@/lib/services/content-extractor';
import { uploadContextFile } from '@/lib/services/storage';
import { createClient } from '@/lib/supabase/server';

/**
 * API handler for extracting content from uploaded files
 *
 * We're adding special configuration to prevent timeouts and memory issues
 * with larger files, particularly PDFs that might be complex to process.
 */
export const maxDuration = 60; // 60 seconds maximum processing time
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb', // Allow larger file uploads (up to 25MB)
    },
    responseLimit: false, // No response size limit
  },
};

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'File is required' },
        { status: 400 }
      );
    }

    console.log(`Processing file: ${file.name}, Size: ${file.size} bytes, Type: ${file.type}`);

    try {
      // Get authenticated user from server-side client
      const supabase = await createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return NextResponse.json(
          { error: 'Authentication required. Please log in.' },
          { status: 401 }
        );
      }
      
      // Extract content from file
      const content = await extractContentFromFile(file);
      
      // Upload file to Supabase storage using the server-side client
      const fileUpload = await uploadContextFile(file, undefined, supabase);
      
      // Return both the extracted content and the file URL
      console.log(`Successfully extracted content from ${file.name} and uploaded to Supabase storage`);
      console.log(`Extracted content length: ${content?.length || 0} characters`);
      
      // Ensure content is included in the response
      return NextResponse.json({ 
        content, // This is the extracted text content
        extractedContent: content, // Adding an additional field for clarity
        fileUrl: fileUpload.url,
        filePath: fileUpload.path,
        fileName: fileUpload.filename,
        fileType: fileUpload.contentType,
        fileSize: fileUpload.size
      });
    } catch (extractionError: unknown) {
      console.error(`Specific extraction error for ${file.name}:`, extractionError);

      // More detailed error response
      const errorDetails = extractionError instanceof Error
        ? {
            message: extractionError.message,
            stack: process.env.NODE_ENV === 'development' ? extractionError.stack : undefined,
            name: extractionError.name
          }
        : { message: 'Unknown extraction error type' };

      return NextResponse.json(
        {
          error: 'Content extraction failed',
          details: errorDetails,
          fileInfo: {
            name: file.name,
            type: file.type,
            size: file.size
          }
        },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Error in API route:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to process request', message: errorMessage },
      { status: 500 }
    );
  }
}