import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

// Define the message type for better type safety
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Define system prompt for poll creation
const SYSTEM_PROMPT = `You are PollGPT, an AI assistant that specializes in helping users create polls.
Your goal is to understand the user's needs and help them create an effective poll.

Ask meaningful follow-up questions to understand:
- The topic of their poll
- Their target audience
- The purpose of the poll
- Any specific questions they want to include

Once you have enough information, offer to generate a complete poll with appropriate questions.
When the user is ready, respond with a JSON object containing the poll details in this format:
{
  "title": "Poll title",
  "description": "Brief description of the poll purpose",
  "questions": [
    {
      "text": "Question text",
      "type": "single|multiple|open",
      "options": ["Option 1", "Option 2"],
      "required": true
    }
  ]
}

Keep your responses conversational, helpful, and focused on creating an effective poll.`;

export async function POST(req: NextRequest) {
  try {
    // Extract the messages from the request
    const { messages } = await req.json() as { messages: ChatMessage[] };

    // Check if API key is available
    const apiKey = process.env.MISTRAL_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Mistral API key is not configured' },
        { status: 500 }
      );
    }

    // Add system prompt if not present
    if (!messages.some(m => m.role === 'system')) {
      messages.unshift({
        role: 'system',
        content: SYSTEM_PROMPT
      });
    }

    // Format messages to ensure proper formats for Mistral API
    const formattedMessages = messages.map(message => ({
      role: message.role,
      content: message.content
    }));

    // Make direct API call to Mistral
    const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'mistral-large-2411',
        messages: formattedMessages,
        temperature: 0.7,
        max_tokens: 2000
      })
    });

    // Check if the response is successful
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Mistral API error: ${response.status}`, errorText);
      throw new Error(`Mistral API error: ${response.status}`);
    }

    // Get response data
    const data = await response.json();
    const responseText = data.choices[0]?.message?.content || 'No response from AI';

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        controller.enqueue(encoder.encode(responseText));
        controller.close();
      }
    });

    // Return the response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'X-Content-Type-Options': 'nosniff',
      }
    });
  } catch (error) {
    console.error('Error in Mistral chat API:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 }
    );
  }
}
