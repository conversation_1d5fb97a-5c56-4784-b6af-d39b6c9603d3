import { NextRequest, NextResponse } from 'next/server';
import { streamText } from 'ai';
import { createPerplexity } from '@ai-sdk/perplexity';

export const runtime = 'edge';

// Define the message type for better type safety
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Define system prompt for poll creation
const SYSTEM_PROMPT = `You are PollGPT, an AI assistant that specializes in helping users create polls.
Your goal is to understand the user's needs and help them create an effective poll.

Ask meaningful follow-up questions to understand:
- The topic of their poll
- Their target audience
- The purpose of the poll
- Any specific questions they want to include

Once you have enough information, offer to generate a complete poll with appropriate questions.
When the user is ready, respond with a JSON object containing the poll details in this format:
{
  "title": "Poll title",
  "description": "Brief description of the poll purpose",
  "questions": [
    {
      "text": "Question text",
      "type": "single|multiple|open",
      "options": ["Option 1", "Option 2"],
      "required": true
    }
  ]
}

Keep your responses conversational, helpful, and focused on creating an effective poll.`;

export async function POST(req: NextRequest) {
  try {
    // Extract messages from the request (handling both formats for backward compatibility)
    const body = await req.json();
    let messages: ChatMessage[] = [];

    if (body.messages) {
      // New format from ConversationInterface
      messages = body.messages;
    } else if (body.prompt) {
      // Old format with just a prompt
      messages = [
        { role: 'user', content: body.prompt }
      ];
    } else {
      return NextResponse.json(
        { error: 'Invalid request format: missing messages or prompt' },
        { status: 400 }
      );
    }

    // Get API key from environment variables
    const apiKey = process.env.PERPLEXITY_API_KEY;

    if (!apiKey) {
      return NextResponse.json(
        { error: 'Perplexity API key is not configured' },
        { status: 500 }
      );
    }

    // Initialize Perplexity provider with AI SDK
    const perplexity = createPerplexity({
      apiKey: apiKey,
    });

    // Ensure we have a system prompt
    if (!messages.some(m => m.role === 'system')) {
      messages.unshift({ role: 'system', content: SYSTEM_PROMPT });
    }

    // Filter and reformat messages to ensure alternating roles
    const formattedMessages: ChatMessage[] = [];

    // First add all system messages
    messages.filter(m => m.role === 'system').forEach(m => {
      formattedMessages.push(m);
    });

    // Then ensure user and assistant messages alternate
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    let expectedRole: 'user' | 'assistant' = 'user'; // Start with user

    for (let i = 0; i < nonSystemMessages.length; i++) {
      const message = nonSystemMessages[i];

      if (message.role === expectedRole) {
        formattedMessages.push(message);
        // Toggle expected role
        expectedRole = expectedRole === 'user' ? 'assistant' : 'user';
      }
    }

    // If the last message wasn't from user and we need to send, add a default user message
    if (formattedMessages.length === 0 || formattedMessages[formattedMessages.length - 1].role !== 'user') {
      formattedMessages.push({ role: 'user', content: 'Hi, I need help creating a poll.' });
    }

    console.log('Calling Perplexity via AI SDK with messages:',
      JSON.stringify(formattedMessages.map(m => ({ role: m.role, contentLength: m.content.length }))));

    // Use AI SDK streamText with Perplexity Sonar model (best for web content)
    const result = streamText({
      model: perplexity('llama-3.1-sonar-large-128k-online'), // Best for URL extraction and web content
      messages: formattedMessages,
      temperature: 0.7,
      maxTokens: 2000,
    });

    // Return streaming response using the correct AI SDK method
    return result.toDataStreamResponse({
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
      }
    });

  } catch (error) {
    console.error('Error in Perplexity AI SDK route:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 }
    );
  }
}
