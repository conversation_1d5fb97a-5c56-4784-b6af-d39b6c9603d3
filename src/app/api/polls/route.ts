/**
 * Example API Route with Performance Optimizations
 *
 * Demonstrates integration of all performance optimization services:
 * - Rate limiting
 * - Database connection pooling
 * - Caching with Redis
 * - Performance monitoring
 * - Background job processing
 */

import { NextRequest, NextResponse } from 'next/server';
import { withRateLimit, userIdentifier } from '@/lib/middleware/rate-limit';
import { PerformanceMonitor } from '@/lib/services/performance-monitor';
import { redisCacheService } from '@/lib/services/redis-cache';
import { Poll } from '@/lib/validation/schemas';
import { DatabasePaginator } from '@/lib/services/database-paginator';

// Get the singleton instance of PerformanceMonitor
const performanceMonitor = PerformanceMonitor.getInstance();

// Type definitions for cached results
interface CachedResult {
  data: Poll[];
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * GET /api/polls - Fetch polls with performance optimizations
 */
async function handleGetPolls(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    // Build cache key
    const cacheKey = `polls:list:${page}:${pageSize}:${sortBy}:${sortOrder}:${category || 'all'}:${search || 'none'}`;

    // Try to get from cache first
    const cachedResult = await redisCacheService.get(cacheKey);
    if (cachedResult) {
      console.log('Cache hit for polls list');

      // Record cache hit metric
      await performanceMonitor.recordMetric('cache_hits', 1);

      const cachedData = JSON.parse(cachedResult as string) as CachedResult;
      return NextResponse.json({
        ...cachedData,
        meta: {
          ...cachedData.meta,
          cached: true,
          responseTime: Date.now() - startTime
        }
      });
    }

    // If not in cache, fetch from database
    const result = await DatabasePaginator.paginateWithOffset('polls', {
      page,
      pageSize,
      sortBy,
      sortOrder,
      filters: {
        ...(category ? { category } : {}),
        ...(search ? { title: `%${search}%` } : {})
      },
      cacheKey: `polls:${category || 'all'}:${search || 'none'}`
    });

    // Record database query metric
    await performanceMonitor.recordMetric('database_queries', 1);
    await performanceMonitor.recordMetric('query_duration', Date.now() - startTime);

    // Cache the result
    await redisCacheService.set(cacheKey, JSON.stringify(result), 60 * 5); // Cache for 5 minutes

    return NextResponse.json({
      ...result,
      meta: {
        ...result.meta,
        responseTime: Date.now() - startTime
      }
    });
  } catch (error) {
    console.error('Error fetching polls:', error);
    await performanceMonitor.recordMetric('errors', 1, { type: 'database' });
    return NextResponse.json(
      { error: 'Failed to fetch polls', details: (error as Error).message },
      { status: 500 }
    );
  }
}

// Stub handlers for other HTTP methods
async function handleCreatePoll(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Not implemented' },
    { status: 501 }
  );
}

async function handleDeletePoll(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Not implemented' },
    { status: 501 }
  );
}

// Export rate-limited handlers
export const GET = withRateLimit(handleGetPolls, {
  configName: 'moderate',
  identifier: userIdentifier
});

export const POST = withRateLimit(handleCreatePoll, {
  configName: 'strict',
  identifier: userIdentifier,
  onLimitReached: async (identifier: string, req: NextRequest) => {
    console.log(`Rate limit reached for ${identifier} on ${req.url}`);
  }
});

export const DELETE = withRateLimit(handleDeletePoll, {
  configName: 'strict',
  identifier: userIdentifier
});
