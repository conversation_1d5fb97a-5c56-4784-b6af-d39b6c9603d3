import { NextRequest, NextResponse } from 'next/server';
import { extractContentFromUrl, extractContentFromWebsite } from '@/lib/services/content-extractor';

/**
 * API handler for extracting content from URLs and websites
 */
export async function POST(req: NextRequest) {
  try {
    const { url, type } = await req.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Add special handling for Airtable URLs
    if (url.includes('airtable.com')) {
      return NextResponse.json({
        content: `This appears to be an Airtable URL. Airtable typically requires authentication and restricts content extraction. Please consider:
        1. Using the "Share" button in Airtable to create a public view link
        2. Copying and pasting the relevant content directly into the poll description
        3. Exporting your Airtable data to a format like CSV and extracting content from that file`,
        warning: 'Airtable content extraction limited'
      });
    }

    let content = '';

    // Set timeout to handle long-running extractions
    const extractionPromise = type === 'website'
      ? extractContentFromWebsite(url)
      : extractContentFromUrl(url);

    // Create a timeout promise
    const timeoutPromise = new Promise<string>((_, reject) => {
      setTimeout(() => reject(new Error('Content extraction timed out')), 25000); // 25 second timeout
    });

    // Race the extraction against the timeout
    try {
      content = await Promise.race([extractionPromise, timeoutPromise]);
    } catch (error) {
      if (error instanceof Error && error.message.includes('timed out')) {
        return NextResponse.json({
          content: `The content extraction process took too long and timed out. This may happen with complex websites or sites that restrict automated access. You could try:
          1. Using a different URL with similar content
          2. Copying the text directly into the poll description
          3. Trying again later`,
          warning: 'Extraction timed out'
        });
      } else {
        throw error; // Re-throw for the outer catch block
      }
    }

    // Return the extracted content
    return NextResponse.json({ content });
  } catch (error: unknown) {
    console.error('Error extracting content:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

    // Provide more helpful error messages based on common failures
    let userFriendlyMessage = 'Failed to extract content from the provided URL.';

    if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('ENOTFOUND')) {
      userFriendlyMessage = 'Unable to connect to the website. The site may be down or blocking our requests.';
    } else if (errorMessage.includes('navigation') || errorMessage.includes('timeout')) {
      userFriendlyMessage = 'The website took too long to respond. It might be too large or complex to process.';
    } else if (errorMessage.includes('Permission denied') || errorMessage.includes('403')) {
      userFriendlyMessage = 'This website restricts access to automated tools. Try copying content manually instead.';
    }

    return NextResponse.json(
      {
        error: 'Failed to extract content',
        message: userFriendlyMessage,
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

// For file uploads
export async function PUT() {
  return NextResponse.json(
    { error: 'File uploads should use the /api/extract-file endpoint with multipart/form-data' },
    { status: 400 }
  );
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};