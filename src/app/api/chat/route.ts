import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

// Define the system prompt for poll creation
const SYSTEM_PROMPT = `You are PollGPT, an AI assistant that specializes in helping users create polls.
Your goal is to understand the user's needs and help them create an effective poll.

Ask meaningful follow-up questions to understand:
- The topic of their poll
- Their target audience
- The purpose of the poll
- Any specific questions they want to include

Once you have enough information, offer to generate a complete poll with appropriate questions.
When the user is ready, respond with a JSON object containing the poll details in this format:
{
  "title": "Poll title",
  "description": "Brief description of the poll purpose",
  "questions": [
    {
      "text": "Question text",
      "type": "single|multiple|open",
      "options": ["Option 1", "Option 2"],
      "required": true
    }
  ]
}

Keep your responses conversational, helpful, and focused on creating an effective poll.`;

// Define the message type for better type safety
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export async function POST(req: NextRequest) {
  try {
    // Extract the messages and model from the request
    const { messages, model } = await req.json() as {
      messages: ChatMessage[],
      model: string
    };

    // Validate messages
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages are required and must be an array' },
        { status: 400 }
      );
    }

    // Select the appropriate API based on the model
    // Note: We're only using Mistral and Gemini for chat interface
    // Perplexity is only used for poll generation, not for chat

    // Instead of relying on request headers, use environment variables or fixed production URLs
    // This avoids issues with Vercel Edge runtime and request header inconsistencies
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.pollgpt.com';
    // Extract base URL from NEXT_PUBLIC_SITE_URL or use the fixed URL
    const baseUrl = siteUrl;

    let apiUrl;
    if (model === 'mistral') {
      apiUrl = `${baseUrl}/api/mistral`;
    } else if (model === 'gemini') {
      apiUrl = `${baseUrl}/api/gemini`;
    } else if (model === 'perplexity') {
      apiUrl = `${baseUrl}/api/perplexity`;
    } else {
      // Default to Mistral if no model is specified
      apiUrl = `${baseUrl}/api/mistral`;
    }

    // Add system prompt if not already present, but keep it small for memory efficiency
    if (!messages.some(msg => msg.role === 'system')) {
      messages.unshift({ role: 'system', content: SYSTEM_PROMPT });
    }

    // Limit the number of messages to save memory
    const limitedMessages = (messages.length > 10)
      ? [...messages.filter(msg => msg.role === 'system'),
         ...messages.filter(msg => msg.role !== 'system').slice(-9)]
      : messages;

    // Call the selected API endpoint with reduced timeout
    console.log(`Routing chat request to ${apiUrl}`);

    // Add more efficient timeout and retry logic
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10-second timeout

    let attempts = 0;
    const maxAttempts = 2; // Reduced attempts to save resources
    let response;

    while (attempts < maxAttempts) {
      try {
        response = await fetch(apiUrl, {
          signal: controller.signal,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: limitedMessages,
        model: model,
        stream: true
      })
    });

        // If successful, break the retry loop
        if (response.ok) break;

        // If we got a 500 error, retry
        attempts++;
        console.log(`Attempt ${attempts}/${maxAttempts} failed with status ${response.status}`);

        if (attempts < maxAttempts) {
          // Wait before retrying (exponential backoff)
          await new Promise(r => setTimeout(r, 1000 * Math.pow(2, attempts)));
        }
      } catch (fetchError) {
        attempts++;
        console.error(`Fetch error on attempt ${attempts}/${maxAttempts}:`, fetchError);

        if (attempts >= maxAttempts) {
          clearTimeout(timeoutId);
          throw fetchError;
        }

        // Wait before retrying
        await new Promise(r => setTimeout(r, 1000 * Math.pow(2, attempts)));
      }
    }

    // Ensure response is defined before continuing
    if (!response) {
      clearTimeout(timeoutId);
      throw new Error('Failed to get a response after multiple attempts');
    }

    // Clear the timeout since we got a response
    clearTimeout(timeoutId);

    // Check if the response is successful
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status}`);
    }

    // Log response headers for debugging
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    // If not streaming, handle the JSON response
    if (!response.body) {
      return NextResponse.json(
        { error: 'No streaming response body' },
        { status: 500 }
      );
    }

    // Return the streaming response
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'X-Content-Type-Options': 'nosniff',
      }
    });
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 }
    );
  }
}
