import { NextResponse } from 'next/server';

// Function to generate dynamic sitemap content
function generateSitemapXML(): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://pollgpt.com';
  const lastMod = new Date().toISOString();

  // Enhanced routes with more comprehensive SEO-focused landing pages and priorities
  const routes = [
    { url: baseUrl, lastmod: lastMod, changefreq: 'daily', priority: 1 },
    { url: `${baseUrl}/pollgpt`, lastmod: lastMod, changefreq: 'daily', priority: 0.9 },
    { url: `${baseUrl}/poll-gpt`, lastmod: lastMod, changefreq: 'daily', priority: 0.9 },
    { url: `${baseUrl}/free-ai-polls`, lastmod: lastMod, changefreq: 'weekly', priority: 0.8 },
    { url: `${baseUrl}/ai-poll-generator`, lastmod: lastMod, changefreq: 'weekly', priority: 0.8 },
    { url: `${baseUrl}/poll-gpt-login`, lastmod: lastMod, changefreq: 'weekly', priority: 0.7 },
    { url: `${baseUrl}/login`, lastmod: lastMod, changefreq: 'monthly', priority: 0.6 },
    { url: `${baseUrl}/register`, lastmod: lastMod, changefreq: 'monthly', priority: 0.6 },
    { url: `${baseUrl}/dashboard`, lastmod: lastMod, changefreq: 'weekly', priority: 0.8 }
  ];

  // Create XML sitemap
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" ';
  xml += 'xmlns:news="http://www.google.com/schemas/sitemap-news/0.9" ';
  xml += 'xmlns:xhtml="http://www.w3.org/1999/xhtml" ';
  xml += 'xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0" ';
  xml += 'xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" ';
  xml += 'xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">\n';

  // Add each URL entry
  for (const route of routes) {
    xml += '  <url>\n';
    xml += `    <loc>${route.url}</loc>\n`;
    xml += `    <lastmod>${route.lastmod}</lastmod>\n`;
    xml += `    <changefreq>${route.changefreq}</changefreq>\n`;
    xml += `    <priority>${route.priority}</priority>\n`;
    xml += '  </url>\n';
  }

  xml += '</urlset>';
  return xml;
}

export async function GET() {
  try {
    // Generate fresh sitemap content
    const sitemapContent = generateSitemapXML();

    // Return the sitemap with the correct content type and additional headers
    return new NextResponse(sitemapContent, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        'X-Content-Type-Options': 'nosniff', // Prevent MIME type sniffing
        'X-Robots-Tag': 'all', // Ensure search engines can access the sitemap
      }
    });
  } catch (error) {
    console.error('Error serving sitemap:', error);
    return new NextResponse('Error serving sitemap', { status: 500 });
  }
}
