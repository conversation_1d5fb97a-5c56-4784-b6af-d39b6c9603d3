import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';
import { streamText } from 'ai';
import { aiProviders } from '@/lib/ai/providers';
import { SimulationResponse } from '@/lib/types/simulation';
import { getFinalFeatureFlags } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Rate limiting for streaming insights
const streamRateLimitMap = new Map<string, { count: number; resetTime: number }>();

const STREAM_RATE_LIMITS = {
  requests: 30, // 30 streaming requests per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

function checkStreamRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `stream_insights_${userId}`;
  const userLimit = streamRateLimitMap.get(key);

  if (!userLimit || now > userLimit.resetTime) {
    streamRateLimitMap.set(key, {
      count: 1,
      resetTime: now + STREAM_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: STREAM_RATE_LIMITS.requests - 1 };
  }

  if (userLimit.count >= STREAM_RATE_LIMITS.requests) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, remaining: 0, retryAfter };
  }

  userLimit.count++;
  const remaining = STREAM_RATE_LIMITS.requests - userLimit.count;
  return { allowed: true, remaining };
}

export async function POST(request: NextRequest) {
  try {
    console.log('AI SDK streaming simulation insights endpoint called');

    // Parse request body
    const body = await request.json();
    const {
      simulationData,
      analysisType = 'comprehensive',
      focusAreas = [],
      pollQuestion,
      pollOptions
    } = body;

    if (!simulationData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing simulationData in request'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check feature flags
    const flags = getFinalFeatureFlags();
    if (!flags.streamingInsights) {
      return NextResponse.json(
        {
          success: false,
          error: 'Streaming insights feature is not enabled'
        },
        { status: 503 }
      );
    }

    // Check rate limits
    const rateLimit = checkStreamRateLimit(userId);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded for streaming insights',
          retryAfter: rateLimit.retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimit.retryAfter?.toString() || '3600',
            'X-RateLimit-Limit': STREAM_RATE_LIMITS.requests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          }
        }
      );
    }

    console.log('Processing streaming insights request:', {
      analysisType,
      focusAreasCount: focusAreas.length,
      userId: userId,
      hasSimulationData: !!simulationData
    });

    // Build streaming analysis prompt
    const prompt = buildStreamingInsightsPrompt(
      simulationData,
      analysisType,
      focusAreas,
      pollQuestion,
      pollOptions
    );

    // Create streaming response
    const result = await streamText({
      model: aiProviders.analysis,
      prompt,
      temperature: 0.3,
      maxTokens: 2000,
    });

    // Return streaming response
    return new Response(result.toDataStream(), {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'X-RateLimit-Limit': STREAM_RATE_LIMITS.requests.toString(),
        'X-RateLimit-Remaining': rateLimit.remaining.toString(),
        'Access-Control-Allow-Origin': '*',
      },
    });

  } catch (error) {
    console.error('Error in streaming simulation insights:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during streaming insights',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

function buildStreamingInsightsPrompt(
  simulationData: SimulationResponse | SimulationResponse[],
  analysisType: string,
  focusAreas: string[],
  pollQuestion?: string,
  pollOptions?: string[]
): string {
  const isMultipleSimulations = Array.isArray(simulationData);

  let dataContext = '';
  if (isMultipleSimulations) {
    dataContext = `MULTIPLE SIMULATION RESULTS:
${simulationData.map((sim, i) => `
Simulation ${i + 1} - ${sim.metadata.demographic}:
- Sample Size: ${sim.metadata.sampleSize}
- Distribution: ${JSON.stringify(sim.results.distribution)}
- Confidence: ${(sim.metadata.confidence * 100).toFixed(1)}%
`).join('')}`;
  } else {
    dataContext = `SIMULATION RESULT:
- Demographic: ${simulationData.metadata.demographic}
- Sample Size: ${simulationData.metadata.sampleSize}
- Distribution: ${JSON.stringify(simulationData.results.distribution)}
- Confidence: ${(simulationData.metadata.confidence * 100).toFixed(1)}%
- Analysis: ${simulationData.results.analysis}`;
  }

  const contextInfo = pollQuestion && pollOptions ? `
POLL CONTEXT:
Question: "${pollQuestion}"
Options: ${pollOptions.join(', ')}
` : '';

  const focusContext = focusAreas.length > 0 ? `
FOCUS AREAS: ${focusAreas.join(', ')}
` : '';

  return `You are an expert data analyst specializing in poll simulation insights. Provide ${analysisType} analysis of the following simulation results.

${contextInfo}
${dataContext}
${focusContext}

ANALYSIS TYPE: ${analysisType}

Provide streaming insights that include:

1. **Key Findings**: Most significant patterns and trends
2. **Statistical Insights**: What the numbers reveal about voter behavior
3. **Demographic Patterns**: How different groups respond differently
4. **Confidence Assessment**: Reliability and limitations of the data
5. **Strategic Implications**: What this means for decision-makers
6. **Actionable Recommendations**: Specific next steps based on the data

${isMultipleSimulations ? `
7. **Cross-Demographic Analysis**: Compare and contrast between groups
8. **Consensus vs Polarization**: Identify areas of agreement and division
` : ''}

Format your response as clear, actionable insights with specific data points and percentages when relevant. Stream the analysis in real-time as you process the information.`;
}
