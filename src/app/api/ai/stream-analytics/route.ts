import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Type definitions for streaming analytics
interface PollOption {
  id: string;
  text: string;
  votes: number;
}

interface PollQuestion {
  id: string;
  text: string;
  type: string;
  options: PollOption[];
}

interface PollData {
  id: string;
  title: string;
  description?: string;
  questions: PollQuestion[];
  totalResponses: number;
  createdAt: string;
  status: string;
}

interface StreamAnalyticsRequest {
  pollData: PollData;
  analysisType?: string;
}

/**
 * API route for streaming real-time poll analytics
 * Provides continuous AI-powered insights as poll data changes
 */
export async function POST(req: NextRequest) {
  try {
    const { pollData, analysisType = 'comprehensive' }: StreamAnalyticsRequest = await req.json();

    if (!pollData) {
      return new Response('Poll data is required', { status: 400 });
    }

    // Check if streaming analytics is enabled
    const isEnabled = await FeatureGate.isEnabled('streamingAnalytics');
    if (!isEnabled) {
      return new Response('Streaming analytics is not enabled', { status: 403 });
    }

    const stream = await streamText({
      model: aiProviders.contentExtraction, // Using Mistral for analytics
      prompt: buildAnalyticsPrompt(pollData, analysisType),
      temperature: 0.3,
      maxTokens: 1000,
    });

    return stream.toDataStreamResponse();

  } catch (error) {
    console.error('Streaming analytics error:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to start analytics stream',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Build optimized prompt for streaming analytics
 */
function buildAnalyticsPrompt(pollData: PollData, analysisType: string): string {
  const basePrompt = `
You are an expert data analyst providing real-time insights on poll performance and response patterns.

Poll Data:
${JSON.stringify(pollData, null, 2)}

Analysis Type: ${analysisType}

Provide streaming insights focusing on:
`;

  switch (analysisType) {
    case 'performance':
      return basePrompt + `
- Response rate trends and completion patterns
- Drop-off points and user engagement metrics
- Question performance and optimization opportunities
- Time-based response patterns
- Overall poll effectiveness

Stream your analysis as you discover insights, focusing on actionable recommendations.`;

    case 'demographics':
      return basePrompt + `
- Demographic response patterns and correlations
- Age, gender, and location-based insights
- Professional background influences
- Behavioral segmentation opportunities
- Cross-demographic comparisons and trends

Analyze demographic patterns and provide insights on audience engagement.`;

    case 'trends':
      return basePrompt + `
- Emerging response trends and pattern recognition
- Temporal analysis of response changes
- Sentiment evolution over time
- Popular response combinations
- Predictive insights for future responses

Focus on identifying and explaining significant trends in real-time.`;

    case 'sentiment':
      return basePrompt + `
- Sentiment analysis of open-ended responses
- Emotional tone and satisfaction indicators
- Positive/negative sentiment trends
- Response quality and engagement levels
- Mood and attitude patterns

Provide real-time sentiment insights and emotional intelligence.`;

    case 'comprehensive':
    default:
      return basePrompt + `
- Overall poll performance and effectiveness
- Key response patterns and insights
- Demographic trends and correlations
- Engagement metrics and optimization opportunities
- Actionable recommendations for improvement

Provide a comprehensive real-time analysis covering all major aspects of poll performance.`;
  }
}

/**
 * GET endpoint for checking analytics status
 */
export async function GET() {
  const isEnabled = await FeatureGate.isEnabled('streamingAnalytics');

  return Response.json({
    enabled: isEnabled,
    features: ['real-time', 'demographics', 'trends', 'sentiment', 'performance'],
    timestamp: new Date().toISOString(),
  });
}
