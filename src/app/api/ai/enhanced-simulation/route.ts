import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';
import { SimulationRequest, SimulationResponse } from '@/lib/types/simulation';
import { enhancedSimulationEngine } from '@/lib/ai/simulation-engine';
import { simulatePoll } from '@/lib/services/perplexity-ai'; // Fallback
import { FeatureGate } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Rate limiting for enhanced simulations
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

const ENHANCED_SIMULATION_RATE_LIMITS = {
  requests: 15, // 15 enhanced simulations per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

function checkRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `enhanced_simulation_${userId}`;
  const userLimit = rateLimitMap.get(key);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset the limit
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + ENHANCED_SIMULATION_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: ENHANCED_SIMULATION_RATE_LIMITS.requests - 1 };
  }

  if (userLimit.count >= ENHANCED_SIMULATION_RATE_LIMITS.requests) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, remaining: 0, retryAfter };
  }

  userLimit.count++;
  const remaining = ENHANCED_SIMULATION_RATE_LIMITS.requests - userLimit.count;
  return { allowed: true, remaining };
}

export async function POST(request: NextRequest) {
  try {
    console.log('Enhanced AI SDK simulation endpoint called');

    // Parse request body
    const body = await request.json();
    console.log('Request body received:', Object.keys(body));

    // Validate required fields
    const {
      pollQuestion,
      pollOptions,
      demographic,
      responseFormat = 'distribution',
      specialInstructions,
      pollContext,
      pollId,
      questionId,
      useEnhancedEngine = true // New flag to opt into AI SDK enhancement
    } = body;

    if (!pollQuestion || !pollOptions || !demographic) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: pollQuestion, pollOptions, or demographic'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check rate limits for enhanced simulations
    const rateLimit = checkRateLimit(userId);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded for enhanced simulations',
          retryAfter: rateLimit.retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimit.retryAfter?.toString() || '3600',
            'X-RateLimit-Limit': ENHANCED_SIMULATION_RATE_LIMITS.requests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          }
        }
      );
    }

    // Prepare the simulation request
    const simulationRequest: SimulationRequest = {
      pollQuestion,
      pollOptions,
      demographic: {
        group: demographic.group,
        size: demographic.size,
        context: demographic.context
      },
      responseFormat,
      specialInstructions,
      pollContext
    };

    console.log('Processing enhanced simulation request:', {
      question: pollQuestion,
      optionsCount: pollOptions.length,
      demographic: demographic.group,
      sampleSize: demographic.size,
      userId: userId,
      pollId: pollId,
      questionId: questionId,
      useEnhancedEngine,
      enhancedSimulationEnabled: FeatureGate.isEnabled('enhancedSimulation')
    });

    let simulationResponse: SimulationResponse;

    // Choose simulation engine based on feature flags and user preference
    if (useEnhancedEngine && FeatureGate.isEnabled('enhancedSimulation')) {
      console.log('Using AI SDK Enhanced Simulation Engine');

      try {
        // Use the new AI SDK enhanced simulation engine
        simulationResponse = await enhancedSimulationEngine.runPollSimulation(simulationRequest);

        // Mark response as enhanced
        console.log('Enhanced simulation completed successfully');
      } catch (enhancedError) {
        console.warn('Enhanced simulation failed, falling back to standard simulation:', enhancedError);

        // Fallback to existing simulation system
        simulationResponse = await simulatePoll(simulationRequest);
        console.log('Standard simulation fallback completed');
      }

    } else {
      console.log('Using standard simulation engine');

      // Use existing simulation system
      simulationResponse = await simulatePoll(simulationRequest);
    }

    // Additional fallback for critical failures
    if (!simulationResponse) {
      console.error('Both enhanced and standard simulation failed, using emergency fallback');
      simulationResponse = await simulatePoll(simulationRequest);
    }

    // Store simulation if pollId is provided
    if (pollId) {
      try {
        // Check if poll exists and get owner's ID
        const { data: pollData, error: pollError } = await supabase
          .from('polls')
          .select('user_id')
          .eq('id', pollId)
          .single();

        if (pollError) {
          console.error('Error fetching poll:', pollError);
        } else if (pollData.user_id !== userId) {
          console.warn('User does not own this poll, skipping storage');
        } else {
          // Store the simulation result
          const { error: insertError } = await supabase
            .from('poll_simulations')
            .insert({
              poll_id: pollId,
              question_id: questionId,
              demographic_group: demographic.group,
              sample_size: demographic.size,
              simulation_data: simulationResponse,
              confidence_score: simulationResponse.metadata.confidence || 0.75,
              citations: simulationResponse.metadata.citations || [],
              created_by: userId
            });

          if (insertError) {
            console.error('Error storing enhanced simulation:', insertError);
            // Don't fail the request, just log the error
          } else {
            console.log('Enhanced simulation stored successfully');
          }
        }
      } catch (storageError) {
        console.error('Error in simulation storage:', storageError);
        // Don't fail the request
      }
    }

    // Return successful response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        simulation: simulationResponse,
        enhanced: useEnhancedEngine && FeatureGate.isEnabled('enhancedSimulation'),
        metadata: {
          engine: useEnhancedEngine && FeatureGate.isEnabled('enhancedSimulation') ? 'ai-sdk-enhanced' : 'standard',
          userId: userId,
          rateLimit: {
            remaining: rateLimit.remaining,
            resetTime: new Date(Date.now() + ENHANCED_SIMULATION_RATE_LIMITS.windowMs)
          }
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': ENHANCED_SIMULATION_RATE_LIMITS.requests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
        }
      }
    );

  } catch (error) {
    console.error('Error in enhanced simulation endpoint:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during enhanced simulation',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

// Handle batch simulations
export async function PUT(request: NextRequest) {
  try {
    console.log('Enhanced batch simulation endpoint called');

    const body = await request.json();
    const {
      pollQuestion,
      pollOptions,
      demographics,
      pollId
    } = body;

    if (!pollQuestion || !pollOptions || !demographics || demographics.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields for batch simulation'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check rate limits (batch simulations count as multiple requests)
    const rateLimit = checkRateLimit(userId);
    const requiredRequests = demographics.length;

    if (rateLimit.remaining < requiredRequests) {
      return NextResponse.json(
        {
          success: false,
          error: `Batch simulation requires ${requiredRequests} requests, but only ${rateLimit.remaining} remaining`,
          retryAfter: rateLimit.retryAfter
        },
        { status: 429 }
      );
    }

    // Check if enhanced batch processing is enabled
    if (!FeatureGate.isEnabled('batchSimulationProcessing')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Enhanced batch simulation processing is not enabled'
        },
        { status: 503 }
      );
    }

    console.log(`Processing enhanced batch simulation for ${demographics.length} demographics`);

    // Run enhanced batch simulation
    const batchResult = await enhancedSimulationEngine.runBatchSimulation(
      demographics,
      pollQuestion,
      pollOptions,
      (progress) => {
        console.log(`Batch simulation progress: ${(progress * 100).toFixed(1)}%`);
      }
    );

    // Store batch result if pollId provided
    if (pollId) {
      try {
        const { error: batchStoreError } = await supabase
          .from('poll_simulations')
          .insert({
            poll_id: pollId,
            demographic_group: 'batch_simulation',
            sample_size: batchResult.metadata.totalSampleSize,
            simulation_data: batchResult,
            confidence_score: batchResult.metadata.averageConfidence,
            citations: [],
            created_by: userId
          });

        if (batchStoreError) {
          console.error('Error storing batch simulation:', batchStoreError);
        }
      } catch (error) {
        console.error('Error in batch storage:', error);
      }
    }

    return NextResponse.json({
      success: true,
      batchResult,
      metadata: {
        engine: 'ai-sdk-enhanced-batch',
        userId: userId,
        processedDemographics: demographics.length
      }
    });

  } catch (error) {
    console.error('Error in enhanced batch simulation:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during batch simulation',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
