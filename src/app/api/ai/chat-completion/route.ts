import { Message, streamText, generateText, createDataStreamResponse } from 'ai';
import { FeatureGate } from '@/lib/ai/feature-flags';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { withFallback } from '@/lib/ai/fallback-service';
import { conversationalTools } from '@/lib/ai/conversational-tools';

/**
 * Unified chat completion API route with AI SDK integration
 * Supports multiple models with fallbacks and streaming
 * Now includes human-in-the-loop tool calling for poll creation
 */
export async function POST(req: NextRequest) {
  try {
    const { messages, preferredModel, context, features } = await req.json();

    // Select provider based on preference with fallbacks
    let provider;
    switch (preferredModel) {
      case 'gemini':
        provider = aiProviders.chatGemini; // Using gemini-1.5-pro
        break;
      case 'mistral':
        provider = aiProviders.chatMistral; // Using mistral-large-2411
        break;
      default:
        // Default to mistral for conversational
        provider = aiProviders.chatMistral;
    }

    // Check if structured output is requested
    const needsStructuredOutput = features?.includes('structured-output') || features?.includes('json-extraction');

    // Using structuredGeneration as a proxy for unified chat completion feature
    return await FeatureGate.whenEnabledAsync(
      'structuredGeneration',
      // AI SDK approach with tool calling
      async () => {
        return withFallback(
          async () => {
            // Build system message based on context
            const systemMessage = buildSystemMessage(context, features);

            // Prepare messages for AI SDK format with validation to prevent empty messages
            const formattedMessages = [
              { role: 'system', content: systemMessage },
              ...messages.filter(msg => msg.content && msg.content.trim() !== '') // Filter out empty messages
            ] as Message[];
            
            // Log message count for debugging
            console.log(`Processing ${formattedMessages.length} messages (${messages.length} original, filtered out ${messages.length - (formattedMessages.length - 1)} empty messages)`);

            // For conversational poll creation, use streaming with tools
            if (context === 'poll-creation') {
              return createDataStreamResponse({
                execute: async dataStream => {
                  // Process tool confirmations if they exist in the last message
                  const processedMessages = await processToolConfirmations(formattedMessages);

                  const result = streamText({
                    model: provider,
                    messages: processedMessages,
                    tools: conversationalTools,
                    temperature: 0.7,
                    maxTokens: 2048,
                    maxSteps: 5, // Allow multiple tool calls
                    onError: ({ error }) => {
                      console.error('StreamText error:', error);
                    }
                  });

                  result.mergeIntoDataStream(dataStream);
                }
              });
            }

            if (needsStructuredOutput) {
              // For structured output, use generateText and return JSON
              const result = await generateText({
                model: provider,
                messages: formattedMessages,
                temperature: 0.7,
                maxTokens: 2048
              });

              // Return JSON response for structured output
              return new Response(JSON.stringify({
                content: result.text,
                role: 'assistant'
              }), {
                headers: { 'Content-Type': 'application/json' }
              });
            } else {
              // For regular conversation, use streaming
              const result = streamText({
                model: provider,
                messages: formattedMessages,
                temperature: 0.7,
                maxTokens: 2048,
                onError: ({ error }) => {
                  console.error('StreamText error:', error);
                }
              });

              // Use the proper streaming response method with headers
              return result.toDataStreamResponse({
                headers: {
                  'Transfer-Encoding': 'chunked',
                  'Connection': 'keep-alive'
                }
              });
            }
          },
          // Fallback to existing endpoints
          async () => {
            return legacyFallback(preferredModel, messages);
          },
          'Unified Chat Completion'
        );
      },
      // Feature disabled: Use existing logic
      async () => {
        return legacyFallback(preferredModel, messages);
      }
    );
  } catch (error) {
    console.error('Chat completion error:', error);

    // Return a proper error response in the expected format
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal Server Error',
        type: 'error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

/**
 * Build system message based on context and features
 */
function buildSystemMessage(context: string, features: string[] = []): string {
  let systemMessage = '';

  // Base system message for poll creation
  if (context === 'poll-creation') {
    systemMessage = `You are PollGPT, an AI assistant specialized in helping users create effective polls and surveys through conversation.

Your personality:
- Friendly, helpful, and enthusiastic about creating great polls
- Ask clarifying questions to understand user needs better
- Provide suggestions and explain your reasoning
- Be conversational and engaging

Your process:
1. UNDERSTAND the user's needs through conversation
2. GATHER information about their topic, audience, and purpose
3. SUGGEST a poll structure when you have enough information
4. WAIT for user confirmation before proceeding

IMPORTANT RULES:
- NEVER create polls automatically - always use the suggestPollCreation tool and wait for user approval
- Use analyzeConversationContext to understand what the user wants
- Use gatherPollRequirements when you need more specific information
- Be specific about what information you need
- Suggest poll structures that match the user's goals
- Explain why you're recommending certain question types or structures

Available tools:
- analyzeConversationContext: Understand the conversation context (auto-executes)
- gatherPollRequirements: Request specific information when needed
- suggestPollCreation: Propose a complete poll structure (requires user confirmation)

Example conversation flow:
1. User expresses interest in creating a poll
2. You ask clarifying questions about topic, audience, purpose
3. When you have enough info, you suggest a poll structure
4. User can accept, reject, or request modifications
5. Only after acceptance do you indicate they can create the poll

Remember: Your goal is to help create polls that serve the user's specific needs, not just any poll.`;

    // Add feature-specific instructions
    if (features?.includes('structured-output')) {
      systemMessage += `\n\nWhen appropriate, use the tools to structure your responses and provide actionable suggestions.`;
    }
  } else {
    // Default system message
    systemMessage = `You are PollGPT, an AI assistant that helps users create and analyze polls and surveys.`;
  }

  return systemMessage;
}

/**
 * Legacy fallback to existing endpoints
 */
async function legacyFallback(model: string, messages: { role: string, content: string }[]) {
  // Choose API endpoint based on selected model
  const apiEndpoint = model === 'gemini' ? '/api/gemini' :
                     model === 'mistral' ? '/api/mistral' :
                     '/api/chat';

  // Call the appropriate API
  const response = await fetch(new URL(apiEndpoint, process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      messages,
      model
    })
  });

  // Return the response as is
  return response;
}

/**
 * Process tool confirmations from user messages
 * Handles the human-in-the-loop pattern for poll creation
 */
async function processToolConfirmations(messages: Message[]): Promise<Message[]> {
  const lastMessage = messages[messages.length - 1];
  const parts = lastMessage.parts;

  if (!parts) return messages;

  const processedParts = await Promise.all(
    parts.map(async part => {
      // Only process tool invocations parts that are in 'result' state
      if (part.type !== 'tool-invocation') return part;

      const { toolInvocation } = part;

      // Only continue if it's in a 'result' state (meaning user has responded)
      if (toolInvocation.state !== 'result') return part;

      let result;
      const toolName = toolInvocation.toolName;

      // Handle different tool confirmations
      switch (toolName) {
        case 'suggestPollCreation':
          if (toolInvocation.result === 'Yes, create this poll') {
            // Poll was confirmed - we could trigger actual poll creation here
            // For now, just return success message
            result = 'Poll suggestion confirmed. The user is ready to create this poll with the suggested structure.';
          } else if (toolInvocation.result === 'No, let me refine the requirements') {
            result = 'Poll suggestion rejected. The user wants to refine the requirements further.';
          } else {
            result = `User response: ${toolInvocation.result}`;
          }
          break;

        case 'gatherPollRequirements':
          // Requirements gathering was acknowledged
          result = 'Requirements gathering completed. User will provide the requested information.';
          break;

        default:
          result = `Tool ${toolName} confirmation received: ${toolInvocation.result}`;
      }

      // Tool confirmation processed
      // Note: In a full implementation, this would trigger actual poll creation

      // Return updated toolInvocation with the processed result
      return {
        ...part,
        toolInvocation: {
          ...toolInvocation,
          result,
        },
      };
    }),
  );

  // Return the processed messages
  return [...messages.slice(0, -1), { ...lastMessage, parts: processedParts }];
}
