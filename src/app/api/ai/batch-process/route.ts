/**
 * Phase 2: Batch Document Processing API
 * Handle multiple document processing with queue management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBatchProcessor } from '@/lib/ai/batch-processor';
import type { DocumentInput, BatchProcessingOptions } from '@/lib/ai/batch-processor';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const maxDuration = 300; // 5 minutes for batch operations

/**
 * POST: Submit batch processing job
 */
export async function POST(req: NextRequest) {
  try {
    // Check if batch processing is enabled
    if (!FeatureGate.isEnabled('batchProcessing')) {
      return NextResponse.json(
        { error: 'Batch processing is not enabled' },
        { status: 403 }
      );
    }

    const formData = await req.formData();
    const files = formData.getAll('files') as File[];
    const optionsJson = formData.get('options') as string;

    if (files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Parse options
    let options: BatchProcessingOptions = {};
    if (optionsJson) {
      try {
        options = JSON.parse(optionsJson);    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const _ = error; // Acknowledge the error variable
      return NextResponse.json(
          { error: 'Invalid options JSON' },
          { status: 400 }
        );
      }
    }

    // Convert files to DocumentInput
    const documents: DocumentInput[] = [];
    for (const file of files) {
      const buffer = Buffer.from(await file.arrayBuffer());
      documents.push({
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        buffer,
        mimeType: file.type,
        size: file.size
      });
    }

    // Submit batch job
    const jobId = await getBatchProcessor().submitBatch(documents, {
      ...options,
      includeNLP: options.includeNLP ?? true,
      includeSentiment: options.includeSentiment ?? true,
      includeTopics: options.includeTopics ?? true,
      includePollSuggestions: options.includePollSuggestions ?? true,
      includeQuality: options.includeQuality ?? true
    });

    return NextResponse.json({
      jobId,
      message: 'Batch processing job submitted successfully',
      documentCount: documents.length,
      estimatedTime: `${documents.length * 2}-${documents.length * 5} minutes`
    });

  } catch (error) {
    console.error('Batch processing submission error:', error);

    return NextResponse.json(
      {
        error: 'Failed to submit batch processing job',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET: Retrieve batch processing status or results
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const jobId = searchParams.get('jobId');
    const includeResults = searchParams.get('includeResults') === 'true';

    if (!jobId) {
      // Return queue statistics
      const stats = await getBatchProcessor().getQueueStats();
      return NextResponse.json({
        queueStats: stats,
        timestamp: new Date().toISOString()
      });
    }

    if (includeResults) {
      // Return full job results
      const results = await getBatchProcessor().getBatchResults(jobId);
      if (!results) {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        job: results,
        timestamp: new Date().toISOString()
      });
    } else {
      // Return job status/progress
      const status = await getBatchProcessor().getBatchStatus(jobId);
      if (!status) {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        status,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Batch processing status error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve batch processing status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE: Cancel batch processing job
 */
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const cancelled = await getBatchProcessor().cancelBatch(jobId);

    if (!cancelled) {
      return NextResponse.json(
        { error: 'Job not found or cannot be cancelled' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Batch processing job cancelled successfully',
      jobId
    });

  } catch (error) {
    console.error('Batch processing cancellation error:', error);

    return NextResponse.json(
      {
        error: 'Failed to cancel batch processing job',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
