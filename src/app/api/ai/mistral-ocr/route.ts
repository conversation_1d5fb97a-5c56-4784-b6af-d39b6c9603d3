/**
 * Mistral OCR API Endpoint
 * Handles PDF extraction using Mistral's OCR capabilities
 */

import { NextRequest, NextResponse } from 'next/server';
import { Mistral } from '@mistralai/mistralai';

export const maxDuration = 120; // 2 minutes for OCR processing

/**
 * POST: Process a PDF document with Mistral OCR
 */
export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file size
    const maxSize = parseInt(process.env.MAX_DOCUMENT_SIZE_MB || '50') * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File size exceeds maximum of ${maxSize / (1024 * 1024)}MB` },
        { status: 400 }
      );
    }

    // Check if file is PDF
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: `Unsupported file type: ${file.type}. Only PDF files are supported.` },
        { status: 400 }
      );
    }

    // Get API key from environment variables
    const apiKey = process.env.MISTRAL_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Mistral API key is not configured' },
        { status: 500 }
      );
    }

    // Process the file
    const startTime = Date.now();
    
    // Convert File to ArrayBuffer and encode as base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Data = Buffer.from(arrayBuffer).toString('base64');
    
    // Initialize Mistral client using the correct approach from documentation
    const client = new Mistral({ apiKey });
    
    // According to Mistral docs, we need to use the OCR process endpoint for PDF extraction
    // Reference: https://docs.mistral.ai/capabilities/OCR/basic_ocr/#ocr-with-uploaded-pdf
    const ocrResponse = await client.ocr.process({
      model: "mistral-ocr-latest",
      document: {
        type: "document_url",
        documentUrl: `data:${file.type};base64,${base64Data}`
      },
      includeImageBase64: false
    });

    // Extract text from all pages in the OCR response
    let extractedText = '';
    if (ocrResponse.pages && ocrResponse.pages.length > 0) {
      extractedText = ocrResponse.pages.map(page => page.markdown || '').join('\n\n');
    } else {
      extractedText = 'No text extracted from document';
    }
    
    const processingTime = Date.now() - startTime;

    // Return the extracted text
    return NextResponse.json({
      content: extractedText,
      document: {
        name: file.name,
        type: file.type,
        size: file.size
      },
      processing: {
        totalTime: processingTime
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Mistral OCR processing error:', error);

    return NextResponse.json(
      {
        error: 'Failed to process document with Mistral OCR',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
