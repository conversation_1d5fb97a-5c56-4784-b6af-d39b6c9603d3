import { NextRequest, NextResponse } from 'next/server';
import { summarizeContent } from '@/lib/utils/content-summarizer';

export async function POST(request: NextRequest) {
  try {
    const { content, sourceType, maxLength } = await request.json();

    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      );
    }

    const summary = await summarizeContent(content, {
      maxLength: maxLength || 150,
      focus: 'poll-creation',
      sourceType: sourceType || 'document'
    });

    return NextResponse.json({ summary });
  } catch (error) {
    console.error('Summarization error:', error);
    return NextResponse.json(
      { error: 'Failed to summarize content' },
      { status: 500 }
    );
  }
}
