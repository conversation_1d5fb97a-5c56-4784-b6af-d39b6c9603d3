import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';
import { generateObject } from 'ai';
import { z } from 'zod';
import { aiProviders } from '@/lib/ai/providers';
import { getFinalFeatureFlags } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Rate limiting for config generation
const configRateLimitMap = new Map<string, { count: number; resetTime: number }>();

const CONFIG_RATE_LIMITS = {
  requests: 20, // 20 config generations per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

// Schema for simulation configuration
const SimulationConfigSchema = z.object({
  recommendedDemographics: z.array(z.object({
    group: z.string(),
    priority: z.enum(['high', 'medium', 'low']),
    sampleSize: z.number().min(50).max(1000),
    reasoning: z.string(),
    expectedResponse: z.object({
      primary: z.string(),
      confidence: z.number().min(0).max(1)
    })
  })),
  simulationParameters: z.object({
    totalSampleSize: z.number().min(100).max(5000),
    analysisDepth: z.enum(['basic', 'standard', 'comprehensive']),
    biasFactors: z.array(z.string()),
    confidenceThreshold: z.number().min(0.5).max(1.0)
  }),
  expectedOutcomes: z.object({
    primaryOption: z.string(),
    predictedDistribution: z.record(z.string(), z.number()),
    certaintyLevel: z.enum(['low', 'medium', 'high']),
    keyInsights: z.array(z.string())
  }),
  recommendations: z.object({
    optimalDemographics: z.array(z.string()),
    timingAdvice: z.string(),
    budgetEstimate: z.object({
      cost: z.number(),
      currency: z.string().default('USD')
    }),
    alternatives: z.array(z.string())
  })
});

function checkConfigRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `config_gen_${userId}`;
  const userLimit = configRateLimitMap.get(key);

  if (!userLimit || now > userLimit.resetTime) {
    configRateLimitMap.set(key, {
      count: 1,
      resetTime: now + CONFIG_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: CONFIG_RATE_LIMITS.requests - 1 };
  }

  if (userLimit.count >= CONFIG_RATE_LIMITS.requests) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, remaining: 0, retryAfter };
  }

  userLimit.count++;
  const remaining = CONFIG_RATE_LIMITS.requests - userLimit.count;
  return { allowed: true, remaining };
}

export async function POST(request: NextRequest) {
  try {
    console.log('AI SDK simulation config generation endpoint called');

    // Parse request body
    const body = await request.json();
    const {
      pollQuestion,
      pollOptions,
      targetAudience,
      objectives = [],
      constraints = {},
      pollContext
    } = body;

    if (!pollQuestion || !pollOptions || pollOptions.length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: pollQuestion and pollOptions (minimum 2 options)'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check feature flags
    const flags = getFinalFeatureFlags();
    if (!flags.simulationConfigGeneration) {
      return NextResponse.json(
        {
          success: false,
          error: 'Simulation config generation feature is not enabled'
        },
        { status: 503 }
      );
    }

    // Check rate limits
    const rateLimit = checkConfigRateLimit(userId);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded for config generation',
          retryAfter: rateLimit.retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimit.retryAfter?.toString() || '3600',
            'X-RateLimit-Limit': CONFIG_RATE_LIMITS.requests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          }
        }
      );
    }

    console.log('Processing simulation config generation:', {
      question: pollQuestion,
      optionsCount: pollOptions.length,
      targetAudience: targetAudience,
      objectivesCount: objectives.length,
      userId: userId
    });

    // Build configuration generation prompt
    const prompt = buildConfigGenerationPrompt(
      pollQuestion,
      pollOptions,
      targetAudience,
      objectives,
      constraints,
      pollContext
    );

    // Generate structured configuration using AI SDK
    const { object } = await generateObject({
      model: aiProviders.planning,
      schema: SimulationConfigSchema,
      prompt,
      temperature: 0.2,
      maxRetries: 2
    });

    // Calculate estimated cost
    const estimatedCost = calculateEstimatedCost(object.simulationParameters.totalSampleSize);

    // Enhance the response with additional metadata
    const enhancedConfig = {
      ...object,
      metadata: {
        generatedAt: new Date().toISOString(),
        userId: userId,
        estimatedCost,
        configVersion: '1.0',
        aiModel: 'enhanced-config-generator'
      },
      recommendations: {
        ...object.recommendations,
        budgetEstimate: {
          cost: estimatedCost,
          currency: 'USD'
        }
      }
    };

    // Return successful response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        config: enhancedConfig,
        metadata: {
          generationTime: new Date().toISOString(),
          rateLimit: {
            remaining: rateLimit.remaining,
            resetTime: new Date(Date.now() + CONFIG_RATE_LIMITS.windowMs)
          }
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': CONFIG_RATE_LIMITS.requests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
        }
      }
    );

  } catch (error) {
    console.error('Error in simulation config generation:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during config generation',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

function buildConfigGenerationPrompt(
  pollQuestion: string,
  pollOptions: string[],
  targetAudience?: string,
  objectives: string[] = [],
  constraints: Record<string, unknown> = {},
  pollContext?: string
): string {
  const contextInfo = pollContext ? `\nPOLL CONTEXT: ${pollContext}` : '';
  const audienceInfo = targetAudience ? `\nTARGET AUDIENCE: ${targetAudience}` : '';
  const objectivesInfo = objectives.length > 0 ? `\nOBJECTIVES: ${objectives.join(', ')}` : '';
  const constraintsInfo = Object.keys(constraints).length > 0 ? `\nCONSTRAINTS: ${JSON.stringify(constraints)}` : '';

  return `You are an expert survey methodologist and polling strategist. Generate an optimal simulation configuration for the following poll:

POLL QUESTION: "${pollQuestion}"

ANSWER OPTIONS: ${pollOptions.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}
${contextInfo}
${audienceInfo}
${objectivesInfo}
${constraintsInfo}

TASK: Generate a comprehensive simulation configuration that includes:

1. **Recommended Demographics**: Identify 3-5 key demographic groups that would provide the most meaningful insights for this poll question. For each group, specify:
   - Priority level (high/medium/low)
   - Optimal sample size (50-1000)
   - Reasoning for inclusion
   - Expected primary response and confidence

2. **Simulation Parameters**: Define optimal settings including:
   - Total sample size across all demographics
   - Analysis depth (basic/standard/comprehensive)
   - Potential bias factors to consider
   - Minimum confidence threshold

3. **Expected Outcomes**: Predict likely results including:
   - Most likely winning option
   - Predicted distribution across all options (percentages)
   - Overall certainty level
   - Key insights likely to emerge

4. **Strategic Recommendations**: Provide actionable advice including:
   - Most valuable demographics to focus on
   - Optimal timing for running the simulation
   - Estimated budget requirements
   - Alternative approaches if constraints exist

Consider factors like:
- Statistical significance requirements
- Demographic representation
- Cost-effectiveness
- Time constraints
- Potential sources of bias
- Historical polling patterns for similar questions

Ensure all recommendations are data-driven, realistic, and optimized for accuracy and actionable insights.`;
}

function calculateEstimatedCost(totalSampleSize: number): number {
  // Base cost calculation: $0.03 per simulation demographic
  // Assuming average of 5 demographics per simulation
  const baseCostPerDemographic = 0.03;
  const estimatedDemographics = Math.ceil(totalSampleSize / 200); // Assume 200 responses per demographic
  return Math.round((baseCostPerDemographic * estimatedDemographics) * 100) / 100;
}
