import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { ContentExtractionSchema, DocumentAnalysisSchema } from '@/lib/ai/schemas';
import { withFallback } from '@/lib/ai/fallback-service';
import { FeatureGate } from '@/lib/ai/feature-flags';

/**
 * Enhanced content extraction API route with AI SDK integration
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { content, extractionType = 'text', analysisType = 'basic' } = body;

    // Validate required fields
    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      );
    }

    if (content.length > 50000) {
      return NextResponse.json(
        { error: 'Content too large (max 50,000 characters)' },
        { status: 400 }
      );
    }

    // Choose extraction approach based on feature flags and analysis type
    const result = await FeatureGate.whenEnabledAsync(
      'structuredGeneration',
      // AI SDK approach
      async () => {
        if (analysisType === 'comprehensive') {
          return extractComprehensiveAnalysis(content);
        } else {
          return extractBasicContent(content, extractionType);
        }
      },
      // Feature disabled: Basic extraction
      async () => {
        return {
          extractedText: content,
          keyThemes: [],
          suggestedQuestions: [],
          confidence: 0.5,
          extractionQuality: 'basic' as const
        };
      }
    );

    return NextResponse.json(result);

  } catch (error) {
    console.error('Content extraction API error:', error);

    return NextResponse.json(
      {
        error: 'Failed to extract content',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Basic content extraction with AI SDK
 */
async function extractBasicContent(content: string, extractionType: string) {
  return withFallback(
    // Primary: AI SDK extraction
    async () => {
      const { object } = await generateObject({
        model: aiProviders.contentExtraction,
        schema: ContentExtractionSchema,
        prompt: buildContentExtractionPrompt(content, extractionType),
        temperature: 0.3
      });

      return object;
    },
    // Fallback: Basic processing
    async () => {
      return {
        extractedText: content,
        keyThemes: extractBasicThemes(content),
        suggestedQuestions: generateBasicQuestions(content),
        confidence: 0.6
      };
    },
    'Content Extraction'
  );
}

/**
 * Comprehensive document analysis with AI SDK
 */
async function extractComprehensiveAnalysis(content: string) {
  return withFallback(
    // Primary: AI SDK comprehensive analysis
    async () => {
      const { object } = await generateObject({
        model: aiProviders.contentExtraction,
        schema: DocumentAnalysisSchema,
        prompt: buildDocumentAnalysisPrompt(content),
        temperature: 0.3
      });

      return {
        ...object,
        extractedText: content.substring(0, 1000) + (content.length > 1000 ? '...' : ''),
        keyThemes: object.mainTopics,
        suggestedQuestions: object.pollSuggestions.map(s => s.question),
        confidence: object.extractionQuality === 'high' ? 0.9 :
                   object.extractionQuality === 'medium' ? 0.7 : 0.5
      };
    },
    // Fallback: Basic analysis
    async () => {
      return {
        summary: content.substring(0, 200) + '...',
        mainTopics: extractBasicThemes(content),
        pollSuggestions: [],
        extractionQuality: 'low' as const,
        extractedText: content,
        keyThemes: extractBasicThemes(content),
        suggestedQuestions: generateBasicQuestions(content),
        confidence: 0.5
      };
    },
    'Document Analysis'
  );
}

/**
 * Build prompt for content extraction
 */
function buildContentExtractionPrompt(content: string, extractionType: string): string {
  return `
Analyze the following content and extract key information for poll creation:

Content: ${content}

Extraction Focus: ${extractionType}

Please provide:
1. Clean, extracted text (remove formatting artifacts, keep essential content)
2. Key themes and topics (3-5 main themes that would make good poll subjects)
3. Suggested poll questions (3-5 specific questions based on the content)
4. Confidence level (0-1) in the extraction quality

Focus on identifying content elements that would translate well into engaging poll questions that gather meaningful insights.

Guidelines:
- Prioritize actionable topics over generic themes
- Suggest questions that would have clear, measurable responses
- Consider different question types (rating, choice, open-ended)
- Ensure questions are relevant to the content's main purpose
  `.trim();
}

/**
 * Build prompt for comprehensive document analysis
 */
function buildDocumentAnalysisPrompt(content: string): string {
  return `
Perform a comprehensive analysis of this document for poll creation purposes:

Content: ${content}

Please provide:
1. Executive summary of the main content and purpose
2. List of main topics/themes that could be poll subjects
3. Specific poll question suggestions with reasoning for each
4. Assessment of content extraction quality (high/medium/low)

For each suggested poll question, include:
- The question text
- Recommended question type (single/multiple/open)
- Brief reasoning for why this question would be valuable

Focus on practical, actionable insights that would help someone understand audience perspectives on the document's topics.

Quality Assessment Criteria:
- High: Clear, well-structured content with obvious poll opportunities
- Medium: Readable content with some poll potential, minor ambiguities
- Low: Fragmented, unclear, or very limited poll opportunities
  `.trim();
}

/**
 * Fallback function for basic theme extraction
 */
function extractBasicThemes(content: string): string[] {
  const words = content.toLowerCase().match(/\b\w{4,}\b/g) || [];
  const wordCount: Record<string, number> = {};

  for (const word of words) {
    wordCount[word] = (wordCount[word] || 0) + 1;
  }

  return Object.entries(wordCount)
    .sort(([, a], [, b]) => (b as number) - (a as number))
    .slice(0, 5)
    .map(([word]) => word)
    .filter(word => !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'will', 'would'].includes(word));
}

/**
 * Fallback function for basic question generation
 */
function generateBasicQuestions(content: string): string[] {
  const themes = extractBasicThemes(content);
  return themes.slice(0, 3).map(theme =>
    `What is your opinion about ${theme}?`
  );
}

/**
 * GET endpoint for health check
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'AI SDK Content Extraction',
    features: {
      structuredGeneration: FeatureGate.isEnabled('structuredGeneration'),
      toolCalling: FeatureGate.isEnabled('toolCalling'),
      fallbackAvailable: true
    },
    limits: {
      maxContentLength: 50000,
      supportedTypes: ['text', 'poll-questions', 'key-themes']
    },
    timestamp: new Date().toISOString()
  });
}
