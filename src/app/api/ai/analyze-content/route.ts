import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

/**
 * API route for streaming content analysis
 * Provides real-time AI analysis of content for poll creation
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const content = body.content;

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return new Response('Content is required for analysis', { status: 400 });
    }

    // Check if streaming is enabled
    const isEnabled = await FeatureGate.isEnabled('streamingAnalytics');
    if (!isEnabled) {
      return new Response('Content analysis streaming is not enabled', { status: 403 });
    }

    const stream = await streamText({
      model: aiProviders.contentExtraction, // Using Mistral for content analysis
      prompt: buildContentAnalysisPrompt(content),
      temperature: 0.3,
      maxTokens: 800,
    });

    return stream.toDataStreamResponse();

  } catch (error) {
    console.error('Content analysis streaming error:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to analyze content',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Build optimized prompt for content analysis
 */
function buildContentAnalysisPrompt(content: string): string {
  return `
You are an expert content analyst helping to create effective polls. Analyze the following content and provide insights for poll creation.

Content to analyze:
${content}

Please provide a streaming analysis covering:

1. **Key Themes**: Identify the main topics and themes
2. **Audience Insights**: Determine the target audience and their characteristics
3. **Poll Opportunities**: Suggest specific areas that would make good poll questions
4. **Tone & Context**: Analyze the content's tone and context for appropriate poll design
5. **Recommendations**: Provide actionable recommendations for poll creation

Stream your analysis as you work through each aspect, providing detailed insights and specific suggestions for creating engaging and effective polls.

Focus on being practical and actionable - suggest specific question types, audience targeting, and poll structure recommendations.
`;
}

/**
 * GET endpoint for content analysis capabilities
 */
export async function GET() {
  const isEnabled = await FeatureGate.isEnabled('streamingAnalytics');

  return Response.json({
    enabled: isEnabled,
    capabilities: [
      'theme_extraction',
      'audience_identification',
      'poll_recommendations',
      'tone_analysis',
      'content_summarization'
    ],
    max_content_length: 10000,
    supported_formats: ['text', 'markdown'],
    timestamp: new Date().toISOString(),
  });
}
