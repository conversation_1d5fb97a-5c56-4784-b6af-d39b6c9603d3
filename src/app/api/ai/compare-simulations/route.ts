import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';
import { generateObject } from 'ai';
import { z } from 'zod';
import { aiProviders } from '@/lib/ai/providers';
import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { getFinalFeatureFlags } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Rate limiting for simulation comparison
const comparisonRateLimitMap = new Map<string, { count: number; resetTime: number }>();

const COMPARISON_RATE_LIMITS = {
  requests: 15, // 15 comparisons per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

// Schema for simulation comparison analysis
const SimulationComparisonSchema = z.object({
  comparisonSummary: z.object({
    totalSimulations: z.number(),
    comparisonType: z.enum(['demographic', 'temporal', 'methodological', 'cross-poll']),
    overallSimilarity: z.number().min(0).max(1),
    significantDifferences: z.array(z.string()),
    keyTrends: z.array(z.string())
  }),
  statisticalAnalysis: z.object({
    varianceAnalysis: z.object({
      lowVariance: z.array(z.string()), // Options with consistent results
      highVariance: z.array(z.string()), // Options with divergent results
      varianceScore: z.number().min(0).max(1)
    }),
    correlationAnalysis: z.object({
      strongCorrelations: z.array(z.object({
        simulation1: z.string(),
        simulation2: z.string(),
        correlation: z.number().min(-1).max(1),
        significance: z.enum(['low', 'medium', 'high'])
      })),
      outliers: z.array(z.string())
    }),
    confidenceAnalysis: z.object({
      averageConfidence: z.number().min(0).max(1),
      confidenceRange: z.object({
        min: z.number().min(0).max(1),
        max: z.number().min(0).max(1)
      }),
      reliabilityAssessment: z.enum(['low', 'medium', 'high'])
    })
  }),
  demographicInsights: z.object({
    consensusDemographics: z.array(z.object({
      demographic: z.string(),
      consensusOption: z.string(),
      agreement: z.number().min(0).max(1)
    })),
    polarizedDemographics: z.array(z.object({
      demographic: z.string(),
      primaryDivision: z.string(),
      polarizationScore: z.number().min(0).max(1)
    })),
    demographicPatterns: z.array(z.string()),
    surprisingFindings: z.array(z.string())
  }),
  actionableInsights: z.object({
    strategicImplications: z.array(z.string()),
    targetingRecommendations: z.array(z.string()),
    messagingInsights: z.array(z.string()),
    riskAssessments: z.array(z.string()),
    opportunityIdentification: z.array(z.string())
  }),
  comparisonMetrics: z.object({
    stabilityScore: z.number().min(0).max(1), // How consistent results are
    predictionAccuracy: z.number().min(0).max(1), // If real data available
    demographicCoverage: z.number().min(0).max(1), // How well demographics are covered
    methodologyRating: z.enum(['poor', 'fair', 'good', 'excellent'])
  })
});

function checkComparisonRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `comparison_analysis_${userId}`;
  const userLimit = comparisonRateLimitMap.get(key);

  if (!userLimit || now > userLimit.resetTime) {
    comparisonRateLimitMap.set(key, {
      count: 1,
      resetTime: now + COMPARISON_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: COMPARISON_RATE_LIMITS.requests - 1 };
  }

  if (userLimit.count >= COMPARISON_RATE_LIMITS.requests) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, remaining: 0, retryAfter };
  }

  userLimit.count++;
  const remaining = COMPARISON_RATE_LIMITS.requests - userLimit.count;
  return { allowed: true, remaining };
}

export async function POST(request: NextRequest) {
  try {
    console.log('AI SDK simulation comparison endpoint called');

    // Parse request body
    const body = await request.json();
    const {
      simulations,
      comparisonType = 'demographic',
      analysisDepth = 'standard',
      realWorldData,
      pollQuestion,
      pollOptions,
      comparisonContext
    } = body;

    if (!simulations || !Array.isArray(simulations) || simulations.length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'At least 2 simulations required for comparison'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check feature flags
    const flags = getFinalFeatureFlags();
    if (!flags.simulationComparison) {
      return NextResponse.json(
        {
          success: false,
          error: 'Simulation comparison feature is not enabled'
        },
        { status: 503 }
      );
    }

    // Check rate limits
    const rateLimit = checkComparisonRateLimit(userId);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded for simulation comparison',
          retryAfter: rateLimit.retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimit.retryAfter?.toString() || '3600',
            'X-RateLimit-Limit': COMPARISON_RATE_LIMITS.requests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          }
        }
      );
    }

    console.log('Processing simulation comparison request:', {
      simulationsCount: simulations.length,
      comparisonType: comparisonType,
      analysisDepth: analysisDepth,
      hasRealWorldData: !!realWorldData,
      userId: userId
    });

    // Build comparison analysis prompt
    const prompt = buildComparisonAnalysisPrompt(
      simulations,
      comparisonType,
      analysisDepth,
      realWorldData,
      pollQuestion,
      pollOptions,
      comparisonContext
    );

    // Generate structured comparison analysis using AI SDK
    const { object } = await generateObject({
      model: aiProviders.analysis,
      schema: SimulationComparisonSchema,
      prompt,
      temperature: 0.2,
      maxRetries: 2
    });

    // Calculate additional comparison metrics
    const additionalMetrics = calculateComparisonMetrics(simulations, realWorldData);

    // Enhance the response with additional analysis
    const enhancedComparison = {
      ...object,
      additionalMetrics,
      metadata: {
        comparisonGeneratedAt: new Date().toISOString(),
        userId: userId,
        simulationsAnalyzed: simulations.length,
        comparisonType: comparisonType,
        analysisDepth: analysisDepth,
        processingTime: Date.now(),
        aiModel: 'simulation-comparator'
      }
    };

    // Return successful response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        comparison: enhancedComparison,
        summary: {
          totalSimulations: simulations.length,
          overallSimilarity: object.comparisonSummary.overallSimilarity,
          keyFindings: object.comparisonSummary.keyTrends,
          reliability: object.statisticalAnalysis.confidenceAnalysis.reliabilityAssessment
        },
        metadata: {
          comparisonCompletedAt: new Date().toISOString(),
          rateLimit: {
            remaining: rateLimit.remaining,
            resetTime: new Date(Date.now() + COMPARISON_RATE_LIMITS.windowMs)
          }
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': COMPARISON_RATE_LIMITS.requests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
        }
      }
    );

  } catch (error) {
    console.error('Error in simulation comparison:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during simulation comparison',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

function buildComparisonAnalysisPrompt(
  simulations: (SimulationResponse | BatchSimulationResult)[],
  comparisonType: string,
  analysisDepth: string,
  realWorldData?: Record<string, unknown>,
  pollQuestion?: string,
  pollOptions?: string[],
  comparisonContext?: string
): string {
  const contextInfo = comparisonContext ? `\nCOMPARISON CONTEXT: ${comparisonContext}` : '';
  const pollInfo = pollQuestion && pollOptions ? `
POLL QUESTION: "${pollQuestion}"
ANSWER OPTIONS: ${pollOptions.join(', ')}` : '';

  const simulationData = simulations.map((sim, i) => {
    if ('batchId' in sim) {
      // BatchSimulationResult
      return `Simulation ${i + 1} (Batch):
- Batch ID: ${sim.batchId}
- Demographics: ${sim.results.length}
- Total Responses: ${sim.metadata.totalResponses}
- Average Confidence: ${((sim.metadata.averageConfidence || 0) * 100).toFixed(1)}%
- Consensus Options: ${sim.comparison.consensusOptions.join(', ')}
- Polarizing Options: ${sim.comparison.polarizingOptions.join(', ')}`;
    } else {
      // SimulationResponse
      return `Simulation ${i + 1}:
- Demographic: ${sim.metadata.demographic}
- Sample Size: ${sim.metadata.sampleSize}
- Confidence: ${(sim.metadata.confidence * 100).toFixed(1)}%
- Distribution: ${JSON.stringify(sim.results.distribution)}
- Analysis: ${sim.results.analysis}`;
    }
  }).join('\n\n');

  const realDataInfo = realWorldData ? `\nREAL WORLD DATA: ${JSON.stringify(realWorldData)}` : '';

  return `You are an expert statistician and survey analyst specializing in comparative analysis. Analyze and compare the following poll simulations:

${pollInfo}
${contextInfo}

SIMULATION RESULTS:
${simulationData}
${realDataInfo}

COMPARISON TYPE: ${comparisonType}
ANALYSIS DEPTH: ${analysisDepth}

TASK: Provide a comprehensive comparative analysis that includes:

1. **Comparison Summary**: High-level overview of the comparison including:
   - Total simulations analyzed
   - Type of comparison being performed
   - Overall similarity score (0-1 scale)
   - Most significant differences identified
   - Key trends observed across simulations

2. **Statistical Analysis**: Deep statistical examination including:
   - Variance analysis (identify low and high variance options)
   - Correlation analysis between simulations
   - Confidence analysis and reliability assessment
   - Outlier identification

3. **Demographic Insights**: Demographic-specific analysis including:
   - Demographics showing consensus vs polarization
   - Patterns in demographic responses
   - Surprising or unexpected findings
   - Cross-demographic correlations

4. **Actionable Insights**: Strategic and practical implications including:
   - Strategic implications for decision-makers
   - Targeting recommendations based on patterns
   - Messaging insights from response patterns
   - Risk assessments and opportunity identification

5. **Comparison Metrics**: Quantitative assessment including:
   - Stability score (consistency across simulations)
   - Prediction accuracy (if real-world data available)
   - Demographic coverage completeness
   - Overall methodology rating

Consider factors like:
- Statistical significance of differences
- Confidence intervals and margins of error
- Demographic representation quality
- Temporal factors if applicable
- Methodological differences between simulations
- Cultural and contextual influences
- Sample size adequacy across comparisons

Provide specific, data-driven insights with quantitative measures where possible. Identify both convergent patterns (where simulations agree) and divergent patterns (where they disagree significantly).`;
}

function calculateComparisonMetrics(
  simulations: (SimulationResponse | BatchSimulationResult)[],
  realWorldData?: Record<string, unknown>
): {
  totalSimulations: number;
  averageConfidence: number;
  totalSampleSize: number;
  confidenceRange: {
    min: number;
    max: number;
  };
  hasRealWorldComparison: boolean;
  analysisTimestamp: string;
} {
  // Calculate basic comparison metrics
  const totalSimulations = simulations.length;

  // Calculate average confidence across all simulations
  const confidenceScores = simulations.map(sim => {
    if ('batchId' in sim) {
      return sim.metadata.averageConfidence;
    } else {
      return sim.metadata.confidence;
    }
  }).filter((conf): conf is number => conf !== undefined);

  const averageConfidence = confidenceScores.length > 0
    ? confidenceScores.reduce((sum, conf) => sum + conf, 0) / confidenceScores.length
    : 0;

  // Calculate total sample size
  const totalSampleSize = simulations.reduce((sum, sim) => {
    if ('batchId' in sim) {
      return sum + sim.metadata.totalResponses;
    } else {
      return sum + sim.metadata.sampleSize;
    }
  }, 0);

  return {
    totalSimulations,
    averageConfidence,
    totalSampleSize,
    confidenceRange: {
      min: confidenceScores.length > 0 ? Math.min(...confidenceScores) : 0,
      max: confidenceScores.length > 0 ? Math.max(...confidenceScores) : 0
    },
    hasRealWorldComparison: !!realWorldData,
    analysisTimestamp: new Date().toISOString()
  };
}
