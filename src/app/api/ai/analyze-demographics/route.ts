import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';
import { generateObject } from 'ai';
import { z } from 'zod';
import { aiProviders } from '@/lib/ai/providers';
import { getFinalFeatureFlags } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Rate limiting for demographic analysis
const demographicsRateLimitMap = new Map<string, { count: number; resetTime: number }>();

const DEMOGRAPHICS_RATE_LIMITS = {
  requests: 25, // 25 demographic analyses per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

// Schema for demographic analysis
const DemographicAnalysisSchema = z.object({
  recommendedDemographics: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    priority: z.enum(['critical', 'high', 'medium', 'low']),
    expectedEngagement: z.number().min(0).max(1),
    keyCharacteristics: z.array(z.string()),
    predictedResponse: z.object({
      primaryChoice: z.string(),
      confidence: z.number().min(0).max(1),
      reasoning: z.string()
    }),
    sampleSizeRecommendation: z.number().min(50).max(500)
  })),
  demographicInsights: z.object({
    targetAudienceMatch: z.number().min(0).max(1),
    coverageCompleteness: z.number().min(0).max(1),
    diversityScore: z.number().min(0).max(1),
    representativenessScore: z.number().min(0).max(1)
  }),
  crossDemographicAnalysis: z.object({
    expectedConsensus: z.array(z.object({
      option: z.string(),
      consensusLevel: z.number().min(0).max(1),
      demographics: z.array(z.string())
    })),
    expectedPolarization: z.array(z.object({
      option: z.string(),
      polarizationLevel: z.number().min(0).max(1),
      conflictingGroups: z.array(z.string())
    })),
    keyDividers: z.array(z.string()),
    unifiers: z.array(z.string())
  }),
  strategicRecommendations: z.object({
    priorityOrder: z.array(z.string()),
    minimalViableSet: z.array(z.string()),
    comprehensiveSet: z.array(z.string()),
    budgetOptimized: z.array(z.string()),
    timeOptimized: z.array(z.string())
  })
});

function checkDemographicsRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `demographics_analysis_${userId}`;
  const userLimit = demographicsRateLimitMap.get(key);

  if (!userLimit || now > userLimit.resetTime) {
    demographicsRateLimitMap.set(key, {
      count: 1,
      resetTime: now + DEMOGRAPHICS_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: DEMOGRAPHICS_RATE_LIMITS.requests - 1 };
  }

  if (userLimit.count >= DEMOGRAPHICS_RATE_LIMITS.requests) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, remaining: 0, retryAfter };
  }

  userLimit.count++;
  const remaining = DEMOGRAPHICS_RATE_LIMITS.requests - userLimit.count;
  return { allowed: true, remaining };
}

export async function POST(request: NextRequest) {
  try {
    console.log('AI SDK demographic analysis endpoint called');

    // Parse request body
    const body = await request.json();
    const {
      pollQuestion,
      pollOptions,
      targetAudience,
      existingDemographics = [],
      analysisScope = 'comprehensive',
      geographicFocus,
      industryContext,
      pollContext
    } = body;

    if (!pollQuestion || !pollOptions || pollOptions.length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: pollQuestion and pollOptions (minimum 2 options)'
        },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Check feature flags
    const flags = getFinalFeatureFlags();
    if (!flags.demographicAnalysis) {
      return NextResponse.json(
        {
          success: false,
          error: 'Demographic analysis feature is not enabled'
        },
        { status: 503 }
      );
    }

    // Check rate limits
    const rateLimit = checkDemographicsRateLimit(userId);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded for demographic analysis',
          retryAfter: rateLimit.retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimit.retryAfter?.toString() || '3600',
            'X-RateLimit-Limit': DEMOGRAPHICS_RATE_LIMITS.requests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          }
        }
      );
    }

    console.log('Processing demographic analysis request:', {
      question: pollQuestion,
      optionsCount: pollOptions.length,
      targetAudience: targetAudience,
      analysisScope: analysisScope,
      existingDemographicsCount: existingDemographics.length,
      userId: userId
    });

    // Build demographic analysis prompt
    const prompt = buildDemographicAnalysisPrompt(
      pollQuestion,
      pollOptions,
      targetAudience,
      existingDemographics,
      analysisScope,
      geographicFocus,
      industryContext,
      pollContext
    );

    // Generate structured demographic analysis using AI SDK
    const { object } = await generateObject({
      model: aiProviders.analysis,
      schema: DemographicAnalysisSchema,
      prompt,
      temperature: 0.3,
      maxRetries: 2
    });

    // Calculate additional metrics
    const enhancedAnalysis = {
      ...object,
      metadata: {
        analysisGeneratedAt: new Date().toISOString(),
        userId: userId,
        analysisScope: analysisScope,
        totalRecommendedDemographics: object.recommendedDemographics.length,
        highPriorityCount: object.recommendedDemographics.filter(d => d.priority === 'critical' || d.priority === 'high').length,
        estimatedTotalSampleSize: object.recommendedDemographics.reduce((sum, d) => sum + d.sampleSizeRecommendation, 0),
        estimatedCost: calculateDemographicAnalysisCost(object.recommendedDemographics),
        aiModel: 'demographic-analyzer'
      }
    };

    // Return successful response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        analysis: enhancedAnalysis,
        recommendations: {
          immediate: object.strategicRecommendations.minimalViableSet,
          comprehensive: object.strategicRecommendations.comprehensiveSet,
          budgetOptimized: object.strategicRecommendations.budgetOptimized
        },
        metadata: {
          analysisCompletedAt: new Date().toISOString(),
          rateLimit: {
            remaining: rateLimit.remaining,
            resetTime: new Date(Date.now() + DEMOGRAPHICS_RATE_LIMITS.windowMs)
          }
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': DEMOGRAPHICS_RATE_LIMITS.requests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
        }
      }
    );

  } catch (error) {
    console.error('Error in demographic analysis:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during demographic analysis',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

function buildDemographicAnalysisPrompt(
  pollQuestion: string,
  pollOptions: string[],
  targetAudience?: string,
  existingDemographics: string[] = [],
  analysisScope: string = 'comprehensive',
  geographicFocus?: string,
  industryContext?: string,
  pollContext?: string
): string {
  const contextInfo = pollContext ? `\nPOLL CONTEXT: ${pollContext}` : '';
  const audienceInfo = targetAudience ? `\nTARGET AUDIENCE: ${targetAudience}` : '';
  const geoInfo = geographicFocus ? `\nGEOGRAPHIC FOCUS: ${geographicFocus}` : '';
  const industryInfo = industryContext ? `\nINDUSTRY CONTEXT: ${industryContext}` : '';
  const existingInfo = existingDemographics.length > 0 ? `\nEXISTING DEMOGRAPHICS: ${existingDemographics.join(', ')}` : '';

  return `You are an expert demographic analyst and survey methodologist. Analyze the following poll question and provide comprehensive demographic recommendations:

POLL QUESTION: "${pollQuestion}"

ANSWER OPTIONS: ${pollOptions.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}
${contextInfo}
${audienceInfo}
${geoInfo}
${industryInfo}
${existingInfo}

ANALYSIS SCOPE: ${analysisScope}

TASK: Provide a comprehensive demographic analysis that includes:

1. **Recommended Demographics**: Identify 5-8 key demographic groups most relevant to this poll question. For each group:
   - Unique identifier and descriptive name
   - Clear description of the demographic
   - Priority level (critical/high/medium/low)
   - Expected engagement level (0-1 scale)
   - Key characteristics that make them relevant
   - Predicted response and reasoning
   - Optimal sample size recommendation

2. **Demographic Insights**: Evaluate the overall demographic strategy:
   - Target audience match score (how well demographics align with intended audience)
   - Coverage completeness (what percentage of relevant population is covered)
   - Diversity score (how diverse the demographic selection is)
   - Representativeness score (how representative of broader population)

3. **Cross-Demographic Analysis**: Analyze inter-group dynamics:
   - Expected consensus areas (options where groups agree)
   - Expected polarization (options where groups strongly disagree)
   - Key demographic dividers (characteristics that split opinion)
   - Unifying factors (what brings different groups together)

4. **Strategic Recommendations**: Provide multiple demographic selection strategies:
   - Priority order for sequential data collection
   - Minimal viable set (3-4 demographics for basic insights)
   - Comprehensive set (6-8 demographics for full analysis)
   - Budget-optimized selection (best value demographics)
   - Time-optimized selection (fastest to highest confidence)

Consider factors like:
- Historical voting/opinion patterns for similar questions
- Socioeconomic factors affecting responses
- Generational differences in perspective
- Geographic and cultural influences
- Professional/industry-specific viewpoints
- Educational background impacts
- Current events and cultural zeitgeist

Ensure recommendations are:
- Statistically sound and representative
- Cost-effective and actionable
- Balanced across different demographic dimensions
- Tailored to the specific poll question and context`;
}

function calculateDemographicAnalysisCost(demographics: Array<{ sampleSizeRecommendation?: number }>): number {
  // Base cost calculation: $0.03 per demographic simulation
  const baseCostPerDemographic = 0.03;
  return Math.round((baseCostPerDemographic * demographics.length) * 100) / 100;
}
