import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Type definitions for poll insights
interface PollOption {
  id: string;
  text: string;
  votes?: number;
}

interface PollQuestion {
  id: string;
  text: string;
  type: string;
  options: PollOption[];
}

interface PollData {
  id: string;
  title: string;
  description?: string;
  questions: PollQuestion[];
  totalResponses?: number;
  createdAt?: string;
  status?: string;
}

interface StreamPollInsightsRequest {
  pollData: PollData;
  insightType?: string;
}

/**
 * API route for streaming poll insights and suggestions
 * Provides real-time AI insights about generated polls
 */
export async function POST(req: NextRequest) {
  try {
    const { pollData, insightType = 'general' }: StreamPollInsightsRequest = await req.json();

    if (!pollData) {
      return new Response('Poll data is required', { status: 400 });
    }

    // Check if poll insights streaming is enabled
    const isEnabled = await FeatureGate.isEnabled('streamingInsights');
    if (!isEnabled) {
      return new Response('Poll insights streaming is not enabled', { status: 403 });
    }

    const stream = await streamText({
      model: aiProviders.contentExtraction, // Using Mistral for insights
      prompt: buildInsightsPrompt(pollData, insightType),
      temperature: 0.4, // Slightly higher for more creative insights
      maxTokens: 600,
    });

    return stream.toDataStreamResponse();

  } catch (error) {
    console.error('Poll insights streaming error:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to generate poll insights',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Build optimized prompt for poll insights
 */
function buildInsightsPrompt(pollData: PollData, insightType: string): string {
  const basePrompt = `
You are an expert poll strategist providing insights about poll effectiveness and optimization.

Poll Details:
${JSON.stringify(pollData, null, 2)}

Insight Type: ${insightType}

Provide streaming insights about:
`;

  switch (insightType) {
    case 'optimization':
      return basePrompt + `
- Question clarity and bias reduction opportunities
- Answer option improvements and balance
- Question order and flow optimization
- Target audience alignment assessment
- Response rate prediction and improvement strategies

Focus on specific, actionable recommendations to improve poll performance.`;

    case 'strategy':
      return basePrompt + `
- Distribution strategy recommendations
- Optimal timing for maximum engagement
- Target audience refinement suggestions
- Incentive and engagement strategies
- Platform-specific optimization tips

Provide strategic insights for poll deployment and audience engagement.`;

    case 'quality':
      return basePrompt + `
- Question quality assessment and scoring
- Bias detection and neutrality improvements
- Response option completeness and balance
- Overall poll structure evaluation
- Professional polling standards compliance

Evaluate the poll's quality and suggest specific improvements.`;

    case 'prediction':
      return basePrompt + `
- Expected response patterns and trends
- Demographic response predictions
- Potential challenges and drop-off points
- Engagement level forecasting
- Success metrics and benchmarks

Predict how the poll will perform and what to expect.`;

    case 'general':
    default:
      return basePrompt + `
- Overall poll strength and effectiveness assessment
- Key opportunities for improvement
- Audience engagement predictions
- Strategic recommendations for success
- Quick wins for immediate optimization

Provide comprehensive insights covering the most important aspects of poll optimization and strategy.`;
  }
}

/**
 * GET endpoint for insights capabilities
 */
export async function GET() {
  const isEnabled = await FeatureGate.isEnabled('streamingInsights');

  return Response.json({
    enabled: isEnabled,
    insight_types: ['general', 'optimization', 'strategy', 'quality', 'prediction'],
    features: [
      'real_time_analysis',
      'bias_detection',
      'engagement_prediction',
      'strategic_recommendations',
      'quality_assessment'
    ],
    timestamp: new Date().toISOString(),
  });
}
