import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { PollSchema, type PollCreationInput } from '@/lib/ai/schemas';
import { withFallback } from '@/lib/ai/fallback-service';
import { FeatureGate } from '@/lib/ai/feature-flags';
import { generatePollQuestions, type GeneratedPoll } from '@/lib/services/perplexity-ai';

/**
 * Enhanced poll generation API route with AI SDK integration
 * Falls back to existing Perplexity service if AI SDK fails
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { topic, audience, content, title, additionalInfo } = body;

    // Validate required fields
    if (!topic || !audience) {
      return NextResponse.json(
        { error: 'Topic and audience are required' },
        { status: 400 }
      );
    }

    const input: PollCreationInput = {
      title: title || `Poll about ${topic}`,
      topic,
      audience,
      additionalInfo,
      content
    };

    // Use feature gate to determine approach
    const result = await FeatureGate.whenEnabledAsync(
      'structuredGeneration',
      // AI SDK approach
      async () => {
        return withFallback(
          // Primary: AI SDK with structured generation
          async () => {
            const { object } = await generateObject({
              model: aiProviders.contentExtraction,
              schema: PollSchema,
              prompt: buildPollPrompt(input),
              temperature: 0.3
            });

            // Convert to existing format for consistency
            const result: GeneratedPoll = {
              title: input.title,
              description: object.description,
              questions: object.questions.map((q, index) => ({
                text: q.text,
                type: q.type,
                options: q.options?.map(opt => ({ text: opt.text, value: opt.value })) || [],
                required: q.required,
                order: q.order || index + 1
              }))
            };
            return result;
          },
          // Fallback: Existing Perplexity service
          async () => {
            return generatePollQuestions(input);
          },
          'AI SDK Poll Generation'
        );
      },
      // Feature disabled: Use existing service
      async () => {
        return generatePollQuestions(input);
      }
    );

    return NextResponse.json(result);

  } catch (error) {
    console.error('Poll generation API error:', error);

    return NextResponse.json(
      {
        error: 'Failed to generate poll',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Build optimized prompt for AI SDK poll generation
 */
function buildPollPrompt(input: PollCreationInput): string {
  return `
Create a comprehensive poll about "${input.topic}" for "${input.audience}".

Title: "${input.title}"
${input.additionalInfo ? `Additional Context: ${input.additionalInfo}` : ''}
${input.content ? `Content to Analyze: ${input.content}` : ''}

Generate a poll with 4-5 well-designed questions that will provide actionable insights. Include:

1. A clear, engaging description explaining the poll's purpose
2. A strategic mix of question types:
   - Single choice: For clear preferences, demographics, or rating questions
   - Multiple choice: For complex topics with multiple valid options
   - Open-ended: For qualitative insights and suggestions

3. For each multiple choice/single choice question:
   - Provide 3-5 clear, mutually exclusive options
   - Include "Other" or "Prefer not to say" when appropriate
   - Ensure options cover the full spectrum of likely responses

4. Question quality guidelines:
   - Use clear, unbiased language
   - Avoid leading questions
   - Make questions specific and actionable
   - Consider the audience's expertise level

5. Logical question flow:
   - Start with easier, general questions
   - Progress to more specific or sensitive topics
   - End with open feedback opportunities

Focus on creating questions that will genuinely help the poll creator understand their audience's perspectives and make informed decisions.
  `.trim();
}

/**
 * GET endpoint for health check
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'AI SDK Poll Generation',
    features: {
      structuredGeneration: FeatureGate.isEnabled('structuredGeneration'),
      fallbackAvailable: true
    },
    timestamp: new Date().toISOString()
  });
}
