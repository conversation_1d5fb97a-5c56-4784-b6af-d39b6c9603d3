import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { aiProviders } from '@/lib/ai/providers';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const runtime = 'edge';

// Type definitions for poll optimization
interface PollOption {
  id: string;
  text: string;
  votes?: number;
}

interface PollQuestion {
  id: string;
  text: string;
  type: string;
  options: PollOption[];
}

interface PollData {
  id: string;
  title: string;
  description?: string;
  questions: PollQuestion[];
  totalResponses?: number;
  createdAt?: string;
  status?: string;
}

interface OptimizePollRequest {
  pollData: PollData;
  requestType?: string;
}

/**
 * API route for streaming poll optimization suggestions
 * Provides real-time AI-powered recommendations for poll improvement
 */
export async function POST(req: NextRequest) {
  try {
    const { pollData, requestType = 'optimization' }: OptimizePollRequest = await req.json();

    if (!pollData) {
      return new Response('Poll data is required for optimization', { status: 400 });
    }

    // Check if optimization streaming is enabled
    const isEnabled = await FeatureGate.isEnabled('streamingAnalytics');
    if (!isEnabled) {
      return new Response('Poll optimization streaming is not enabled', { status: 403 });
    }

    const prompt = buildOptimizationPrompt(pollData, requestType);

    const stream = await streamText({
      model: aiProviders.contentExtraction,
      prompt,
      maxTokens: 800,
      temperature: 0.7,
    });

    return stream.toDataStreamResponse();
  } catch (error) {
    console.error('Poll optimization error:', error);
    return new Response('Failed to optimize poll', { status: 500 });
  }
}

/**
 * Build optimized prompt for poll optimization
 */
function buildOptimizationPrompt(pollData: PollData, requestType: string): string {
  const basePrompt = `
You are an expert poll optimization consultant. Analyze this poll and provide streaming recommendations for improvement.

Current Poll:
${JSON.stringify(pollData, null, 2)}

Optimization Focus: ${requestType}

Please provide comprehensive optimization recommendations including:

## Question Quality Assessment
- Clarity and comprehensibility
- Bias detection and neutrality
- Leading question identification
- Ambiguity resolution

## Answer Options Analysis
- Option completeness and coverage
- Balance and fairness
- Mutual exclusivity
- Exhaustiveness check

## Engagement Optimization
- Question ordering and flow
- Length and attention optimization
- Visual presentation improvements
- Mobile-friendly considerations

## Audience Targeting
- Demographic considerations
- Accessibility improvements
- Cultural sensitivity
- Language clarity

## Response Quality Enhancement
- Skip logic recommendations
- Validation improvements
- Error prevention
- Completion rate optimization

## Data Quality Assurance
- Statistical validity
- Sample size considerations
- Bias minimization
- Response reliability

## Performance Predictions
- Expected response rates
- Completion time estimates
- Engagement metrics
- Quality indicators

Please stream your analysis with specific, actionable recommendations for each area. Format your response with clear sections and prioritized suggestions.

Focus on practical improvements that will:
1. Increase response quality
2. Improve completion rates
3. Reduce bias and ambiguity
4. Enhance user experience
5. Optimize for mobile devices
6. Ensure accessibility
7. Maximize statistical validity

Provide specific examples and suggested rewording where applicable.
`;

  // Add specific analysis based on request type
  if (requestType === 'engagement') {
    return basePrompt + `
SPECIAL FOCUS: Engagement Optimization
Prioritize recommendations that will increase user engagement, reduce abandonment, and improve completion rates.
`;
  }

  if (requestType === 'bias-detection') {
    return basePrompt + `
SPECIAL FOCUS: Bias Detection and Neutrality
Conduct deep analysis for potential biases, leading questions, cultural insensitivity, and neutrality issues.
`;
  }

  if (requestType === 'accessibility') {
    return basePrompt + `
SPECIAL FOCUS: Accessibility and Inclusivity
Analyze the poll for accessibility barriers, inclusive language, and universal design principles.
`;
  }

  if (requestType === 'mobile-optimization') {
    return basePrompt + `
SPECIAL FOCUS: Mobile Experience Optimization
Optimize for mobile devices, touch interfaces, small screens, and mobile user behaviors.
`;
  }

  return basePrompt;
}