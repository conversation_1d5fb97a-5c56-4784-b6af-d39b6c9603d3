/**
 * Phase 2: Enhanced Single Document Processing API
 * Advanced document processing with OCR, sentiment analysis, and NLP
 */

import { NextRequest, NextResponse } from 'next/server';
import { documentProcessor, ProcessingOptions } from '@/lib/ai/document-processor';
import { nlpAnalyzer } from '@/lib/ai/nlp-analysis';
import { FeatureGate } from '@/lib/ai/feature-flags';

export const maxDuration = 120; // 2 minutes for single document processing

/**
 * POST: Process a single document with enhanced features
 */
export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const optionsJson = formData.get('options') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file size
    const maxSize = parseInt(process.env.MAX_DOCUMENT_SIZE_MB || '50') * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File size exceeds maximum of ${maxSize / (1024 * 1024)}MB` },
        { status: 400 }
      );
    }

    // Check if document type is supported
    if (!documentProcessor.isSupported(file.type)) {
      return NextResponse.json(
        { error: `Unsupported file type: ${file.type}` },
        { status: 400 }
      );
    }

    // Parse processing options
    let options: ProcessingOptions & {
      includeNLP?: boolean;
      includeSentiment?: boolean;
      includeTopics?: boolean;
      includePollSuggestions?: boolean;
      includeQuality?: boolean;
      audience?: string;
    } = {};

    if (optionsJson) {
      try {
        options = JSON.parse(optionsJson);    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const _ = error; // Acknowledge the error variable
      return NextResponse.json(
          { error: 'Invalid options JSON' },
          { status: 400 }
        );
      }
    }

    // Set default values
    const processingOptions: ProcessingOptions = {
      enableOCR: FeatureGate.isEnabled('ocrProcessing') && (options.enableOCR ?? true),
      ocrLanguage: options.ocrLanguage || 'eng',
      extractTables: options.extractTables ?? true,
      extractImages: options.extractImages ?? true,
      confidenceThreshold: parseFloat(process.env.OCR_CONFIDENCE_THRESHOLD || '0.8')
    };

    const nlpOptions = {
      includeNLP: options.includeNLP ?? true,
      includeSentiment: FeatureGate.isEnabled('sentimentAnalysis') && (options.includeSentiment ?? true),
      includeTopics: options.includeTopics ?? true,
      includePollSuggestions: options.includePollSuggestions ?? true,
      includeQuality: options.includeQuality ?? true,
      audience: options.audience
    };

    // Process document
    const buffer = Buffer.from(await file.arrayBuffer());
    const startTime = Date.now();

    const processedDocument = await documentProcessor.processDocument(
      buffer,
      file.type,
      processingOptions
    );

    let nlpAnalysis;
    if (nlpOptions.includeNLP && processedDocument.text.trim().length > 0) {
      try {
        nlpAnalysis = await nlpAnalyzer.analyzeContent(processedDocument.text, {
          includeSentiment: nlpOptions.includeSentiment,
          includeTopics: nlpOptions.includeTopics,
          includePollSuggestions: nlpOptions.includePollSuggestions,
          includeQuality: nlpOptions.includeQuality,
          audience: nlpOptions.audience,
          useAI: FeatureGate.isEnabled('structuredGeneration')
        });
      } catch (error) {
        console.warn('NLP analysis failed:', error);
        // Continue without NLP analysis
      }
    }

    const totalProcessingTime = Date.now() - startTime;

    // Prepare response
    const response = {
      document: {
        name: file.name,
        type: file.type,
        size: file.size,
        ...processedDocument
      },
      nlpAnalysis,
      processing: {
        totalTime: totalProcessingTime,
        documentProcessingTime: processedDocument.processingTime,
        nlpProcessingTime: nlpAnalysis ? totalProcessingTime - processedDocument.processingTime : 0,
        featuresUsed: {
          ocr: processingOptions.enableOCR,
          sentiment: nlpOptions.includeSentiment,
          topics: nlpOptions.includeTopics,
          pollSuggestions: nlpOptions.includePollSuggestions,
          quality: nlpOptions.includeQuality
        }
      },
      meta: {
        supportedTypes: documentProcessor.getSupportedMimeTypes(),
        maxFileSize: `${maxSize / (1024 * 1024)}MB`,
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Enhanced document processing error:', error);

    return NextResponse.json(
      {
        error: 'Failed to process document',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET: Get processing capabilities and status
 */
export async function GET() {
  return NextResponse.json({
    capabilities: {
      supportedTypes: documentProcessor.getSupportedMimeTypes(),
      maxFileSize: `${parseInt(process.env.MAX_DOCUMENT_SIZE_MB || '50')}MB`,
      features: {
        ocr: FeatureGate.isEnabled('ocrProcessing'),
        sentiment: FeatureGate.isEnabled('sentimentAnalysis'),
        structuredGeneration: FeatureGate.isEnabled('structuredGeneration'),
        batchProcessing: FeatureGate.isEnabled('batchProcessing')
      }
    },
    configuration: {
      ocrConfidenceThreshold: parseFloat(process.env.OCR_CONFIDENCE_THRESHOLD || '0.8'),
      maxBatchSize: parseInt(process.env.MAX_BATCH_SIZE || '10'),
      defaultOcrLanguage: 'eng'
    },
    status: 'operational',
    timestamp: new Date().toISOString()
  });
}
