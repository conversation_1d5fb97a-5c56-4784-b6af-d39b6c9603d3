import { NextRequest, NextResponse } from 'next/server';
import { extractContentFromUrlServerless } from '@/lib/services/alternative-extractor';
import { withExtractionErrorHandling } from '@/lib/services/extraction-error-handler';
import { ExtractionError } from '@/lib/services/extraction-error';

/**
 * API endpoint to extract content from URLs
 * This provides a more reliable way to extract content in production environments
 * with proper error handling and timeouts
 */
async function handler(req: NextRequest) {
  if (req.method !== 'POST') {
    return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    // Parse the JSON body
    const body = await req.json();
    const { url } = body;

    if (!url || typeof url !== 'string') {
      throw new ExtractionError('URL is required', 400, 'MISSING_URL');
    }

    if (!url.startsWith('http')) {
      throw new ExtractionError('Invalid URL format', 400, 'INVALID_URL');
    }

    // Extract content using the serverless-friendly extractor
    const content = await extractContentFromUrlServerless(url);

    // Return the extracted content
    return NextResponse.json({ content, url });

  } catch (error) {
    // Let the error handling middleware handle the error
    throw error;
  }
}

// Export the POST handler with error handling applied
export async function POST(req: NextRequest, ...args: unknown[]) {
  return await withExtractionErrorHandling(handler, req, ...args);
}
