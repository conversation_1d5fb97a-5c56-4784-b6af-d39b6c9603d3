import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// Check if we have the required environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_MCP_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables for profile creation')
}

// Create a service role client for server-side operations
const supabaseAdmin = supabaseUrl && supabaseServiceKey
  ? createClient(
      supabaseUrl,
      supabaseServiceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
  : null

export async function POST(request: NextRequest) {
  try {
    // Check if we have the admin client available
    if (!supabaseAdmin) {
      console.error('Supabase admin client not available - missing environment variables')
      return NextResponse.json(
        { error: 'Service configuration error' },
        { status: 500 }
      )
    }

    const body = await request.json()
    const { userId, username, fullName, email } = body

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Check if profile already exists
    const { data: existingProfile } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single()

    if (existingProfile) {
      return NextResponse.json({ message: 'Profile already exists', profile: existingProfile })
    }

    // Create the profile using service role (bypasses RLS)
    // Use the name field instead of username/full_name to match your table structure
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: userId,
        name: fullName || username || 'User',
        email: email,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (profileError) {
      console.error('Profile creation error:', profileError)
      return NextResponse.json(
        { error: 'Failed to create profile', details: profileError },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Profile created successfully', profile }, { status: 201 })
  } catch (error) {
    console.error('Unexpected error in create-profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
