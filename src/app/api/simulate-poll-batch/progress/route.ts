import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { getProgress } from '@/lib/utils/simulation-progress';
import { Database } from '@/lib/database.types';

// Using the utility functions from simulation-progress.ts instead of defining them here

// Server-sent events endpoint for progress updates
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll(),
          setAll: (cookiesToSet) => {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get simulation ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Missing simulation ID' },
        { status: 400 }
      );
    }

    // Set up server-sent events
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        // Send initial progress
        const progress = getProgress(id) || {
          batchId: id,
          totalDemographics: 0,
          completedDemographics: 0,
          status: 'pending',
          startedAt: new Date()
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(progress)}\n\n`));

        // Set up interval to check for progress updates
        const interval = setInterval(() => {
          const currentProgress = getProgress(id);

          if (currentProgress) {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(currentProgress)}\n\n`));

            // Close stream if simulation is completed or failed
            if (currentProgress.status === 'completed' || currentProgress.status === 'failed') {
              clearInterval(interval);
              controller.close();
            }
          }
        }, 1000);

        // Clean up interval if connection is closed
        request.signal.addEventListener('abort', () => {
          clearInterval(interval);
        });
      }
    });

    // Return server-sent events stream
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  } catch (error) {
    console.error('Progress API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
