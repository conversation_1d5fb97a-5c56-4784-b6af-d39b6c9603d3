import { NextRequest, NextResponse } from 'next/server';
import { simulationCacheService } from '@/lib/services/simulation-cache';
import { SimulationResponse } from '@/lib/types/simulation';

/**
 * API route to check cache status for batch simulations
 * This helps provide immediate feedback to users about which demographics are cached
 */
export async function POST(req: NextRequest) {
  try {
    
    // Parse request body
    const body = await req.json();
    const { pollQuestion, pollOptions, demographics } = body;
    
    if (!pollQuestion || !pollOptions || !demographics || !Array.isArray(demographics)) {
      return NextResponse.json(
        { error: 'Invalid request parameters' },
        { status: 400 }
      );
    }
    
    // Check if the entire batch result is cached
    const batchResult = await simulationCacheService.getBatchResult(
      pollQuestion,
      pollOptions,
      demographics
    );
    
    if (batchResult) {
      // The entire batch is cached
      return NextResponse.json({
        fullyCached: true,
        partiallyCached: false,
        cachedDemographics: demographics.map(d => d.group),
        estimatedTimeSavings: demographics.length * 2 // Estimate 2 seconds per demographic
      });
    }
    
    // Check individual demographic caches
    const cachedResults: Record<string, SimulationResponse> = 
      await simulationCacheService.getBatchSimulations(
        pollQuestion,
        pollOptions,
        demographics
      );
    
    // Determine which demographics are cached
    const cachedDemographics = demographics
      .filter(d => {
        const key = simulationCacheService.generateSimulationKey(
          pollQuestion,
          pollOptions,
          d.group,
          d.size
        );
        return !!cachedResults[key];
      })
      .map(d => d.group);
    
    // Calculate estimated time savings (rough estimate: 2 seconds per cached demographic)
    const estimatedTimeSavings = cachedDemographics.length * 2;
    
    return NextResponse.json({
      fullyCached: false,
      partiallyCached: cachedDemographics.length > 0,
      cachedDemographics,
      estimatedTimeSavings
    });
    
  } catch (error) {
    console.error('Error checking cache status:', error);
    return NextResponse.json(
      { error: 'Failed to check cache status' },
      { status: 500 }
    );
  }
}
