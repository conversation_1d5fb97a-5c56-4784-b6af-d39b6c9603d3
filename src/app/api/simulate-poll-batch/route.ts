import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { SimulationService } from '@/lib/services/simulation';
import { BatchSimulationRequest } from '@/lib/types/simulation';
import { validateSimulationRequest } from '@/lib/utils/prompt-builder';
import { updateProgress } from '@/lib/utils/simulation-progress';
import { checkSimulationRateLimit } from '@/lib/utils/simulation-rate-limiter';
import { Database } from '@/lib/database.types';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll(),
          setAll: (cookiesToSet) => {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: BatchSimulationRequest = await request.json();

    // Validate request structure
    if (!body.pollQuestion || !body.pollOptions || !Array.isArray(body.demographics)) {
      return NextResponse.json(
        { error: 'Invalid request: missing pollQuestion, pollOptions, or demographics' },
        { status: 400 }
      );
    }

    if (body.demographics.length === 0) {
      return NextResponse.json(
        { error: 'At least one demographic must be specified' },
        { status: 400 }
      );
    }

    if (body.demographics.length > 5) {
      return NextResponse.json(
        { error: 'Maximum 5 demographics allowed per batch simulation' },
        { status: 400 }
      );
    }

    // Validate each demographic simulation request
    for (const demographic of body.demographics) {
      const validationErrors = validateSimulationRequest(
        body.pollQuestion,
        body.pollOptions,
        demographic.group,
        demographic.size
      );

      if (validationErrors.length > 0) {
        return NextResponse.json(
          { error: `Invalid demographic ${demographic.group}: ${validationErrors.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Check rate limit for batch simulations (more restrictive)
    const isAllowed = await checkSimulationRateLimit(user.id, 2, 3600); // 2 batch simulations per hour

    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Maximum 2 batch simulations per hour.' },
        { status: 429 }
      );
    }

    // Extract poll ID from request headers
    const pollId = request.headers.get('X-Poll-ID');

    // Process batch simulation
    console.log(`Starting batch simulation for user ${user.id} with ${body.demographics.length} demographics`);

    // Initialize progress tracking
    const batchId = body.batchId || `batch_${Date.now()}`;

    // Create initial progress object
    updateProgress(batchId, {
      batchId,
      totalDemographics: body.demographics.length,
      completedDemographics: 0,
      status: 'running',
      startedAt: new Date()
    });

    // Process simulation with progress tracking
    const result = await SimulationService.processBatchSimulation(
      body,
      pollId || undefined,
      user.id,
      (progress) => {
        // Update progress in the store
        updateProgress(batchId, progress);
      }
    );

    // Mark as completed in progress tracking
    updateProgress(batchId, {
      batchId,
      totalDemographics: body.demographics.length,
      completedDemographics: body.demographics.length,
      status: 'completed',
      startedAt: new Date(result.metadata.completedAt),
      estimatedCompletionAt: new Date(result.metadata.completedAt)
    });

    console.log(`Batch simulation completed: ${result.batchId}`);

    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('Batch simulation API error:', error);

    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded' },
          { status: 429 }
        );
      }

      if (error.message.includes('validation')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error during batch simulation' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll(),
          setAll: (cookiesToSet) => {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const pollId = searchParams.get('pollId');
    const batchId = searchParams.get('batchId');

    if (batchId) {
      // Get specific batch simulation result
      const simulations = await SimulationService.getSimulationsForPoll(pollId || '');
      const batchResult = simulations.find(s =>
        s.simulation_data &&
        typeof s.simulation_data === 'object' &&
        'batchId' in s.simulation_data &&
        s.simulation_data.batchId === batchId
      );

      if (!batchResult) {
        return NextResponse.json(
          { error: 'Batch simulation not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(batchResult.simulation_data, { status: 200 });
    }

    if (pollId) {
      // Get all batch simulations for a poll
      const simulations = await SimulationService.getSimulationsForPoll(pollId);
      const batchSimulations = simulations.filter(s =>
        s.demographic_group === 'batch_simulation'
      );

      return NextResponse.json(batchSimulations, { status: 200 });
    }

    // Get user's batch simulation statistics
    const stats = await SimulationService.getSimulationStats(user.id);
    return NextResponse.json(stats, { status: 200 });

  } catch (error) {
    console.error('GET batch simulation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
