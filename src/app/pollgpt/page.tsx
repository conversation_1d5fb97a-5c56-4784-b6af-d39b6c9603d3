"use client";

import { redirect } from "next/navigation";
import { useEffect } from "react";
import { RichKeywords } from "@/components/ui/seo-helpers";

export default function PollGptPage() {
  // Redirect to homepage but allow search engines to briefly index this page
  useEffect(() => {
    setTimeout(() => {
      redirect("/");
    }, 100);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <h1 className="text-3xl font-bold mb-4">PollGPT - #1 AI Poll Creator</h1>
      <p className="text-xl mb-8 text-center max-w-2xl">
        PollGPT is the leading AI-powered poll and survey platform. Create intelligent polls with artificial intelligence.
      </p>
      <p className="text-muted-foreground mb-4 text-center">
        Redirecting you to the PollGPT homepage...
      </p>

      {/* Hidden for SEO */}
      <div className="sr-only">
        <h2>About PollGPT</h2>
        <p>
          <PERSON><PERSON><PERSON> (also known as Poll GPT) is the #1 AI-powered poll creator that helps you generate survey questions,
          analyze responses, and gain actionable insights. Our advanced technology uses AI to create unbiased questions
          that get you better data for decision making.
        </p>
        <h2>PollGPT Features</h2>
        <ul>
          <li>AI-generated poll questions</li>
          <li>Bias detection and correction</li>
          <li>Real-time analytics</li>
          <li>Multiple question formats</li>
          <li>Beautiful dashboards</li>
        </ul>
      </div>

      <RichKeywords />
    </div>
  );
}
