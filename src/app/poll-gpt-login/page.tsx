"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { PageMetadata } from "../page-metadata";
import { RichKeywords } from "@/components/ui/seo-helpers";
import { LinkArchive } from "@/components/ui/link-archive";
import { SEOConstruct } from "@/components/ui/seo-construct";

export default function PollGptLoginPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <PageMetadata />

      {/* Hero section */}
      <div className="flex-1">
        <div className="container px-4 md:px-6 flex flex-col items-center justify-center min-h-[calc(80vh-4rem)] py-10">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
              <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
            </div>
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Log in to PollGPT
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Access your AI-powered polls, surveys, and insights. The #1 platform for intelligent poll creation.
              </p>
            </div>
            <div className="grid gap-4 mt-6 w-full max-w-sm">
              <div className="space-y-2">
                <Link href="/login" className="w-full">
                  <Button className="w-full" size="lg">
                    Continue to Login
                  </Button>
                </Link>
              </div>
              <div className="flex items-center justify-center">
                <span className="text-sm text-muted-foreground">Don&apos;t have an account?</span>
                <Link href="/register" className="ml-2 text-sm font-medium text-primary hover:underline">
                  Sign up
                </Link>
              </div>
            </div>
          </div>

          {/* Features section */}
          <div className="grid gap-6 md:gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-5xl mx-auto w-full px-4 py-16">
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Secure Login</h3>
              <p className="text-muted-foreground text-center">Access your PollGPT account with secure authentication</p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Poll Dashboard</h3>
              <p className="text-muted-foreground text-center">View and manage all your Poll GPT surveys in one place</p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-6 border rounded-lg">
              <div className="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M3 3v18h18" />
                  <path d="m19 9-5 5-4-4-3 3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Analysis Tools</h3>
              <p className="text-muted-foreground text-center">Access advanced AI analytics for your poll results</p>
            </div>
          </div>

          {/* FAQ section specific to login */}
          <div className="w-full max-w-3xl mx-auto px-4 py-8">
            <h2 className="text-2xl font-bold mb-6 text-center">Frequently Asked Questions</h2>
            <div className="space-y-4">
              <div itemScope itemType="https://schema.org/Question" className="border p-4 rounded-lg">
                <h3 itemProp="name" className="font-medium mb-2">How do I log in to my PollGPT account?</h3>
                <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                  <div itemProp="text" className="text-muted-foreground">
                    <p>Click the &quot;Continue to Login&quot; button above, then enter your email and password. You can also use social login options if you previously set up your account that way.</p>
                  </div>
                </div>
              </div>
              <div itemScope itemType="https://schema.org/Question" className="border p-4 rounded-lg">
                <h3 itemProp="name" className="font-medium mb-2">I forgot my Poll GPT password. What should I do?</h3>
                <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                  <div itemProp="text" className="text-muted-foreground">
                    <p>On the login page, click the &quot;Forgot password?&quot; link. Enter your email address, and we&apos;ll send you instructions to reset your password.</p>
                  </div>
                </div>
              </div>
              <div itemScope itemType="https://schema.org/Question" className="border p-4 rounded-lg">
                <h3 itemProp="name" className="font-medium mb-2">Is the PollGPT login process secure?</h3>
                <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                  <div itemProp="text" className="text-muted-foreground">
                    <p>Yes, PollGPT uses industry-standard encryption and authentication protocols to keep your account secure. We use secure connections and never store plaintext passwords.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SEO Construct for semantic markup */}
      <SEOConstruct />

      {/* Rich keywords section */}
      <RichKeywords />

      {/* Link Archive for SEO */}
      <LinkArchive />

      {/* Footer */}
      <div className="w-full py-6 bg-muted">
        <div className="container px-4 md:px-6 text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
              <span className="font-bold text-lg text-foreground">PollGPT</span>
            </div>
            <nav className="flex gap-4">
              <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
