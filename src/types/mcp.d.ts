// Type definitions for Model Context Protocol packages

declare module '@modelcontextprotocol/sdk' {
  // Define parameter types
  export interface MCPFunctionOptions {
    [key: string]: unknown;
  }

  export interface MCPFunctionResult {
    [key: string]: unknown;
  }

  export type MCPFunctionHandler = (options: MCPFunctionOptions) => Promise<MCPFunctionResult>;

  export interface MCPToolParameter {
    type: string;
    description?: string;
    properties?: Record<string, MCPToolParameter>;
    items?: MCPToolParameter;
    required?: string[];
    enum?: string[];
    [key: string]: unknown;
  }

  export interface MCPTool {
    name: string;
    description: string;
    parameters: MCPToolParameter;
    handler: MCPFunctionHandler;
  }

  export interface MCPServer {
    name: string;
    description: string;
    getTools: () => Promise<Array<MCPTool>>;
  }

  export class SDK {
    constructor();
    register(server: MCPServer): void;
    getTools(): Array<MCPTool>;
  }
}

declare module '@modelcontextprotocol/server-brave-search' {
  export class BraveSearchServer {
    constructor(options: { apiKey: string });
  }
}

declare module '@modelcontextprotocol/server-sequential-thinking' {
  export class SequentialThinkingServer {
    constructor();
  }
}

declare module '@modelcontextprotocol/server-postgres' {
  export class PostgresServer {
    constructor(options: { connectionString: string });
  }
}

declare module 'puppeteer-mcp-server' {
  export class PuppeteerMcpServer {
    constructor();
  }
}
