import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Ensure cookies are properly set with appropriate options
              cookieStore.set(name, value, {
                ...options,
                // Make sure cookies are accessible to client-side code
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                path: '/',
                maxAge: options?.maxAge || 60 * 60 * 24 * 7, // 7 days default
              })
            })
          } catch (error) {
            console.error('Error setting cookies:', error)
          }
        },
      },
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        storageKey: 'sb-sumruaeyfidjlssrmfrm-auth-token',
        debug: true, // Enable debug logging to troubleshoot
      },
    }
  )
}
