// Removed unused supabase import
import { redisCacheService } from './redis-cache';
import { v4 as uuidv4 } from 'uuid';

// Job types for the PollGPT application
export type JobType =
  | 'simulation_task'
  | 'poll_analysis'
  | 'cache_warmup'
  | 'data_cleanup'
  | 'report_generation'
  | 'batch_email'
  | 'poll_scraping'
  | 'ai_processing';

// Job status enumeration
export type JobStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';

// Job priority levels
export type JobPriority = 'low' | 'normal' | 'high' | 'critical';

// Job interface
export interface Job {
  id: string;
  type: JobType;
  status: JobStatus;
  priority: JobPriority;
  payload: Record<string, unknown>;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  updatedAt: Date;
  scheduledFor?: Date;
  completedAt?: Date;
  error?: string;
  result?: unknown;
  userId?: string;
  pollId?: string;
  lastError?: string;
  data?: Record<string, unknown>; // Adding data property for backward compatibility
}

// Job processing result
export interface JobResult {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
}

// Job handler function type
export type JobHandler = (job: Job) => Promise<JobResult>;

/**
 * Background Job Queue System for PollGPT
 *
 * Features:
 * - Priority-based job queues
 * - Job scheduling for future execution
 * - Automatic retries with exponential backoff
 * - Dead letter queue for failed jobs
 * - Concurrent job processing
 * - Job timeouts
 * - Job cancellation
 * - Job progress tracking
 * - Job status monitoring
 * - Job scheduling for future execution
 */
export class JobQueue {
  private readonly handlers: Map<JobType, JobHandler> = new Map();
  private readonly workers: Set<string> = new Set();
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;

  // Queue configuration
  private readonly config = {
    defaultPriority: 'normal' as JobPriority,
    defaultMaxAttempts: 3,
    retryDelay: 1000, // Base retry delay in ms
    maxRetryDelay: 60000, // Max retry delay in ms
    processingInterval: 5000, // Process jobs every 5 seconds
    maxConcurrentJobs: 5,
    jobTimeout: 300000, // 5 minutes timeout per job
  };

  constructor() {
    // No initialization needed
  }

  /**
   * Register a job handler for a specific job type
   */
  registerHandler(type: JobType, handler: JobHandler): void {
    this.handlers.set(type, handler);
    console.log(`[JobQueue] Registered handler for job type: ${type}`);
  }

  /**
   * Add a job to the queue
   */
  async addJob(
    type: JobType,
    payload: Record<string, unknown>,
    options: {
      priority?: JobPriority;
      maxAttempts?: number;
      scheduledFor?: Date;
      userId?: string;
      pollId?: string;
    } = {}
  ): Promise<string> {
    const jobId = uuidv4();
    const now = new Date();
    const job: Job = {
      id: jobId,
      type,
      status: 'pending',
      priority: options.priority || this.config.defaultPriority,
      payload,
      attempts: 0,
      maxAttempts: options.maxAttempts || this.config.defaultMaxAttempts,
      createdAt: now,
      updatedAt: now,
      scheduledFor: options.scheduledFor,
      userId: options.userId,
      pollId: options.pollId,
      data: payload, // Set data to payload for backward compatibility
    };

    // Store job in Redis with appropriate key structure
    const jobKey = this.getJobKey(jobId);
    await redisCacheService.set(jobKey, job, 3600); // Jobs expire after 1 hour if not processed

    // Add to priority queue
    const queueKey = this.getQueueKey(job.priority);
    const score = options.scheduledFor ? options.scheduledFor.getTime() : now.getTime();

    // Store job in queue with score for scheduling
    await redisCacheService.set(`${queueKey}:${jobId}:score`, score, 3600);

    // Track job by type for monitoring
    const typeKey = this.getTypeKey(type);
    await redisCacheService.set(`${typeKey}:${jobId}`, true, 3600);

    // Track user jobs if userId provided
    if (options.userId) {
      const userKey = this.getUserKey(options.userId);
      await redisCacheService.set(`${userKey}:${jobId}`, true, 3600);
    }

    console.log(`[JobQueue] Added job ${jobId} of type ${type} with priority ${job.priority}`);
    return jobId;
  }

  /**
   * Get a job by its ID
   */
  async getJob(jobId: string): Promise<Job | null> {
    const jobKey = this.getJobKey(jobId);
    const job = await redisCacheService.get(jobKey);
    return job as Job | null;
  }

  /**
   * Update a job's status and other properties
   */
  async updateJob(jobId: string, updates: Partial<Job>): Promise<void> {
    const job = await this.getJob(jobId);
    if (!job) {
      console.log(`[JobQueue] Job ${jobId} not found for update`);
      return;
    }

    const updatedJob: Job = {
      ...job,
      ...updates,
      updatedAt: new Date(),
    };

    const jobKey = this.getJobKey(jobId);
    await redisCacheService.set(jobKey, updatedJob, 3600);

    console.log(`[JobQueue] Updated job ${jobId} status to ${updatedJob.status}`);
  }

  /**
   * Start processing jobs
   */
  async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      console.log('[JobQueue] Already processing jobs');
      return;
    }

    this.isProcessing = true;
    console.log('[JobQueue] Starting job processing');

    // Process immediately, then set up interval
    await this.processJobs();

    this.processingInterval = setInterval(async () => {
      await this.processJobs();
    }, this.config.processingInterval);
  }

  /**
   * Stop processing jobs
   */
  stopProcessing(): void {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    console.log('[JobQueue] Stopped job processing');
  }

  /**
   * Process jobs from all priority queues
   */
  private async processJobs(): Promise<void> {
    if (this.workers.size >= this.config.maxConcurrentJobs) {
      return;
    }

    const availableWorkers = this.config.maxConcurrentJobs - this.workers.size;
    if (availableWorkers <= 0) {
      return;
    }

    // Process jobs from each priority queue, starting with highest priority
    const priorities: JobPriority[] = ['critical', 'high', 'normal', 'low'];

    for (const priority of priorities) {
      const jobs = await this.getJobsFromQueue(priority, availableWorkers);

      for (const job of jobs) {
        this.processJob(job).catch(error => {
          console.error(`[JobQueue] Error processing job ${job.id}:`, error);
        });

        if (this.workers.size >= this.config.maxConcurrentJobs) {
          break;
        }
      }

      if (this.workers.size >= this.config.maxConcurrentJobs) {
        break;
      }
    }
  }

  /**
   * Get jobs from a specific priority queue
   */
  private async getJobsFromQueue(priority: JobPriority, count: number): Promise<Job[]> {
    // const queueKey = this.getQueueKey(priority);
    const now = Date.now();
    const jobs: Job[] = [];

    // Get all keys in the cache with the queue prefix
    // In a real implementation, we would use Redis ZRANGEBYSCORE
    // For now, simulate by checking all jobs with the queue prefix

    // This is a simplified implementation
    // In a real system, we would use a proper Redis client with sorted sets
    const allKeys = await this.getAllKeysWithPrefix();

    for (let i = 0; i < allKeys.length && jobs.length < count; i++) {
      const key = allKeys[i];
      if (key.includes(':score')) {
        const jobId = key.split(':')[2]; // Extract jobId from key pattern
        const score = await redisCacheService.get(key) as number;

        if (score <= now) {
          // Remove from queue by deleting the score key
          await redisCacheService.delete(key);

          // Get the actual job data
          const job = await this.getJob(jobId);

          if (job && job.status === 'pending') {
            jobs.push(job);
          }
        }
      }
    }

    return jobs;
  }

  /**
   * Helper method to get all keys with a prefix
   * This simulates Redis KEYS command
   */
  private async getAllKeysWithPrefix(/* _prefix: string */): Promise<string[]> {
    // In a real implementation, this would use Redis KEYS or SCAN
    // For now, we'll return an empty array as we can't list all keys in the cache
    return [];
  }

  /**
   * Process a single job
   */
  private async processJob(job: Job): Promise<void> {
    const workerId = uuidv4();
    this.workers.add(workerId);

    try {
      // Mark job as processing
      await this.updateJob(job.id, {
        status: 'processing',
        attempts: job.attempts + 1,
      });

      console.log(`[JobQueue] Processing job ${job.id} of type ${job.type} (attempt ${job.attempts + 1}/${job.maxAttempts})`);

      // Get handler for job type
      const handler = this.handlers.get(job.type);
      if (!handler) {
        throw new Error(`No handler registered for job type: ${job.type}`);
      }

      // Set up timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Job ${job.id} timed out after ${this.config.jobTimeout}ms`));
        }, this.config.jobTimeout);
      });

      // Execute handler with timeout
      const result = await Promise.race([
        handler(job),
        timeoutPromise,
      ]);

      // Mark job as completed
      await this.updateJob(job.id, {
        status: 'completed',
        completedAt: new Date(),
        result: result.data,
      });

      console.log(`[JobQueue] Job ${job.id} completed successfully`);
    } catch (error) {
      console.error(`[JobQueue] Job ${job.id} failed:`, error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check if we should retry
      if (job.attempts < job.maxAttempts) {
        await this.scheduleRetry(job, errorMessage);
      } else {
        // Mark as failed
        await this.updateJob(job.id, {
          status: 'failed',
          error: errorMessage,
        });

        // Move to dead letter queue
        await this.moveToDeadLetterQueue(job);
      }
    } finally {
      this.workers.delete(workerId);
    }
  }

  /**
   * Schedule a job for retry
   */
  private async scheduleRetry(job: Job, errorMessage: string): Promise<void> {
    // Calculate retry delay with exponential backoff
    const retryAttempt = job.attempts;
    const delay = Math.min(
      this.config.retryDelay * Math.pow(2, retryAttempt - 1),
      this.config.maxRetryDelay
    );

    const retryAt = new Date(Date.now() + delay);

    // Update job status
    await this.updateJob(job.id, {
      status: 'pending',
      scheduledFor: retryAt,
      lastError: errorMessage,
    });

    // Re-add to queue with delay
    const queueKey = this.getQueueKey(job.priority);
    await redisCacheService.set(`${queueKey}:${job.id}:score`, retryAt.getTime(), 3600);

    console.log(`[JobQueue] Job ${job.id} scheduled for retry in ${delay}ms`);
  }

  /**
   * Move a job to the dead letter queue
   */
  private async moveToDeadLetterQueue(job: Job): Promise<void> {
    const dlqKey = 'job_queue:dead_letter';
    await redisCacheService.set(`${dlqKey}:${job.id}`, Date.now(), 3600);
    console.log(`[JobQueue] Job ${job.id} moved to dead letter queue`);
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = await this.getJob(jobId);
    if (!job) {
      console.log(`[JobQueue] Job ${jobId} not found for cancellation`);
      return false;
    }

    if (job.status === 'completed' || job.status === 'failed') {
      console.log(`[JobQueue] Job ${jobId} already finished with status ${job.status}`);
      return false;
    }

    // Remove from queue
    const queueKey = this.getQueueKey(job.priority);
    await redisCacheService.delete(`${queueKey}:${jobId}:score`);

    // Update status
    await this.updateJob(jobId, {
      status: 'failed',
      error: 'Cancelled by user',
      lastError: 'Cancelled by user'
    });

    console.log(`[JobQueue] Job ${jobId} cancelled`);
    return true;
  }

  /**
   * Get key for a job in Redis
   */
  private getJobKey(jobId: string): string {
    return `job_queue:jobs:${jobId}`;
  }

  /**
   * Get key for a priority queue in Redis
   */
  private getQueueKey(priority: JobPriority): string {
    return `job_queue:queues:${priority}`;
  }

  /**
   * Get key for job type set in Redis
   */
  private getTypeKey(type: JobType): string {
    return `job_queue:types:${type}`;
  }

  /**
   * Get key for user jobs set in Redis
   */
  private getUserKey(userId: string): string {
    return `job_queue:users:${userId}`;
  }

  /**
   * Get job queue statistics
   */
  async getStats(): Promise<Record<string, unknown>> {
    try {
      // Get counts for each job status
      const pending = 0;   // In a real implementation, this would count pending jobs
      const processing = this.workers.size;
      const completed = 0;  // In a real implementation, this would count completed jobs
      const failed = 0;     // In a real implementation, this would count failed jobs

      return {
        pending,
        processing,
        completed,
        failed,
        workers: this.workers.size,
        maxConcurrent: this.config.maxConcurrentJobs,
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('[JobQueue] Error getting stats:', error);
      return {
        error: 'Failed to get job queue statistics',
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
export const jobQueue = new JobQueue();

// Define job handlers
const jobHandlers: Record<string, JobHandler> = {
  simulation_task: async (job: Job): Promise<JobResult> => {
    const pollId = job.pollId || (job.data?.pollId as string | undefined);

    try {
      // Simulation logic would go here
      console.log(`[JobHandler] Simulating poll responses for poll ${pollId || 'unknown'}`);

      return {
        success: true,
        data: {
          simulationCompleted: true,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {},
        error: error instanceof Error ? error.message : 'Simulation failed'
      };
    }
  },

  poll_analysis: async (job: Job): Promise<JobResult> => {
    const pollId = job.pollId || (job.data?.pollId as string | undefined);

    try {
      // Analysis logic would go here
      console.log(`[JobHandler] Analyzing poll ${pollId || 'unknown'}`);

      return {
        success: true,
        data: {
          analysisCompleted: true,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {},
        error: error instanceof Error ? error.message : 'Analysis failed'
      };
    }
  },

  cache_warmup: async (): Promise<JobResult> => {
    try {
      console.log('[JobHandler] Warming up cache');

      // Cache warming logic would go here

      return {
        success: true,
        data: {
          cacheWarmed: true,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {},
        error: error instanceof Error ? error.message : 'Cache warmup failed'
      };
    }
  },

  data_cleanup: async (): Promise<JobResult> => {
    try {
      console.log('[JobHandler] Cleaning up old data');

      // Data cleanup logic would go here

      return {
        success: true,
        data: {
          cleanupCompleted: true,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {},
        error: error instanceof Error ? error.message : 'Data cleanup failed'
      };
    }
  },

  report_generation: async (job: Job): Promise<JobResult> => {
    const pollId = job.pollId || (job.data?.pollId as string | undefined);
    const reportType = job.data?.reportType as string | undefined || 'standard';

    try {
      console.log(`[JobHandler] Generating ${reportType} report for poll ${pollId || 'unknown'}`);

      // Generate comprehensive report
      const reportData = {
        pollId,
        type: reportType,
        generatedAt: new Date(),
        data: {
          // This would contain the actual report data
          summary: 'Generated by background job',
        },
      };

      // Store report or send notification
      return { success: true, data: reportData };
    } catch (error) {
      return {
        success: false,
        data: {},
        error: error instanceof Error ? error.message : 'Report generation failed'
      };
    }
  },
};

// Register all job handlers
Object.entries(jobHandlers).forEach(([type, handler]) => {
  jobQueue.registerHandler(type as JobType, handler);
});
