// Enhanced Database Service with Connection Pooling and Caching
import { supabase } from '@/lib/supabase';
import { QueryClient } from '@tanstack/react-query';

// Types for better type safety
interface Question {
  id?: string;
  poll_id?: string;
  question_text: string;
  question_type: string;
  options?: Record<string, unknown>;
  required?: boolean;
  order: number;
}

interface Answer {
  question_id: string;
  answer_value: string;
}

// Enhanced database service with performance optimizations
export class OptimizedDatabaseService {
  private static instance: OptimizedDatabaseService;
  private queryClient: QueryClient;

  private constructor() {
    this.queryClient = new QueryClient();
    this.initializeConnectionPool();
  }

  public static getInstance(): OptimizedDatabaseService {
    if (!OptimizedDatabaseService.instance) {
      OptimizedDatabaseService.instance = new OptimizedDatabaseService();
    }
    return OptimizedDatabaseService.instance;
  }

  private initializeConnectionPool() {
    // Connection pool will be handled by Supabase, but we can optimize our queries
    console.log('Initializing optimized database service...');
  }

  // Optimized poll fetching with caching
  async getPolls(userId: string, options: {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    useCache?: boolean;
  } = {}) {
    const { page = 1, pageSize = 20, search = '', status, useCache = true } = options;

    const cacheKey = ['polls', userId, page, pageSize, search, status];

    // Check cache first if enabled
    if (useCache) {
      const cachedData = this.queryClient.getQueryData(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      // Use the optimized database function
      const { data, error } = await supabase.rpc('get_polls_with_counts', {
        user_id_param: userId,
        page_number: page,
        page_size: pageSize,
        fetch_all: false,
        search_query_param: search,
        status_filter_param: status,
      });

      if (error) throw error;

      // Cache the result
      if (useCache) {
        this.queryClient.setQueryData(cacheKey, data, {
          updatedAt: Date.now(),
        });
      }

      return data;
    } catch (error) {
      console.error('Error fetching polls:', error);
      throw error;
    }
  }

  // Optimized single poll fetching
  async getPoll(pollId: string, userId: string, useCache: boolean = true) {
    const cacheKey = ['poll', pollId];

    if (useCache) {
      const cachedData = this.queryClient.getQueryData(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      const { data, error } = await supabase.rpc('get_poll_with_counts', {
        poll_id_param: pollId,
        user_id_param: userId,
      });

      if (error) throw error;

      if (useCache) {
        this.queryClient.setQueryData(cacheKey, data);
      }

      return data;
    } catch (error) {
      console.error('Error fetching poll:', error);
      throw error;
    }
  }

  // Batch operations for better performance
  async batchCreateQuestions(pollId: string, questions: Question[]) {
    try {
      const { data, error } = await supabase
        .from('questions')
        .insert(questions.map(q => ({ ...q, poll_id: pollId })))
        .select();

      if (error) throw error;

      // Invalidate related caches
      this.queryClient.invalidateQueries({ queryKey: ['poll', pollId] });
      this.queryClient.invalidateQueries({ queryKey: ['polls'] });

      return data;
    } catch (error) {
      console.error('Error batch creating questions:', error);
      throw error;
    }
  }

  // Optimized response submission
  async submitResponse(pollId: string, answers: Answer[]) {
    try {
      // Create response first
      const { data: response, error: responseError } = await supabase
        .from('responses')
        .insert({
          poll_id: pollId,
          created_at: new Date().toISOString(),
        })
        .select('id')
        .single();

      if (responseError) throw responseError;

      // Batch insert answers
      const { data: answersData, error: answersError } = await supabase
        .from('answers')
        .insert(answers.map(answer => ({
          response_id: response.id,
          question_id: answer.question_id,
          answer_value: answer.answer_value,
        })))
        .select();

      if (answersError) throw answersError;

      // Invalidate related caches
      this.queryClient.invalidateQueries({ queryKey: ['poll', pollId] });
      this.queryClient.invalidateQueries({ queryKey: ['polls'] });

      return { response, answers: answersData };
    } catch (error) {
      console.error('Error submitting response:', error);
      throw error;
    }
  }

  // Cache management utilities
  clearCache(pattern?: string) {
    if (pattern) {
      this.queryClient.removeQueries({ queryKey: [pattern] });
    } else {
      this.queryClient.clear();
    }
  }

  // Preload data for better UX
  async preloadPolls(userId: string) {
    try {
      await this.getPolls(userId, { page: 1, pageSize: 10, useCache: true });
    } catch (error) {
      console.error('Error preloading polls:', error);
    }
  }

  // Database health check
  async healthCheck() {
    try {
      const { error } = await supabase
        .from('polls')
        .select('count(*)')
        .limit(1);

      if (error) throw error;

      return { healthy: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Database health check failed:', error);
      return { healthy: false, error: (error as Error).message, timestamp: new Date().toISOString() };
    }
  }
}

// Export singleton instance
export const optimizedDb = OptimizedDatabaseService.getInstance();

// Utility functions for common operations
export const dbUtils = {
  // Debounced search for better performance
  debounce: <T extends (...args: unknown[]) => void>(func: T, wait: number) => {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Connection status monitoring
  monitorConnection: () => {
    // This would be implemented with actual connection monitoring
    console.log('Monitoring database connection health...');
  },

  // Performance metrics
  getPerformanceMetrics: () => {
    // This would return actual performance metrics
    return {
      avgQueryTime: 0,
      activeConnections: 0,
      cacheHitRate: 0,
    };
  },
};
