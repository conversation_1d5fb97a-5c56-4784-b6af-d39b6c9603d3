/**
 * Rate Limiter Service
 *
 * Implements Redis-based rate limiting for API calls with multiple limiting strategies:
 * - Fixed window rate limiting
 * - Sliding window log rate limiting
 * - Token bucket rate limiting
 * - IP-based and user-based limiting
 * - Global rate limiting with quotas
 */

import Redis from 'ioredis';
import type { NextApiRequest, NextApiResponse } from 'next';

interface RateLimitConfig {
  windowMs: number;        // Time window in milliseconds
  maxRequests: number;     // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (identifier: string) => string;
  onLimitReached?: (identifier: string) => void;
  strategy?: 'fixed' | 'sliding' | 'token_bucket';
}

interface TokenBucketConfig extends RateLimitConfig {
  capacity: number;        // Token bucket capacity
  refillRate: number;      // Tokens added per second
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

interface RateLimitInfo {
  identifier: string;
  requests: number;
  windowStart: number;
  lastRefill?: number;
  tokens?: number;
}

class RateLimiter {
  private redis: Redis;
  private fallbackCache = new Map<string, RateLimitInfo>();
  private configs = new Map<string, RateLimitConfig>();

  // Predefined rate limit configurations
  private static readonly PRESETS = {
    // API endpoint limits
    strict: { windowMs: 60 * 1000, maxRequests: 10 },      // 10 req/min
    moderate: { windowMs: 60 * 1000, maxRequests: 60 },    // 60 req/min
    lenient: { windowMs: 60 * 1000, maxRequests: 300 },    // 300 req/min

    // Special purpose limits
    auth: { windowMs: 15 * 60 * 1000, maxRequests: 5 },    // 5 auth attempts per 15min
    simulation: { windowMs: 5 * 60 * 1000, maxRequests: 10 }, // 10 simulations per 5min
    ai_requests: { windowMs: 60 * 1000, maxRequests: 20 },  // 20 AI requests per minute

    // Global limits
    global_user: { windowMs: 60 * 60 * 1000, maxRequests: 1000 }, // 1000 req/hour per user
    global_ip: { windowMs: 60 * 60 * 1000, maxRequests: 2000 },   // 2000 req/hour per IP
  };

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.redis.on('error', (error) => {
      console.warn('Redis rate limiter connection error:', error.message);
    });

    // Initialize default configurations
    Object.entries(RateLimiter.PRESETS).forEach(([name, config]) => {
      this.configs.set(name, config);
    });
  }

  /**
   * Add or update a rate limit configuration
   */
  public setConfig(name: string, config: RateLimitConfig): void {
    this.configs.set(name, config);
  }

  /**
   * Check if a request should be rate limited
   */
  public async checkLimit(
    identifier: string,
    configName: string = 'moderate'
  ): Promise<RateLimitResult> {
    const config = this.configs.get(configName);
    if (!config) {
      throw new Error(`Rate limit configuration '${configName}' not found`);
    }

    try {
      switch (config.strategy || 'fixed') {
        case 'sliding':
          return await this.checkSlidingWindow(identifier, config);
        case 'token_bucket':
          return await this.checkTokenBucket(identifier, config as TokenBucketConfig);
        default:
          return await this.checkFixedWindow(identifier, config);
      }
    } catch (error) {
      console.error('Rate limit check failed, falling back to in-memory:', error);
      return this.checkFixedWindowFallback(identifier, config);
    }
  }

  /**
   * Fixed window rate limiting using Redis
   */
  private async checkFixedWindow(
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = `rate_limit:${identifier}:${config.windowMs}`;
    const now = Date.now();
    const window = Math.floor(now / config.windowMs);
    const windowKey = `${key}:${window}`;

    const multi = this.redis.multi();
    multi.incr(windowKey);
    multi.expire(windowKey, Math.ceil(config.windowMs / 1000));

    const results = await multi.exec();
    const requestCount = results?.[0]?.[1] as number || 0;

    const remaining = Math.max(0, config.maxRequests - requestCount);
    const resetTime = (window + 1) * config.windowMs;

    return {
      allowed: requestCount <= config.maxRequests,
      limit: config.maxRequests,
      remaining,
      resetTime,
      retryAfter: requestCount > config.maxRequests ?
        Math.ceil((resetTime - now) / 1000) : undefined
    };
  }

  /**
   * Sliding window log rate limiting
   */
  private async checkSlidingWindow(
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = `rate_limit_sliding:${identifier}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    const multi = this.redis.multi();
    // Remove expired entries
    multi.zremrangebyscore(key, 0, windowStart);
    // Add current request
    multi.zadd(key, now, `${now}-${Math.random()}`);
    // Count requests in window
    multi.zcard(key);
    // Set expiration
    multi.expire(key, Math.ceil(config.windowMs / 1000));

    const results = await multi.exec();
    const requestCount = results?.[2]?.[1] as number || 0;

    const remaining = Math.max(0, config.maxRequests - requestCount);
    const resetTime = now + config.windowMs;

    return {
      allowed: requestCount <= config.maxRequests,
      limit: config.maxRequests,
      remaining,
      resetTime,
      retryAfter: requestCount > config.maxRequests ?
        Math.ceil(config.windowMs / 1000) : undefined
    };
  }

  /**
   * Token bucket rate limiting
   */
  private async checkTokenBucket(
    identifier: string,
    config: TokenBucketConfig
  ): Promise<RateLimitResult> {
    const key = `rate_limit_bucket:${identifier}`;
    const now = Date.now();

    // Lua script for atomic token bucket operations
    const luaScript = `
      local key = KEYS[1]
      local capacity = tonumber(ARGV[1])
      local refillRate = tonumber(ARGV[2])
      local now = tonumber(ARGV[3])

      local bucket = redis.call('HMGET', key, 'tokens', 'lastRefill')
      local tokens = tonumber(bucket[1]) or capacity
      local lastRefill = tonumber(bucket[2]) or now

      -- Calculate tokens to add based on time elapsed
      local elapsed = (now - lastRefill) / 1000
      local tokensToAdd = elapsed * refillRate
      tokens = math.min(capacity, tokens + tokensToAdd)

      -- Check if request can be served
      local allowed = tokens >= 1
      if allowed then
        tokens = tokens - 1
      end

      -- Update bucket state
      redis.call('HMSET', key, 'tokens', tokens, 'lastRefill', now)
      redis.call('EXPIRE', key, 3600)

      return {allowed and 1 or 0, tokens, capacity}
    `;

    const result = await this.redis.eval(
      luaScript,
      1,
      key,
      config.capacity.toString(),
      config.refillRate.toString(),
      now.toString()
    ) as [number, number, number];

    const [allowedNum, tokens, capacity] = result;
    const allowed = allowedNum === 1;

    return {
      allowed,
      limit: capacity,
      remaining: Math.floor(tokens),
      resetTime: now + (capacity - tokens) / config.refillRate * 1000,
      retryAfter: allowed ? undefined : Math.ceil(1 / config.refillRate)
    };
  }

  /**
   * Fallback to in-memory rate limiting when Redis is unavailable
   */
  private checkFixedWindowFallback(
    identifier: string,
    config: RateLimitConfig
  ): RateLimitResult {
    const now = Date.now();
    const window = Math.floor(now / config.windowMs);
    const key = `${identifier}:${window}`;

    let info = this.fallbackCache.get(key);
    if (!info) {
      info = { identifier, requests: 0, windowStart: window * config.windowMs };
      this.fallbackCache.set(key, info);
    }

    info.requests++;

    // Clean up old entries
    setTimeout(() => {
      this.fallbackCache.delete(key);
    }, config.windowMs);

    const remaining = Math.max(0, config.maxRequests - info.requests);
    const resetTime = (window + 1) * config.windowMs;

    return {
      allowed: info.requests <= config.maxRequests,
      limit: config.maxRequests,
      remaining,
      resetTime,
      retryAfter: info.requests > config.maxRequests ?
        Math.ceil((resetTime - now) / 1000) : undefined
    };
  }

  /**
   * Get current usage statistics for an identifier
   */
  public async getUsageStats(
    identifier: string,
    configName: string = 'moderate'
  ): Promise<RateLimitInfo | null> {
    const config = this.configs.get(configName);
    if (!config) return null;

    try {
      const key = `rate_limit:${identifier}:${config.windowMs}`;
      const now = Date.now();
      const window = Math.floor(now / config.windowMs);
      const windowKey = `${key}:${window}`;

      const requests = await this.redis.get(windowKey);

      return {
        identifier,
        requests: parseInt(requests || '0'),
        windowStart: window * config.windowMs
      };
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      return null;
    }
  }

  /**
   * Reset rate limit for an identifier
   */
  public async resetLimit(identifier: string, configName?: string): Promise<void> {
    try {
      if (configName) {
        const config = this.configs.get(configName);
        if (config) {
          const key = `rate_limit:${identifier}:${config.windowMs}`;
          await this.redis.del(`${key}:*`);
        }
      } else {
        // Reset all limits for identifier
        const keys = await this.redis.keys(`rate_limit:${identifier}:*`);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
    } catch (error) {
      console.error('Failed to reset rate limit:', error);
    }
  }

  /**
   * Get rate limiting middleware for Express/Next.js
   */
  public middleware(configName: string = 'moderate') {
    return async (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void> | void) => {
      const identifier = (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() || req.socket?.remoteAddress || 'unknown';
      const result = await this.checkLimit(identifier, configName);

      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', result.limit.toString());
      res.setHeader('X-RateLimit-Remaining', result.remaining.toString());
      res.setHeader('X-RateLimit-Reset', new Date(result.resetTime).toISOString());

      if (!result.allowed) {
        if (result.retryAfter) {
          res.setHeader('Retry-After', result.retryAfter.toString());
        }

        const config = this.configs.get(configName);
        config?.onLimitReached?.(identifier);

        return res.status(429).json({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter: result.retryAfter
        });
      }

      next();
    };
  }

  /**
   * Close Redis connection
   */
  public async close(): Promise<void> {
    await this.redis.quit();
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

// Export for named imports
export { rateLimiter, RateLimiter };

// Also export as default for modules that use default import
export default rateLimiter;

// Export utility functions
export const checkRateLimit = (identifier: string, configName?: string) =>
  rateLimiter.checkLimit(identifier, configName);

export const resetRateLimit = (identifier: string, configName?: string) =>
  rateLimiter.resetLimit(identifier, configName);

export const rateLimitMiddleware = (configName?: string) =>
  rateLimiter.middleware(configName);
