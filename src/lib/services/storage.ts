import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define the bucket name as a constant
const CONTEXT_BUCKET_NAME = 'context';

/**
 * Interface for file upload response
 */
export interface FileUploadResponse {
  url: string;
  path: string;
  filename: string;
  contentType: string;
  size: number;
}

/**
 * Uploads a file to the Supabase storage "context" bucket
 * @param file The file to upload
 * @param customFilename Optional custom filename to use instead of the original
 * @param customClient Optional Supabase client to use (for server-side operations)
 * @returns Promise with the file URL and metadata
 */
export async function uploadContextFile(
  file: File,
  customFilename?: string,
  customClient?: SupabaseClient<Database>
): Promise<FileUploadResponse> {
  // Use provided client or fall back to browser client
  const client = customClient || supabase;

  try {
    // Generate a unique filename to avoid collisions
    const fileExtension = file.name.split('.').pop() || '';
    const uniqueId = uuidv4().substring(0, 8);
    const timestamp = Date.now();
    
    // Use custom filename if provided, otherwise use original name with uniqueness
    const filename = customFilename 
      ? `${customFilename.replace(/\s+/g, '_')}_${uniqueId}.${fileExtension}`
      : `${file.name.replace(/\s+/g, '_').replace(`.${fileExtension}`, '')}_${timestamp}_${uniqueId}.${fileExtension}`;
    
    // Create a storage path with user folder structure
    const { data: { user } } = await client.auth.getUser();
    if (!user) {
      throw new Error('User must be logged in to upload files');
    }
    
    // Use user ID as folder name for organization and security
    const storagePath = `${user.id}/${filename}`;
    
    // Upload the file to the context bucket
    const { data, error } = await client.storage
      .from(CONTEXT_BUCKET_NAME)
      .upload(storagePath, file, {
        contentType: file.type,
        upsert: true, // Overwrite if file exists
      });
    
    if (error) {
      console.error('Error uploading file to Supabase storage:', error);
      throw error;
    }
    
    if (!data) {
      throw new Error('File upload failed with no error');
    }
    
    // Get the public URL for the file
    const publicUrl = client.storage
      .from(CONTEXT_BUCKET_NAME)
      .getPublicUrl(storagePath).data.publicUrl;
    
    if (!publicUrl) {
      throw new Error('Failed to get public URL for uploaded file');
    }
    
    return {
      url: publicUrl,
      path: storagePath,
      filename: filename,
      contentType: file.type,
      size: file.size,
    };
  } catch (error) {
    console.error('File upload service error:', error);
    throw error;
  }
};

/**
 * Deletes a file from the Supabase storage "context" bucket
 * @param path The storage path of the file to delete
 * @param customClient Optional Supabase client to use (for server-side operations)
 * @returns Promise with success status
 */
export async function deleteContextFile(path: string, customClient?: SupabaseClient<Database>): Promise<boolean> {
  try {
    const client = customClient || supabase;
    const { error } = await client.storage
      .from(CONTEXT_BUCKET_NAME)
      .remove([path]);
    
    if (error) {
      console.error('Error deleting file from Supabase storage:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('File deletion service error:', error);
    throw error;
  }
};

/**
 * Gets the public URL for a file in the Supabase storage "context" bucket
 * @param path The storage path of the file
 * @param customClient Optional Supabase client to use (for server-side operations)
 * @returns The public URL for the file
 */
export function getContextFileUrl(path: string, customClient?: SupabaseClient<Database>): string {
  const client = customClient || supabase;
  return client.storage
    .from(CONTEXT_BUCKET_NAME)
    .getPublicUrl(path).data.publicUrl;
};

/**
 * Interface for file objects returned by Supabase storage
 */
export interface StorageFileObject {
  name: string;
  id: string;
  metadata: {
    size: number;
    mimetype: string;
    cacheControl: string;
    lastModified: string;
  };
  created_at: string;
  updated_at: string;
  last_accessed_at: string;
}

/**
 * Lists all files in a user's folder in the "context" bucket
 * @param customClient Optional Supabase client to use (for server-side operations)
 * @returns Promise with array of file objects
 */
export async function listContextFiles(customClient?: SupabaseClient<Database>): Promise<StorageFileObject[]> {
  try {
    const client = customClient || supabase;
    // Get the current user
    const { data: { user } } = await client.auth.getUser();
    if (!user) {
      throw new Error('User must be logged in to list files');
    }
    
    // List files in the user's folder
    const { data, error } = await client.storage
      .from(CONTEXT_BUCKET_NAME)
      .list(user.id);
    
    if (error) {
      console.error('Error listing files from Supabase storage:', error);
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('File listing service error:', error);
    throw error;
  }
};
