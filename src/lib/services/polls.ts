import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { Poll, PollQuestion, QuestionType } from '@/lib/validation/schemas';
// Import getItem, setItem, and removeItem for application-specific cache
import { getItem, setItem, removeItem, StorageKey } from './local-storage';

// Helper function to generate a unique ID
export const generateId = () => {
  return uuidv4();
};

// Helper function to generate a unique slug
export const generateSlug = (title: string): string => {
  // Convert title to slug format
  const baseSlug = title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric characters with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading and trailing hyphens
    .substring(0, 50); // Limit length to 50 characters

  // Add timestamp to ensure uniqueness
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8); // 6 random characters

  return `${baseSlug || 'poll'}-${timestamp}-${randomSuffix}`;
};

// Define the database question shape for consistent typing
export interface DbQuestionShape {
  id: string;
  poll_id: string;
  question_text: string;
  question_type: string;
  options?: Array<{id: string; text: string; value: string}>;
  required?: boolean;
  order?: number;
}

export interface PollResponse {
  id: string;
  pollId: string;
  responses: Record<string, string | string[]>;
  submittedAt: string;
  respondentInfo?: {
    deviceType: string;
    browser: string;
    os: string;
    region?: string;
  }
  deviceType: string;
  browser: string;
  os: string;
  region?: string;
}

// Interface for pagination results

// Shape of the payload for updating a poll in the database
export interface PollDbUpdatePayload {
  title?: string;
  slug?: string;
  description?: string | null;
  expires_at?: string | null;
  status?: 'draft' | 'active' | 'completed';
  is_public?: boolean;
  access_code?: string | null;
  context?: string | null;
  source_url?: string | null;
  source_type?: 'url' | 'pdf' | 'website' | null;
  source_filename?: string | null;
  show_source?: boolean;
  // user_id should not be updatable here
  // created_at is fixed
  // responses and views are derived counts, not directly updated
}

export interface PaginatedPolls {
  polls: Poll[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

// Enhanced caching system for polls data
const pollsCache = new Map<string, { data: PaginatedPolls, timestamp: number, etag?: string }>();
const CACHE_EXPIRY_MS = 15 * 60 * 1000; // 15 minutes - balance between freshness and performance
const STALE_WHILE_REVALIDATE_MS = 5 * 60 * 1000; // 5 minutes - serve stale data while fetching fresh

/**
 * Get a pagination-specific cache key based on the current authenticated user and page parameters
 * This ensures different users and different page requests have separate caches
 */
// This function is used within getPolls and other cache-related functions
// to generate consistent user-specific cache keys

const getUserCacheKey = async (): Promise<string | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  return user?.id || null;
};

// Helper function to clear all poll-related caches
const clearAllPollRelatedCaches = () => {
  // Clear main polls cache
  removeItem('pollgpt_polls_cache');

  // Clear any poll-specific caches from localStorage
  if (typeof window !== 'undefined') {
    const keyPrefix = 'pollgpt_poll_';
    const allKeys = Object.keys(localStorage).filter(k => k.startsWith(keyPrefix));
    allKeys.forEach(key => {
      // Only try to remove if it matches our expected pattern
      if (key.startsWith('pollgpt_poll_')) {
        // Key is already validated as a poll key pattern which matches our StorageKey type
        removeItem(key as `pollgpt_poll_${string}`);
      }
    });
  }

  // Clear in-memory cache
  pollsCache.clear();

  // Optionally force reload if on polls page
  if (typeof window !== 'undefined' && window.location.pathname.includes('/dashboard/polls')) {
    // Only force reload if we're on the polls page
    window.location.reload();
  }
};

// Prefetch flag to track if we've already started prefetching
let hasPrefetchedFirstPage = false;

// Track metrics for API calls
const apiMetrics = {
  lastCallTime: 0,
  totalCalls: 0,
  cacheHits: 0
};

// Get user from cache if available to avoid repeated auth calls
let cachedUser: { id: string; email: string } | null = null;
let cachedUserTimestamp = 0;
const USER_CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

// Get polls for the current user with pagination support
export const getPolls = async (
  page: number = 1,
  pageSize: number = 10,
  fetchAll: boolean = false
): Promise<PaginatedPolls> => {
  try {
    // Use a consistent cache key for polls list
    const cacheKey: StorageKey = 'pollgpt_polls_cache';
    const userCacheKey = await getUserCacheKey();
    let user;

    // For cache expiration checks
    const now = Date.now();

    // OPTIMIZATION: Use a 3-tier approach to get user data
    // 1. Memory cache (fastest)
    // 2. localStorage (fast fallback)
    // 3. Supabase auth API (slowest, but authoritative)

    // 1. Check memory cache first
    if (cachedUser && (now - cachedUserTimestamp < USER_CACHE_EXPIRY_MS)) {
      user = cachedUser;
      console.log('Using cached user data from memory');
    } else {
      // 2. Try localStorage as fast fallback
      let localStorageUser = null;
      if (typeof window !== 'undefined') {
        try {
          // Supabase manages its own token; access directly as it's not an app StorageKey
          const storedSessionString = localStorage.getItem('sb-sumruaeyfidjlssrmfrm-auth-token');
          if (storedSessionString) {
            const parsedSession = JSON.parse(storedSessionString);
            if (parsedSession?.user?.id) {
              localStorageUser = parsedSession.user;
              console.log('Using user from localStorage');
            }
          }
        } catch (localStorageError) {
          console.warn('Error reading user from localStorage:',
            localStorageError instanceof Error ? localStorageError.message : 'Unknown error');
        }
      }

      if (localStorageUser) {
        // Use localStorage user and update memory cache
        user = localStorageUser;
        cachedUser = localStorageUser;
        cachedUserTimestamp = now;
      } else {
        // 3. Last resort: Call Supabase auth API with timeout
        try {
          const authStart = Date.now();
          // Use a shorter timeout (10s) for auth - if it takes longer, there's likely a network issue
          const { data: { user: freshUser }, error: userError } = await Promise.race([
            supabase.auth.getUser(),
            new Promise<{data:{user:null}, error: Error}>((_, reject) =>
              setTimeout(() => reject(new Error("Auth connection timed out")), 10000)
            )
          ]);

          const connectionTime = Date.now() - authStart;
          console.log(`Auth connection response time: ${connectionTime}ms`);

          if (userError) {
            console.error("Error fetching user:", userError);
            throw new Error(`User authentication error: ${userError.message}`);
          }

          // Cache the user for future calls
          user = freshUser;
          cachedUser = freshUser;
          cachedUserTimestamp = now;
        } catch (authError) {
          console.error('Auth API error:', authError);

          // CRITICAL FALLBACK: If we still have localStorage user, use it as last resort
          if (localStorageUser) {
            console.log('FALLBACK: Using localStorage user after auth API failure');
            user = localStorageUser;
            cachedUser = localStorageUser;
            cachedUserTimestamp = now;
          } else {
            throw authError; // No fallback available, must propagate error
          }
        }
      }
    }

    if (!user) {
      console.warn("No authenticated user found when fetching polls");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    console.log(`Fetching polls for user: ${user.id}, page: ${page}, pageSize: ${pageSize}, fetchAll: ${fetchAll}`);

    // Get polls from Supabase where user_id matches current user with timeout
    // Make sure we have auth context properly set for RLS policies to work
    const fetchStart = Date.now();

    // OPTIMIZATION: Fetch polls with counts in a single query using Postgres functions with pagination
    // Check in-memory cache first
    const pollSpecificCacheKey = `polls_${user.id}_${page}_${pageSize}_${fetchAll}`;
    const cachedData = pollsCache.get(pollSpecificCacheKey);

    // Check localStorage for persistent cache as fallback
    let localStorageCache: { data: PaginatedPolls; timestamp: number; etag?: string } | null = null;
    if (!cachedData && typeof window !== 'undefined' && !fetchAll) {
      try {
        const allPollsCache = getItem<{ [key: string]: { data: PaginatedPolls; timestamp: number; etag?: string } }>('pollgpt_polls_cache') || {};
        const specificPollCache = allPollsCache[`pollgpt_${pollSpecificCacheKey}`];
        if (specificPollCache) {
          localStorageCache = specificPollCache; // specificPollCache is already { data, timestamp }
          console.log('Found localStorage cache for polls via service');
        }
      } catch (error) { // Catching potential errors during processing, though getItem itself logs
        console.warn('Error processing polls cache from localStorage:', error instanceof Error ? error.message : 'Unknown error');
      }
    }

    // Check cache with stale-while-revalidate strategy
    if ((cachedData || localStorageCache) && !fetchAll) {
      const now = Date.now();
      const cache = cachedData || (localStorageCache ? { data: localStorageCache.data, timestamp: localStorageCache.timestamp } : null);

      if (cache) {
        const cacheAge = now - cache.timestamp;

        if (cacheAge < STALE_WHILE_REVALIDATE_MS) {
          // Data is fresh, return immediately
          console.log(`Using fresh cached polls data for ${pollSpecificCacheKey}`);

          // If we found data in localStorage but not in memory, add it to memory cache
          if (!cachedData && localStorageCache) {
            pollsCache.set(pollSpecificCacheKey, {
              data: localStorageCache.data,
              timestamp: localStorageCache.timestamp,
              etag: localStorageCache.etag
            });
          }

          // Prefetch next page if this is page 1 and we haven't prefetched yet
          if (page === 1 && !hasPrefetchedFirstPage) {
            hasPrefetchedFirstPage = true;
            setTimeout(() => {
              try {
                console.log('Prefetching page 2 for faster navigation');
                getPolls(2, pageSize, false);
              } catch {
                // Ignore prefetch errors
              }
            }, 1000);
          }

          return cache.data;
        } else if (cacheAge < CACHE_EXPIRY_MS) {
          // Data is stale but not expired, return stale data and fetch fresh data in background
          console.log(`Using stale cached polls data for ${pollSpecificCacheKey}, fetching fresh data in background`);

          // Return stale data immediately
          const staleData = cache.data;

          // Fetch fresh data in background (don't await)
          setTimeout(async () => {
            try {
              console.log(`Background refresh for ${pollSpecificCacheKey}`);
              await getPolls(page, pageSize, true); // Force refresh in background
            } catch (error) {
              console.warn('Background refresh failed:', error);
            }
          }, 100);

          return staleData;
        } else {
          // Data is expired, remove from cache
          console.log(`Cached data for ${pollSpecificCacheKey} has expired`);
          pollsCache.delete(pollSpecificCacheKey);
          if (typeof window !== 'undefined') {
            try {
              const storageCacheKey = `pollgpt_${pollSpecificCacheKey}` as StorageKey;
              removeItem(storageCacheKey);
            } catch {
              // Ignore localStorage errors
            }
          }
        }
      }
    }

    // TEMPORARY FIX: Skip the RPC function and use the fallback implementation directly
    // This bypasses any potential issues with the RPC function
    console.log('Using fallback implementation directly to avoid timeout issues');

    // Track API metrics for consistency
    apiMetrics.lastCallTime = Date.now();
    apiMetrics.totalCalls++;

    // Get all polls using the fallback method
    const fallbackPolls = await getPolls_fallback(user.id);

    // Apply pagination manually
    const startIndex = (page - 1) * pageSize;
    const endIndex = fetchAll ? fallbackPolls.length : startIndex + pageSize;
    const paginatedPolls = fallbackPolls.slice(startIndex, endIndex);

    // Create a mock data object that matches the structure expected by the rest of the function
    // Make sure we're using the correct property names that match the expected format
    const data = paginatedPolls.map(poll => ({
      id: poll.id,
      title: poll.title,
      slug: poll.slug,
      description: poll.description,
      created_at: poll.createdAt,
      updated_at: poll.updatedAt,
      user_id: poll.userId || user.id,
      questions: poll.questions,
      status: 'active', // Default status
      is_public: true,  // Default to public
      access_code: poll.access_code || null,
      total_count: fallbackPolls.length,
      response_count: poll.responses_count,
      view_count: poll.views_count
    }));

    const fetchTime = Date.now() - fetchStart;
    console.log(`Poll fetch response time: ${fetchTime}ms`);

    // Since we're using the fallback method directly, we just need to check if data is empty
    if (!data || data.length === 0) {
      console.log("No poll data returned from fallback method");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    // Handle null/undefined data case
    if (!data || data.length === 0) {
      console.log("No poll data returned");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    // Extract the total count from the first row (all rows have the same total_count value)
    const totalCount = data[0].total_count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    console.log(`Found ${data.length} polls (page ${page}/${totalPages}, total: ${totalCount})`);

    // Create result object to be returned (and cached)
    const result: PaginatedPolls = {
      polls: [],
      totalCount,
      currentPage: page,
      totalPages,
      pageSize
    };

    // Define the type for database poll objects from RPC
    type DbPollFromRPC = {
      id: string;
      title: string;
      slug: string | null;
      description: string | null;
      created_at: string;
      updated_at: string | null;
      user_id: string;
      questions: unknown; // Will be cast to DbQuestionShape[]
      status: 'draft' | 'active' | 'completed';
      response_count: number;
      view_count: number;
      is_public: boolean | null;
      access_code: string | null;
      source_url: string | null;
      source_type: 'url' | 'pdf' | 'website' | null;
      source_filename: string | null;
      show_source: boolean | null;
      context: string | null;
      total_count: number;
    };

    // Map the database format to our application format with improved data handling
    const polls: Poll[] = data.map((value) => {
      const dbPoll = value as DbPollFromRPC;
      // Use the globally defined interface
      // DbQuestionShape is defined at the top of this file

      // Parse questions from the JSON array if available
      // Use proper typing for questions
      const questions = dbPoll.questions ? (dbPoll.questions as DbQuestionShape[]).map(q => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type as QuestionType,
        options: [], // We don't need options for the polls list view
        required: false, // Default value
        order: q.order || 0
      })) : [];

      return {
        id: dbPoll.id,
        title: dbPoll.title || 'Untitled Poll',
        slug: dbPoll.slug,
        description: dbPoll.description || '',
        questions: questions,
        questions_count: questions.length, // Calculate from parsed questions array
        createdAt: dbPoll.created_at,
        updatedAt: dbPoll.updated_at || dbPoll.created_at,
        expiresAt: null,
        userId: dbPoll.user_id,
        // Use status column directly (is_published no longer exists)
        status: dbPoll.status as 'draft'|'active'|'completed',
        responses_count: dbPoll.response_count || 0, // Use response_count from database function
        views_count: dbPoll.view_count || 0, // Use view_count from database function
        is_public: dbPoll.is_public === true || (dbPoll.is_public === null || dbPoll.is_public === undefined ? true : false),
        access_code: dbPoll.access_code,
        // Add source attachment fields with proper null handling
        source_url: dbPoll.source_url || null,
        source_type: dbPoll.source_type as 'url' | 'pdf' | 'website' | null || null,
        source_filename: dbPoll.source_filename || null,
        show_source: dbPoll.show_source === null ? true : !!dbPoll.show_source,
        context: dbPoll.context || null,
      };
    });

    // Update the result object with the processed polls
    result.polls = polls;

    // Cache the fetched data with timestamp for expiry check
    if (userCacheKey) {
      try {
        // Update in-memory cache with timestamp
        pollsCache.set(pollSpecificCacheKey, { data: result, timestamp: Date.now() });

        // Update localStorage cache
        try {
          // Get existing cache or initialize new one
          const existingCache = getItem<Record<string, { data: PaginatedPolls, timestamp: number }>>(cacheKey) || {};

          // Store in the user-specific section of the cache
          existingCache[userCacheKey] = {
            data: result,
            timestamp: Date.now()
          };

          // cacheKey is already properly typed as StorageKey
          setItem(cacheKey, existingCache);

          // Also store a direct cache entry for faster access next time
          const directCacheKey = `pollgpt_polls_${user.id}_${page}_${pageSize}` as StorageKey;
          setItem(directCacheKey, {
            data: result,
            timestamp: Date.now()
          });

        } catch (err) {
          console.error("Error updating cache:", err);
        }
      } catch (err) {
        console.error("Error updating cache:", err);
      }
    }

    return result;
  } catch (error) {
    console.error("Error fetching polls:", error);
    // Always return a valid PaginatedPolls object with empty polls array
    return {
      polls: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      pageSize
    };
  }
};

// Fallback implementation using multiple queries
// This is used if the optimized RPC function doesn't exist

// Delete a poll
export const deletePoll = async (id: string): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      // Using console.error for service layer, UI layer can use toast
      console.error("User must be logged in to delete polls.");
      return false;
    }

    // First, delete related records (questions, responses, answers)
    // This order is important due to foreign key constraints

    // Delete answers associated with responses to this poll
    const { error: deleteAnswersError } = await supabase
      .from('answers')
      .delete()
      .in('response_id', supabase.from('responses').select('id').eq('poll_id', id));

    if (deleteAnswersError) {
      console.error('Error deleting answers:', deleteAnswersError);
      throw new Error(`Failed to delete poll's answers: ${deleteAnswersError.message}`);
    }

    // Delete responses to this poll
    const { error: deleteResponsesError } = await supabase
      .from('responses')
      .delete()
      .eq('poll_id', id);

    if (deleteResponsesError) {
      console.error('Error deleting responses:', deleteResponsesError);
      throw new Error(`Failed to delete poll's responses: ${deleteResponsesError.message}`);
    }

    // Delete questions associated with this poll
    const { error: deleteQuestionsError } = await supabase
      .from('questions')
      .delete()
      .eq('poll_id', id);

    if (deleteQuestionsError) {
      console.error('Error deleting questions:', deleteQuestionsError);
      throw new Error(`Failed to delete poll's questions: ${deleteQuestionsError.message}`);
    }

    // Finally, delete the poll itself
    const { error: deletePollError } = await supabase
      .from('polls')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id); // Ensure user can only delete their own polls

    if (deletePollError) {
      console.error("Error deleting poll in service:", deletePollError.message);
      throw new Error(`Failed to delete poll: ${deletePollError.message}`); // Re-throw as a new error for consistent error handling
    }

    console.log("Poll deleted successfully from service!");
    clearAllPollRelatedCaches(); // Clear all poll-related caches
    return true;
  } catch (error: unknown) {
    let errorMessage = "An unknown error occurred while deleting the poll.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error("Error deleting poll in service:", errorMessage);
    throw new Error(`Failed to delete poll: ${errorMessage}`);
  }
};

export const duplicatePoll = async (originalPollId: string): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to duplicate polls.");
      return null;
    }

    const originalPoll = await getPollById(originalPollId);
    if (!originalPoll) {
      console.error(`Original poll with ID ${originalPollId} not found for duplication.`);
      return null;
    }

    // Ensure the user owns the original poll (optional, getPollById might already handle this if it filters by user)
    if (originalPoll.userId !== user.id) {
        console.error(`User ${user.id} does not own original poll ${originalPollId}. Cannot duplicate.`);
        return null;
    }

    const newPollId = generateId();
    const now = new Date().toISOString();

    const newPollData: Partial<Poll> = {
      // id: newPollId, // createPoll will handle ID generation if not provided, or we can pass it.
      title: `Copy of ${originalPoll.title}`,
      slug: generateSlug(`Copy of ${originalPoll.title}`),
      description: originalPoll.description,
      userId: user.id, // Explicitly set new owner, though createPoll should do this
      createdAt: now, // createPoll will set this
      updatedAt: now, // createPoll will set this
      expiresAt: originalPoll.expiresAt,
      status: 'draft', // New duplicate starts as draft
      is_public: originalPoll.is_public,
      access_code: originalPoll.access_code,
      questions_count: originalPoll.questions_count !== undefined ? originalPoll.questions_count : originalPoll.questions.length,
      responses_count: 0,
      views_count: 0,
      questions: originalPoll.questions.map(q => ({
        ...q, // Spread original question properties
        id: generateId(), // New ID for the duplicated question
        poll_id: newPollId, // This will be overridden by createPoll if it handles questions internally
                               // or needs to be set if questions are created in a separate step.
        options: q.options ? q.options.map(opt => ({ ...opt, id: generateId() })) : [],
      }))
    };

    // Use createPoll to handle insertion of poll and its questions
    // createPoll needs to be robust enough to handle pre-defined question IDs or generate new ones if not provided.
    // For simplicity, we assume createPoll can take full Poll structure with questions.
    // If createPoll doesn't handle questions directly, we'd insert poll first, then questions.

    const createdPoll = await createPoll(newPollData);
    // createPoll internally calls getPollById, so this should be the complete new poll.

    if (!createdPoll) {
        console.error(`Failed to create a duplicate of poll ${originalPollId}.`);
        return null;
    }

    console.log(`Poll ${originalPollId} duplicated successfully as ${createdPoll.id}.`);
    return createdPoll;

  } catch (error) {
    console.error(`Error duplicating poll ${originalPollId}:`, error);
    throw error;
  }
};

export const closePoll = async (id: string): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to close polls.");
      return null;
    }

    const updates: Partial<PollDbUpdatePayload> = {
      status: 'completed',
      // expires_at: new Date().toISOString(), // Optionally set expires_at to now when closing
    };

    // The updatePoll function already handles setting updatedAt and user_id check
    const updatedPoll = await updatePoll(id, updates as Partial<Poll>); // Cast needed if updates doesn't perfectly match Partial<Poll>

    if (!updatedPoll) {
      console.error(`Failed to close poll ${id}. It might not exist or user may not have permission.`);
      return null;
    }

    console.log(`Poll ${id} closed successfully.`);
    clearAllPollRelatedCaches(); // Clear all poll-related caches
    return updatedPoll;

  } catch (error) {
    console.error(`Error closing poll ${id}:`, error);
    throw error;
  }
};

// Create a new poll
export const createPoll = async (pollData: Partial<Poll>): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to create polls.");
      return null;
    }

    // Debug incoming poll data
    console.log('CreatePoll received pollData:', {
      title: pollData.title,
      description: pollData.description?.substring(0, 50) + '...',
      context: pollData.context,
      source_url: pollData.source_url,
      source_type: pollData.source_type,
      source_filename: pollData.source_filename
    });

    // Prepare data for insertion, ensuring all required fields for DB are present
    const newPollData = {
      title: pollData.title || 'Untitled Poll',
      slug: pollData.slug || generateSlug(pollData.title || 'Untitled Poll'),
      description: pollData.description || '',
      // questions: pollData.questions || [], // Questions are typically handled as related records
      expires_at: pollData.expiresAt || null,
      user_id: user.id,
      status: pollData.status || 'active', // Changed from 'draft' to 'active' to make polls active by default
      is_public: pollData.is_public !== undefined ? pollData.is_public : true,
      access_code: pollData.access_code || null,
      context: pollData.context || null, // Now contains summary
      source_url: pollData.source_url || null, // File/URL link
      source_type: pollData.source_type || null,
      source_filename: pollData.source_filename || null,
      show_source: pollData.show_source !== undefined ? pollData.show_source : true,
    };

    // Debug data being sent to database
    console.log('Data being sent to database:', {
      context: newPollData.context,
      source_url: newPollData.source_url,
      source_type: newPollData.source_type,
      source_filename: newPollData.source_filename
    });

    // Log the exact data being sent to the database
    console.log('Exact data being sent to Supabase:', JSON.stringify(newPollData, null, 2));

    const { data: insertedPoll, error: insertError } = await supabase
      .from('polls')
      .insert(newPollData)
      .select()
      .single();

    // Log the response from Supabase
    console.log('Supabase insert response:', { data: insertedPoll, error: insertError });

    if (insertError) {
      console.error('Error creating poll:', insertError);
      throw insertError;
    }
    if (!insertedPoll) return null;

    // Handle questions if provided
    if (pollData.questions && pollData.questions.length > 0) {
      const questionsToInsert = pollData.questions.map(q => ({
        // Ensure all fields required by 'questions' table are mapped
        poll_id: insertedPoll.id,
        question_text: q.text,
        question_type: q.type,
        options: q.options || [],
        required: q.required !== undefined ? q.required : false,
        order: q.order !== undefined ? q.order : 0,
        // id: q.id // Let DB generate question IDs or handle if provided
      }));
      const { error: questionError } = await supabase.from('questions').insert(questionsToInsert);
      if (questionError) {
        console.error('Error inserting questions for new poll:', questionError);
        // Optionally delete the created poll or mark as incomplete
      }
    }

    // Re-fetch the poll with questions to ensure consistency or map manually
    // It's often better to return the inserted data directly if possible,
    // or construct the Poll object from insertedPoll and questionsToInsert if successful.
    // For simplicity here, we re-fetch.
    clearAllPollRelatedCaches(); // Clear all poll-related caches
    return getPollById(insertedPoll.id);

  } catch (error) {
    console.error('Service error creating poll:', error);
    throw error;
  }
};

// Get a single poll by its ID - handles both public and private polls
export const getPollById = async (id: string): Promise<Poll | null> => {
  try {
    // Get current session (might be null for unauthenticated users)
    const { data: { session } } = await supabase.auth.getSession();
    const isAuthenticated = !!session?.user?.id;

    console.log(`Fetching poll ${id}, authenticated: ${isAuthenticated}`);

    // Try to use the RPC function first for better performance (for authenticated users)
    if (isAuthenticated) {
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_poll_with_counts', {
        poll_id_param: id,
        user_id_param: session.user.id
      });

      if (!rpcError && rpcData && rpcData.length > 0) {
        const data = rpcData[0];

        // Map DB response to Poll type
        const poll: Poll = {
          id: data.id,
          title: data.title,
          slug: data.slug,
          description: data.description || '',
          questions: Array.isArray(data.questions) ? data.questions.map((q: DbQuestionShape) => ({
            id: q.id,
            text: q.question_text,
            type: q.question_type as QuestionType,
            options: q.options || [],
            required: q.required !== undefined ? q.required : false,
            order: q.order !== undefined ? q.order : 0
          })) : [],
          createdAt: data.created_at,
          updatedAt: data.updated_at,
          expiresAt: data.expires_at,
          userId: data.user_id,
          status: data.status as 'draft' | 'active' | 'completed',
          questions_count: data.questions_count || 0,
          responses_count: data.responses_count || 0,
          views_count: data.views || 0,
          is_public: data.is_public === true || (data.is_public === null || data.is_public === undefined ? true : false),
          access_code: data.access_code,
          // Add required source fields
          source_url: data.source_url || null,
          source_type: data.source_type as 'url' | 'pdf' | 'website' | null || null,
          source_filename: data.source_filename || null,
          show_source: data.show_source === null ? true : !!data.show_source,
          context: data.context || null,
        };
        console.log(`Successfully fetched poll via RPC: ${poll.title} (public: ${poll.is_public})`);
        return poll;
      }
    }

    // Fallback to regular query - this will work for both authenticated and unauthenticated users
    // The RLS policies will handle access control (public polls OR owned polls)
    console.log('Using regular query for getPollById (RLS will handle access control)');

    // Get the poll data WITHOUT user_id filter - let RLS policies decide access
    const { data: pollData, error: pollError } = await supabase
      .from('polls')
      .select(`
        *,
        questions (id, poll_id, question_text, question_type, options, required, "order")
      `)
      .eq('id', id)
      .single(); // Use single() to get one record or null

    if (pollError) {
      if (pollError.code === 'PGRST116') { // Resource not found
        console.log(`Poll with id ${id} not found or access denied.`);
        return null;
      }
      console.error(`Error fetching poll by id ${id}:`, pollError);
      throw pollError;
    }

    if (!pollData) {
      console.log(`Poll with id ${id} returned no data.`);
      return null;
    }

    // Get response count
    const { count: responsesCount, error: responsesError } = await supabase
      .from('responses')
      .select('*', { count: 'exact', head: true})
      .eq('poll_id', id);

    if (responsesError) {
      console.error(`Error fetching response count for poll ${id}:`, responsesError);
    }

    // Map DB response to Poll type
    const poll: Poll = {
      id: pollData.id,
      title: pollData.title,
      slug: pollData.slug,
      description: pollData.description || '',
      questions: pollData.questions ? pollData.questions.map((q: DbQuestionShape) => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type as QuestionType,
        options: q.options || [],
        required: q.required !== undefined ? q.required : false,
        order: q.order !== undefined ? q.order : 0
      })) : [],
      createdAt: pollData.created_at,
      updatedAt: pollData.updated_at,
      expiresAt: pollData.expires_at,
      userId: pollData.user_id,
      status: pollData.status as 'draft' | 'active' | 'completed',
      questions_count: pollData.questions ? pollData.questions.length : 0,
      responses_count: responsesCount || 0,
      views_count: pollData.views || 0,
      is_public: pollData.is_public === true || (pollData.is_public === null || pollData.is_public === undefined ? true : false),
      access_code: pollData.access_code,
      // Add required source fields
      source_url: pollData.source_url || null,
      source_type: pollData.source_type as 'url' | 'pdf' | 'website' | null || null,
      source_filename: pollData.source_filename || null,
      show_source: pollData.show_source === null ? true : !!pollData.show_source,
      context: pollData.context || null,
    };

    console.log(`Successfully fetched poll: ${poll.title} (public: ${poll.is_public}, owner: ${poll.userId})`);
    return poll;
  } catch (error) {
    console.error(`Service error fetching poll by id ${id}:`, error);
    throw error;
  }
};

// Update an existing poll
export const updatePoll = async (id: string, updates: Partial<Poll>): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to update polls.");
      return null;
    }

    const { questions, ...pollUpdates } = updates;
    // Map Poll fields to DB column names if different (e.g., expiresAt to expires_at)
    const dbUpdates: PollDbUpdatePayload = {};

    // Explicitly map fields to ensure type safety and correct column names
    if (pollUpdates.title !== undefined) dbUpdates.title = pollUpdates.title;
    if (pollUpdates.description !== undefined) dbUpdates.description = pollUpdates.description;
    if (pollUpdates.status !== undefined) dbUpdates.status = pollUpdates.status;
    if (pollUpdates.is_public !== undefined) dbUpdates.is_public = pollUpdates.is_public;
    if (pollUpdates.access_code !== undefined) dbUpdates.access_code = pollUpdates.access_code;
    if (pollUpdates.expiresAt !== undefined) dbUpdates.expires_at = pollUpdates.expiresAt;
    if (pollUpdates.context !== undefined) dbUpdates.context = pollUpdates.context;
    if (pollUpdates.source_url !== undefined) dbUpdates.source_url = pollUpdates.source_url;
    if (pollUpdates.source_type !== undefined) dbUpdates.source_type = pollUpdates.source_type;
    if (pollUpdates.source_filename !== undefined) dbUpdates.source_filename = pollUpdates.source_filename;
    if (pollUpdates.show_source !== undefined) dbUpdates.show_source = pollUpdates.show_source;
    // Note: createdAt, userId, responses, and views are not part of PollDbUpdatePayload
    // and thus are not (and should not be) included in dbUpdates.

    const { data: updatedPollData, error: updateError } = await supabase
      .from('polls')
      .update(dbUpdates)
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user owns the poll
      .select()
      .single();

    if (updateError) {
      console.error(`Error updating poll ${id}:`, updateError);
      throw updateError;
    }
    if (!updatedPollData) return null;

    // Handle questions update if 'questions' array is provided in updates
    if (questions) {
      // Delete existing questions for this poll
      const { error: deleteError } = await supabase.from('questions').delete().eq('poll_id', id);
      if (deleteError) {
        console.error(`Error deleting old questions for poll ${id}:`, deleteError);
        // Potentially throw error or handle more gracefully
      }

      // Insert new questions
      if (questions.length > 0) {
        const newQuestionsData = questions.map(q => ({
          poll_id: id,
          question_text: q.text,
          question_type: q.type,
          options: q.options || [],
          required: q.required !== undefined ? q.required : false,
          order: q.order !== undefined ? q.order : 0,
          // id: q.id // Let DB generate question IDs or handle if provided
        }));
        const { error: insertError } = await supabase.from('questions').insert(newQuestionsData);
        if (insertError) {
          console.error(`Error inserting new questions for poll ${id}:`, insertError);
          // Potentially throw error or handle more gracefully
        }
      }
    }

    clearAllPollRelatedCaches(); // Clear all poll-related caches
    // Re-fetch the poll with updated questions for consistency
    return getPollById(id);

  } catch (error) {
    console.error(`Service error updating poll ${id}:`, error);
    throw error;
  }
};

// Initialize with sample data (stub)
export const initializeWithSampleData = async (): Promise<void> => {
  console.warn("initializeWithSampleData() is a stub and not fully implemented.");
  return Promise.resolve();
};

// Get responses for a specific poll
export const getPollResponses = async (pollId: string): Promise<PollResponse[] | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to get poll responses.");
      return null;
    }

    // First verify the user owns this poll
    const { data: pollData } = await supabase
      .from('polls')
      .select('user_id')
      .eq('id', pollId)
      .single();

    if (!pollData || pollData.user_id !== user.id) {
      console.error("User does not have permission to access this poll's responses.");
      return null;
    }

    // Get all responses for this poll with answers
    const { data: dbResponses, error } = await supabase
      .from('responses')
      .select(`
        id,
        created_at,
        respondent_info,
        answers (
          id,
          question_id,
          answer_value
        )
      `)
      .eq('poll_id', pollId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching poll responses:', error);
      return null;
    }

    if (!dbResponses) {
      return [];
    }

    // Transform the database format to PollResponse format
    const transformedResponses: PollResponse[] = dbResponses.map(dbResponse => {
      // Convert answers array to responses object
      const responses: Record<string, string | string[]> = {};

      if (dbResponse.answers) {
        dbResponse.answers.forEach(answer => {
          try {
            // Try to parse as JSON (for multi-select answers)
            const parsedValue = JSON.parse(answer.answer_value);
            responses[answer.question_id] = parsedValue;
          } catch {
            // If parsing fails, use as string (for single answers)
            responses[answer.question_id] = answer.answer_value;
          }
        });
      }

      // Extract respondent info from metadata if available
      const respondentInfo = dbResponse.respondent_info || {};

      return {
        id: dbResponse.id,
        pollId: pollId,
        responses: responses,
        submittedAt: dbResponse.created_at,
        respondentInfo: {
          deviceType: respondentInfo.deviceType || 'unknown',
          browser: respondentInfo.browser || 'unknown',
          os: respondentInfo.os || 'unknown',
          region: respondentInfo.region
        },
        // Legacy format support (these might be used by the UI)
        deviceType: respondentInfo.deviceType || 'unknown',
        browser: respondentInfo.browser || 'unknown',
        os: respondentInfo.os || 'unknown',
        region: respondentInfo.region
      };
    });

    console.log(`Transformed ${transformedResponses.length} responses for poll ${pollId}`);

    // Debug: log first response structure
    if (transformedResponses.length > 0) {
      console.log('Sample transformed response:', {
        id: transformedResponses[0].id,
        responseKeys: Object.keys(transformedResponses[0].responses),
        submittedAt: transformedResponses[0].submittedAt
      });
    }

    return transformedResponses;
  } catch (error) {
    console.error('Error in getPollResponses:', error);
    return null;
  }
};

// Increment the view count for a poll
export const incrementPollViews = async (pollId: string): Promise<boolean> => {
  try {
    const { error } = await supabase.rpc('increment_poll_views', { poll_id: pollId });

    if (error) {
      // If the RPC function doesn't exist, fall back to a direct update
      console.warn('RPC function not found, using direct update');
      const { error: updateError } = await supabase
        .from('polls')
        .update({ views: supabase.sql`views + 1` })
        .eq('id', pollId);

      if (updateError) {
        console.error('Error incrementing poll views:', updateError);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error incrementing poll views:', error);
    return false;
  }
};

// Check if a user has already responded to a poll
export const hasUserRespondedToPoll = async (pollId: string): Promise<boolean> => {
  try {
    console.log(`[DEBUG] hasUserRespondedToPoll called for pollId: ${pollId}`);
    const { data: { user } } = await supabase.auth.getUser();
    console.log(`[DEBUG] User status:`, user ? `Authenticated (${user.id})` : 'Anonymous');

    // For authenticated users, check the database
    if (user) {
      console.log(`[DEBUG] Checking database for authenticated user responses`);
      const { error, count } = await supabase
        .from('responses')
        .select('id', { count: 'exact' })
        .eq('poll_id', pollId)
        .eq('user_id', user.id);

      if (error) {
        console.error('[DEBUG] Error checking if user responded to poll:', error);
        return false;
      }

      const hasResponded = count !== null && count > 0;
      console.log(`[DEBUG] Database check result: count=${count}, hasResponded=${hasResponded}`);
      return hasResponded;
    }

    // For anonymous users, check localStorage (client-side only)
    if (typeof window !== 'undefined') {
      console.log(`[DEBUG] Checking localStorage for anonymous user`);
      const storageKey = `poll_response_${pollId}`;
      const storedResponse = localStorage.getItem(storageKey);
      console.log(`[DEBUG] localStorage check - key: ${storageKey}, value:`, storedResponse);
      const hasStoredResponse = storedResponse !== null;
      console.log(`[DEBUG] localStorage check result: hasStoredResponse=${hasStoredResponse}`);
      return hasStoredResponse;
    }

    // Server-side check for anonymous users
    console.log(`[DEBUG] Server-side check for anonymous user - returning false`);
    return false;
  } catch (error) {
    console.error('[DEBUG] Error in hasUserRespondedToPoll:', error);
    return false;
  }
};

// Add a response to a poll
interface PollResponsePayload {
  pollId: string;
  responses: Record<string, string | string[]>;
  respondentInfo?: {
    deviceType: string;
    browser?: string;
    os?: string;
    region?: string;
  };
}

export const addPollResponse = async (payload: PollResponsePayload): Promise<boolean> => {
  try {
    console.log(`[DEBUG] addPollResponse called for pollId: ${payload.pollId}`);
    const { pollId, responses, respondentInfo } = payload;
    const { data: { user } } = await supabase.auth.getUser();
    console.log(`[DEBUG] User status:`, user ? `Authenticated (${user.id})` : 'Anonymous');

    // Create the response record
    const responseId = uuidv4();
    console.log(`[DEBUG] Created response ID: ${responseId}`);

    const { error: responseError } = await supabase
      .from('responses')
      .insert({
        id: responseId,
        poll_id: pollId,
        user_id: user?.id || null, // Allow anonymous responses
        respondent_info: respondentInfo,
      });

    if (responseError) {
      console.error('[DEBUG] Error creating response:', responseError);
      return false;
    }
    console.log(`[DEBUG] Successfully created response in database`);

    // Process and insert all answers
    // Convert from Record<string, string | string[]> to array of formatted answers
    const formattedAnswers = Object.entries(responses).map(([questionId, value]) => {
      return {
        id: uuidv4(),
        response_id: responseId,
        question_id: questionId,
        answer_value: typeof value === 'string' ? value : JSON.stringify(value),
      };
    });

    console.log(`[DEBUG] Formatted ${formattedAnswers.length} answers for insertion`);

    const { error: answersError } = await supabase
      .from('answers')
      .insert(formattedAnswers);

    if (answersError) {
      console.error('[DEBUG] Error adding answers:', answersError);
      // Try to clean up the response if answers failed
      await supabase.from('responses').delete().eq('id', responseId);
      return false;
    }
    console.log(`[DEBUG] Successfully inserted all answers`);

    // For anonymous users, also store in localStorage to prevent duplicate responses
    if (!user && typeof window !== 'undefined') {
      console.log(`[DEBUG] Storing response in localStorage for anonymous user`);
      const storageKey = `poll_response_${pollId}`;
      const storageData = {
        responseId: responseId,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(storageKey, JSON.stringify(storageData));
      console.log(`[DEBUG] localStorage stored - key: ${storageKey}, data:`, storageData);

      // Verify it was stored
      const verification = localStorage.getItem(storageKey);
      console.log(`[DEBUG] localStorage verification:`, verification);
    } else if (user) {
      console.log(`[DEBUG] Authenticated user - not storing in localStorage`);
    } else {
      console.log(`[DEBUG] Server-side execution - cannot store in localStorage`);
    }

    return true;
  } catch (error) {
    console.error('[DEBUG] Error in addPollResponse:', error);
    return false;
  }
};

// Fallback implementation using multiple queries
// This is used if the optimized RPC function doesn't exist
export const getPolls_fallback = async (userId: string): Promise<Poll[]> => {
  try {
    // Get polls from Supabase where user_id matches current user
    const { data: pollsData, error: pollsError } = await supabase
      .from('polls')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (pollsError) {
      console.error("Error fetching polls:", pollsError);
      return [];
    }

    if (!pollsData || pollsData.length === 0) {
      return [];
    }

    // Get all questions for these polls
    const pollIds = pollsData.map(poll => poll.id);
    const { data: questionsData, error: questionsError } = await supabase
      .from('questions')
      .select('*')
      .in('poll_id', pollIds)
      .order('order', { ascending: true });

    if (questionsError) {
      console.error("Error fetching questions:", questionsError);
    }

    // Count responses for each poll
    const pollsWithCounts = await Promise.all(pollsData.map(async (poll) => {
      const { count: responseCount, error: countError } = await supabase
        .from('responses')
        .select('id', { count: 'exact', head: true })
        .eq('poll_id', poll.id);

      if (countError) {
        console.error(`Error counting responses for poll ${poll.id}:`, countError);
      }

      // Group questions by poll
      const pollQuestions = questionsData?.filter(q => q.poll_id === poll.id) || [];

      return {
        ...poll,
        questions: pollQuestions,
        questions_count: pollQuestions.length, // Calculate questions_count
        responses_count: responseCount || 0,    // Use responses_count
        views_count: 0 // We don't track views_count in the fallback implementation, so set to 0
      };
    }));

    // Define the type for database poll objects with counts
    type DbPollWithCounts = {
      id: string;
      title: string;
      slug: string | null;
      description: string | null;
      created_at: string;
      updated_at: string | null;
      expires_at: string | null;
      user_id: string;
      questions: DbQuestionShape[];
      status: 'draft' | 'active' | 'completed';
      response_count: number;
      view_count: number;
      is_public: boolean | null;
      access_code: string | null;
      source_url: string | null;
      source_type: 'url' | 'pdf' | 'website' | null;
      source_filename: string | null;
      show_source: boolean | null;
      questions_count?: number;
    };

    // Map the database format to our application format
    const polls: Poll[] = pollsWithCounts.map((dbPoll: DbPollWithCounts) => {
      // Parse questions directly if provided by the server
      const questions: PollQuestion[] = [];

      try {
        if (dbPoll.questions && Array.isArray(dbPoll.questions)) {
          dbPoll.questions.forEach((question: DbQuestionShape) => {
            questions.push({
              id: question.id,
              text: question.question_text,
              type: question.question_type as QuestionType,
              options: question.options || [],
              required: question.required !== undefined ? question.required : false,
              order: question.order !== undefined ? question.order : 0,
            });
          });
        }
      } catch (e) {
        console.error("Error parsing questions for poll", dbPoll.id, e);
      }

      // Ensure we use the correct counts from the database, with proper fallbacks
      const responseCount = typeof dbPoll.response_count === 'number' ? dbPoll.response_count : 0;
      const viewCount = typeof dbPoll.view_count === 'number' ? dbPoll.view_count : 0;

      return {
        id: dbPoll.id,
        title: dbPoll.title || 'Untitled Poll',
        slug: dbPoll.slug,
        description: dbPoll.description || '',
        questions: questions,
        questions_count: questions.length,
        responses_count: responseCount,
        views_count: viewCount,
        createdAt: dbPoll.created_at,
        updatedAt: dbPoll.updated_at || dbPoll.created_at, // Use created_at as fallback if updated_at is null
        userId: dbPoll.user_id,
        expiresAt: dbPoll.expires_at,
        status: dbPoll.status || 'draft',
        is_public: dbPoll.is_public === true || (dbPoll.is_public === null || dbPoll.is_public === undefined ? true : false),
        access_code: dbPoll.access_code,
        // Add source attachment fields
        source_url: dbPoll.source_url || null,
        source_type: dbPoll.source_type as 'url' | 'pdf' | 'website' | null || null,
        source_filename: dbPoll.source_filename || null,
        show_source: dbPoll.show_source !== null ? dbPoll.show_source : true,
      };
    }); // Closes the callback for pollsWithCounts.map

    return polls;
  } catch (error) {
    console.error("Error in fallback poll fetching:", error);
    return [];
  }
};

/**
 * Helper function to determine if the current user can edit a poll
 * @param poll The poll to check
 * @param currentUserId The current user's ID (can be null/undefined)
 * @returns boolean indicating if the user can edit the poll
 */
export function canEditPoll(poll: Poll | null, currentUserId: string | null | undefined): boolean {
  if (!poll || !currentUserId) return false;
  return poll.userId === currentUserId;
}

/**
 * Helper function to determine if a poll is publicly viewable
 * @param poll The poll to check
 * @returns boolean indicating if the poll is public
 */
export function isPollPublic(poll: Poll | null): boolean {
  if (!poll) return false;
  return poll.is_public === true && poll.status === 'active';
}
