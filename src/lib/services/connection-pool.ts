import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/database.types';

/**
 * Database Connection Pool Manager for PollGPT
 *
 * Features:
 * - Connection pooling with configurable pool size
 * - Connection health monitoring
 * - Automatic connection cleanup
 * - Connection reuse for better performance
 * - Query timeout management
 * - Connection retry logic with exponential backoff
 */
export class DatabaseConnectionPool {
  private connections: SupabaseClient<Database>[] = [];
  private availableConnections: SupabaseClient<Database>[] = [];
  private busyConnections: Set<SupabaseClient<Database>> = new Set();
  private connectionHealth: Map<SupabaseClient<Database>, boolean> = new Map();
  private lastHealthCheck = 0;

  private readonly config = {
    minConnections: 2,
    maxConnections: 10,
    healthCheckInterval: 30000, // 30 seconds
    connectionTimeout: 5000, // 5 seconds
    queryTimeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second base delay
  };

  private readonly supabaseUrl: string;
  private readonly supabaseKey: string;

  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    this.supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

    if (!this.supabaseUrl || !this.supabaseKey) {
      throw new Error('Missing Supabase configuration');
    }

    // Initialize connection pool
    this.initializePool();

    // Start health check interval
    this.startHealthCheck();
  }

  /**
   * Initialize the connection pool with minimum connections
   */
  private async initializePool(): Promise<void> {
    console.log('[ConnectionPool] Initializing connection pool');

    for (let i = 0; i < this.config.minConnections; i++) {
      const connection = this.createConnection();
      this.connections.push(connection);
      this.availableConnections.push(connection);
      this.connectionHealth.set(connection, true);
    }

    console.log(`[ConnectionPool] Initialized with ${this.config.minConnections} connections`);
  }

  /**
   * Create a new Supabase connection
   */
  private createConnection(): SupabaseClient<Database> {
    return createClient<Database>(this.supabaseUrl, this.supabaseKey, {
      auth: {
        autoRefreshToken: false, // Disable auto-refresh for pooled connections
        persistSession: false,   // Don't persist sessions for pooled connections
      },
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'X-Connection-Pool': 'true',
        },
      },
    });
  }

  /**
   * Acquire a connection from the pool
   */
  async acquireConnection(): Promise<SupabaseClient<Database>> {
    // Try to get an available connection
    if (this.availableConnections.length > 0) {
      const connection = this.availableConnections.pop()!;
      this.busyConnections.add(connection);

      // Check if connection is healthy
      if (!this.connectionHealth.get(connection)) {
        await this.testConnection(connection);
      }

      return connection;
    }

    // If no available connections and under max limit, create new one
    if (this.connections.length < this.config.maxConnections) {
      const connection = this.createConnection();
      this.connections.push(connection);
      this.busyConnections.add(connection);
      this.connectionHealth.set(connection, true);

      console.log(`[ConnectionPool] Created new connection. Pool size: ${this.connections.length}`);
      return connection;
    }

    // Wait for a connection to become available
    return this.waitForConnection();
  }

  /**
   * Release a connection back to the pool
   */
  releaseConnection(connection: SupabaseClient<Database>): void {
    if (this.busyConnections.has(connection)) {
      this.busyConnections.delete(connection);
      this.availableConnections.push(connection);
    }
  }

  /**
   * Wait for a connection to become available
   */
  private async waitForConnection(): Promise<SupabaseClient<Database>> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      const checkForConnection = () => {
        if (this.availableConnections.length > 0) {
          const connection = this.availableConnections.pop()!;
          this.busyConnections.add(connection);
          resolve(connection);
          return;
        }

        if (Date.now() - startTime > this.config.connectionTimeout) {
          reject(new Error('Connection timeout: No available connections'));
          return;
        }

        setTimeout(checkForConnection, 100);
      };

      checkForConnection();
    });
  }

  /**
   * Test connection health
   */
  private async testConnection(connection: SupabaseClient<Database>): Promise<boolean> {
    try {
      // Simple health check query
      const { error } = await connection
        .from('polls')
        .select('id')
        .limit(1)
        .single();

      const isHealthy = !error || error.code === 'PGRST116'; // No rows is also healthy
      this.connectionHealth.set(connection, isHealthy);

      return isHealthy;
    } catch (error) {
      console.error('[ConnectionPool] Connection health check failed:', error);
      this.connectionHealth.set(connection, false);
      return false;
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck(): void {
    setInterval(async () => {
      const now = Date.now();
      if (now - this.lastHealthCheck < this.config.healthCheckInterval) {
        return;
      }

      this.lastHealthCheck = now;
      await this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  /**
   * Perform health check on all connections
   */
  private async performHealthCheck(): Promise<void> {
    console.log('[ConnectionPool] Performing health check');

    const healthCheckPromises = this.connections.map(async (connection) => {
      if (!this.busyConnections.has(connection)) {
        await this.testConnection(connection);
      }
    });

    await Promise.all(healthCheckPromises);

    // Remove unhealthy connections
    const unhealthyConnections = this.connections.filter(
      conn => !this.connectionHealth.get(conn) && !this.busyConnections.has(conn)
    );

    for (const connection of unhealthyConnections) {
      this.removeConnection(connection);
    }

    // Ensure we have minimum connections
    while (this.connections.length < this.config.minConnections) {
      const newConnection = this.createConnection();
      this.connections.push(newConnection);
      this.availableConnections.push(newConnection);
      this.connectionHealth.set(newConnection, true);
    }

    console.log(`[ConnectionPool] Health check complete. Active connections: ${this.connections.length}`);
  }

  /**
   * Remove a connection from the pool
   */
  private removeConnection(connection: SupabaseClient<Database>): void {
    const connectionIndex = this.connections.indexOf(connection);
    if (connectionIndex > -1) {
      this.connections.splice(connectionIndex, 1);
    }

    const availableIndex = this.availableConnections.indexOf(connection);
    if (availableIndex > -1) {
      this.availableConnections.splice(availableIndex, 1);
    }

    this.busyConnections.delete(connection);
    this.connectionHealth.delete(connection);

    console.log(`[ConnectionPool] Removed unhealthy connection. Pool size: ${this.connections.length}`);
  }

  /**
   * Execute a query with connection pooling and retry logic
   */
  async executeQuery<T>(
    queryFn: (client: SupabaseClient<Database>) => Promise<T>,
    options: {
      timeout?: number;
      retries?: number;
    } = {}
  ): Promise<T> {
    const timeout = options.timeout || this.config.queryTimeout;
    const maxRetries = options.retries || this.config.retryAttempts;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      let connection: SupabaseClient<Database> | null = null;

      try {
        connection = await this.acquireConnection();

        // Execute query with timeout
        const queryPromise = queryFn(connection);
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Query timeout')), timeout);
        });

        const result = await Promise.race([queryPromise, timeoutPromise]);
        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        console.error(`[ConnectionPool] Query attempt ${attempt} failed:`, lastError.message);

        // Mark connection as unhealthy if it's a connection-related error
        if (connection && this.isConnectionError(lastError)) {
          this.connectionHealth.set(connection, false);
        }

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

      } finally {
        if (connection) {
          this.releaseConnection(connection);
        }
      }
    }

    throw lastError || new Error('Query failed after all retries');
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: Error): boolean {
    const connectionErrorMessages = [
      'connection',
      'timeout',
      'network',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
    ];

    return connectionErrorMessages.some(msg =>
      error.message.toLowerCase().includes(msg.toLowerCase())
    );
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    totalConnections: number;
    availableConnections: number;
    busyConnections: number;
    healthyConnections: number;
  } {
    const healthyCount = Array.from(this.connectionHealth.values())
      .filter(isHealthy => isHealthy).length;

    return {
      totalConnections: this.connections.length,
      availableConnections: this.availableConnections.length,
      busyConnections: this.busyConnections.size,
      healthyConnections: healthyCount,
    };
  }

  /**
   * Close all connections and cleanup
   */
  async shutdown(): Promise<void> {
    console.log('[ConnectionPool] Shutting down connection pool');

    // Clear all collections
    this.connections.length = 0;
    this.availableConnections.length = 0;
    this.busyConnections.clear();
    this.connectionHealth.clear();

    console.log('[ConnectionPool] Connection pool shutdown complete');
  }
}

/**
 * Query Performance Monitor
 * Tracks query performance and identifies slow queries
 */
export class QueryPerformanceMonitor {
  private queryMetrics: Map<string, {
    count: number;
    totalTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
  }> = new Map();

  private slowQueryThreshold = 1000; // 1 second
  private slowQueries: Array<{
    query: string;
    duration: number;
    timestamp: Date;
    error?: string;
  }> = [];

  /**
   * Record query execution
   */
  recordQuery(
    queryName: string,
    duration: number,
    error?: Error
  ): void {
    // Update metrics
    const existing = this.queryMetrics.get(queryName) || {
      count: 0,
      totalTime: 0,
      minTime: Infinity,
      maxTime: 0,
      errors: 0,
    };

    existing.count++;
    existing.totalTime += duration;
    existing.minTime = Math.min(existing.minTime, duration);
    existing.maxTime = Math.max(existing.maxTime, duration);

    if (error) {
      existing.errors++;
    }

    this.queryMetrics.set(queryName, existing);

    // Track slow queries
    if (duration > this.slowQueryThreshold) {
      this.slowQueries.push({
        query: queryName,
        duration,
        timestamp: new Date(),
        error: error?.message,
      });

      // Keep only last 100 slow queries
      if (this.slowQueries.length > 100) {
        this.slowQueries.shift();
      }

      console.warn(`[QueryMonitor] Slow query detected: ${queryName} took ${duration}ms`);
    }
  }

  /**
   * Get query performance statistics
   */
  getQueryStats(queryName: string) {
    const metrics = this.queryMetrics.get(queryName);
    if (!metrics) {
      return null;
    }

    return {
      ...metrics,
      avgTime: metrics.totalTime / metrics.count,
      errorRate: metrics.errors / metrics.count,
    };
  }

  /**
   * Get all performance metrics
   */
  getAllStats() {
    const stats: Record<string, unknown> = {};

    for (const [queryName, metrics] of this.queryMetrics) {
      stats[queryName] = {
        ...metrics,
        avgTime: metrics.totalTime / metrics.count,
        errorRate: metrics.errors / metrics.count,
      };
    }

    return stats;
  }

  /**
   * Get slow queries
   */
  getSlowQueries(limit = 10) {
    return this.slowQueries
      .slice(-limit)
      .sort((a, b) => b.duration - a.duration);
  }

  /**
   * Clear metrics (useful for testing or periodic cleanup)
   */
  clearMetrics(): void {
    this.queryMetrics.clear();
    this.slowQueries.length = 0;
  }
}

// Singleton instances
export const connectionPool = new DatabaseConnectionPool();
export const queryMonitor = new QueryPerformanceMonitor();

/**
 * Enhanced database client with connection pooling and monitoring
 */
class EnhancedDatabaseClient {
  private pool: DatabaseConnectionPool;
  private monitor: QueryPerformanceMonitor;

  constructor() {
    this.pool = connectionPool;
    this.monitor = queryMonitor;
  }

  /**
   * Execute a query with pooling, monitoring, and retry logic
   */
  async query<T>(
    queryName: string,
    queryFn: (client: SupabaseClient<Database>) => Promise<T>,
    options: {
      timeout?: number;
      retries?: number;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    let error: Error | undefined;

    try {
      const result = await this.pool.executeQuery(queryFn, options);
      return result;
    } catch (err) {
      error = err instanceof Error ? err : new Error('Unknown error');
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      this.monitor.recordQuery(queryName, duration, error);
    }
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats() {
    return this.pool.getStats();
  }

  /**
   * Get query performance statistics
   */
  getQueryStats(queryName?: string) {
    return queryName
      ? this.monitor.getQueryStats(queryName)
      : this.monitor.getAllStats();
  }

  /**
   * Get slow queries
   */
  getSlowQueries(limit?: number) {
    return this.monitor.getSlowQueries(limit);
  }
}

// Export enhanced client instance
export const enhancedDb = new EnhancedDatabaseClient();
// Export class for type usage
export { EnhancedDatabaseClient };
