'use server';

import pdfParse from 'pdf-parse/lib/pdf-parse.js';

/**
 * Custom wrapper for pdf-parse with enhanced error handling and font warning suppression
 * @param buffer PDF file buffer
 * @returns Parsed PDF data
 */
export async function parsePdf(buffer: Buffer) {
  try {
    // Suppress console warnings during PDF parsing
    const originalWarn = console.warn;
    const originalLog = console.log;

    // Temporarily override console methods to filter out font warnings
    console.warn = (message: unknown, ...args: unknown[]) => {
      if (typeof message === 'string' &&
          (message.includes('font private use area') ||
           message.includes('Indexing all PDF objects'))) {
        // Suppress font-related warnings
        return;
      }
      originalWarn(message, ...args);
    };

    console.log = (message: unknown, ...args: unknown[]) => {
      if (typeof message === 'string' &&
          (message.includes('Warning:') && message.includes('font'))) {
        // Suppress font-related log messages
        return;
      }
      originalLog(message, ...args);
    };

    // Configure pdf-parse with options to handle problematic PDFs
    const data = await pdfParse(buffer, {
      // Normalize whitespace to handle font encoding issues
      normalizeWhitespace: true,
      // Continue on font errors
      max: 0, // No page limit
      version: 'v1.10.100', // Use specific PDF.js version
    });

    // Restore original console methods
    console.warn = originalWarn;
    console.log = originalLog;

    return data;
  } catch (error) {
    // Restore console methods in case of error
    const originalWarn = console.warn;
    const originalLog = console.log;
    console.warn = originalWarn;
    console.log = originalLog;

    console.error('Error in PDF parsing:', error);

    // If it's a font-related error, provide a more helpful message
    if (error instanceof Error && error.message.includes('font')) {
      throw new Error('PDF contains complex fonts that could not be processed. Consider using a different PDF or converting to text first.');
    }

    throw error;
  }
}
