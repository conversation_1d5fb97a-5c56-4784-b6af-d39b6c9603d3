'use server';

import { Message } from '@/components/conversation/ConversationInterface';

// Define types for Gemini API
interface GeminiChatRequest {
  contents: Array<{
    role: 'user' | 'model';
    parts: Array<{
      text: string;
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
}

interface GeminiChatResponse {
  candidates: Array<{
    content: {
      role: string;
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback?: {
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  };
}

/**
 * Call the Google Gemini API for chat completion
 * @param messages Array of chat messages to send to the API
 * @returns The AI response text
 */
export async function callGeminiAPI(messages: Message[]): Promise<string> {
  // Use GOOGLE_API_KEY for Gemini API
  const apiKey = process.env.GOOGLE_API_KEY || process.env.GOOGLE_API_KEY;

  if (!apiKey) {
    throw new Error('Google/Gemini API key is not configured');
  }

  try {
    console.log('Making API request to Google Gemini...');

    // Convert messages to Gemini format
    const contents = messages.map(msg => {
      // Map roles to Gemini's expected format
      let role = 'user';
      if (msg.role === 'assistant') role = 'model';
      // System messages are treated as user messages with a special prefix
      const content = msg.role === 'system' ? `[SYSTEM: ${msg.content}]` : msg.content;

      return {
        role: role as 'user' | 'model',
        parts: [{ text: content }]
      };
    });

    // Prepare the request payload
    const payload: GeminiChatRequest = {
      contents,
      generationConfig: {
        temperature: 0.7,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 2048,
      }
    };

    // Based on the latest Google documentation
    // Using Gemini 2.5 Flash for better performance and higher rate limits
    console.log('Making direct API call to Gemini 2.5 Flash API...');

    // Define a list of models to try in order of preference
    const currentModels = [
      'gemini-2.5-flash-preview-04-17',  // Primary model with higher rate limits
      'gemini-2.0-flash',               // Fallback with good rate limits
      'gemini-1.5-pro',                 // Further fallback for reliability
      'gemini-pro'                      // Legacy model name as last resort
    ];

    let response: Response | null = null;
    let lastError: { model: string; status?: number; text?: string; error?: unknown } | null = null;

    // Try each model in sequence until one works
    for (const model of currentModels) {
      try {
        console.log(`Trying Gemini model: ${model}`);

        response = await fetch(
          `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-goog-api-key': apiKey,
            },
            body: JSON.stringify(payload),
          }
        );

        if (response.ok) {
          console.log(`Successfully used model: ${model}`);
          break;
        } else {
          const errorText = await response.text();
          console.error(`Error with model ${model}: ${response.status}`, errorText);
          lastError = { model, status: response.status, text: errorText };

          // Add delay before trying next model
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error(`Exception trying model ${model}:`, error);
        lastError = { model, error };
      }
    }

    // If all models failed, throw the last error
    if (!response || !response.ok) {
      const errorMessage = lastError?.text ||
        (lastError?.error instanceof Error ? lastError.error.message : String(lastError?.error)) ||
        'No details available';
      throw new Error(`Gemini API error: ${lastError?.status || 'Unknown'} when using ${lastError?.model}. Details: ${errorMessage}`);
    }

    const data = await response.json() as GeminiChatResponse;
    console.log('Gemini API response received');

    // Extract the response text
    const responseText = data.candidates[0]?.content?.parts[0]?.text || '';
    return responseText;
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

/**
 * Generate a poll suggestion using Google Gemini API
 * @param topic The topic of the poll
 * @param audience The target audience for the poll
 * @param additionalInfo Additional context or requirements
 * @returns A structured poll suggestion
 */
export async function generatePollWithGemini(
  topic: string,
  audience: string,
  additionalInfo?: string
): Promise<{
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'likert' | 'open';
    options?: Array<{ text: string; value: string }>;
    required: boolean;
    order: number;
  }>;
}> {
  try {
    // Create a detailed prompt for the AI
    const prompt = `
      I need to create a poll about "${topic}" for an audience of "${audience}".
      ${additionalInfo ? `Additional context: ${additionalInfo}` : ''}

      Please generate a poll with 4-5 well-designed questions that would provide meaningful insights.

      The poll should include:
      1. A title for the poll
      2. A brief description of the poll's purpose
      3. A mix of question types (single choice, multiple choice, Likert scale, and open-ended)
      4. Appropriate options for each question

      Format your response as a JSON object with this structure:
      {
        "title": "Poll Title",
        "description": "Brief description of poll purpose",
        "questions": [
          {
            "text": "Question text",
            "type": "single|multiple|likert|open",
            "options": [
              { "text": "Option text", "value": "option_value" }
            ],
            "required": true,
            "order": 1
          }
        ]
      }

      Return ONLY the JSON object without any additional text or explanation.
    `;

    // Call the Gemini API
    const response = await callGeminiAPI([{ role: 'user', content: prompt }]);

    // Extract the JSON from the response
    let jsonContent = response;

    // First, try to extract JSON from markdown code blocks
    const jsonBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      jsonContent = jsonBlockMatch[1].trim();
    }

    // Clean up any potential trailing text or formatting issues
    const possibleJsonStart = jsonContent.indexOf('{');
    const possibleJsonEnd = jsonContent.lastIndexOf('}');

    if (possibleJsonStart !== -1 && possibleJsonEnd !== -1 && possibleJsonEnd > possibleJsonStart) {
      jsonContent = jsonContent.substring(possibleJsonStart, possibleJsonEnd + 1);
    }

    try {
      // Parse the JSON response
      const parsedResponse = JSON.parse(jsonContent);
      console.log('Successfully parsed Gemini JSON response');

      // Validate and format the response
      return {
        title: parsedResponse.title || `Poll about ${topic}`,
        description: parsedResponse.description || `A poll about ${topic} for ${audience}`,
        questions: (parsedResponse.questions || []).map((q: {
          text: string;
          type: 'single' | 'multiple' | 'likert' | 'open';
          options?: Array<{ text: string; value: string }>;
          required?: boolean;
          order?: number;
        }, index: number) => ({
          text: q.text,
          type: q.type,
          options: q.options,
          required: q.required !== undefined ? q.required : true,
          order: q.order !== undefined ? q.order : index + 1
        }))
      };
    } catch (error) {
      console.error('Failed to parse Gemini response:', error);
      console.log('Gemini response was:', response);
      console.log('Attempted to parse:', jsonContent);

      // Return a basic fallback poll
      return {
        title: `Poll about ${topic}`,
        description: `A poll about ${topic} for ${audience}`,
        questions: [
          {
            text: `What do you think about ${topic}?`,
            type: 'open',
            required: true,
            order: 1
          }
        ]
      };
    }
  } catch (error) {
    console.error('Error generating poll with Gemini:', error);
    throw error;
  }
}
