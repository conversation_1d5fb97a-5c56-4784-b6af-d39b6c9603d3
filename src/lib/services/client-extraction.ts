'use client'

/**
 * Client-side service for content extraction
 * This file handles the client-side portion of content extraction
 * by making API calls to the server-side endpoints
 */

/**
 * Extract content from a URL
 * @param url The URL to extract content from
 * @returns The extracted content
 */
export async function extractContentFromUrl(url: string): Promise<string> {
  try {
    const response = await fetch('/api/extract-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, type: 'url' }),
    });

    const data = await response.json();

    // Handle both success and error cases from the API
    if (!response.ok) {
      console.error('Error response from extract-content:', data);
      return `Could not extract content from URL: ${data.message || data.error || 'Unknown error'}`;
    }

    // If there's a warning but content was still extracted, return it
    if (data.warning) {
      console.warn('Content extraction warning:', data.warning);
    }

    return data.content || '';
  } catch (error) {
    console.error('Error extracting content from URL:', error);
    return 'Error extracting content. You can continue creating your poll without extracted content.';
  }
}

/**
 * Extract content from a website
 * @param url The website URL to extract content from
 * @returns The extracted content
 */
export async function extractContentFromWebsite(url: string): Promise<string> {
  try {
    const response = await fetch('/api/extract-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, type: 'website' }),
    });

    const data = await response.json();

    // Handle both success and error cases from the API
    if (!response.ok) {
      console.error('Error response from extract-content:', data);
      return `Could not extract content from website: ${data.message || data.error || 'Unknown error'}`;
    }

    // If there's a warning but content was still extracted, return it
    if (data.warning) {
      console.warn('Content extraction warning:', data.warning);
    }

    return data.content || '';
  } catch (error) {
    console.error('Error extracting content from website:', error);
    return 'Error extracting content. You can continue creating your poll without extracted content.';
  }
}

/**
 * Extract content from a file
 * @param file The file to extract content from
 * @returns Object containing the extracted content and file metadata
 */
export interface FileExtractionResult {
  content: string;
  fileUrl?: string;
  filePath?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
}

export async function extractContentFromFile(file: File): Promise<FileExtractionResult> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/extract-file', {
      method: 'POST',
      body: formData,
    });

    const data = await response.json();

    // Handle both success and error cases from the API
    if (!response.ok) {
      console.error('Error response from extract-file:', data);
      return {
        content: `Could not extract content from file: ${data.message || data.error || 'Unknown error'}`
      };
    }

    // Return both the content and file metadata
    return {
      content: data.content || '',
      fileUrl: data.fileUrl,
      filePath: data.filePath,
      fileName: data.fileName,
      fileType: data.fileType,
      fileSize: data.fileSize
    };
  } catch (error) {
    console.error('Error extracting content from file:', error);
    return {
      content: 'Error extracting content. You can continue creating your poll without extracted content.'
    };
  }
}