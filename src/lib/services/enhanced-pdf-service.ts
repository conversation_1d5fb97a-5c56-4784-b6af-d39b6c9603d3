'use server';

import { parsePdf } from './pdf-parser';
import { Mistral } from '@mistralai/mistralai';

// Initialize the Mistral client with API key from environment variables
const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY || '';

/**
 * Enhanced PDF extraction service that can handle both text-based and image-based PDFs
 * Uses multiple approaches in order of reliability:
 * 1. Mistral AI OCR (most reliable for all PDF types)
 * 2. Standard PDF text extraction (pdf-parse) as fallback
 *
 * @param fileBuffer Binary data of the PDF file
 * @returns Extracted text content
 */
export async function extractPdfContent(fileBuffer: ArrayBuffer): Promise<string> {
  try {
    console.log(`Processing PDF file, size: ${fileBuffer.byteLength} bytes`);

    // Convert ArrayBuffer to Buffer for processing
    const buffer = Buffer.from(fileBuffer);

    // First try: Use Mistral AI OCR (most reliable approach)
    if (MISTRAL_API_KEY) {
      try {
        console.log("Attempting PDF extraction with Mistral AI OCR...");

        // Initialize Mistral client
        const mistral = new Mistral({
          apiKey: MISTRAL_API_KEY
        });

        // Convert buffer to base64 for API
        const base64Data = buffer.toString('base64');

        // Use Mistral's OCR API specifically designed for PDFs
        const response = await mistral.ocr.process({
          model: "mistral-ocr-latest",
          document: {
            type: "document_url",
            documentUrl: `data:application/pdf;base64,${base64Data}`
          },
          includeImageBase64: false // We only need text content
        });

        const result = response.pages
          ?.map(page => page.markdown)
          ?.join('\n\n')
          ?.trim() || '';

        if (result && result.length > 50) {
          console.log("Mistral AI OCR extraction succeeded");
          return result.replace(/\s+/g, ' ').trim();
        } else {
          console.log("Mistral AI OCR returned insufficient content, trying fallback...");
        }
      } catch (mistralError) {
        console.warn("Mistral AI OCR extraction failed:", mistralError);
        // Continue with fallback methods
      }
    } else {
      console.warn("MISTRAL_API_KEY not set, skipping AI-based extraction");
    }

    // Second try: Standard PDF text extraction (with improved error handling)
    try {
      console.log("Attempting standard PDF text extraction...");
      const pdfData = await parsePdf(buffer);

      if (pdfData && pdfData.text && pdfData.text.trim().length > 50) {
        console.log("Standard PDF extraction succeeded");
        return pdfData.text.replace(/\s+/g, ' ').trim();
      } else {
        console.log("Standard PDF extraction returned insufficient text");
      }
    } catch (pdfError) {
      console.warn("Standard PDF extraction failed:", pdfError);

      // Provide more helpful error messages based on error type
      if (pdfError instanceof Error) {
        if (pdfError.message.includes('font') || pdfError.message.includes('encoding')) {
          console.log("PDF has font/encoding issues, this is common with scanned documents");
        }
      }
    }

    // If we reach here, all extraction methods failed or returned insufficient content
    return 'Could not extract sufficient content from the PDF. The document may be heavily image-based, password-protected, or contain complex formatting. Please try converting the PDF to text first or use a different document format.';
  } catch (error) {
    console.error('PDF extraction error:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`PDF extraction failed: ${errorMessage}`);
  }
}

/**
 * Extract text from image files using Mistral AI
 * @param imageBuffer Binary data of the image file
 * @param mimeType MIME type of the image
 * @returns Extracted text content
 */
export async function extractImageContent(imageBuffer: ArrayBuffer, mimeType: string): Promise<string> {
  try {
    console.log(`Processing image file, size: ${imageBuffer.byteLength} bytes, type: ${mimeType}`);

    if (!MISTRAL_API_KEY) {
      throw new Error('MISTRAL_API_KEY is not set in environment variables');
    }

    // Initialize Mistral client
    const mistral = new Mistral({
      apiKey: MISTRAL_API_KEY
    });

    // Convert buffer to base64 for API
    const buffer = Buffer.from(imageBuffer);
    const base64Data = buffer.toString('base64');

    // Use Mistral's vision capabilities to extract text from the image
    const response = await mistral.chat.complete({
      model: "mistral-large-2411",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Please extract all the text content from this image. Include all important information, such as dates, names, and key details. Format the output as plain text."
            },
            {
              type: "image_url",
              imageUrl: {
                url: `data:${mimeType};base64,${base64Data}`
              }
            }
          ]
        }
      ],
      maxTokens: 4000,
    });

    const result = typeof response.choices[0]?.message?.content === 'string'
      ? response.choices[0]?.message?.content
      : '';

    if (result && result.length > 0) {
      console.log("Successfully extracted content from image");
      return result;
    }

    return 'No text content could be extracted from the image';
  } catch (error) {
    console.error('Image extraction error:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Image extraction failed: ${errorMessage}`);
  }
}
