import { NextRequest, NextResponse } from 'next/server';
import { ExtractionError } from './extraction-error';

/**
 * Production-safe error handler for serverless extraction
 * @param error The error to handle
 * @param req The request object
 * @returns A NextResponse with appropriate status and error message
 */
export async function handleExtractionError(error: unknown, req: NextRequest) {
  console.error('Extraction error middleware:', error);

  if (error instanceof ExtractionError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        url: req.url
      },
      { status: error.status }
    );
  }

  // Handle timeout errors specifically
  if (error instanceof Error && error.message.includes('timeout')) {
    return NextResponse.json(
      {
        error: 'The request timed out. The site might be too large or slow to process.',
        code: 'TIMEOUT_ERROR',
        url: req.url
      },
      { status: 504 } // Gateway Timeout
    );
  }

  // Handle CORS errors
  if (error instanceof Error && (
      error.message.includes('CORS') ||
      error.message.includes('blocked') ||
      error.message.includes('cross-origin')
  )) {
    return NextResponse.json(
      {
        error: 'The website blocked our request due to security settings.',
        code: 'CORS_ERROR',
        url: req.url
      },
      { status: 403 } // Forbidden
    );
  }

  // Generic error handler
  return NextResponse.json(
    {
      error: error instanceof Error ? error.message : 'An unknown error occurred',
      code: 'UNKNOWN_ERROR',
      url: req.url
    },
    { status: 500 }
  );
}

/**
 * Applies error handling to a route handler function execution
 * @param handler The route handler function
 * @param req The request object
 * @param args Additional arguments
 * @returns Response with error handling applied
 */
export async function withExtractionErrorHandling(
  handler: (req: NextRequest, ...args: unknown[]) => Promise<NextResponse>,
  req: NextRequest,
  ...args: unknown[]
): Promise<NextResponse> {
  try {
    // Apply a reasonable timeout for the entire handler
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new ExtractionError('Handler timed out', 504, 'TIMEOUT')), 25000);
    });

    // Race between the handler and the timeout
    return await Promise.race([
      handler(req, ...args),
      timeoutPromise
    ]);
  } catch (error) {
    return await handleExtractionError(error, req);
  }
}
