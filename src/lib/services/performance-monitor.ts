import { RedisCacheService } from './redis-cache';

/**
 * Performance Metrics Collection System for PollGPT
 *
 * Features:
 * - Real-time performance monitoring
 * - Custom metrics collection
 * - Alert system for performance issues
 * - Historical data tracking
 * - Dashboard-ready metrics export
 */

export interface MetricValue {
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface MetricDefinition {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'timer';
  description: string;
  unit?: string;
}

export interface HistogramBucketData {
  count: number;
  sum: number;
  min: number;
  max: number;
  values: number[]; // For percentile calculations, might be trimmed
}

export interface PerformanceAlert {
  id: string;
  metric: string;
  threshold: number;
  condition: 'above' | 'below' | 'equal';
  message: string;
  active: boolean;
  lastTriggered?: Date;
}

interface MetricsSummary {
  database: {
    connections: string; // Was 'N/A' or actual stats, simplified to string for now
    queryStats: string;
    slowQueries: string;
  };
  cache: {
    hitRatio: number | null;
    memoryUsage: number | null;
  };
  application: {
    polls: {
      created: number | null;
      responses: number | null;
    };
    simulations: {
      completed: number | null;
      avgDuration: number | null;
    };
  };
  system: {
    memory: string; // Was 'N/A' or actual stats
    uptime: string;
  };
}

/**
 * Metrics collector with Redis storage and real-time monitoring
 */
export class MetricsCollector {
  private readonly redis: RedisCacheService;
  private readonly metrics: Map<string, MetricDefinition> = new Map();
  private readonly alerts: Map<string, PerformanceAlert> = new Map();

  // Configuration
  private readonly config = {
    retentionDays: 7,
    aggregationIntervals: [60, 300, 3600], // 1min, 5min, 1hour in seconds
    alertCheckInterval: 30000, // 30 seconds
    maxMetricsPerKey: 1000,
  };

  constructor() {
    this.redis = new RedisCacheService();
    this.initializeDefaultMetrics();
    this.startAlertMonitoring();
  }

  /**
   * Initialize default metrics for PollGPT
   */
  private initializeDefaultMetrics(): void {
    const defaultMetrics: MetricDefinition[] = [
      // Database metrics
      { name: 'db.query.duration', type: 'histogram', description: 'Database query duration', unit: 'ms' },
      { name: 'db.query.count', type: 'counter', description: 'Total database queries' },
      { name: 'db.query.errors', type: 'counter', description: 'Database query errors' },
      { name: 'db.connections.active', type: 'gauge', description: 'Active database connections' },
      { name: 'db.connections.idle', type: 'gauge', description: 'Idle database connections' },

      // Cache metrics
      { name: 'cache.hits', type: 'counter', description: 'Cache hit count' },
      { name: 'cache.misses', type: 'counter', description: 'Cache miss count' },
      { name: 'cache.hit_ratio', type: 'gauge', description: 'Cache hit ratio percentage' },
      { name: 'cache.memory_usage', type: 'gauge', description: 'Cache memory usage', unit: 'bytes' },

      // Application metrics
      { name: 'polls.created', type: 'counter', description: 'Total polls created' },
      { name: 'polls.responses', type: 'counter', description: 'Total poll responses' },
      { name: 'simulations.completed', type: 'counter', description: 'Simulation tasks completed' },
      { name: 'simulations.duration', type: 'histogram', description: 'Simulation duration', unit: 'ms' },

      // API metrics
      { name: 'api.requests', type: 'counter', description: 'Total API requests' },
      { name: 'api.response_time', type: 'histogram', description: 'API response time', unit: 'ms' },
      { name: 'api.errors', type: 'counter', description: 'API errors' },

      // Job queue metrics
      { name: 'jobs.queued', type: 'gauge', description: 'Jobs in queue' },
      { name: 'jobs.processing', type: 'gauge', description: 'Jobs being processed' },
      { name: 'jobs.completed', type: 'counter', description: 'Jobs completed' },
      { name: 'jobs.failed', type: 'counter', description: 'Jobs failed' },

      // System metrics
      { name: 'memory.usage', type: 'gauge', description: 'Memory usage', unit: 'bytes' },
      { name: 'cpu.usage', type: 'gauge', description: 'CPU usage percentage' },
    ];

    defaultMetrics.forEach(metric => {
      this.metrics.set(metric.name, metric);
    });

    console.log(`[MetricsCollector] Initialized ${defaultMetrics.length} default metrics`);
  }

  /**
   * Register a custom metric
   */
  registerMetric(metric: MetricDefinition): void {
    this.metrics.set(metric.name, metric);
    console.log(`[MetricsCollector] Registered metric: ${metric.name}`);
  }

  /**
   * Record a metric value
   */
  async recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>
  ): Promise<void> {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`[MetricsCollector] Unknown metric: ${name}`);
      return;
    }

    const timestamp = new Date();
    const metricValue: MetricValue = { value, timestamp, tags };

    // Store raw metric value
    const key = this.getMetricKey(name, tags);
    const retentionSeconds = this.config.retentionDays * 24 * 3600;

    // Fetch existing list
    const existingListStr = await this.redis.get(key);
    let metricList: MetricValue[] = [];
    if (existingListStr && typeof existingListStr === 'string') {
      try {
        const parsed: unknown = JSON.parse(existingListStr);
        if (Array.isArray(parsed)) {
          // Further validation could be added here to ensure elements are MetricValue
          metricList = parsed as MetricValue[];
        } else {
          console.warn(`[MetricsCollector] Expected array for metric key ${key}, got ${typeof parsed}. Resetting.`);
        }
      } catch (e) {
        console.error(`[MetricsCollector] Failed to parse metric list for key ${key}:`, e);
        // metricList remains empty, effectively resetting on parse error
      }
    }

    // Add new value to the beginning (simulating lpush)
    metricList.unshift(metricValue);

    // Trim list to maintain maxMetricsPerKey (simulating ltrim)
    if (metricList.length > this.config.maxMetricsPerKey) {
      metricList = metricList.slice(0, this.config.maxMetricsPerKey);
    }

    // Store updated list, expire is handled by TTL in set
    await this.redis.set(key, JSON.stringify(metricList), retentionSeconds);

    // Update aggregated metrics
    await this.updateAggregatedMetrics(name, value, timestamp, tags);

    // Check alerts
    this.checkAlerts(name, value);
  }

  /**
   * Update aggregated metrics for different time intervals
   */
  private async updateAggregatedMetrics(
    name: string,
    value: number,
    timestamp: Date,
    tags?: Record<string, string>
  ): Promise<void> {
    const metric = this.metrics.get(name);
    if (!metric) return;

    const retentionSeconds = this.config.retentionDays * 24 * 3600;

    for (const interval of this.config.aggregationIntervals) {
      const bucketTimestamp = Math.floor(timestamp.getTime() / 1000 / interval) * interval;
      const bucketKey = this.getAggregatedMetricKey(name, interval, bucketTimestamp, tags);

      switch (metric.type) {
        case 'counter':
          const currentCounterStr = await this.redis.get(bucketKey);
          let counterValue = value;
          if (currentCounterStr && typeof currentCounterStr === 'string') {
            try {
              const parsedCounter: unknown = JSON.parse(currentCounterStr);
              if (typeof parsedCounter === 'number') {
                counterValue = parsedCounter + value;
              } else {
                console.warn(`[MetricsCollector] Expected number for counter key ${bucketKey}, got ${typeof parsedCounter}. Using new value.`);
              }
            } catch (e) {
              console.error(`[MetricsCollector] Failed to parse counter for key ${bucketKey}:`, e);
            }
          }
          await this.redis.set(bucketKey, JSON.stringify(counterValue), retentionSeconds);
          break;

        case 'gauge':
          await this.redis.set(bucketKey, JSON.stringify(value), retentionSeconds);
          break;

        case 'histogram':
        case 'timer':
          const currentHistogramStr = await this.redis.get(bucketKey);
          let updatedHistogramData: HistogramBucketData;

          const initializeNewHistogram = (val: number): HistogramBucketData => ({
            count: 1,
            sum: val,
            min: val,
            max: val,
            values: [val]
          });

          if (currentHistogramStr && typeof currentHistogramStr === 'string') {
            try {
              const parsedHistogram: unknown = JSON.parse(currentHistogramStr);
              const isHistogramBucketData = (
                obj: unknown
              ): obj is HistogramBucketData => {
                if (typeof obj !== 'object' || obj === null) {
                  return false;
                }
                const o = obj as Record<string, unknown>; // Helper for type-safe access
                return (
                  'count' in o && typeof o.count === 'number' &&
                  'sum' in o && typeof o.sum === 'number' &&
                  'min' in o && typeof o.min === 'number' &&
                  'max' in o && typeof o.max === 'number' &&
                  'values' in o && Array.isArray(o.values)
                );
              };

              if (isHistogramBucketData(parsedHistogram)) {
                const currentData = parsedHistogram as HistogramBucketData;
                updatedHistogramData = {
                  count: currentData.count + 1,
                  sum: currentData.sum + value,
                  min: Math.min(currentData.min, value),
                  max: Math.max(currentData.max, value),
                  values: [...currentData.values, value].slice(-this.config.maxMetricsPerKey)
                };
              } else {
                console.warn(`[MetricsCollector] Expected HistogramBucketData for key ${bucketKey}, got ${typeof parsedHistogram}. Resetting.`);
                updatedHistogramData = initializeNewHistogram(value);
              }
            } catch (e) {
              console.error(`[MetricsCollector] Failed to parse histogram for key ${bucketKey}:`, e);
              updatedHistogramData = initializeNewHistogram(value);
            }
          } else {
            updatedHistogramData = initializeNewHistogram(value);
          }
          await this.redis.set(bucketKey, JSON.stringify(updatedHistogramData), retentionSeconds);
          break;
      }
    }
  }

  /**
   * Get metric data for a time range
   */
  async getMetricData(
    name: string,
    startTime: Date,
    endTime: Date,
    interval = 60, // seconds
    tags?: Record<string, string>
  ): Promise<MetricValue[]> {
    const metric = this.metrics.get(name);
    if (!metric) {
      throw new Error(`Unknown metric: ${name}`);
    }

    const data: MetricValue[] = [];
    const startBucket = Math.floor(startTime.getTime() / 1000 / interval) * interval;
    const endBucket = Math.floor(endTime.getTime() / 1000 / interval) * interval;

    for (let bucketTimestamp = startBucket; bucketTimestamp <= endBucket; bucketTimestamp += interval) {
      const bucketKey = this.getAggregatedMetricKey(name, interval, bucketTimestamp, tags);
      const valueStr = await this.redis.get(bucketKey);

      if (valueStr !== null && typeof valueStr === 'string') {
        try {
          const parsedValue: unknown = JSON.parse(valueStr);
          if (metric.type === 'histogram' || metric.type === 'timer') {
            const histData = parsedValue as HistogramBucketData;
            if (histData && typeof histData === 'object' && 'count' in histData && 'sum' in histData) {
              data.push({
                value: histData.count > 0 ? histData.sum / histData.count : 0,
                timestamp: new Date(bucketTimestamp * 1000),
                tags,
              });
            }
          } else { // Counter or Gauge
            if (typeof parsedValue === 'number') {
              data.push({
                value: parsedValue,
                timestamp: new Date(bucketTimestamp * 1000),
                tags,
              });
            }
          }
        } catch (e) {
          console.error(`[MetricsCollector] Failed to parse metric data for key ${bucketKey} in getMetricData:`, e);
        }
      }
    }
    return data;
  }

  /**
   * Get histogram statistics for a specific aggregated bucket or a range by re-aggregating raw data (more complex)
   */
  async getHistogramStats(
    name: string,
    startTime: Date,
    endTime: Date,
    interval = 60,
    tags?: Record<string, string>
  ): Promise<HistogramBucketData & { avg: number; p50: number; p95: number; p99: number } | null> {
    const metric = this.metrics.get(name);
    if (!metric || (metric.type !== 'histogram' && metric.type !== 'timer')) {
      console.warn(`[MetricsCollector] Metric ${name} is not a histogram or timer.`);
      return null;
    }

    const combinedValues: number[] = [];
    let totalCount = 0;
    let totalSum = 0;
    let globalMin = Infinity;
    let globalMax = -Infinity;

    const startBucket = Math.floor(startTime.getTime() / 1000 / interval) * interval;
    const endBucket = Math.floor(endTime.getTime() / 1000 / interval) * interval;

    for (let bucketTimestamp = startBucket; bucketTimestamp <= endBucket; bucketTimestamp += interval) {
      const bucketKey = this.getAggregatedMetricKey(name, interval, bucketTimestamp, tags);
      const histStr = await this.redis.get(bucketKey);
      if (histStr && typeof histStr === 'string') {
        try {
          const histData = JSON.parse(histStr) as HistogramBucketData;
          if (histData && typeof histData === 'object' && 'values' in histData && Array.isArray(histData.values)) {
            combinedValues.push(...histData.values);
            totalCount += histData.count;
            totalSum += histData.sum;
            globalMin = Math.min(globalMin, histData.min);
            globalMax = Math.max(globalMax, histData.max);
          }
        } catch (e) {
          console.error(`[MetricsCollector] Failed to parse histogram data for key ${bucketKey} in getHistogramStats:`, e);
        }
      }
    }

    if (totalCount === 0) {
      return null;
    }

    combinedValues.sort((a, b) => a - b);

    return {
      count: totalCount,
      sum: totalSum,
      min: globalMin === Infinity ? 0 : globalMin,
      max: globalMax === -Infinity ? 0 : globalMax,
      values: combinedValues, // This could be very large, consider sampling or returning limited values
      avg: totalSum / totalCount,
      p50: this.calculatePercentile(combinedValues, 50),
      p95: this.calculatePercentile(combinedValues, 95),
      p99: this.calculatePercentile(combinedValues, 99),
    };
  }

  private calculatePercentile(sortedValues: number[], percentile: number): number {
    if (!sortedValues || sortedValues.length === 0) return 0;
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    if (lower === upper) return sortedValues[lower];
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  registerAlert(alert: PerformanceAlert): void {
    this.alerts.set(alert.id, alert);
    console.log(`[MetricsCollector] Registered alert: ${alert.id} for metric ${alert.metric}`);
  }

  private checkAlerts(metricName: string, value: number): void {
    for (const alert of this.alerts.values()) {
      if (alert.metric !== metricName || !alert.active) continue;
      let triggered = false;
      switch (alert.condition) {
        case 'above': triggered = value > alert.threshold; break;
        case 'below': triggered = value < alert.threshold; break;
        case 'equal': triggered = value === alert.threshold; break;
      }
      if (triggered) this.triggerAlert(alert, value);
    }
  }

  private triggerAlert(alert: PerformanceAlert, value: number): void {
    const now = new Date();
    if (alert.lastTriggered && (now.getTime() - alert.lastTriggered.getTime()) < this.config.alertCheckInterval) {
      return; // Debounce alerts
    }
    alert.lastTriggered = now;
    console.warn(`[MetricsCollector] ALERT: ${alert.message} (${alert.metric} = ${value}, threshold = ${alert.threshold})`);
    this.sendAlertNotification(alert, value);
    // Potentially store triggered alert state in Redis if needed for distributed systems or persistence
    const alertKey = this.getAlertKey(`triggered:${alert.id}`);
    this.redis.set(alertKey, JSON.stringify({ ...alert, value, triggeredAt: now }), 3600); // Store for 1 hour
  }

  private sendAlertNotification(alert: PerformanceAlert, value: number): void {
    console.log(`[Alert] Notification for ${alert.id}: ${alert.message} - Current value: ${value}, Threshold: ${alert.threshold}`);
    // TODO: Implement actual notification (email, Slack, etc.)
  }

  private startAlertMonitoring(): void {
    // This is a placeholder for more advanced periodic checks if needed.
    // Event-driven checks happen in checkAlerts.
    // console.log('[MetricsCollector] Alert monitoring started.');
  }

  private getMetricKey(name: string, tags?: Record<string, string>): string {
    const tagString = tags ? Object.entries(tags).sort(([a], [b]) => a.localeCompare(b)).map(([k, v]) => `${k}=${v}`).join(',') : '';
    return `metrics:raw:${name}${tagString ? `:${tagString}` : ''}`;
  }

  private getAggregatedMetricKey(name: string, interval: number, timestamp: number, tags?: Record<string, string>): string {
    const tagString = tags ? Object.entries(tags).sort(([a], [b]) => a.localeCompare(b)).map(([k, v]) => `${k}=${v}`).join(',') : '';
    const alignedTimestamp = Math.floor(timestamp / interval) * interval;
    return `metrics:agg:${name}:${interval}:${alignedTimestamp}${tagString ? `:${tagString}` : ''}`;
  }

  private getAlertKey(metricName: string): string {
    return `alerts:${metricName}`;
  }

  private async getLatestMetricValue(name: string, tags?: Record<string, string>): Promise<number | null> {
    const key = this.getMetricKey(name, tags);
    const metricListStr = await this.redis.get(key);
    if (metricListStr && typeof metricListStr === 'string') {
      try {
        const parsedList = JSON.parse(metricListStr) as unknown[]; // Parse as unknown first
        if (Array.isArray(parsedList) && parsedList.length > 0) {
          const firstElement = parsedList[0] as Partial<MetricValue>; // Type assertion
          if (firstElement && typeof firstElement.value === 'number') {
            return firstElement.value;
          }
        }
      } catch (e) {
        console.error(`[MetricsCollector] Failed to parse or access latest metric value for key ${key}:`, e);
      }
    }
    return null;
  }

  async getAlerts(metricName: string): Promise<PerformanceAlert[]> {
    const key = this.getAlertKey(metricName); // This key might be for configuration, not triggered alerts
    const alertsStr = await this.redis.get(key);
    if (alertsStr && typeof alertsStr === 'string') {
      try {
        const parsedAlerts = JSON.parse(alertsStr) as unknown;
        const isPerformanceAlertArray = (obj: unknown): obj is PerformanceAlert[] => {
          if (!Array.isArray(obj)) {
            return false;
          }
          return obj.every(item => {
            if (typeof item !== 'object' || item === null) {
              return false;
            }
            const alertItem = item as Record<string, unknown>; // Helper for type-safe access
            return (
              'id' in alertItem && typeof alertItem.id === 'string' &&
              'metric' in alertItem && typeof alertItem.metric === 'string' &&
              'threshold' in alertItem && typeof alertItem.threshold === 'number' &&
              'condition' in alertItem && typeof alertItem.condition === 'string' &&
              'message' in alertItem && typeof alertItem.message === 'string' &&
              'active' in alertItem && typeof alertItem.active === 'boolean'
              // lastTriggered is optional, so not strictly checked here for array validation
            );
          });
        };
        if (isPerformanceAlertArray(parsedAlerts)) {
          return parsedAlerts;
        }
      } catch (e) {
        console.error(`[MetricsCollector] Failed to parse alerts for key ${key}:`, e);
      }
    }
    // Fallback to in-memory alerts if Redis has no/invalid data for this key
    // This depends on how alerts are primarily managed (Redis vs. in-memory map)
    // For now, assuming this method is to fetch configured/active alerts, not triggered ones.
    const inMemoryAlerts: PerformanceAlert[] = [];
    this.alerts.forEach(alert => {
        if (alert.metric === metricName) {
            inMemoryAlerts.push(alert);
        }
    });
    return inMemoryAlerts.length > 0 ? inMemoryAlerts : [];
  }

  async getMetricsSummary(): Promise<MetricsSummary> {
    const summary: MetricsSummary = {
      database: { connections: 'N/A', queryStats: 'N/A', slowQueries: 'N/A' },
      cache: {
        hitRatio: await this.getLatestMetricValue('cache.hit_ratio'),
        memoryUsage: await this.getLatestMetricValue('cache.memory_usage'),
      },
      application: {
        polls: {
          created: await this.getLatestMetricValue('polls.created'),
          responses: await this.getLatestMetricValue('polls.responses'),
        },
        simulations: {
          completed: await this.getLatestMetricValue('simulations.completed'),
          avgDuration: await this.getLatestMetricValue('simulations.duration'),
        },
      },
      system: { memory: 'N/A', uptime: 'N/A' }, // Placeholder for now
    };
    return summary;
  }
} // End of MetricsCollector class

/**
 * Performance monitoring utilities
 */
class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metricsCollector: MetricsCollector;
  // Store alerts in memory for the PerformanceMonitor instance
  private readonly alerts: Map<string, PerformanceAlert> = new Map(); // Local alerts for PerformanceMonitor

  private constructor() { // private constructor for singleton
    this.metricsCollector = new MetricsCollector();
    this.setupDefaultAlerts();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private setupDefaultAlerts(): void {
    const defaultAlertsData: Omit<PerformanceAlert, 'active' | 'lastTriggered' | 'id'>[] = [
      { metric: 'cpu.usage', threshold: 80, condition: 'above', message: 'High CPU Usage Detected' },
      { metric: 'cache.hit_ratio', threshold: 0.6, condition: 'below', message: 'Low Cache Hit Ratio' },
      { metric: 'api.errors', threshold: 10, condition: 'above', message: 'High API Error Rate (per minute)' },
      { metric: 'simulations.duration', threshold: 5000, condition: 'above', message: 'Long Simulation Duration' },
    ];
    defaultAlertsData.forEach((alertDataBase, index) => {
      // Generate a simple id based on metric name or index if needed
      const id = alertDataBase.metric.replace(/\./g, '_') + `_alert_${index}`;
      const alert: PerformanceAlert = { ...alertDataBase, id, active: true };
      this.registerAlert(alert);
    });
  }

  registerAlert(alert: PerformanceAlert): void {
    this.alerts.set(alert.id, alert);
    // Note: MetricsCollector handles its own alert definitions and checks internally.
    // If these specific PerformanceMonitor alerts needed to be synced to MetricsCollector's Redis-backed alerts,
    // MetricsCollector would need a public method like `registerAlertFromMonitor`.
    console.log(`[PerformanceMonitor] Registered alert: ${alert.id} for metric ${alert.metric}`);
  }

  async timeFunction<T>(
    metricName: string,
    fn: () => Promise<T> | T,
    tags?: Record<string, string>
  ): Promise<T> {
    const startTime = Date.now();
    let errorThrown: Error | null = null;
    try {
      const result = await fn(); 
      return result;
    } catch (err: unknown) {
      errorThrown = err instanceof Error ? err : new Error(String(err));
      throw err;
    } finally {
      const duration = Date.now() - startTime;
      if (this.metricsCollector) {
        await this.metricsCollector.recordMetric(metricName, duration, { ...tags, success: errorThrown ? 'false' : 'true' });
        if (errorThrown) {
          await this.metricsCollector.recordMetric(`${metricName}.errors`, 1, tags);
        }
      } else {
        console.error("[PerformanceMonitor] metricsCollector not initialized in timeFunction's finally block.");
      }
    }
  }

  async recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>
  ): Promise<void> {
    if (this.metricsCollector) {
      await this.metricsCollector.recordMetric(name, value, tags);
    } else {
       console.error("[PerformanceMonitor] metricsCollector not initialized in recordMetric.");
    }
  }

  async getMetrics(
    name: string,
    startTime: Date,
    endTime: Date,
    interval = 60,
    tags?: Record<string, string>
  ): Promise<MetricValue[]> {
    if (this.metricsCollector) {
      return this.metricsCollector.getMetricData(name, startTime, endTime, interval, tags);
    }
    console.error("[PerformanceMonitor] metricsCollector not initialized in getMetrics.");
    return []; 
  }

  /**
   * Get current system status
   */
  async getSystemStatus(): Promise<MetricsSummary> {
    if (this.metricsCollector) {
      return this.metricsCollector.getMetricsSummary();
    }
    console.error("[PerformanceMonitor] metricsCollector not initialized in getSystemStatus.");
    throw new Error("MetricsCollector not initialized in PerformanceMonitor.getSystemStatus");
  }
}

// Create singleton instance
const performanceMonitor = PerformanceMonitor.getInstance();

// Export for both named imports
export { PerformanceMonitor, performanceMonitor };

// Add default export for modules that use default import
export default performanceMonitor;
