import { supabase } from '@/lib/supabase';
import { getCurrentUser } from '@/lib/utils/session-manager';

export interface AnalyticsData {
  overview: {
    totalPolls: number;
    totalResponses: number;
    avgResponseRate: number;
    totalViews: number;
    pollsGrowth: number;
    responsesGrowth: number;
    viewsGrowth: number;
  };
  pollTrend: Array<{ date: string; count: number }>;
  responseTrend: Array<{ date: string; rate: number }>;
  topPolls: Array<{
    id: string;
    title: string;
    responses: number;
    views: number;
    responseRate: number;
  }>;
  questionTypePerformance: Array<{
    type: string;
    responseRate: number;
    count: number;
  }>;
  pollStatusDistribution: Array<{
    name: string;
    value: number;
  }>;
  metrics: {
    avgCompletionTime: number;
    dropOffRate: number;
    returningUsers: number;
  };
  geographic: Array<{
    name: string;
    flag: string;
    percentage: number;
    count: number;
  }>;
  deviceDistribution: Array<{
    name: string;
    value: number;
  }>;
  audience: {
    uniqueVisitors: number;
    avgSessionDuration: number;
    bounceRate: number;
    newVsReturning: number;
  };
  quality: {
    overallScore: number;
    duplicateResponses: number;
    suspiciousIPs: number;
    botDetection: number;
  };
  qualityTrend: Array<{ date: string; score: number }>;
  simulationUsage: Array<{ month: string; simulations: number }>;
  aiInsights: {
    predictionAccuracy: number;
    recommendations: Array<{
      title: string;
      description: string;
      impact?: string;
    }>;
  };
}

export async function getAnalyticsData(timeRange: '7d' | '30d' | '90d' | '1y'): Promise<AnalyticsData> {
  const { user } = await getCurrentUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Calculate date range
  const now = new Date();
  const startDate = new Date();
  switch (timeRange) {
    case '7d':
      startDate.setDate(now.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(now.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(now.getDate() - 90);
      break;
    case '1y':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
  }

  // Get overview data
  const { data: overviewData } = await supabase
    .from('polls')
    .select(`
      id,
      created_at,
      title,
      views,
      is_published,
      is_public,
      responses(count)
    `)
    .eq('user_id', user.id)
    .gte('created_at', startDate.toISOString());

  const totalPolls = overviewData?.length || 0;
  const totalResponses = overviewData?.reduce((acc, poll) => acc + (poll.responses?.[0]?.count || 0), 0) || 0;
  const totalViews = overviewData?.reduce((acc, poll) => acc + (poll.views || 0), 0) || 0;

  // Calculate response rate
  const pollsWithResponses = overviewData?.filter(poll => (poll.responses?.[0]?.count || 0) > 0).length || 0;
  const avgResponseRate = totalPolls > 0 ? Math.round((pollsWithResponses / totalPolls) * 100) : 0;

  // Get previous period data for growth calculation
  const prevStartDate = new Date(startDate);
  const diffDays = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  prevStartDate.setDate(startDate.getDate() - diffDays);

  const { data: prevPeriodData } = await supabase
    .from('polls')
    .select('id, views, responses(count)')
    .eq('user_id', user.id)
    .gte('created_at', prevStartDate.toISOString())
    .lt('created_at', startDate.toISOString());

  const prevTotalPolls = prevPeriodData?.length || 0;
  const prevTotalResponses = prevPeriodData?.reduce((acc, poll) => acc + (poll.responses?.[0]?.count || 0), 0) || 0;
  const prevTotalViews = prevPeriodData?.reduce((acc, poll) => acc + (poll.views || 0), 0) || 0;

  // Calculate growth percentages
  const pollsGrowth = prevTotalPolls > 0 ? Math.round(((totalPolls - prevTotalPolls) / prevTotalPolls) * 100) : 0;
  const responsesGrowth = prevTotalResponses > 0 ? Math.round(((totalResponses - prevTotalResponses) / prevTotalResponses) * 100) : 0;
  const viewsGrowth = prevTotalViews > 0 ? Math.round(((totalViews - prevTotalViews) / prevTotalViews) * 100) : 0;

  // Generate poll trend data
  const pollTrend = generateTrendData(overviewData || [], 'created_at', timeRange);

  // Generate response trend data (mock for now - would need more complex query)
  const responseTrend = generateResponseTrendData(timeRange);

  // Get top performing polls
  const topPolls = (overviewData || [])
    .map(poll => {
      const responses = poll.responses?.[0]?.count || 0;
      const views = poll.views || 0;
      const responseRate = views > 0 ? Math.round((responses / views) * 100) : 0;

      return {
        id: poll.id,
        title: poll.title || 'Untitled Poll',
        responses,
        views,
        responseRate
      };
    })
    .sort((a, b) => b.responseRate - a.responseRate)
    .slice(0, 5);

  // Get question type performance
  const { data: questionData } = await supabase
    .from('questions')
    .select(`
      question_type,
      poll_id,
      polls!inner(user_id, created_at),
      answers(count)
    `)
    .eq('polls.user_id', user.id)
    .gte('polls.created_at', startDate.toISOString());

  const questionTypeStats = (questionData || []).reduce((acc, question) => {
    const type = question.question_type || 'multiple_choice';
    if (!acc[type]) {
      acc[type] = { total: 0, responses: 0 };
    }
    acc[type].total++;
    acc[type].responses += question.answers?.[0]?.count || 0;
    return acc;
  }, {} as Record<string, { total: number; responses: number }>);

  const questionTypePerformance = Object.entries(questionTypeStats).map(([type, stats]: [string, { total: number; responses: number }]) => ({
    type: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    responseRate: stats.total > 0 ? Math.round((stats.responses / stats.total) * 100) : 0,
    count: stats.total
  }));

  // Poll status distribution
  const publishedPolls = overviewData?.filter(poll => poll.is_published).length || 0;
  const draftPolls = totalPolls - publishedPolls;
  const publicPolls = overviewData?.filter(poll => poll.is_public).length || 0;
  const privatePolls = totalPolls - publicPolls;

  const pollStatusDistribution = [
    { name: 'Published', value: publishedPolls },
    { name: 'Draft', value: draftPolls },
    { name: 'Public', value: publicPolls },
    { name: 'Private', value: privatePolls }
  ].filter(item => item.value > 0);

  // Get simulation data
  const { data: simulationData } = await supabase
    .from('poll_simulations')
    .select('created_at, confidence_score')
    .eq('created_by', user.id)
    .gte('created_at', startDate.toISOString());

  const simulationUsage = generateSimulationUsageData(simulationData || [], timeRange);
  const avgConfidenceScore = simulationData?.length > 0
    ? Math.round(simulationData.reduce((acc, sim) => acc + (Number(sim.confidence_score) || 0), 0) / simulationData.length)
    : 0;

  // Mock data for features that would require more complex implementation
  const mockData = generateMockAnalyticsData(totalPolls, totalResponses);

  return {
    overview: {
      totalPolls,
      totalResponses,
      avgResponseRate,
      totalViews,
      pollsGrowth,
      responsesGrowth,
      viewsGrowth
    },
    pollTrend,
    responseTrend,
    topPolls,
    questionTypePerformance,
    pollStatusDistribution,
    metrics: mockData.metrics,
    geographic: mockData.geographic,
    deviceDistribution: mockData.deviceDistribution,
    audience: mockData.audience,
    quality: mockData.quality,
    qualityTrend: mockData.qualityTrend,
    simulationUsage,
    aiInsights: {
      predictionAccuracy: avgConfidenceScore,
      recommendations: generateAIRecommendations(totalPolls, totalResponses, avgResponseRate)
    }
  };
}

// Helper functions with proper types
interface PollDataRow {
  id: string;
  created_at: string;
  title: string;
  views?: number;
  is_published?: boolean;
  is_public?: boolean;
  responses?: Array<{ count: number }>;
}

interface SimulationDataRow {
  created_at: string;
  confidence_score: number;
}

function generateTrendData(data: PollDataRow[], dateField: keyof PollDataRow, timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const trend: Array<{ date: string; count: number }> = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    const count = data.filter(item => {
      const itemDate = new Date(item[dateField] as string).toISOString().split('T')[0];
      return itemDate === dateStr;
    }).length;

    trend.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      count
    });
  }

  return trend;
}

function generateResponseTrendData(timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const trend: Array<{ date: string; rate: number }> = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    // Generate realistic response rate trend
    const baseRate = 65;
    const variation = Math.sin(i / 10) * 10 + Math.random() * 10 - 5;
    const rate = Math.max(0, Math.min(100, Math.round(baseRate + variation)));

    trend.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      rate
    });
  }

  return trend;
}

function generateSimulationUsageData(data: SimulationDataRow[], timeRange: string) {
  const months = timeRange === '1y' ? 12 : Math.min(12, Math.ceil((timeRange === '90d' ? 90 : timeRange === '30d' ? 30 : 7) / 30));
  const usage: Array<{ month: string; simulations: number }> = [];

  for (let i = months - 1; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStr = date.toLocaleDateString('en-US', { month: 'short' });

    const count = data.filter(item => {
      const itemDate = new Date(item.created_at);
      return itemDate.getMonth() === date.getMonth() && itemDate.getFullYear() === date.getFullYear();
    }).length;

    usage.push({
      month: monthStr,
      simulations: count
    });
  }

  return usage;
}

function generateAIRecommendations(totalPolls: number, totalResponses: number, avgResponseRate: number) {
  const recommendations: Array<{ title: string; description: string; impact: string }> = [];

  if (avgResponseRate < 50) {
    recommendations.push({
      title: "Improve Response Rates",
      description: "Your response rate is below average. Try shorter polls, clearer questions, and better timing.",
      impact: "High"
    });
  }

  if (totalPolls < 5) {
    recommendations.push({
      title: "Create More Polls",
      description: "Increase your polling frequency to gather more insights and improve engagement.",
      impact: "Medium"
    });
  }

  if (totalResponses < 50) {
    recommendations.push({
      title: "Expand Your Audience",
      description: "Consider using our AI simulation feature to predict responses before launching polls.",
      impact: "High"
    });
  }

  recommendations.push({
    title: "Use AI Simulations",
    description: "Test your polls with AI simulations to optimize questions before going live.",
    impact: "Medium"
  });

  return recommendations;
}

function generateMockAnalyticsData(totalPolls: number, totalResponses: number) {
  return {
    metrics: {
      avgCompletionTime: Math.round(120 + Math.random() * 180), // 2-5 minutes
      dropOffRate: Math.round(15 + Math.random() * 20), // 15-35%
      returningUsers: Math.round(25 + Math.random() * 30) // 25-55%
    },
    geographic: [
      { name: "United States", flag: "🇺🇸", percentage: 45, count: Math.round(totalResponses * 0.45) },
      { name: "United Kingdom", flag: "🇬🇧", percentage: 20, count: Math.round(totalResponses * 0.20) },
      { name: "Canada", flag: "🇨🇦", percentage: 15, count: Math.round(totalResponses * 0.15) },
      { name: "Australia", flag: "🇦🇺", percentage: 12, count: Math.round(totalResponses * 0.12) },
      { name: "Germany", flag: "🇩🇪", percentage: 8, count: Math.round(totalResponses * 0.08) }
    ],
    deviceDistribution: [
      { name: "Desktop", value: 60 },
      { name: "Mobile", value: 35 },
      { name: "Tablet", value: 5 }
    ],
    audience: {
      uniqueVisitors: Math.round(totalResponses * 1.5),
      avgSessionDuration: Math.round(8 + Math.random() * 10), // 8-18 minutes
      bounceRate: Math.round(25 + Math.random() * 20), // 25-45%
      newVsReturning: Math.round(60 + Math.random() * 20) // 60-80%
    },
    quality: {
      overallScore: Math.round(75 + Math.random() * 20), // 75-95
      duplicateResponses: Math.round(Math.random() * 5), // 0-5%
      suspiciousIPs: Math.round(Math.random() * 3), // 0-3%
      botDetection: Math.round(Math.random() * 2) // 0-2%
    },
    qualityTrend: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      score: Math.round(75 + Math.random() * 20)
    }))
  };
}
