import { createHash } from 'crypto';

// Cache entry interface
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  expiresAt: number;
}

// Default expiration time: 24 hours
const DEFAULT_EXPIRATION = 24 * 60 * 60 * 1000;

/**
 * A simple in-memory cache service with TTL support
 */
class CacheService {
  private cache: Map<string, CacheEntry<unknown>> = new Map();

  /**
   * Generate a cache key from a source and data
   */
  generateKey(source: string, data: string | File): string {
    if (typeof data === 'string') {
      // For URLs and text
      return createHash('md5').update(`${source}:${data}`).digest('hex');
    } else {
      // For files, use name + size + last modified
      const fileInfo = `${data.name}:${data.size}:${data.lastModified}`;
      return createHash('md5').update(`${source}:${fileInfo}`).digest('hex');
    }
  }

  /**
   * Get a value from cache if it exists and has not expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    // Return null if not found
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Use type assertion to convert from unknown to T
    // This is safe because we always store values with their original type
    return entry.value as T;
  }

  /**
   * Store a value in cache with optional expiration time
   */
  set<T>(key: string, value: T, ttl: number = DEFAULT_EXPIRATION): void {
    const now = Date.now();
    this.cache.set(key, {
      value,
      timestamp: now,
      expiresAt: now + ttl
    });
  }

  /**
   * Remove a value from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all entries from cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Remove all expired entries from cache
   */
  cleanExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let activeEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      if (now <= entry.expiresAt) {
        activeEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      activeEntries,
      expiredEntries
    };
  }
}

// Export a singleton instance
export const cacheService = new CacheService();

/**
 * Run a function with cache support
 * @param cacheKey The cache key
 * @param fn The function to run if cache miss
 * @param ttl Time to live in ms
 */
export async function withCache<T>(
  cacheKey: string,
  fn: () => Promise<T>,
  ttl: number = DEFAULT_EXPIRATION
): Promise<T> {
  // Check cache first
  const cachedValue = cacheService.get<T>(cacheKey);
  if (cachedValue !== null) {
    return cachedValue;
  }

  // Cache miss, run the function
  const result = await fn();

  // Store in cache
  cacheService.set(cacheKey, result, ttl);

  return result;
}