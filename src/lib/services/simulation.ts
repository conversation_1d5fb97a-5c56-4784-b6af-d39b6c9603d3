import { supabase } from '@/lib/supabase';
import {
  SimulationRequest,
  SimulationResponse,
  PollSimulation,
  BatchSimulationRequest,
  BatchSimulationResult,
  BatchSimulationProgress,
  DemographicComparison
} from '@/lib/types/simulation';
import { simulatePoll } from './perplexity-ai';
// No longer need withEnhancedCache as we're using the specialized simulation cache service
import { simulationCacheService } from './simulation-cache';

/**
 * Simulation service for managing poll simulations with caching
 */
class SimulationService {
  /**
   * Create a poll simulation with enhanced caching
   */
  static async createSimulation(
    request: SimulationRequest,
    pollId?: string,
    userId?: string
  ): Promise<SimulationResponse> {
    try {
      // Check cache first
      const cachedResponse = await simulationCacheService.getSimulation(
        request.pollQuestion,
        request.pollOptions,
        request.demographic.group,
        request.demographic.size
      );

      if (cachedResponse) {
        console.log('Cache hit - using cached simulation');
        return cachedResponse;
      }

      // Cache miss - generate new simulation
      console.log('Cache miss - generating new simulation');
      const simulationResponse = await simulatePoll(request);

      // Store in database if poll ID and user ID are provided
      if (pollId && userId) {
        await this.storeSimulation(pollId, userId, request, simulationResponse);
      }

      // Cache the result
      await simulationCacheService.cacheSimulation(
        request.pollQuestion,
        request.pollOptions,
        request.demographic.group,
        request.demographic.size,
        simulationResponse
      );

      return simulationResponse;
    } catch (error) {
      console.error('Error in createSimulation:', error);
      throw error;
    }
  }  /**
   * Process batch simulation across multiple demographics with enhanced caching
   */
  static async processBatchSimulation(
    request: BatchSimulationRequest,
    pollId?: string,
    userId?: string,
    onProgress?: (progress: BatchSimulationProgress) => void
  ): Promise<BatchSimulationResult> {
    const batchId = request.batchId || this.generateBatchId();
    const startTime = new Date();

    try {
      // First, check if we have the entire batch result cached
      const cachedBatchResult = await simulationCacheService.getBatchResult(
        request.pollQuestion,
        request.pollOptions,
        request.demographics
      );

      if (cachedBatchResult) {
        console.log('Cache hit - using cached batch simulation result');
        return cachedBatchResult;
      }

      // Initialize progress tracking
      const progress: BatchSimulationProgress = {
        batchId,
        totalDemographics: request.demographics.length,
        completedDemographics: 0,
        status: 'running',
        startedAt: startTime
      };

      onProgress?.(progress);

      const results: SimulationResponse[] = [];
      let totalCost = 0;

      // Pre-check cache for all demographics using batch operations
      const cachedResults = await simulationCacheService.getBatchSimulations(
        request.pollQuestion,
        request.pollOptions,
        request.demographics
      );

      // Process each demographic, using cached results where available
      for (let i = 0; i < request.demographics.length; i++) {
        const demographic = request.demographics[i];
        const cacheKey = simulationCacheService.generateSimulationKey(
          request.pollQuestion,
          request.pollOptions,
          demographic.group,
          demographic.size
        );

        // Update progress
        progress.currentDemographic = demographic.group;
        progress.completedDemographics = i;
        onProgress?.(progress);

        try {
          let result = cachedResults[cacheKey];

          if (result) {
            console.log(`Using cached result for demographic: ${demographic.group}`);
            results.push(result);
          } else {
            // Create individual simulation request
            const individualRequest: SimulationRequest = {
              pollQuestion: request.pollQuestion,
              pollOptions: request.pollOptions,
              demographic,
              responseFormat: request.responseFormat
            };

            // Process simulation with enhanced caching
            result = await this.createSimulation(individualRequest, pollId, userId);
            results.push(result);
          }

          totalCost += 0.03; // Estimated cost per simulation

          // Add delay between requests to respect rate limits (only for new simulations)
          if (!result && i < request.demographics.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.error(`Error processing demographic ${demographic.group}:`, error);
          // Continue with other demographics rather than failing entirely
        }
      }

      // Complete progress
      progress.completedDemographics = results.length;
      progress.status = 'completed';
      onProgress?.(progress);

      // Generate comparison analysis
      const comparison = this.generateDemographicComparison(request.pollOptions, results);

      // Calculate cache performance metrics
      const cachedDemographicGroups = request.demographics
        .filter(d => {
          const key = simulationCacheService.generateSimulationKey(
            request.pollQuestion,
            request.pollOptions,
            d.group,
            d.size
          );
          return !!cachedResults[key];
        })
        .map(d => d.group);

      const hitRate = cachedDemographicGroups.length / request.demographics.length;
      const timeSavedSeconds = cachedDemographicGroups.length * 2; // Rough estimate: 2 seconds per cached demographic

      const batchResult: BatchSimulationResult = {
        batchId,
        pollQuestion: request.pollQuestion,
        pollOptions: request.pollOptions,
        results,
        comparison,
        metadata: {
          totalDemographics: request.demographics.length,
          totalResponses: results.reduce((sum, r) => sum + r.metadata.sampleSize, 0),
          completedAt: new Date(),
          totalCost,
          cachePerformance: {
            hitRate,
            timeSavedSeconds,
            cachedDemographics: cachedDemographicGroups
          }
        }
      };

      // Store batch result if needed
      if (pollId && userId) {
        await this.storeBatchResult(batchResult, pollId, userId);
      }

      // Cache the batch result for future use
      await simulationCacheService.cacheBatchResult(batchResult);
      console.log('Cached batch simulation result for future use');

      return batchResult;
    } catch (error) {
      console.error('Error in batch simulation:', error);

      // Update progress with error
      const errorProgress: BatchSimulationProgress = {
        batchId,
        totalDemographics: request.demographics.length,
        completedDemographics: 0,
        status: 'failed',
        startedAt: startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      onProgress?.(errorProgress);

      throw error;
    }
  }

  /**
   * Generate demographic comparison analysis
   */
  private static generateDemographicComparison(
    pollOptions: string[],
    results: SimulationResponse[]
  ): DemographicComparison {
    const optionRankings: Record<string, Array<{ demographic: string; percentage: number; rank: number }>> = {};
    const consensusOptions: string[] = [];
    const polarizingOptions: string[] = [];
    const significantDifferences: Array<{
      option: string;
      demographics: string[];
      difference: number;
      significance: 'low' | 'medium' | 'high';
    }> = [];

    // Calculate rankings for each option across demographics
    pollOptions.forEach(option => {
      const optionData = results.map(result => ({
        demographic: result.metadata.demographic,
        percentage: (result.results.distribution[option] || 0) * 100,
        rank: 0 // Will calculate rank after sorting
      }));

      // Sort by percentage descending to assign ranks
      optionData.sort((a, b) => b.percentage - a.percentage);
      optionData.forEach((item, index) => {
        item.rank = index + 1;
      });

      optionRankings[option] = optionData;

      // Analyze consensus vs polarization
      const percentages = optionData.map(d => d.percentage);
      const variance = this.calculateVariance(percentages);
      const mean = percentages.reduce((sum, p) => sum + p, 0) / percentages.length;

      if (variance < 50 && mean > 30) {
        consensusOptions.push(option);
      } else if (variance > 200) {
        polarizingOptions.push(option);
      }

      // Find significant differences
      const max = Math.max(...percentages);
      const min = Math.min(...percentages);
      const difference = max - min;

      if (difference > 20) {
        const highDemographics = optionData.filter(d => d.percentage >= mean + 10).map(d => d.demographic);
        const lowDemographics = optionData.filter(d => d.percentage <= mean - 10).map(d => d.demographic);

        significantDifferences.push({
          option,
          demographics: [...highDemographics, ...lowDemographics],
          difference,
          significance: difference > 40 ? 'high' : difference > 25 ? 'medium' : 'low'
        });
      }
    });

    // Generate insights
    const insights = this.generateComparisonInsights(optionRankings, consensusOptions, polarizingOptions, significantDifferences);

    return {
      optionRankings,
      significantDifferences,
      insights,
      consensusOptions,
      polarizingOptions
    };
  }

  /**
   * Generate textual insights from comparison data
   */
  private static generateComparisonInsights(
    optionRankings: Record<string, Array<{ demographic: string; percentage: number; rank: number }>>,
    consensusOptions: string[],
    polarizingOptions: string[],
    significantDifferences: Array<{ option: string; demographics: string[]; difference: number; significance: 'low' | 'medium' | 'high' }>
  ): string[] {
    const insights: string[] = [];

    // Consensus insights
    if (consensusOptions.length > 0) {
      insights.push(`Strong cross-demographic consensus on: ${consensusOptions.join(', ')}`);
    }

    // Polarization insights
    if (polarizingOptions.length > 0) {
      insights.push(`Highly polarizing options with significant demographic variation: ${polarizingOptions.join(', ')}`);
    }

    // Significant difference insights
    const highSignificance = significantDifferences.filter(d => d.significance === 'high');
    if (highSignificance.length > 0) {
      insights.push(`Major demographic differences observed in: ${highSignificance.map(d => d.option).join(', ')}`);
    }

    // Overall pattern insights
    const totalOptions = Object.keys(optionRankings).length;
    const consensusRate = (consensusOptions.length / totalOptions) * 100;
    const polarizationRate = (polarizingOptions.length / totalOptions) * 100;

    if (consensusRate > 50) {
      insights.push('High overall consensus across demographics - responses are relatively aligned');
    } else if (polarizationRate > 30) {
      insights.push('Significant demographic polarization - consider targeted messaging strategies');
    } else {
      insights.push('Mixed response patterns - moderate demographic variation observed');
    }

    return insights;
  }

  /**
   * Calculate variance for an array of numbers
   */
  private static calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  }

  /**
   * Generate unique batch ID
   */
  private static generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Store batch simulation result
   */
  private static async storeBatchResult(
    batchResult: BatchSimulationResult,
    pollId: string,
    userId: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('poll_simulations')
        .insert({
          poll_id: pollId,
          demographic_group: 'batch_simulation',
          sample_size: batchResult.metadata.totalResponses,
          simulation_data: batchResult,
          confidence_score: batchResult.results.reduce((sum, r) => sum + r.metadata.confidence, 0) / batchResult.results.length,
          citations: batchResult.results.flatMap(r => r.metadata.citations),
          created_by: userId
        });

      if (error) {
        console.error('Error storing batch result:', error);
      }
    } catch (error) {
      console.error('Error in storeBatchResult:', error);
    }
  }

  /**
   * Get cached simulation result
   */
  private static async getCachedSimulation(
    questionHash: string,
    demographicKey: string
  ): Promise<SimulationResponse | null> {
    try {
      const { data, error } = await supabase
        .from('simulation_cache')
        .select('cached_result, cache_expires_at')
        .eq('question_hash', questionHash)
        .eq('demographic_key', demographicKey)
        .gt('cache_expires_at', new Date().toISOString())
        .maybeSingle();

      if (error) {
        console.error('Error checking cache:', error);
        return null;
      }

      if (data) {
        return data.cached_result as SimulationResponse;
      }

      return null;
    } catch (error) {
      console.error('Error in getCachedSimulation:', error);
      return null;
    }
  }

  /**
   * Cache a simulation result
   */
  private static async cacheSimulation(
    questionHash: string,
    demographicKey: string,
    result: SimulationResponse
  ): Promise<void> {
    try {
      // Cache for 24 hours
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      const { error } = await supabase
        .from('simulation_cache')
        .upsert({
          question_hash: questionHash,
          demographic_key: demographicKey,
          cached_result: result,
          cache_expires_at: expiresAt.toISOString()
        }, {
          onConflict: 'question_hash,demographic_key'
        });

      if (error) {
        console.error('Error caching simulation:', error);
        // Don't throw - caching failure shouldn't break the simulation
      }
    } catch (error) {
      console.error('Error in cacheSimulation:', error);
      // Don't throw - caching failure shouldn't break the simulation
    }
  }

  /**
   * Store simulation in database
   */
  private static async storeSimulation(
    pollId: string,
    userId: string,
    request: SimulationRequest,
    response: SimulationResponse
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('poll_simulations')
        .insert({
          poll_id: pollId,
          demographic_group: request.demographic.group,
          sample_size: request.demographic.size,
          simulation_data: response,
          confidence_score: response.metadata.confidence,
          citations: response.metadata.citations,
          created_by: userId
        });

      if (error) {
        console.error('Error storing simulation:', error);
        // Don't throw - storage failure shouldn't break the simulation
      }
    } catch (error) {
      console.error('Error in storeSimulation:', error);
      // Don't throw - storage failure shouldn't break the simulation
    }
  }

  /**
   * Get simulations for a poll
   */
  static async getSimulationsForPoll(pollId: string): Promise<PollSimulation[]> {
    try {
      const { data, error } = await supabase
        .from('poll_simulations')
        .select('*')
        .eq('poll_id', pollId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching simulations:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getSimulationsForPoll:', error);
      return [];
    }
  }

  /**
   * Get simulation statistics for a user
   */
  static async getSimulationStats(userId: string): Promise<{
    totalSimulations: number;
    uniqueDemographics: number;
    averageConfidence: number;
    recentSimulations: number;
  }> {
    try {
      const { data, error } = await supabase
        .rpc('get_simulation_statistics', { user_id_param: userId });

      if (error) {
        console.error('Error fetching simulation stats:', error);
        return {
          totalSimulations: 0,
          uniqueDemographics: 0,
          averageConfidence: 0,
          recentSimulations: 0
        };
      }

      return data[0] || {
        totalSimulations: 0,
        uniqueDemographics: 0,
        averageConfidence: 0,
        recentSimulations: 0
      };
    } catch (error) {
      console.error('Error in getSimulationStats:', error);
      return {
        totalSimulations: 0,
        uniqueDemographics: 0,
        averageConfidence: 0,
        recentSimulations: 0
      };
    }
  }

  /**
   * Clean up expired cache entries
   */
  static async cleanupExpiredCache(): Promise<number> {
    try {
      const { data, error } = await supabase
        .rpc('cleanup_expired_simulation_cache');

      if (error) {
        console.error('Error cleaning up cache:', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      console.error('Error in cleanupExpiredCache:', error);
      return 0;
    }
  }

  /**
   * Delete a simulation
   */
  static async deleteSimulation(simulationId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('poll_simulations')
        .delete()
        .eq('id', simulationId)
        .eq('created_by', userId);

      if (error) {
        console.error('Error deleting simulation:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteSimulation:', error);
      return false;
    }
  }
}

// Export both named and default export for SimulationService
export { SimulationService };

// Create an instance for default export
const simulationService = SimulationService;
export default simulationService;
