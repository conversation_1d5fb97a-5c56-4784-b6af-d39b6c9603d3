'use server';

/**
 * Fallback PDF text extraction utility
 * This is used when Mistral API doesn't have vision capabilities
 * It provides basic text extraction from PDFs without requiring additional dependencies
 */

/**
 * Attempt to extract text content from a PDF file
 * This is a very basic extraction that might not preserve formatting
 * but can be used as a fallback when AI-based extraction isn't available
 *
 * @param buffer PDF file buffer
 * @returns Extracted text content or error message
 */
export async function extractTextFromPdf(buffer: ArrayBuffer): Promise<string> {
  try {
    console.log(`PDF extractor received buffer of size ${buffer.byteLength} bytes`);

    // Convert ArrayBuffer to Buffer
    const pdfBuffer = Buffer.from(buffer);

    // Check if this looks like a PDF (starts with %PDF)
    const header = pdfBuffer.slice(0, 4).toString('utf-8');
    if (header !== '%PDF') {
      console.warn('PDF validation warning: File does not start with %PDF signature');
      // Continue anyway - some PDFs might have extra bytes at the beginning
    }

    // Very basic text extraction - looks for text objects in the PDF
    // This is a simplified approach and won't work for all PDFs,
    // especially those with complex formatting or scanned content
    const pdfText = pdfBuffer.toString('utf-8');

    // Extract text blocks using regex patterns common in PDF text objects
    const textBlocks: string[] = [];

    // Enhanced PDF text extraction with multiple patterns for better coverage

    // Pattern 1: Standard text objects (BT...ET blocks)
    const btEtRegex = /BT\s*([\s\S]*?)\s*ET/g;
    let match;

    // Pattern 2: Look for common text operators like Tj and TJ
    const textOperatorRegex = /\/(F\w+)\s+(\d+(\.\d+)?)\s+Tf[\s\S]*?((\(.*?\)|<[0-9A-Fa-f]+>)[\s\S]*?(Tj|TJ))/g;

    // First try BT...ET blocks (most common in well-formed PDFs)
    while ((match = btEtRegex.exec(pdfText)) !== null) {
      const textBlock = match[1];

      // Extract actual text strings (enclosed in parentheses or <>)
      // Use standard global regex for maximum compatibility
      const textStringRegex = /\((.*?)\)|<([0-9A-Fa-f]+)>/g;
      let textMatch;

      while ((textMatch = textStringRegex.exec(textBlock)) !== null) {
        if (textMatch[1]) {
          // Handle text in parentheses
          textBlocks.push(decodeText(textMatch[1]));
        } else if (textMatch[2]) {
          // Handle hex-encoded text
          textBlocks.push(decodeHexText(textMatch[2]));
        }
      }
    }

    // Try the second pattern if no text was found in BT...ET blocks
    if (textBlocks.length === 0) {
      console.log('No text found in BT...ET blocks, trying text operator pattern');
      let opMatch;
      while ((opMatch = textOperatorRegex.exec(pdfText)) !== null) {
        const textPart = opMatch[4];
        if (textPart) {
          // Extract the text part without the operators
          const cleanedText = textPart.replace(/(Tj|TJ)$/, '').trim();
          if (cleanedText.startsWith('(') && cleanedText.endsWith(')')) {
            textBlocks.push(decodeText(cleanedText.substring(1, cleanedText.length - 1)));
          } else if (cleanedText.startsWith('<') && cleanedText.endsWith('>')) {
            textBlocks.push(decodeHexText(cleanedText.substring(1, cleanedText.length - 1)));
          }
        }
      }
    }

    // If text extraction worked with either method, return the text
    if (textBlocks.length > 0) {
      const extractedText = textBlocks.join(' ').trim();
      console.log(`Successfully extracted ${extractedText.length} characters from PDF`);
      return extractedText;
    }

    // Last resort - try to find any text-like content
    console.log('Standard PDF extraction methods failed, trying text pattern fallback');
    const anyTextRegex = /\(([^\)]{2,})\)/g;
    const anyTextMatches: string[] = [];
    let anyTextMatch;

    while ((anyTextMatch = anyTextRegex.exec(pdfText)) !== null) {
      if (anyTextMatch[1] && anyTextMatch[1].length > 2) {
        anyTextMatches.push(decodeText(anyTextMatch[1]));
      }
    }

    if (anyTextMatches.length > 0) {
      const fallbackText = anyTextMatches.join(' ').trim();
      console.log(`Extracted ${fallbackText.length} characters using fallback pattern`);
      return fallbackText;
    }

    return 'PDF text extraction failed. This PDF may contain scanned images or be encrypted. Please manually enter the key information from your document.';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    // Try a simpler approach as last resort
    try {
      const simpleText = Buffer.from(buffer).toString('utf-8')
        .replace(/[\x00-\x1F\x7F-\xFF]/g, ' ') // Remove non-printable chars
        .replace(/\s+/g, ' ')                  // Normalize whitespace
        .trim();

      if (simpleText.length > 100) {
        console.log('Using raw buffer text as fallback (last resort)');
        return simpleText;
      }
    } catch (e) {
      console.error('Even last resort extraction failed:', e);
    }

    return 'PDF extraction failed. Please manually enter the key information from your document.';
  }
}

/**
 * Decode regular PDF text (handles basic escape sequences)
 */
function decodeText(text: string): string {
  return text
    .replace(/\\n/g, '\n')
    .replace(/\\r/g, '\r')
    .replace(/\\t/g, '\t')
    .replace(/\\\(/g, '(')
    .replace(/\\\)/g, ')');
}

/**
 * Decode hex-encoded PDF text
 */
function decodeHexText(hex: string): string {
  let result = '';
  for (let i = 0; i < hex.length; i += 2) {
    const hexChar = hex.substr(i, 2);
    result += String.fromCharCode(parseInt(hexChar, 16));
  }
  return result;
}
