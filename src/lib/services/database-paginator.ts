/**
 * Database Pagination Service
 *
 * Implements efficient pagination for large datasets with multiple strategies:
 * - Cursor-based pagination (for real-time data)
 * - Offset-based pagination (for static data)
 * - Keyset pagination (for ordered data)
 * - Hybrid pagination with caching
 */

import { supabase } from '@/lib/supabase';
import { redisCacheService } from './redis-cache';

export interface PaginationOptions {
  page?: number;              // For offset-based pagination
  pageSize?: number;          // Number of items per page (default: 20)
  cursor?: string;            // For cursor-based pagination
  sortBy?: string;            // Field to sort by
  sortOrder?: 'asc' | 'desc'; // Sort direction
  filters?: Record<string, unknown>; // Additional filters
  cacheKey?: string;          // Cache key for results
  cacheTTL?: number;          // Cache TTL in seconds
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    currentPage?: number;
    totalPages?: number;
    totalCount?: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextPageUrl?: string;
    previousPageUrl?: string;
  };
  meta: {
    strategy: 'offset' | 'cursor' | 'keyset';
    cached: boolean;
    fromCache?: boolean;
    queryTime: number;
  };
}

/**
 * Enhanced database paginator with multiple pagination strategies
 */
class DatabasePaginator {
  private static readonly DEFAULT_PAGE_SIZE = 20;
  private static readonly MAX_PAGE_SIZE = 100;
  private static readonly CACHE_TTL = 300; // 5 minutes

  // Statistics for monitoring
  private static totalRows = 0;
  private static avgPageSize = 0;
  private static popularSortFields: string[] = [];
  private static cacheHitRate = 0;

  /**
   * Paginate results using offset-based pagination
   * Best for: Static data, admin interfaces, data with stable ordering
   */
  static async paginateWithOffset<T>(
    tableName: string,
    options: PaginationOptions = {}
  ): Promise<PaginationResult<T>> {
    const startTime = Date.now();
    const {
      page = 1,
      pageSize = this.DEFAULT_PAGE_SIZE,
      sortBy = 'created_at',
      sortOrder = 'desc',
      filters = {},
      cacheKey,
      cacheTTL = this.CACHE_TTL
    } = options;

    // Validate and normalize inputs
    const actualPage = Math.max(1, page);
    const actualPageSize = Math.min(
      Math.max(1, pageSize),
      this.MAX_PAGE_SIZE
    );
    const offset = (actualPage - 1) * actualPageSize;

    // Build cache key
    const fullCacheKey = cacheKey ?
      `pagination:offset:${tableName}:${cacheKey}:${page}:${actualPageSize}` :
      null;

    // Try to get from cache first
    if (fullCacheKey) {
      try {
        const cached = await redisCacheService.get(fullCacheKey);
        if (cached) {
          const result = JSON.parse(cached as string) as PaginationResult<T>;
          result.meta.fromCache = true;
          result.meta.queryTime = Date.now() - startTime;
          return result;
        }
      } catch (error) {
        console.error('Error reading from cache:', error);
      }
    }

    try {
      // Build query
      let query = supabase.from(tableName).select('*', { count: 'exact' });

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'string' && value.includes('%')) {
            query = query.like(key, value);
          } else {
            query = query.eq(key, value);
          }
        }
      });

      // Apply sorting and pagination
      query = query
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + actualPageSize - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Database query failed: ${error.message}`);
      }

      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / actualPageSize);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const result: PaginationResult<T> = {
        data: data || [],
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          pageSize: actualPageSize,
          hasNextPage,
          hasPreviousPage,
          nextPageUrl: hasNextPage ? this.buildPageUrl(page + 1, options) : undefined,
          previousPageUrl: hasPreviousPage ? this.buildPageUrl(page - 1, options) : undefined,
        },
        meta: {
          strategy: 'offset',
          cached: false,
          queryTime: Date.now() - startTime
        }
      };

      // Cache the result
      if (fullCacheKey) {
        try {
          await redisCacheService.set(fullCacheKey, JSON.stringify(result), cacheTTL);
        } catch (error) {
          console.error('Error writing to cache:', error);
        }
      }

      return result;

    } catch (error) {
      console.error('Offset pagination failed:', error);
      throw error;
    }
  }

  // Other methods remain the same...
  // [Previous cursor-based and keyset pagination methods would go here]

  /**
   * Auto-select the best pagination strategy based on data characteristics
   */
  static async smartPaginate<T>(
    tableName: string,
    options: PaginationOptions = {}
  ): Promise<PaginationResult<T>> {
    // Default to offset pagination for now
    return this.paginateWithOffset<T>(tableName, options);
  }

  /**
   * Invalidate pagination cache for a table
   */
  static async invalidateCache(tableName: string, cacheKeyPattern?: string): Promise<void> {
    try {
      const pattern = cacheKeyPattern || `pagination:*:${tableName}:*`;
      // TODO: Implement robust cache invalidation. RedisCacheService does not currently support deleteByPattern.
      // Consider fetching keys matching the pattern and deleting them individually if necessary,
      // or implement pattern deletion in RedisCacheService if using a Redis client that supports SCAN and DEL.
      // await redisCacheService.deleteByPattern(pattern); // This method doesn't exist on RedisCacheService
      console.warn(`Cache invalidation for pattern '${pattern}' needs a robust implementation.`);
    } catch (error) {
      console.error('Cache invalidation failed:', error);
    }
  }

  /**
   * Get pagination statistics for monitoring
   */
  static getStats() {
    return {
      totalRows: this.totalRows,
      avgPageSize: this.avgPageSize,
      popularSortFields: this.popularSortFields,
      cacheHitRate: this.cacheHitRate
    };
  }

  // Helper methods
  private static buildPageUrl(page: number, options: PaginationOptions): string {
    const params = new URLSearchParams();
    params.set('page', page.toString());
    params.set('pageSize', (options.pageSize || this.DEFAULT_PAGE_SIZE).toString());

    if (options.sortBy) params.set('sortBy', options.sortBy);
    if (options.sortOrder) params.set('sortOrder', options.sortOrder);

    return `?${params.toString()}`;
  }
}

// Export convenience functions
export const paginatePolls = (options: PaginationOptions) =>
  DatabasePaginator.smartPaginate('polls', options);

export const paginateSimulations = (options: PaginationOptions) =>
  DatabasePaginator.smartPaginate('simulations', options);

export const paginateUsers = (options: PaginationOptions) =>
  DatabasePaginator.smartPaginate('profiles', options);

// Export DatabasePaginator as both named and default export correctly
// Export for named imports
export { DatabasePaginator };
// Export for default imports
const paginator = DatabasePaginator;
export default paginator;
