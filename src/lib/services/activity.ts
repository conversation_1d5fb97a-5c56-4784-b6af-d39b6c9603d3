import { supabase } from '@/lib/supabase';
import { getCurrentUser } from '@/lib/utils/session-manager';

// Types for activity data
export interface ActivityItem {
  id: string;
  type: 'poll_created' | 'response_received' | 'poll_completed' | 'milestone_reached';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    pollId?: string;
    pollTitle?: string;
    responseCount?: number;
    milestoneType?: string;
    milestoneValue?: number;
  };
}

// Activity type configurations
const ACTIVITY_CONFIG = {
  poll_created: {
    icon: 'file-plus',
    color: 'blue',
    title: 'New Poll Created',
  },
  response_received: {
    icon: 'message-circle',
    color: 'green',
    title: 'New Response Received',
  },
  poll_completed: {
    icon: 'check-circle',
    color: 'green',
    title: 'Poll Completed 🎉',
  },
  milestone_reached: {
    icon: 'thumbs-up',
    color: 'amber',
    title: 'Milestone Reached 🎉',
  },
} as const;

/**
 * Get recent activity for the current user
 * @param limit Number of activity items to fetch (default: 10)
 * @returns Array of activity items
 */
// Cache for activity data to prevent repeated fetches
interface ActivityCache {
  data: ActivityItem[];
  timestamp: number;
}

// Use a module-level variable for caching - needs to be let since we update it
let activityCache: ActivityCache | null = null;
const CACHE_DURATION = 60000; // 1 minute cache

export async function getUserActivity(limit: number = 10, signal?: AbortSignal): Promise<ActivityItem[]> {
  // Return cached data if available and fresh
  if (activityCache && (Date.now() - activityCache.timestamp < CACHE_DURATION)) {
    console.log('Returning cached activity data');
    return activityCache.data;
  }
  try {
    // Get current user
    const { user, error: userError } = await getCurrentUser();
    if (userError || !user) {
      console.error('Error getting current user for activity:', userError);
      return [];
    }

    const activities: ActivityItem[] = [];

    // 1. Get recent poll creations
    const { data: recentPolls, error: pollsError } = await supabase
      .from('polls')
      .select('id, title, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(Math.min(limit, 5)) // Get up to 5 recent polls
      .abortSignal(signal); // Add abort signal to cancel request if needed

    if (pollsError) {
      console.error('Error fetching recent polls:', pollsError);
    } else if (recentPolls) {
      recentPolls.forEach(poll => {
        activities.push({
          id: `poll_created_${poll.id}`,
          type: 'poll_created',
          title: 'New Poll Created',
          description: `You created "${poll.title}"`,
          timestamp: poll.created_at,
          metadata: {
            pollId: poll.id,
            pollTitle: poll.title,
          },
        });
      });
    }

    // 2. Get recent responses to user's polls
    const { data: recentResponses, error: responsesError } = await supabase
      .from('responses')
      .select(`
        id,
        created_at,
        poll_id,
        polls!inner(title, user_id)
      `)
      .eq('polls.user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(Math.min(limit, 8)) // Get up to 8 recent responses
      .abortSignal(signal); // Add abort signal to cancel request if needed

    if (responsesError) {
      console.error('Error fetching recent responses:', responsesError);
    } else if (recentResponses) {
      recentResponses.forEach(response => {
        const poll = response.polls as { title: string; user_id: string };
        activities.push({
          id: `response_received_${response.id}`,
          type: 'response_received',
          title: 'New Response Received',
          description: `Your poll "${poll.title}" received a new response`,
          timestamp: response.created_at,
          metadata: {
            pollId: response.poll_id,
            pollTitle: poll.title,
          },
        });
      });
    }

    // 3. Check for poll completion milestones (polls with significant response counts)
    const { data: pollStats, error: statsError } = await supabase
      .rpc('get_polls_with_counts', {
        user_id_param: user.id,
        page_number: 1,
        page_size: 50,
        fetch_all: true
      });

    if (statsError) {
      console.error('Error fetching poll stats:', statsError);
      // Fallback: Use a simpler approach
      const { data: fallbackPolls, error: fallbackError } = await supabase
        .from('polls')
        .select('id, title, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!fallbackError && fallbackPolls) {
        for (const poll of fallbackPolls) {
          const { count, error: countError } = await supabase
            .from('responses')
            .select('*', { count: 'exact', head: true })
            .eq('poll_id', poll.id);

          if (!countError && count && count >= 10) {
            activities.push({
              id: `poll_completed_${poll.id}`,
              type: 'poll_completed',
              title: 'Poll Completed 🎉',
              description: `Your poll "${poll.title}" received ${count} responses`,
              timestamp: poll.created_at,
              metadata: {
                pollId: poll.id,
                pollTitle: poll.title,
                responseCount: count,
              },
            });
          }
        }
      }
    } else if (pollStats) {
      pollStats.forEach((poll: { id: string; title: string; created_at: string; response_count: number }) => {
        const responseCount = poll.response_count || 0;

        // Add completion milestone for polls with 10+ responses
        if (responseCount >= 10) {
          activities.push({
            id: `poll_completed_${poll.id}`,
            type: 'poll_completed',
            title: 'Poll Completed 🎉',
            description: `Your poll "${poll.title}" received ${responseCount} responses`,
            timestamp: poll.created_at,
            metadata: {
              pollId: poll.id,
              pollTitle: poll.title,
              responseCount,
            },
          });
        }
      });
    }

    // 4. Calculate overall milestones by counting total responses
    const { data: totalResponsesData, error: totalResponsesError } = await supabase
      .from('responses')
      .select(`
        id,
        polls!inner(user_id)
      `)
      .eq('polls.user_id', user.id);

    if (totalResponsesError) {
      console.error('Error fetching total responses:', totalResponsesError);
    } else if (totalResponsesData) {
      const totalResponses = totalResponsesData.length;

      // Add milestone for response count milestones
      const milestones = [25, 50, 100, 250, 500, 1000];
      const reachedMilestone = milestones.find(milestone =>
        totalResponses >= milestone && totalResponses < milestone * 2
      );

      if (reachedMilestone) {
        activities.push({
          id: `milestone_responses_${reachedMilestone}`,
          type: 'milestone_reached',
          title: 'Milestone Reached 🎉',
          description: `Your polls have collected over ${reachedMilestone} responses!`,
          timestamp: new Date().toISOString(), // Use current time for milestones
          metadata: {
            milestoneType: 'responses',
            milestoneValue: reachedMilestone,
          },
        });
      }
    }

    // Sort all activities by timestamp (most recent first) and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
    
    // Store in cache for future requests
    activityCache = {
      data: sortedActivities,
      timestamp: Date.now()
    };
    
    return sortedActivities;

  } catch (error) {
    console.error('Error fetching user activity:', error);
    return [];
  }
}

/**
 * Format timestamp for display in activity feed
 * @param timestamp ISO timestamp string
 * @returns Formatted time string (e.g., "2h ago", "Yesterday")
 */
export function formatActivityTime(timestamp: string): string {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else if (diffInSeconds < 172800) {
    return 'Yesterday';
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  } else {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks}w ago`;
  }
}

/**
 * Get activity configuration for styling
 * @param type Activity type
 * @returns Configuration object with icon, color, and title
 */
export function getActivityConfig(type: ActivityItem['type']) {
  return ACTIVITY_CONFIG[type];
}
