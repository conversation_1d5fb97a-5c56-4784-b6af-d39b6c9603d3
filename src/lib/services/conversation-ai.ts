import { Message } from '@/components/conversation/ConversationInterface';
import { generatePollQuestions } from './perplexity-ai';

export interface PollSuggestion {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'text';
    options?: string[];
    required: boolean;
  }>;
}

// Process the conversation to extract key information for poll generation
export function extractPollInfoFromConversation(messages: Message[]): {
  topic: string;
  audience: string;
  purpose: string;
  additionalInfo: string;
} {
  // Default values
  let topic = '';
  let audience = 'general audience';
  let purpose = 'gather feedback';
  let additionalInfo = '';

  // Extract information from the conversation
  const userMessages = messages.filter(m => m.role === 'user').map(m => m.content);
  const fullConversation = messages.map(m => `${m.role}: ${m.content}`).join('\n');

  // Simple extraction logic - can be enhanced with more sophisticated NLP
  if (userMessages.length > 0) {
    // Use the first user message as a starting point for the topic
    topic = userMessages[0].split('.')[0];

    // Look for audience indicators
    if (fullConversation.toLowerCase().includes('customer')) audience = 'customers';
    if (fullConversation.toLowerCase().includes('employee')) audience = 'employees';
    if (fullConversation.toLowerCase().includes('student')) audience = 'students';

    // Look for purpose indicators
    if (fullConversation.toLowerCase().includes('feedback')) purpose = 'gather feedback';
    if (fullConversation.toLowerCase().includes('satisfaction')) purpose = 'measure satisfaction';
    if (fullConversation.toLowerCase().includes('opinion')) purpose = 'collect opinions';

    // Combine all user messages for additional context
    additionalInfo = userMessages.join(' ');
  }

  return { topic, audience, purpose, additionalInfo };
}

// Generate a poll suggestion based on the conversation
export async function generatePollFromConversation(messages: Message[]): Promise<PollSuggestion> {
  // Extract key information from the conversation
  const { topic, audience, additionalInfo } = extractPollInfoFromConversation(messages);

  try {
    // Call the AI service to generate questions
    const response = await generatePollQuestions({
      title: topic,
      topic,
      audience,
      additionalInfo
    });

    // Try to parse the response
    try {
      // First try to parse directly if it's a string
      if (typeof response === 'string') {
        const parsedResponse = JSON.parse(response);
        return parsedResponse;
      } else {
        // Convert the response to the expected format
        const result: PollSuggestion = {
          title: response.title || topic,
          description: response.description || `A poll about ${topic}`,
          questions: response.questions.map(q => ({
            text: q.text,
            type: q.type as 'single' | 'multiple' | 'text',
            options: q.options?.map(o => typeof o === 'string' ? o : o.text) || [],
            required: q.required
          }))
        };
        return result;
      }
    } catch {
      // If direct parsing fails, try to extract JSON from the text
      const responseStr = typeof response === 'string' ? response : JSON.stringify(response);
      const jsonMatch = responseStr.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const extractedJson = jsonMatch[0];
          const parsedJson = JSON.parse(extractedJson);
          return parsedJson;
        } catch (extractError) {
          console.error("Failed to extract JSON from response", extractError);
        }
      }

      // Fallback to a simple structure if parsing fails
      return {
        title: topic || "New Poll",
        description: `A poll about ${topic} for ${audience}`,
        questions: [
          {
            text: "What do you think about this topic?",
            type: "text",
            required: true
          }
        ]
      };
    }
  } catch (error) {
    console.error("Error generating poll from conversation:", error);
    // Return a fallback poll if generation fails
    return {
      title: topic || "New Poll",
      description: "Tell us your thoughts",
      questions: [
        {
          text: "What do you think about this topic?",
          type: "text",
          required: true
        }
      ]
    };
  }
}

// Generate the next assistant message in the conversation
export async function generateNextAssistantMessage(messages: Message[]): Promise<string> {
  // If this is the first message, use a standard greeting
  if (messages.length === 0 || (messages.length === 1 && messages[0].role === 'assistant')) {
    return "Hi there! I'm PollGPT. Tell me about the poll you want to create. What topic are you interested in?";
  }

  // Get the last user message
  const lastUserMessage = [...messages].reverse().find(m => m.role === 'user');
  if (!lastUserMessage) {
    return "What kind of poll would you like to create?";
  }

  // Count how many user messages we have
  const userMessageCount = messages.filter(m => m.role === 'user').length;

  // First user message - ask about purpose
  if (userMessageCount === 1) {
    return `Great! A poll about "${lastUserMessage.content}". What's the main purpose of this poll? Who is your target audience?`;
  }

  // Second user message - ask for more details
  if (userMessageCount === 2) {
    return "Thanks for the details! Any specific questions you'd like to include in your poll? Or should I suggest some based on our conversation?";
  }

  // Third user message - suggest generating the poll
  if (userMessageCount === 3) {
    return "I think I have enough information to generate a poll for you. Would you like me to create it now? Or is there anything else you'd like to add?";
  }

  // Fourth or more - generic response
  return "I'm ready to generate your poll whenever you are. Just let me know if you want to proceed or if you have more details to add.";
}
