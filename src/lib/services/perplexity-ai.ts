import { PollResponse } from '@/lib/validation/schemas';
import {
  SimulationRequest,
  SimulationResponse,
  SimulatedResponse
} from '../types/simulation';
import {
  buildSimulationPrompt,
  validateSimulationRequest,
  generateSimulationId,
  parseSimulationResponse
} from '../utils/prompt-builder';

export type PollCreationInput = {
  title: string;
  topic: string;
  audience: string;
  additionalInfo?: string;
};

export type QuestionOption = {
  text: string;
  value: string;
};

export type GeneratedQuestion = {
  text: string;
  type: 'single' | 'multiple' | 'open';
  options?: QuestionOption[];
  required: boolean;
  order: number;
};

export type GeneratedPoll = {
  title: string;
  description: string;
  questions: GeneratedQuestion[];
};

// Function to call the Perplexity AI API
// Function to call Perplexity via our API route when on client-side
async function callPerplexityViaAPIRoute(prompt: string): Promise<string> {
  try {
    const response = await fetch('/api/perplexity', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt })
    });

    if (!response.ok) {
      throw new Error(`API route failed: ${response.status}`);
    }

    const data = await response.json();
    return data.content;
  } catch (error) {
    console.error('Error calling Perplexity via API route:', error);
    throw error;
  }
}

// Simple cache for API calls to prevent redundant calls with the same prompt
const apiCallCache: Record<string, {response: string, timestamp: number}> = {};

async function callPerplexityAPI(prompt: string): Promise<string> {
  // Generate a cache key from the prompt (using a simple hash)
  const cacheKey = hashString(prompt);
  const now = Date.now();

  // Check if we have a cached response that isn't expired
  const cachedData = apiCallCache[cacheKey];
  if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRATION_MS)) {
    console.log('Using cached API response');
    return cachedData.response;
  }

  // Access API key based on environment (server or client-side)
  const apiKey = typeof window === 'undefined'
    ? process.env.PERPLEXITY_API_KEY
    : process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;

  // For client-side calls, we should use the API route instead of direct API calls
  if (typeof window !== 'undefined' && !apiKey) {
    console.log('Using API route for client-side Perplexity calls');
    return callPerplexityViaAPIRoute(prompt);
  }

  if (!apiKey) {
    throw new Error('Perplexity API key is not configured');
  }

  try {
    console.log("Making API request to Perplexity...");
    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'sonar', // Using Perplexity's current Sonar model
        messages: [
          { role: 'system', content: 'You are an expert poll designer who creates engaging, unbiased questions.' },
          { role: 'user', content: prompt }
        ],
        max_tokens: 1024,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${await response.text()}`);
    }

    const data = await response.json();
    console.log("API response received");
    const content = data.choices[0].message.content;

    // Cache the response
    apiCallCache[cacheKey] = {
      response: content,
      timestamp: now
    };

    return content;
  } catch (error) {
    console.error('Error calling Perplexity API:', error);
    throw error;
  }
}

// Simple string hash function for creating cache keys
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString();
}

// Add interface for AI response
interface AIGeneratedQuestion {
  text: string;
  type: 'single' | 'multiple' | 'open';
  options?: QuestionOption[];
  required?: boolean;
  order?: number;
}

interface AIGeneratedResponse {
  description: string;
  questions: AIGeneratedQuestion[];
}

// Generate poll questions using the Perplexity AI API
export async function generatePollQuestions(input: PollCreationInput): Promise<GeneratedPoll> {
  console.log('Generating poll questions with input:', input);

  try {
    // Create a detailed prompt for the AI
    const prompt = `
      I need to create a poll about "${input.topic}" for an audience of "${input.audience}".
      The poll title is: "${input.title}"
      ${input.additionalInfo ? `Additional context: ${input.additionalInfo}` : ''}

      Please generate a poll with 4-5 well-designed questions that would provide meaningful insights.

      The poll should include:
      1. A brief description of the poll's purpose
      2. A mix of question types (single choice, multiple choice, and open-ended)
      3. Appropriate options for each question

      Format your response as a JSON object with this structure:
      {
        "description": "Brief description of poll purpose",
        "questions": [
          {
            "text": "Question text",
            "type": "single|multiple|open",
            "options": [
              { "text": "Option text", "value": "option_value" }
            ],
            "required": true|false,
            "order": number
          }
        ]
      }
    `;

    // Call the Perplexity API
    const aiResponse = await callPerplexityAPI(prompt);

    // Extract the JSON from the response
    let jsonContent = aiResponse;

    // First, try to extract JSON from markdown code blocks
    const jsonBlockMatch = aiResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      jsonContent = jsonBlockMatch[1].trim();
    }

    // Clean up any potential trailing text or formatting issues
    // This helps with cases where there might be explanatory text after the JSON
    const possibleJsonStart = jsonContent.indexOf('{');
    const possibleJsonEnd = jsonContent.lastIndexOf('}');

    if (possibleJsonStart !== -1 && possibleJsonEnd !== -1 && possibleJsonEnd > possibleJsonStart) {
      jsonContent = jsonContent.substring(possibleJsonStart, possibleJsonEnd + 1);
    }

    let parsedResponse: AIGeneratedResponse;
    try {
      // Try to fix common JSON formatting errors before parsing
      let cleanedJson = jsonContent;

      // Fix malformed question objects (missing property name or extra braces)
      cleanedJson = cleanedJson.replace(/"text":\s*"([^"]+)",\s*\{\s*"type":/g, '"text": "$1", "type":');

      // Fix boolean values that might be unquoted
      cleanedJson = cleanedJson.replace(/"value":\s*true/g, '"value": "true"');
      cleanedJson = cleanedJson.replace(/"value":\s*false/g, '"value": "false"');

      // Fix missing commas between objects
      cleanedJson = cleanedJson.replace(/}\s*{/g, '},{');

      parsedResponse = JSON.parse(cleanedJson);
      console.log('Successfully parsed JSON response');
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      console.log('AI response was:', aiResponse);
      console.log('Attempted to parse:', jsonContent);
      // Fall back to mock implementation if parsing fails
      return generateMockPollQuestions(input);
    }

    // Ensure the response has the expected structure
    if (!parsedResponse.description || !Array.isArray(parsedResponse.questions)) {
      console.error('AI response has incorrect format:', parsedResponse);
      return generateMockPollQuestions(input);
    }

    // Return the formatted poll
    return {
      title: input.title,
      description: parsedResponse.description,
      questions: parsedResponse.questions.map((q: AIGeneratedQuestion, index: number) => ({
        text: q.text,
        type: q.type,
        options: q.options,
        required: q.required !== undefined ? q.required : true,
        order: q.order !== undefined ? q.order : index + 1
      }))
    };
  } catch (error) {
    console.error('Error during poll generation:', error);
    // Fall back to the mock implementation if something goes wrong
    return generateMockPollQuestions(input);
  }
}

// The original mock implementation as a fallback
function generateMockPollQuestions(input: PollCreationInput): GeneratedPoll {
  // Generate a description based on the input
  const description = `A poll about ${input.topic.toLowerCase()} designed for ${input.audience.toLowerCase()}.`;

  // Sample questions based on common poll types
  const questions: GeneratedQuestion[] = [
    {
      text: `How satisfied are you with ${input.title}?`,
      type: 'single',
      options: [
        { text: 'Very Dissatisfied', value: '1' },
        { text: 'Dissatisfied', value: '2' },
        { text: 'Neutral', value: '3' },
        { text: 'Satisfied', value: '4' },
        { text: 'Very Satisfied', value: '5' }
      ],
      required: true,
      order: 1
    },
    {
      text: `Which aspects of ${input.title} do you find most valuable?`,
      type: 'multiple',
      options: [
        { text: 'Ease of use', value: 'ease_of_use' },
        { text: 'Features and functionality', value: 'features' },
        { text: 'Performance and reliability', value: 'performance' },
        { text: 'Customer support', value: 'support' },
        { text: 'Value for money', value: 'value' }
      ],
      required: true,
      order: 2
    },
    {
      text: `How likely are you to recommend ${input.title} to others?`,
      type: 'single',
      options: [
        { text: 'Not at all likely', value: '1' },
        { text: 'Slightly likely', value: '2' },
        { text: 'Moderately likely', value: '3' },
        { text: 'Very likely', value: '4' },
        { text: 'Extremely likely', value: '5' }
      ],
      required: true,
      order: 3
    },
    {
      text: `What improvements would you suggest for ${input.title}?`,
      type: 'open',
      required: false,
      order: 4
    }
  ];

  // Add a custom question based on additional info if provided
  if (input.additionalInfo && input.additionalInfo.length > 10) {
    questions.push({
      text: `Do you have any specific feedback about ${input.additionalInfo.slice(0, 50)}...?`,
      type: 'open',
      required: false,
      order: 5
    });
  }

  // Return the generated poll
  return {
    title: input.title,
    description,
    questions
  };
}

// Cache to store insights by pollId to prevent redundant API calls
const insightsCache: Record<string, {insights: string[], timestamp: number}> = {};

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION_MS = 5 * 60 * 1000;

// Generate AI insights from poll results using the Perplexity API
export async function generatePollInsights(pollId: string, responses: PollResponse[]): Promise<string[]> {
  // Check if we have cached insights for this poll that aren't expired
  const cachedData = insightsCache[pollId];
  const now = Date.now();

  if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRATION_MS)) {
    console.log('Using cached insights for poll:', pollId);
    return cachedData.insights;
  }

  console.log('Generating insights for poll:', pollId, 'with responses:', responses.length);

  if (responses.length === 0) {
    return ["No responses to analyze yet."];
  }

  try {
    // Create a prompt for the AI analysis
    const prompt = `
      I have a poll with ${responses.length} responses. Here's a summary of the data:

      ${JSON.stringify(responses, null, 2)}

      Please analyze this data and provide 5-7 key insights about the results.
      Format each insight as a separate string in a JSON array. Focus on:

      1. Overall satisfaction/response trends
      2. Most common selections for multiple choice questions
      3. Patterns in the open-ended responses
      4. Any demographic correlations if demographic data is available
      5. Actionable recommendations based on the responses

      Format your response as a JSON array of strings: ["Insight 1", "Insight 2", ...]
    `;

    // Call the Perplexity API
    const aiResponse = await callPerplexityAPI(prompt);

    // Extract the JSON from the response
    let jsonContent = aiResponse;

    // First, try to extract JSON from markdown code blocks
    const jsonBlockMatch = aiResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      jsonContent = jsonBlockMatch[1].trim();
    }

    // Clean up any potential trailing text or formatting issues
    const possibleJsonStart = jsonContent.indexOf('[');
    const possibleJsonEnd = jsonContent.lastIndexOf(']');

    if (possibleJsonStart !== -1 && possibleJsonEnd !== -1 && possibleJsonEnd > possibleJsonStart) {
      jsonContent = jsonContent.substring(possibleJsonStart, possibleJsonEnd + 1);
    }

    // Check if the response has a recognizable pattern of key-value strings without proper JSON formatting
    if (jsonContent.includes('": "') && jsonContent.includes('The majority of respondents')) {
      console.log('Detected insights format requiring special handling...');

      try {
        // Extract each "insight" as a separate string
        const insightMatches = jsonContent.match(/"([^"]+?)(?:"|:)/g);
        if (insightMatches && insightMatches.length > 0) {
          // Clean up the matches
          const insights = insightMatches
            .map(match => match.replace(/^"|"$|":$/g, '').trim())
            .filter(insight => insight.length > 30); // Only keep substantial insights

          if (insights.length > 0) {
            return insights;
          }
        }
      } catch (error) {
        console.log('Special format handling failed, continuing with standard approach', error);
      }
    }

    try {
      // First, try to parse the JSON directly
      const insights = JSON.parse(jsonContent);
      console.log('Successfully parsed insights JSON response');

      if (Array.isArray(insights)) {
        // Cache the insights
        insightsCache[pollId] = {
          insights: insights,
          timestamp: Date.now()
        };
        return insights;
      } else {
        // In case we get an object or other format instead of array
        return ["The AI analysis couldn't be processed correctly. Please try again later."];
      }
    } catch (error) {
      console.log('Direct JSON parsing failed, attempting to fix common issues...');
      console.error('Parse error:', error);
      console.log('Problematic content:', jsonContent);

      try {
        // More robust approach: Fix unescaped quotes in JSON strings
        let cleanedJson = jsonContent.trim();

        // Remove any potential trailing text after the closing bracket
        const lastBracket = cleanedJson.lastIndexOf(']');
        if (lastBracket !== -1) {
          cleanedJson = cleanedJson.substring(0, lastBracket + 1);
        }

        // If it's not a valid JSON array, try to extract individual strings
        if (!cleanedJson.startsWith('[') || !cleanedJson.endsWith(']')) {
          console.log('Content is not a proper JSON array, attempting manual extraction...');
          return extractInsightsFromMalformedResponse(jsonContent, pollId);
        }

        // Fix unescaped quotes within JSON strings - more comprehensive approach
        cleanedJson = fixUnescapedQuotesInJsonArray(cleanedJson);

        // Try parsing again
        const insights = JSON.parse(cleanedJson);

        if (Array.isArray(insights)) {
          console.log('Successfully parsed insights after quote fixing');
          // Cache the insights
          insightsCache[pollId] = {
            insights: insights,
            timestamp: Date.now()
          };
          return insights;
        } else {
          return ["The AI analysis couldn't be processed correctly. Please try again later."];
        }
      } catch (secondError) {
        console.error('Failed to parse AI insights after cleanup:', secondError, 'Content:', jsonContent);
        return extractInsightsFromMalformedResponse(jsonContent, pollId);
      }
    }
  } catch (error) {
    console.error('Error generating poll insights:', error);
    return generateMockInsights();
  }
}

// Helper function to fix unescaped quotes in JSON array strings
function fixUnescapedQuotesInJsonArray(jsonStr: string): string {
  try {
    // More robust approach: Use regex to find and fix unescaped quotes within array elements
    // This handles the common case where AI responses have unescaped quotes within strings

    // First, ensure we're working with a JSON array format
    if (!jsonStr.trim().startsWith('[') || !jsonStr.trim().endsWith(']')) {
      return jsonStr;
    }

    // Handle the case where the response is an array of objects with string keys that have colons
    // This fixes the specific error where we get strings as object keys
    if (jsonStr.includes('": "') && !jsonStr.includes('": {')) {
      // This might be an array of string key-values formatted incorrectly
      try {
        // Convert the problematic format to proper array of strings
        const extractedStrings: string[] = [];

        // Extract each JSON object in the array
        const objRegex = /{([^}]+)}/g;
        const objects = jsonStr.match(objRegex);

        if (objects && objects.length > 0) {
          objects.forEach(objStr => {
            // Split each object by comma to get individual key-value pairs
            const pairs = objStr.slice(1, -1).split(/,(?=\s*")/);
            pairs.forEach(pair => {
              // Extract the value part from each pair
              const valuePart = pair.trim();
              if (valuePart && valuePart.includes(':')) {
                extractedStrings.push(valuePart.trim());
              }
            });
          });
        }

        if (extractedStrings.length > 0) {
          return `["${extractedStrings.join('", "')}"]`;
        }
      } catch (error) {
        console.log('Failed special case handling, continuing with standard approach', error);
      }
    }

    // Split into array elements while preserving structure
    const arrayContent = jsonStr.slice(1, -1); // Remove [ and ]

    // Split by ", " pattern but be careful about quotes within strings
    const elements: string[] = [];
    let currentElement = '';
    let inQuotes = false;
    let quoteChar = '';
    let i = 0;

    while (i < arrayContent.length) {
      const char = arrayContent[i];
      const nextChar = i < arrayContent.length - 1 ? arrayContent[i + 1] : '';

      if (!inQuotes && char === '"') {
        // Starting a new quoted string
        inQuotes = true;
        quoteChar = '"';
        currentElement += char;
      } else if (inQuotes && char === quoteChar && arrayContent[i - 1] !== '\\') {
        // Ending the quoted string (not escaped)
        currentElement += char;
        inQuotes = false;
        quoteChar = '';
      } else if (!inQuotes && char === ',' && nextChar === ' ') {
        // Found separator between array elements
        elements.push(currentElement.trim());
        currentElement = '';
        i++; // Skip the space after comma
      } else if (inQuotes && char === '"' && quoteChar === '"') {
        // Unescaped quote within a string - escape it
        currentElement += '\\"';
      } else {
        currentElement += char;
      }
      i++;
    }

    // Add the last element
    if (currentElement.trim()) {
      elements.push(currentElement.trim());
    }

    // Reconstruct the array with properly escaped quotes
    const fixedElements = elements.map(element => {
      if (element.startsWith('"') && element.endsWith('"')) {
        // Extract content and re-escape properly
        const content = element.slice(1, -1);
        const escapedContent = content.replace(/"/g, '\\"');
        return `"${escapedContent}"`;
      }
      return element;
    });

    return `[${fixedElements.join(', ')}]`;

  } catch (error) {
    console.error('Error fixing unescaped quotes:', error);
    return jsonStr; // Return original if fixing fails
  }
}

// Helper function to extract insights from malformed responses
function extractInsightsFromMalformedResponse(content: string, pollId: string): string[] {
  console.log('Attempting to extract insights from malformed response...');

  try {
    // Special case for the specific pattern we've seen in the error logs
    if (content.includes("Overall satisfaction/response trends") && content.includes("The majority of respondents")) {
      const insights: string[] = [];

      // Try to extract key lines with recognizable pattern
      const keyPhrases = [
        "Overall satisfaction/response trends",
        "Most common selections for multiple choice questions",
        "Patterns in the open-ended responses",
        "Demographic correlations",
        "Actionable recommendations based on the responses"
      ];

      for (const phrase of keyPhrases) {
        if (content.includes(phrase)) {
          // Try to extract the full sentence/paragraph containing this phrase
          const phraseIndex = content.indexOf(phrase);
          if (phraseIndex >= 0) {
            // Find the end of this insight (either next key phrase or end of content)
            let endIndex = content.length;
            for (const nextPhrase of keyPhrases) {
              const nextPhraseIndex = content.indexOf(nextPhrase, phraseIndex + phrase.length);
              if (nextPhraseIndex > phraseIndex && nextPhraseIndex < endIndex) {
                endIndex = nextPhraseIndex;
              }
            }

            // Extract and clean up the insight
            const insight = content.substring(phraseIndex, endIndex).trim()
              .replace(/^["'{[\s]+|["'}]\s*,?\s*$/g, '') // Remove quotes, brackets at start/end
              .replace(/\\"/g, '"');  // Replace escaped quotes

            if (insight.length > 20) {
              insights.push(insight);
            }
          }
        }
      }

      // If we extracted insights with this approach, return them
      if (insights.length > 0) {
        console.log(`Extracted ${insights.length} structured insights from malformed response`);
        // Cache the insights
        insightsCache[pollId] = {
          insights: insights,
          timestamp: Date.now()
        };
        return insights;
      }
    }

    // Regular extraction logic (fallback)
    // Try to extract content that looks like array items
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const insights: string[] = [];

    for (const line of lines) {
      // Look for lines that start with quote and contain meaningful content
      if (line.includes('"') && line.length > 10) {
        // Extract content between quotes, handling multiple quotes carefully
        const matches = line.match(/"([^"]+(?:"[^"]*"[^"]*)*[^"]*)"/g);
        if (matches) {
          for (const match of matches) {
            const cleaned = match.slice(1, -1).trim(); // Remove surrounding quotes
            if (cleaned.length > 20) { // Only keep substantial insights
              insights.push(cleaned);
            }
          }
        }
      }
    }

    // If we found insights, return them
    if (insights.length > 0) {
      console.log(`Extracted ${insights.length} insights from malformed response`);
      const limitedInsights = insights.slice(0, 7); // Limit to reasonable number
      // Cache the insights
      if (pollId) {
        insightsCache[pollId] = {
          insights: limitedInsights,
          timestamp: Date.now()
        };
      }
      return limitedInsights;
    }

    // Fall back to mock insights if extraction failed
    console.log('Could not extract insights from malformed response, using fallback');
    return generateMockInsights();

  } catch (error) {
    console.error('Error extracting insights from malformed response:', error);
    return generateMockInsights();
  }
}

// Mock insights as fallback
function generateMockInsights(): string[] {
  const mockInsights = [
    "78% of respondents rated their satisfaction as 'Very Satisfied' or 'Satisfied'",
    "The most valued aspect was 'Ease of use', selected by 64% of respondents",
    "92% of respondents would recommend this product to others",
    "Common improvement suggestions include better documentation and more customization options",
    "Sentiment analysis shows mostly positive feedback about the user experience"
  ];

  // Note: We don't cache mock insights as they're not specific to any poll
  return mockInsights;
}



// Generate poll simulation using LLM-as-Simulator approach
export async function simulatePoll(request: SimulationRequest): Promise<SimulationResponse> {
  console.log('Starting poll simulation with request:', request);

  try {
    // Validate the simulation request
    const validationErrors = validateSimulationRequest(
      request.pollQuestion,
      request.pollOptions,
      request.demographic.group,
      request.demographic.size
    );

    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Generate simulation ID
    const simulationId = generateSimulationId();

    // Prepare the prompt data
    const promptData = {
      question: request.pollQuestion,
      options: request.pollOptions,
      demographic: request.demographic.group,
      sampleSize: request.demographic.size,
      context: request.demographic.context,
      specialInstructions: request.specialInstructions,
      pollContext: request.pollContext
    };

    const prompt = buildSimulationPrompt(promptData);

    // Call the Perplexity API with simulation-specific system prompt
    const systemPrompt = `You are a polling research expert conducting a simulated poll. Based on academic research and demographic data, simulate realistic poll responses for specific population groups.

Your simulations must:
1. Be based on real demographic research and statistical patterns
2. Include realistic variation within the demographic group
3. Account for known biases and response patterns
4. Provide academic citations for your assumptions
5. Include confidence levels for your simulation

Always format responses as valid JSON that can be parsed programmatically.`;

    console.log("Making simulation API request to Perplexity...");

    // Use the existing callPerplexityAPI function but with simulation-specific parameters
    const apiKey = typeof window === 'undefined'
      ? process.env.PERPLEXITY_API_KEY
      : process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;

    // For client-side calls, use the API route
    if (typeof window !== 'undefined' && !apiKey) {
      console.log('Using API route for client-side simulation calls');
      const response = await fetch('/api/perplexity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          systemPrompt: systemPrompt,
          model: 'sonar', // Use current Sonar model for better simulation quality
          temperature: 0.3 // Lower temperature for more consistent results
        })
      });

      if (!response.ok) {
        throw new Error(`Simulation API route failed: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse = data.content;

      // Parse the simulation response
      const parsedSimulation = parseSimulationResponse(aiResponse) as Partial<SimulationResponse>;

      // Ensure the response has the correct simulationId
      parsedSimulation.simulationId = simulationId;

      // Try to extract citations and search results from the API route response
      // The API route should include these in the data object
      if (data.citations && Array.isArray(data.citations)) {
        if (parsedSimulation.metadata) {
          parsedSimulation.metadata.citations = data.citations;
        }
      }

      if (data.search_results && Array.isArray(data.search_results)) {
        if (parsedSimulation.metadata) {
          parsedSimulation.metadata.searchResults = data.search_results;
        }
      }

      // If neither citations nor search_results were found, keep existing citations from parsed content
      if ((!data.citations || data.citations.length === 0) &&
          (!data.search_results || data.search_results.length === 0) &&
          parsedSimulation.metadata?.citations &&
          parsedSimulation.metadata.citations.length === 0) {
        // Set a default fallback
        parsedSimulation.metadata.citations = ['Simulation based on AI analysis'];
      }

      // Validate the response has the required properties before returning
      if (!parsedSimulation.results || !parsedSimulation.metadata) {
        throw new Error('Invalid simulation response format');
      }

      return parsedSimulation as SimulationResponse;
    }

    // Server-side API call
    if (!apiKey) {
      throw new Error('Perplexity API key is not configured for simulation');
    }

    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'sonar', // Use current Sonar model for better simulation quality
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        max_tokens: 2048, // More tokens for detailed simulation
        temperature: 0.3 // Lower temperature for more consistent results
      })
    });

    if (!response.ok) {
      throw new Error(`Simulation API request failed: ${response.status} ${await response.text()}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    console.log("Simulation API response received");

    // Parse the simulation response
    const parsedSimulation = parseSimulationResponse(aiResponse) as Partial<SimulationResponse>;

    // Ensure the response has the correct simulationId
    parsedSimulation.simulationId = simulationId;

    // Extract citations and search results from Perplexity API response
    if (data.citations && Array.isArray(data.citations)) {
      // Use direct citations (URLs) if available
      if (parsedSimulation.metadata) {
        parsedSimulation.metadata.citations = data.citations;
      }
    }

    // Also extract search_results if available (more structured data)
    if (data.search_results && Array.isArray(data.search_results)) {
      if (parsedSimulation.metadata) {
        // Store search results in a special field for enhanced source display
        parsedSimulation.metadata.searchResults = data.search_results;
      }
    }

    // If neither citations nor search_results were found, ensure we have at least some citation info
    if ((!data.citations || data.citations.length === 0) &&
        (!data.search_results || data.search_results.length === 0) &&
        parsedSimulation.metadata?.citations &&
        parsedSimulation.metadata.citations.length === 0) {
      // Set a default fallback
      parsedSimulation.metadata.citations = ['Simulation based on AI research and demographic data'];
    }

    // Validate the response has the required properties
    if (!parsedSimulation.results || !parsedSimulation.metadata) {
      throw new Error('Invalid simulation response format');
    }

    return parsedSimulation as SimulationResponse;

  } catch (error) {
    console.error('Error during poll simulation:', error);

    // Return a fallback simulation response
    return generateFallbackSimulation(request);
  }
}

// Fallback simulation for when API fails
function generateFallbackSimulation(request: SimulationRequest): SimulationResponse {
  const simulationId = generateSimulationId();
  const totalOptions = request.pollOptions.length;

  // Generate simple distribution (roughly equal with some variation)
  const distribution: Record<string, number> = {};
  let remaining = 100;

  request.pollOptions.forEach((option, index) => {
    if (index === totalOptions - 1) {
      distribution[option] = remaining;
    } else {
      const percentage = Math.floor(Math.random() * 30) + 15; // 15-45% range
      distribution[option] = Math.min(percentage, remaining - (totalOptions - index - 1) * 5);
      remaining -= distribution[option];
    }
  });

  // Generate a few sample individual responses
  const individuals: SimulatedResponse[] = [];
  for (let i = 0; i < Math.min(5, request.demographic.size); i++) {
    const randomOption = request.pollOptions[Math.floor(Math.random() * totalOptions)];
    individuals.push({
      responseId: `fallback_resp_${i + 1}`,
      selectedOptions: [randomOption],
      reasoning: `Sample response from ${request.demographic.group} demographic`,
      demographicContext: `Member of ${request.demographic.group} group`
    });
  }

  return {
    simulationId,
    metadata: {
      demographic: request.demographic.group,
      sampleSize: request.demographic.size,
      confidence: 0.5, // Low confidence for fallback
      citations: ['Fallback simulation - no academic sources available']
    },
    results: {
      distribution,
      individuals,
      analysis: `Fallback simulation generated due to API unavailability. This is a basic simulation for ${request.demographic.group} with ${request.demographic.size} respondents.`
    }
  };
}
