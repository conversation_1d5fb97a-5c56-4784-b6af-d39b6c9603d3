// Types for local storage
// Support for both static keys and dynamic keys with specific patterns
export type StorageKey =
  // Static predefined keys
  | 'polls'
  | 'user'
  | 'responses'
  | 'currentPollData'       // For data related to the poll being currently viewed or edited
  | 'pollgpt_conversation'  // For storing conversation state, e.g., with AI for poll creation
  | 'pollgpt_draft'         // For unsaved poll drafts
  | 'pollgpt_step'          // For multi-step processes, like conversational poll creation
  | 'theme'                 // For user's theme preferences (e.g., dark/light mode, color)
  | 'userPreferences'       // General user-specific preferences not covered by 'theme'
  | 'simulationState'       // For any simulation or testing states persisted locally
  | 'pollgpt_polls_cache'   // Specific key used for caching polls list, ensuring consistency
  | 'pollgpt_retry_after_refresh' // Flag or timestamp for retry logic after token refresh
  // Dynamic keys - using string pattern matching
  | `pollgpt_poll_${string}`; // For poll-specific data with poll ID

// Type guard to check if something is an object
const isObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
};

/**
 * Gets an item from local storage and parses it as JSON
 * @param key The key to get from local storage
 * @returns The parsed value or null if not found
 */
export const getItem = <T>(key: StorageKey): T | null => {
  if (typeof window === 'undefined') {
    return null;
  }
  
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) as T : null;
  } catch (error) {
    console.error(`Error getting ${key} from local storage:`, error);
    return null;
  }
};

/**
 * Sets an item in local storage after stringifying it
 * @param key The key to set in local storage
 * @param value The value to set
 */
export const setItem = <T>(key: StorageKey, value: T): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting ${key} in local storage:`, error);
  }
};

/**
 * Removes an item from local storage
 * @param key The key to remove from local storage
 */
export const removeItem = (key: StorageKey): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing ${key} from local storage:`, error);
  }
};

/**
 * Updates an object in local storage by merging it with the existing value
 * @param key The key to update in local storage
 * @param value The value to merge with the existing value
 */
export const updateItem = <T>(key: StorageKey, value: Partial<T>): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    const existingValue = getItem<T>(key);
    
    if (existingValue && isObject(existingValue) && isObject(value)) {
      setItem(key, { ...existingValue, ...value });
    } else {
      setItem(key, value as T);
    }
  } catch (error) {
    console.error(`Error updating ${key} in local storage:`, error);
  }
};

/**
 * Updates an array item in local storage 
 * @param key The key to update in local storage
 * @param id The id of the item to update
 * @param idField The field in the object that contains the id
 * @param updateData The data to update
 */
export const updateArrayItem = <T extends Record<string, unknown>>(
  key: StorageKey,
  id: string, 
  idField: keyof T,
  updateData: Partial<T>
): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    const items = getItem<T[]>(key) || [];
    
    const updatedItems = items.map(item => 
      item[idField] === id ? { ...item, ...updateData } : item
    );
    
    setItem(key, updatedItems);
  } catch (error) {
    console.error(`Error updating item in ${key} array:`, error);
  }
};

/**
 * Adds an item to an array in local storage
 * @param key The key to update in local storage
 * @param item The item to add to the array
 */
export const addArrayItem = <T>(key: StorageKey, item: T): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    const items = getItem<T[]>(key) || [];
    setItem(key, [...items, item]);
  } catch (error) {
    console.error(`Error adding item to ${key} array:`, error);
  }
};

/**
 * Removes an item from an array in local storage
 * @param key The key to update in local storage
 * @param id The id of the item to remove
 * @param idField The field in the object that contains the id
 */
export const removeArrayItem = <T extends Record<string, unknown>>(
  key: StorageKey,
  id: string,
  idField: keyof T
): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    const items = getItem<T[]>(key) || [];
    const updatedItems = items.filter(item => item[idField] !== id);
    setItem(key, updatedItems);
  } catch (error) {
    console.error(`Error removing item from ${key} array:`, error);
  }
};

/**
 * Clears all local storage
 */
export const clearAll = (): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    localStorage.clear();
  } catch (error) {
    console.error('Error clearing local storage:', error);
  }
};
