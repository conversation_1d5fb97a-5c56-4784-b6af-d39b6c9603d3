'use server';

import { parsePdf } from './pdf-parser';

/**
 * Extract text from PDF documents
 * @param fileBuffer Binary data of the PDF file
 * @returns Extracted text content
 */
export async function extractPdfWithMistral(fileBuffer: ArrayBuffer): Promise<string> {
  try {
    console.log(`Processing PDF file, size: ${fileBuffer.byteLength} bytes`);

    // Convert ArrayBuffer to Buffer for pdf-parse
    const buffer = Buffer.from(fileBuffer);

    // Use our custom PDF parser to extract text content
    const pdfData = await parsePdf(buffer);

    // Check if we got text content
    if (pdfData && pdfData.text) {
      const cleanedText = pdfData.text
        .replace(/\s+/g, ' ')
        .trim();

      if (cleanedText.length > 0) {
        console.log('Successfully extracted PDF content');
        return cleanedText;
      }
    }

    return 'No text content could be extracted from the document';
  } catch (error) {
    console.error('Error extracting PDF content:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`PDF extraction failed: ${errorMessage}`);
  }
}