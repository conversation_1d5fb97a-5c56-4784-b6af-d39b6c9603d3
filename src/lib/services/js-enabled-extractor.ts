'use server';

import puppeteer from 'puppeteer';

/**
 * Extract content from a JavaScript-rendered website
 * Uses Puppeteer headless browser to execute JavaScript
 */
export async function extractJsEnabledContent(url: string): Promise<string> {
  let browser;

  try {
    // Special handling for known problematic domains
    if (url.includes('airtable.com')) {
      return await extractAirtableContent(url);
    }

    // Launch a headless browser with more robust options
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage', // Overcome limited resource problems
        '--disable-gpu',
        '--disable-features=IsolateOrigins,site-per-process',
        '--disable-web-security' // Help with some CORS issues
      ]
    });

    // Open a new page
    const page = await browser.newPage();

    // Set a more realistic user agent
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

    // Set viewport
    await page.setViewport({ width: 1280, height: 800 });

    // Set shorter timeout to avoid serverless function timeouts
    const timeoutMs = 15000; // 15 seconds max

    // Navigate to the URL with shorter timeout
    await page.goto(url, {
      waitUntil: 'domcontentloaded', // Less strict than networkidle2
      timeout: timeoutMs
    });

    // Try to wait for content to load, but don't fail if selector doesn't appear quickly
    try {
      await page.waitForSelector('body', { timeout: 5000 });
    } catch (error) {
      console.warn('Timeout waiting for body selector, continuing anyway', error.message);
    }

    // Extract the text content with a timeout
    const content = await Promise.race([
      page.evaluate(() => {
        // Remove unnecessary elements that might contain irrelevant content
        const elementsToRemove = document.querySelectorAll('nav, footer, header, aside, [role="banner"], [role="navigation"]');
        elementsToRemove.forEach((el) => {
          if (el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });

        // Get the main content
        const mainContent = document.querySelector('main') ||
                            document.querySelector('article') ||
                            document.querySelector('.main-content') ||
                            document.body;

        // Return the text content
        return mainContent.innerText || document.body.innerText;
      }),
      new Promise<string>((_, reject) =>
        setTimeout(() => reject(new Error('Content extraction timed out')), timeoutMs)
      )
    ]);

    return content || 'No content could be extracted';
  } catch (error: unknown) {
    console.error('Error extracting JS-enabled content:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new Error(`Failed to extract content from JS-enabled site: ${errorMessage}`);
  } finally {
    // Always close the browser
    if (browser) {
      await browser.close().catch(err => console.error('Error closing browser:', err));
    }
  }
}

/**
 * Special handler for Airtable URLs
 * Airtable has specific structure and often requires authentication
 */
async function extractAirtableContent(url: string): Promise<string> {
  try {
    // For Airtable URLs, we use a different approach
    // Extract the base ID and table ID from the URL if possible
    const match = url.match(/airtable\.com\/(app|appapi)\/([\w\d]+)\/([^\/\?]+)/);

    if (!match) {
      return `This appears to be an Airtable URL. Please note that Airtable typically requires authentication and may restrict content extraction. Consider sharing the Airtable content through a public view link or manually copying the content you wish to use for your poll.`;
    }

    const baseId = match[2];
    const tableId = match[3];

    return `This is an Airtable base with ID: ${baseId} and table: ${tableId}. Airtable restricts automated content extraction due to authentication requirements. Please consider one of these alternatives:

    1. Use the "Share" button in Airtable to create a public view link
    2. Copy and paste the relevant content directly into the poll description
    3. Export your Airtable data to a format like CSV and extract content from that file instead

    If you're trying to create a poll based on Airtable data, you might want to specifically mention the questions or topics from your Airtable base in the poll title or description.`;
  } catch (error) {
    console.error('Error handling Airtable URL:', error);
    return `Unable to extract content from Airtable due to access restrictions. Please consider manually copying the content you need for your poll.`;
  }
}

/**
 * Check if a URL is likely to be a JavaScript-driven SPA
 * This is a basic check and might need refinement
 */
export async function isSpaWebsite(url: string): Promise<boolean> {
  try {
    // Some sites are known to be SPAs or require special handling
    const knownSpaDomains = [
      'airtable.com',
      'notion.so',
      'react',
      'angular',
      'vue',
      'ezzyfeedback.com'
    ];

    // Check if the URL contains any known SPA domains
    if (knownSpaDomains.some(domain => url.includes(domain))) {
      return true;
    }

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
    });

    if (!response.ok) {
      // If we can't access the site normally, we should try with Puppeteer
      return true;
    }

    const html = await response.text();

    // Check for common SPA indicators
    const spaIndicators = [
      'react', 'vue', 'angular', 'backbone', 'ember',
      'You need to enable JavaScript',
      'This page requires JavaScript',
      'application/json',
      '<div id="root">', '<div id="app">',
      'ReactDOM', 'ng-app', 'v-app',
      'javascript:void'
    ];

    return spaIndicators.some(indicator => html.toLowerCase().includes(indicator.toLowerCase()));
  } catch (error) {
    console.error('Error checking if website is SPA:', error);
    // Default to treating as SPA if we can't check properly
    return true;
  }
}