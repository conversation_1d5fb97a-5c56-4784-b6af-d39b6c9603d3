'use server';

/**
 * Alternative content extractor for production environments
 * This implementation avoids using Puppeteer which often causes issues in serverless environments
 */

// Import required libraries
import * as cheerio from 'cheerio';

/**
 * Extract content from a URL without using Puppeteer
 * This function is designed to work in serverless environments
 *
 * @param url URL to extract content from
 * @returns Extracted text content
 */
export async function extractContentFromUrlServerless(url: string): Promise<string> {
  try {
    // Basic validation
    if (!url || !url.startsWith('http')) {
      throw new Error('Invalid URL');
    }

    console.log(`Extracting content from URL: ${url}`);

    // Use standard fetch with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Cache-Control': 'no-cache',
      },
      signal: controller.signal
    }).finally(() => clearTimeout(timeoutId));

    if (!response.ok) {
      throw new Error(`Failed to fetch URL: ${response.status} ${response.statusText}`);
    }

    // Get content type from response headers
    const contentType = response.headers.get('content-type') || '';

    // Handle different content types
    if (contentType.includes('application/json')) {
      const jsonData = await response.json();
      return `JSON Content: ${JSON.stringify(jsonData, null, 2).substring(0, 5000)}`;
    } else {
      // For HTML and text content
      const html = await response.text();
      return extractStructuredContent(html, url);
    }
  } catch (error: unknown) {
    console.error('Error extracting content from URL:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    if (errorMessage.includes('abort')) {
      return `The request to ${url} timed out. This might be because the site is slow or blocking our request. Please try a different site or copy the content manually.`;
    }

    return `Failed to extract content from URL (${url}): ${errorMessage}. Please try a different URL or copy the content directly.`;
  }
}

/**
 * Extract structured content from HTML
 * Uses cheerio for more sophisticated parsing than basic regex
 */
function extractStructuredContent(html: string, sourceUrl: string): string {
  try {
    // Load HTML into cheerio
    const $ = cheerio.load(html);

    // Remove script, style, and other non-content elements
    $('script, style, noscript, iframe, svg').remove();

    // Get the title
    const title = $('title').text().trim() || 'Untitled Page';

    // Try to get main content
    let mainContent = '';

    // Look for main content containers
    const contentSelectors = [
      'main', 'article', '[role="main"]', '#content', '.content',
      '#main', '.main', '.post-content', '.article-content',
      '.entry-content', '.page-content', '.body-content'
    ];

    // Try each selector until we find content
    for (const selector of contentSelectors) {
      const content = $(selector).text().trim();
      if (content && content.length > 200) {
        mainContent = content;
        break;
      }
    }

    // If no main content container found, use body
    if (!mainContent) {
      mainContent = $('body').text();
    }

    // Clean up the text
    mainContent = mainContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();

    // Extract meta description
    const metaDescription = $('meta[name="description"]').attr('content') || '';

    // Extract headings
    const headings: string[] = [];
    $('h1, h2, h3').each((_, element) => {
      const headingText = $(element).text().trim();
      if (headingText) {
        headings.push(headingText);
      }
    });

    // Format the output
    let result = `Title: ${title}\n\n`;

    if (metaDescription) {
      result += `Description: ${metaDescription}\n\n`;
    }

    if (headings.length > 0) {
      result += `Key Headings:\n${headings.slice(0, 10).join('\n')}\n\n`;
    }

    result += `Content:\n${mainContent.substring(0, 7500)}`;

    if (mainContent.length > 7500) {
      result += `\n\n[Content trimmed due to length. Extracted from: ${sourceUrl}]`;
    }

    return result;
  } catch (error) {
    console.error('Error parsing HTML content:', error);
    return `Error parsing content: ${error instanceof Error ? error.message : String(error)}. Raw text extraction used instead.`;
  }
}
