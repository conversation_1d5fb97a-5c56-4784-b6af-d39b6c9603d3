'use server';

/**
 * Server-side content extraction service
 */

// Import enhanced document extraction service
import { extractDocumentContent } from './mistral-document-service';
import { extractJsEnabledContent, isSpaWebsite } from './js-enabled-extractor';
import { extractContentFromUrlServerless } from './alternative-extractor';

// Type definitions
export type ExtractionSource = 'url' | 'file' | 'website' | 'text';

/**
 * Extract content from a URL
 *
 * This function attempts to use a production-safe extraction method first,
 * and falls back to Puppeteer only in development or when specifically allowed
 */
export async function extractContentFromUrl(url: string): Promise<string> {
  try {
    // Basic validation
    if (!url || !url.startsWith('http')) {
      throw new Error('Invalid URL');
    }

    // Check for production environment (Vercel, Netlify, etc.)
    const isProduction = process.env.NODE_ENV === 'production' ||
                         process.env.VERCEL ||
                         process.env.NETLIFY;

    // Use the serverless extractor in production
    if (isProduction) {
      console.log(`Using serverless extractor for URL: ${url} in production environment`);
      return await extractContentFromUrlServerless(url);
    }

    // In development, we can use the Puppeteer-based extraction
    // Check if the website is a JavaScript-heavy SPA
    const isSpa = await isSpaWebsite(url);

    if (isSpa) {
      console.log(`Detected JavaScript-heavy website: ${url}, using Puppeteer extraction`);
      try {
        return await extractJsEnabledContent(url);
      } catch (puppeteerError) {
        console.error('Puppeteer extraction failed, falling back to serverless extractor:', puppeteerError);
        return await extractContentFromUrlServerless(url);
      }
    }

    // For non-SPA sites, use the standard fetch approach
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; PollGPT/1.0; +http://pollgpt.com)',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch URL: ${response.status}`);
    }

    // Extract the text content
    const html = await response.text();

    // Very basic HTML parsing - in production you would use a proper HTML parser
    // like cheerio or jsdom to extract meaningful content
    const textContent = extractTextFromHtml(html);

    return textContent;
  } catch (error: unknown) {
    console.error('Error extracting content from URL:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    // Always fall back to the serverless implementation if the main approach fails
    try {
      console.log('Falling back to serverless extractor after error');
      return await extractContentFromUrlServerless(url);
    } catch (fallbackError) {
      console.error('Fallback extraction also failed:', fallbackError);
      throw new Error(`Failed to extract content from URL: ${errorMessage}`);
    }
  }
}

/**
 * Extract content from a website (multiple pages)
 */
export async function extractContentFromWebsite(url: string, maxPages = 5): Promise<string> {
  try {
    // Use extraction with additional error handling
    let content;
    try {
      content = await extractContentFromUrl(url);
    } catch (extractError) {
      console.error('Primary extraction failed, trying serverless fallback:', extractError);
      content = await extractContentFromUrlServerless(url);
    }

    // Use the maxPages parameter in the returned message to avoid the unused variable warning
    return `Website extraction (limited to homepage, max pages configured: ${maxPages}): ${content}`;
  } catch (error: unknown) {
    console.error('Error extracting content from website:', error);

    // Return a user-friendly error message instead of throwing
    return `We couldn't extract content from this website (${url}). This might be because the site blocks automated access or requires JavaScript. Please try copying the content manually or using a different URL.`;
  }
}

/**
 * Extract content from an uploaded file
 */
export async function extractContentFromFile(file: File): Promise<string> {
  try {
    const fileType = file.type || file.name.split('.').pop()?.toLowerCase();
    console.log(`Extracting content from file: ${file.name}, type: ${fileType}, size: ${file.size} bytes`);

    // File size validation
    if (file.size > 25 * 1024 * 1024) { // 25MB limit
      throw new Error('File is too large. Maximum file size is 25MB.');
    }

    if (file.size === 0) {
      throw new Error('File appears to be empty.');
    }

    // For text files - handle directly without AI processing
    if (fileType === 'text/plain' || fileType === 'txt') {
      console.log('Processing text file directly without AI');
      const content = await file.text();
      if (!content || content.trim() === '') {
        throw new Error('Text file is empty.');
      }
      return content;
    }

    // For all document types that need extraction (PDFs, DOCXs, images)
    const supportedTypes = [
      'application/pdf', 'pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'docx',
      'image/jpeg', 'image/png', 'image/gif', 'image/webp'
    ];

    const isSupported = fileType?.startsWith('image/') || supportedTypes.includes(fileType || '');

    if (isSupported) {
      console.log(`Using Mistral document extraction for ${file.name}`);
      const arrayBuffer = await file.arrayBuffer();

      // Add more detailed logging
      console.log(`Successfully converted ${file.name} to ArrayBuffer, size: ${arrayBuffer.byteLength} bytes`);

      const mimeType = file.type || (fileType === 'pdf' ? 'application/pdf' : 'application/octet-stream');
      console.log(`Detected MIME type: ${mimeType}`);

      try {
        // Try to use Mistral document extraction service
        return await extractDocumentContent(arrayBuffer, mimeType, file.name);
      } catch (mistralError) {
        console.error('Mistral extraction error:', mistralError);

        // For any API error with PDFs, try our fallback extractor
        if (fileType?.includes('pdf')) {
          console.log('PDF extraction failed with Mistral API, attempting fallback extraction');
          // Try the fallback PDF extractor immediately
          try {
            const { extractTextFromPdf } = await import('./pdf-text-extractor');
            const pdfText = await extractTextFromPdf(arrayBuffer);

            if (pdfText && pdfText.length > 50) {
              console.log(`Extracted ${pdfText.length} characters from PDF using fallback method`);
              return `Extracted from: ${file.name} (basic extraction)\n\n${pdfText}`;
            } else {
              console.log('PDF fallback extraction returned insufficient text');
            }
          } catch (pdfError) {
            console.error('PDF fallback extraction failed:', pdfError);
          }

          // If we got here, the PDF fallback failed or returned too little text
          return `The file "${file.name}" could not be automatically processed. Please manually enter the key information from your document.`;
        }

        // Handle other file types or capability errors
        if (mistralError.message?.includes('vision') ||
            mistralError.message?.includes('multimodal') ||
            mistralError.message?.includes('capability')) {

          console.log('Vision capability not available, checking for text-based files');

          // For text-based files we can try direct extraction
          if (fileType === 'text/plain' || fileType === 'txt' || fileType?.includes('text')) {
            return await file.text();
          }

          // For other file types
          return `The file "${file.name}" could not be processed automatically. Document extraction requires additional capabilities.

Please manually enter the key information from your document to create your poll.`;
        }

        // Re-throw other types of errors
        throw mistralError;
      }
    }

    throw new Error(`Unsupported file type: ${fileType}. Please upload a PDF, DOCX, or common image format.`);
  } catch (error: unknown) {
    console.error('Error extracting content from file:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new Error(`Failed to extract content from file: ${errorMessage}`);
  }
}

/**
 * Helper: Extract text from HTML
 */
function extractTextFromHtml(html: string): string {
  // Remove script and style tags
  let text = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ' ');
  text = text.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, ' ');

  // Remove HTML tags
  text = text.replace(/<[^>]+>/g, ' ');

  // Decode HTML entities
  text = text.replace(/&nbsp;/g, ' ');
  text = text.replace(/&amp;/g, '&');
  text = text.replace(/&lt;/g, '<');
  text = text.replace(/&gt;/g, '>');

  // Normalize whitespace
  text = text.replace(/\s+/g, ' ').trim();

  return text;
}

// Document extraction is now handled by the mistral-document-service