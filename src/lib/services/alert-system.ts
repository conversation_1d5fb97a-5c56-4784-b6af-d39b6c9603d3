/**
 * Production-Ready Alert and Notification System
 *
 * Features:
 * - Multiple notification channels (email, webhook, SMS)
 * - Alert escalation and throttling
 * - Rich alert templates
 * - Integration with performance monitoring
 * - Notification delivery tracking
 */

import { redisCacheService as redisCache } from './redis-cache';
import { performanceMonitor } from './performance-monitor';

export interface AlertChannel {
  id: string;
  name: string;
  type: 'email' | 'webhook' | 'sms' | 'slack' | 'discord';
  config: AlertChannelConfig;
  enabled: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface AlertChannelConfig {
  // Email configuration
  email?: {
    to: string[];
    cc?: string[];
    bcc?: string[];
    smtpConfig?: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
  };

  // Webhook configuration
  webhook?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
    timeout: number;
    retries: number;
  };

  // SMS configuration
  sms?: {
    provider: 'twilio' | 'aws-sns';
    phoneNumbers: string[];
    config: Record<string, unknown>;
  };

  // Slack configuration
  slack?: {
    webhookUrl: string;
    channel?: string;
    username?: string;
    iconEmoji?: string;
  };

  // Discord configuration
  discord?: {
    webhookUrl: string;
    username?: string;
    avatarUrl?: string;
  };
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'above' | 'below' | 'equal' | 'not_equal';
  threshold: number;
  duration: number; // seconds - how long condition must persist
  severity: 'info' | 'warning' | 'error' | 'critical';
  channels: string[]; // Channel IDs to notify
  enabled: boolean;
  throttle: number; // seconds - minimum time between alerts
  escalation?: {
    enabled: boolean;
    steps: EscalationStep[];
  };
  template?: string;
  tags?: Record<string, string>;
}

export interface EscalationStep {
  delay: number; // seconds to wait before escalating
  channels: string[]; // Additional channels to notify
  message?: string; // Custom escalation message
}

export interface AlertNotification {
  id: string;
  ruleId: string;
  ruleName: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  resolved?: Date;
  channels: string[];
  deliveryStatus: Record<string, AlertDeliveryStatus>;
  tags?: Record<string, string>;
}

export interface AlertDeliveryStatus {
  channelId: string;
  status: 'pending' | 'sent' | 'failed' | 'retrying';
  attempts: number;
  lastAttempt?: Date;
  error?: string;
}

export interface AlertStats {
  totalAlerts: number;
  alertsByLevel: Record<string, number>;
  alertsByChannel: Record<string, number>;
  deliveryStats: {
    success: number;
    failed: number;
    pending: number;
  };
  responseTime: {
    average: number;
    p95: number;
    p99: number;
  };
}

export class AlertSystem {
  private channels: Map<string, AlertChannel> = new Map();
  private rules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, AlertNotification> = new Map();
  private lastAlertTimes: Map<string, Date> = new Map();

  private readonly config = {
    maxRetries: 3,
    retryDelay: 30000, // 30 seconds
    alertRetentionDays: 30,
    maxActiveAlerts: 1000,
  };

  constructor() {
    this.initializeDefaultChannels();
    this.initializeDefaultRules();
    this.startAlertProcessor();
  }

  /**
   * Initialize default notification channels
   */
  private initializeDefaultChannels(): void {
    // Email channel for critical alerts
    this.addChannel({
      id: 'email-critical',
      name: 'Critical Email Alerts',
      type: 'email',
      enabled: true,
      priority: 'critical',
      config: {
        email: {
          to: process.env.ALERT_EMAIL_RECIPIENTS?.split(',') || ['<EMAIL>'],
          smtpConfig: {
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: false,
            auth: {
              user: process.env.SMTP_USER || '',
              pass: process.env.SMTP_PASS || '',
            },
          },
        },
      },
    });

    // Webhook channel for integrations
    this.addChannel({
      id: 'webhook-monitoring',
      name: 'Monitoring Webhook',
      type: 'webhook',
      enabled: true,
      priority: 'high',
      config: {
        webhook: {
          url: process.env.ALERT_WEBHOOK_URL || '',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ALERT_WEBHOOK_TOKEN || ''}`,
          },
          timeout: 10000,
          retries: 3,
        },
      },
    });

    // Slack channel
    if (process.env.SLACK_WEBHOOK_URL) {
      this.addChannel({
        id: 'slack-alerts',
        name: 'Slack Notifications',
        type: 'slack',
        enabled: true,
        priority: 'medium',
        config: {
          slack: {
            webhookUrl: process.env.SLACK_WEBHOOK_URL,
            channel: '#alerts',
            username: 'PollGPT Alerts',
            iconEmoji: ':warning:',
          },
        },
      });
    }
  }

  /**
   * Initialize default alert rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: Partial<AlertRule>[] = [
      {
        name: 'High Memory Usage',
        description: 'Memory usage above 80%',
        metric: 'memory.usage_percent',
        condition: 'above',
        threshold: 80,
        duration: 300, // 5 minutes
        severity: 'warning',
        channels: ['email-critical', 'slack-alerts'],
        throttle: 1800, // 30 minutes
      },
      {
        name: 'Critical Memory Usage',
        description: 'Memory usage above 95%',
        metric: 'memory.usage_percent',
        condition: 'above',
        threshold: 95,
        duration: 60, // 1 minute
        severity: 'critical',
        channels: ['email-critical', 'webhook-monitoring'],
        throttle: 300, // 5 minutes
        escalation: {
          enabled: true,
          steps: [
            {
              delay: 600, // 10 minutes
              channels: ['sms-emergency'],
              message: 'Critical memory usage persisting - immediate attention required',
            },
          ],
        },
      },
      {
        name: 'Database Connection Issues',
        description: 'Database connection pool exhausted',
        metric: 'db.connections.active',
        condition: 'above',
        threshold: 8, // Close to max pool size
        duration: 120,
        severity: 'error',
        channels: ['email-critical', 'webhook-monitoring'],
        throttle: 600,
      },
      {
        name: 'High API Error Rate',
        description: 'API error rate above 5%',
        metric: 'api.error_rate',
        condition: 'above',
        threshold: 5,
        duration: 300,
        severity: 'warning',
        channels: ['slack-alerts'],
        throttle: 900,
      },
      {
        name: 'Cache Hit Rate Low',
        description: 'Cache hit rate below 70%',
        metric: 'cache.hit_ratio',
        condition: 'below',
        threshold: 70,
        duration: 600,
        severity: 'info',
        channels: ['slack-alerts'],
        throttle: 3600,
      },
      {
        name: 'Job Queue Backlog',
        description: 'Too many pending jobs in queue',
        metric: 'jobs.pending',
        condition: 'above',
        threshold: 100,
        duration: 300,
        severity: 'warning',
        channels: ['email-critical'],
        throttle: 1800,
      },
    ];

    defaultRules.forEach((rule, index) => {
      this.addRule({
        id: `default-rule-${index}`,
        enabled: true,
        template: 'default',
        ...rule,
      } as AlertRule);
    });
  }

  /**
   * Add a notification channel
   */
  addChannel(channel: AlertChannel): void {
    this.channels.set(channel.id, channel);
    console.log(`[AlertSystem] Added channel: ${channel.name}`);
  }

  /**
   * Add an alert rule
   */
  addRule(rule: AlertRule): void {
    this.rules.set(rule.id, rule);
    console.log(`[AlertSystem] Added rule: ${rule.name}`);
  }

  /**
   * Process a metric value and check for alerts
   */
  async processMetric(metric: string, value: number, tags?: Record<string, string>): Promise<void> {
    for (const rule of this.rules.values()) {
      if (!rule.enabled || rule.metric !== metric) {
        continue;
      }

      const shouldAlert = this.evaluateCondition(rule, value);
      const alertKey = this.getAlertKey(rule, tags);

      if (shouldAlert) {
        await this.handleAlert(rule, value, tags, alertKey);
      } else {
        await this.handleResolution(rule, alertKey);
      }
    }
  }

  /**
   * Evaluate if a condition should trigger an alert
   */
  private evaluateCondition(rule: AlertRule, value: number): boolean {
    switch (rule.condition) {
      case 'above':
        return value > rule.threshold;
      case 'below':
        return value < rule.threshold;
      case 'equal':
        return value === rule.threshold;
      case 'not_equal':
        return value !== rule.threshold;
      default:
        return false;
    }
  }

  /**
   * Generate unique alert key
   */
  private getAlertKey(rule: AlertRule, tags?: Record<string, string>): string {
    const tagString = tags ? JSON.stringify(tags) : '';
    return `${rule.id}:${tagString}`;
  }

  /**
   * Handle alert triggering
   */
  private async handleAlert(
    rule: AlertRule,
    value: number,
    tags: Record<string, string> | undefined,
    alertKey: string
  ): Promise<void> {
    const now = new Date();
    const lastAlert = this.lastAlertTimes.get(alertKey);

    // Check throttling
    if (lastAlert && (now.getTime() - lastAlert.getTime()) < rule.throttle * 1000) {
      return;
    }

    // Check if alert already exists and is active
    if (this.activeAlerts.has(alertKey)) {
      return;
    }

    const notification: AlertNotification = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ruleId: rule.id,
      ruleName: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      severity: rule.severity,
      message: this.generateAlertMessage(rule, value, tags),
      timestamp: now,
      channels: rule.channels,
      deliveryStatus: {},
      tags,
    };

    this.activeAlerts.set(alertKey, notification);
    this.lastAlertTimes.set(alertKey, now);

    // Send notifications
    await this.sendNotifications(notification);

    // Store in Redis for persistence
    await redisCache.set(`alert:${notification.id}`, notification, 86400 * this.config.alertRetentionDays);

    console.log(`[AlertSystem] Alert triggered: ${rule.name} (${value} ${rule.condition} ${rule.threshold})`);
  }

  /**
   * Handle alert resolution
   */
  private async handleResolution(rule: AlertRule, alertKey: string): Promise<void> {
    const activeAlert = this.activeAlerts.get(alertKey);
    if (!activeAlert) {
      return;
    }

    activeAlert.resolved = new Date();
    this.activeAlerts.delete(alertKey);

    // Send resolution notification
    const resolutionMessage = this.generateResolutionMessage(rule, activeAlert);
    await this.sendResolutionNotifications(activeAlert, resolutionMessage);

    console.log(`[AlertSystem] Alert resolved: ${rule.name}`);
  }

  /**
   * Send notifications to all configured channels
   */
  private async sendNotifications(notification: AlertNotification): Promise<void> {
    for (const channelId of notification.channels) {
      const channel = this.channels.get(channelId);
      if (!channel || !channel.enabled) {
        continue;
      }

      notification.deliveryStatus[channelId] = {
        channelId,
        status: 'pending',
        attempts: 0,
      };

      try {
        await this.sendToChannel(channel, notification);
        notification.deliveryStatus[channelId] = {
          ...notification.deliveryStatus[channelId],
          status: 'sent',
          attempts: 1,
          lastAttempt: new Date(),
        };
      } catch (error) {
        notification.deliveryStatus[channelId] = {
          ...notification.deliveryStatus[channelId],
          status: 'failed',
          attempts: 1,
          lastAttempt: new Date(),
          error: error instanceof Error ? error.message : 'Unknown error',
        };

        console.error(`[AlertSystem] Failed to send to channel ${channelId}:`, error);
      }
    }
  }

  /**
   * Send notification to a specific channel
   */
  private async sendToChannel(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    switch (channel.type) {
      case 'email':
        await this.sendEmailAlert(channel, notification);
        break;
      case 'webhook':
        await this.sendWebhookAlert(channel, notification);
        break;
      case 'slack':
        await this.sendSlackAlert(channel, notification);
        break;
      case 'discord':
        await this.sendDiscordAlert(channel, notification);
        break;
      case 'sms':
        await this.sendSmsAlert(channel, notification);
        break;
      default:
        throw new Error(`Unsupported channel type: ${channel.type}`);
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    // Placeholder for email implementation
    // In production, use nodemailer or similar
    console.log(`[AlertSystem] Sending email alert to ${channel.config.email?.to?.join(', ')}`);
    console.log(`Subject: [${notification.severity.toUpperCase()}] ${notification.ruleName}`);
    console.log(`Message: ${notification.message}`);
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    const webhookConfig = channel.config.webhook;
    if (!webhookConfig) {
      throw new Error('Webhook configuration missing');
    }

    const payload = {
      alert: {
        id: notification.id,
        rule: notification.ruleName,
        metric: notification.metric,
        value: notification.value,
        threshold: notification.threshold,
        severity: notification.severity,
        message: notification.message,
        timestamp: notification.timestamp.toISOString(),
        tags: notification.tags,
      },
      source: 'pollgpt-alert-system',
    };

    // Placeholder for webhook implementation
    console.log(`[AlertSystem] Sending webhook alert to ${webhookConfig.url}`);
    console.log('Payload:', JSON.stringify(payload, null, 2));
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    const slackConfig = channel.config.slack;
    if (!slackConfig) {
      throw new Error('Slack configuration missing');
    }

    const color = this.getSeverityColor(notification.severity);
    const payload = {
      channel: slackConfig.channel,
      username: slackConfig.username,
      icon_emoji: slackConfig.iconEmoji,
      attachments: [
        {
          color,
          title: `🚨 ${notification.ruleName}`,
          text: notification.message,
          fields: [
            {
              title: 'Metric',
              value: notification.metric,
              short: true,
            },
            {
              title: 'Value',
              value: notification.value.toString(),
              short: true,
            },
            {
              title: 'Threshold',
              value: notification.threshold.toString(),
              short: true,
            },
            {
              title: 'Severity',
              value: notification.severity.toUpperCase(),
              short: true,
            },
          ],
          timestamp: Math.floor(notification.timestamp.getTime() / 1000),
        },
      ],
    };

    // Placeholder for Slack implementation
    console.log(`[AlertSystem] Sending Slack alert to ${slackConfig.channel}`);
    console.log('Payload:', JSON.stringify(payload, null, 2));
  }

  /**
   * Send Discord alert
   */
  private async sendDiscordAlert(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    // Placeholder for Discord implementation
    console.log(`[AlertSystem] Sending Discord alert to ${channel.name} for notification ${notification.id}`);
  }

  /**
   * Send SMS alert
   */
  private async sendSmsAlert(channel: AlertChannel, notification: AlertNotification): Promise<void> {
    // Placeholder for SMS implementation
    console.log(`[AlertSystem] Sending SMS alert to ${channel.name} for notification ${notification.id}`);
  }

  /**
   * Send resolution notifications
   */
  private async sendResolutionNotifications(
    notification: AlertNotification,
    message: string
  ): Promise<void> {
    // Send resolution notifications to the same channels
    const resolutionNotification = {
      ...notification,
      message,
      severity: 'info' as const,
    };

    for (const channelId of notification.channels) {
      const channel = this.channels.get(channelId);
      if (channel && channel.enabled) {
        try {
          await this.sendToChannel(channel, resolutionNotification);
        } catch (error) {
          console.error(`[AlertSystem] Failed to send resolution to channel ${channelId}:`, error);
        }
      }
    }
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(
    rule: AlertRule,
    value: number,
    tags?: Record<string, string>
  ): string {
    const tagString = tags ? ` (${Object.entries(tags).map(([k, v]) => `${k}=${v}`).join(', ')})` : '';

    return `Alert: ${rule.name}
Metric: ${rule.metric}${tagString}
Current Value: ${value}
Threshold: ${rule.threshold}
Condition: ${rule.condition}
Severity: ${rule.severity}
Description: ${rule.description}
Time: ${new Date().toISOString()}`;
  }

  /**
   * Generate resolution message
   */
  private generateResolutionMessage(rule: AlertRule, notification: AlertNotification): string {
    const duration = notification.resolved && notification.timestamp
      ? Math.round((notification.resolved.getTime() - notification.timestamp.getTime()) / 1000)
      : 0;

    return `✅ RESOLVED: ${rule.name}
Metric: ${rule.metric}
Duration: ${duration} seconds
Resolved: ${new Date().toISOString()}`;
  }

  /**
   * Get color for Slack/Discord based on severity
   */
  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical':
        return '#ff0000'; // Red
      case 'error':
        return '#ff6600'; // Orange
      case 'warning':
        return '#ffcc00'; // Yellow
      case 'info':
        return '#0066cc'; // Blue
      default:
        return '#cccccc'; // Gray
    }
  }

  /**
   * Start background alert processor
   */
  private startAlertProcessor(): void {
    setInterval(async () => {
      try {
        // Process metric collection integration
        const metrics = await performanceMonitor.getSystemStatus();

        // Check system metrics
        if (metrics.system) {
          // System memory is currently stored as string, so we skip the numerical check
          // TODO: Update when system memory is provided as structured data
          console.debug('[AlertSystem] System memory metric available:', metrics.system.memory);
        }

        // Check database metrics
        if (metrics.database?.connections) {
          // Database connections are currently stored as string, so we skip the numerical check
          // TODO: Update when database connections are provided as structured data
          console.debug('[AlertSystem] Database connections metric available:', metrics.database.connections);
        }

        // Check cache metrics
        if (metrics.cache) {
          await this.processMetric('cache.hit_ratio', metrics.cache.hitRatio || 0);
        }

        // Cleanup old alerts
        await this.cleanupOldAlerts();
      } catch (error) {
        console.error('[AlertSystem] Error in alert processor:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Clean up old alerts
   */
  private async cleanupOldAlerts(): Promise<void> {
    const cutoffTime = Date.now() - (this.config.alertRetentionDays * 24 * 60 * 60 * 1000);

    for (const [key, alert] of this.activeAlerts.entries()) {
      if (alert.timestamp.getTime() < cutoffTime) {
        this.activeAlerts.delete(key);
      }
    }
  }

  /**
   * Get alert statistics
   */
  async getAlertStats(): Promise<AlertStats> {
    const alerts = Array.from(this.activeAlerts.values());

    const alertsByLevel = alerts.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const alertsByChannel = alerts.reduce((acc, alert) => {
      alert.channels.forEach(channel => {
        acc[channel] = (acc[channel] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const deliveryStats = alerts.reduce((acc, alert) => {
      Object.values(alert.deliveryStatus).forEach(status => {
        acc[status.status] = (acc[status.status] || 0) + 1;
      });
      return acc;
    }, { success: 0, failed: 0, pending: 0 });

    return {
      totalAlerts: alerts.length,
      alertsByLevel,
      alertsByChannel,
      deliveryStats,
      responseTime: {
        average: 0, // Would calculate from actual delivery times
        p95: 0,
        p99: 0,
      },
    };
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): AlertNotification[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Get alert rules
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * Get notification channels
   */
  getChannels(): AlertChannel[] {
    return Array.from(this.channels.values());
  }
}

// Export singleton instance
export const alertSystem = new AlertSystem();

// Export utility functions for easy integration
export const sendAlert = (metric: string, value: number, tags?: Record<string, string>) =>
  alertSystem.processMetric(metric, value, tags);

export const getAlerts = () => alertSystem.getActiveAlerts();
export const getAlertStats = () => alertSystem.getAlertStats();
