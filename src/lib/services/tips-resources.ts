import { supabase } from '@/lib/supabase';
import { getCurrentUser } from '@/lib/utils/session-manager';

// Types for tips and resources
export interface Tip {
  id: string;
  type: 'quick_tip' | 'best_practice' | 'feature_highlight' | 'optimization';
  title: string;
  description: string;
  actionText?: string;
  actionUrl?: string;
  priority: number;
  isPersonalized: boolean;
  icon: string;
  gradient: string;
  category: 'creation' | 'analytics' | 'engagement' | 'strategy' | 'technical';
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  current: number;
  target: number;
  unit: string;
  period: 'daily' | 'weekly' | 'monthly';
  icon: string;
  color: string;
  deadline?: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  unlockedAt: Date;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface ResourceCategory {
  id: string;
  title: string;
  description: string;
  icon: string;
  items: ResourceItem[];
}

export interface ResourceItem {
  id: string;
  title: string;
  description: string;
  type: 'guide' | 'template' | 'video' | 'article' | 'tool';
  url?: string;
  estimatedTime?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
}

export interface UserStats {
  totalPolls: number;
  totalResponses: number;
  averageResponseRate: number;
  mostPopularPollType: string;
  totalViews: number;
  accountAge: number;
  streak: number;
  lastActiveDate: Date;
}

// Cache for tips and resources data
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

const CACHE_DURATION = 60000; // 1 minute cache
let tipsCache: CacheItem<Tip[]> | null = null;
let goalsCache: CacheItem<Goal[]> | null = null;
let achievementsCache: CacheItem<Achievement[]> | null = null;
let resourcesCache: CacheItem<ResourceCategory[]> | null = null;

/**
 * Get personalized tips based on user's poll activity
 */
export async function getPersonalizedTips(): Promise<Tip[]> {
  // Return cached data if available and fresh
  if (tipsCache && (Date.now() - tipsCache.timestamp < CACHE_DURATION)) {
    console.log('Returning cached tips data');
    return tipsCache.data;
  }
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError || !user) {
      return getDefaultTips();
    }

    const stats = await getUserStats();
    const tips: Tip[] = [];

    // Beginner tips for new users
    if (stats.totalPolls === 0) {
      tips.push({
        id: 'first_poll',
        type: 'quick_tip',
        title: 'Create Your First Poll',
        description: 'Start with our conversational poll creator for the best experience!',
        actionText: 'Create Poll',
        actionUrl: '/dashboard/create/conversational',
        priority: 10,
        isPersonalized: true,
        icon: '🚀',
        gradient: 'from-blue-500 to-purple-600',
        category: 'creation'
      });
    }

    // Low response rate optimization
    if (stats.totalPolls > 0 && stats.averageResponseRate < 30) {
      tips.push({
        id: 'improve_response_rate',
        type: 'optimization',
        title: 'Boost Your Response Rate',
        description: 'Try shorter polls with compelling titles to increase participation.',
        actionText: 'Learn More',
        actionUrl: '/dashboard/help/response-rates',
        priority: 9,
        isPersonalized: true,
        icon: '📈',
        gradient: 'from-green-500 to-teal-600',
        category: 'engagement'
      });
    }

    // Analytics insight for active users
    if (stats.totalResponses > 50) {
      tips.push({
        id: 'advanced_analytics',
        type: 'feature_highlight',
        title: 'Unlock Advanced Analytics',
        description: 'Discover hidden patterns in your poll data with AI-powered insights.',
        actionText: 'View Analytics',
        actionUrl: '/dashboard/results',
        priority: 8,
        isPersonalized: true,
        icon: '🔍',
        gradient: 'from-purple-500 to-pink-600',
        category: 'analytics'
      });
    }

    // Content extraction for users who haven't used it
    const hasUsedExtraction = await checkIfUserUsedFeature();
    if (!hasUsedExtraction && stats.totalPolls > 0) {
      tips.push({
        id: 'content_extraction',
        type: 'feature_highlight',
        title: 'Save Time with AI Extraction',
        description: 'Extract poll questions from any webpage or document instantly.',
        actionText: 'Try It Now',
        actionUrl: '/dashboard/create',
        priority: 7,
        isPersonalized: true,
        icon: '⚡',
        gradient: 'from-orange-500 to-red-600',
        category: 'creation'
      });
    }

    // Add general tips to fill remaining slots
    const generalTips = await getGeneralTips();
    tips.push(...generalTips.slice(0, 5 - tips.length));

    const sortedTips = tips.sort((a, b) => b.priority - a.priority);

    // Store in cache for future requests
    tipsCache = {
      data: sortedTips,
      timestamp: Date.now()
    };

    return sortedTips;
  } catch (error) {
    console.error('Error getting personalized tips:', error);
    return getDefaultTips();
  }
}

/**
 * Get user's current goals based on their activity
 */
export async function getCurrentGoals(): Promise<Goal[]> {
  // Return cached data if available and fresh
  if (goalsCache && (Date.now() - goalsCache.timestamp < CACHE_DURATION)) {
    console.log('Returning cached goals data');
    return goalsCache.data;
  }
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError || !user) {
      return getDefaultGoals();
    }

    const stats = await getUserStats();
    const goals: Goal[] = [];

    // Weekly poll creation goal
    const weeklyPolls = await getWeeklyPollCount();
    goals.push({
      id: 'weekly_polls',
      title: 'Weekly Poll Goal',
      description: 'Create polls consistently',
      current: weeklyPolls,
      target: Math.max(5, Math.ceil(stats.totalPolls / 4)), // Dynamic target based on history
      unit: 'polls',
      period: 'weekly',
      icon: '🎯',
      color: 'blue'
    });

    // Response goal for active polls
    const activePolls = await getActivePollsCount();
    if (activePolls > 0) {
      const targetResponses = Math.max(50, stats.averageResponseRate * 2);
      goals.push({
        id: 'response_goal',
        title: 'Response Target',
        description: 'Reach your response milestone',
        current: stats.totalResponses,
        target: targetResponses,
        unit: 'responses',
        period: 'monthly',
        icon: '📊',
        color: 'green'
      });
    }

    // Store in cache for future requests
    goalsCache = {
      data: goals,
      timestamp: Date.now()
    };

    return goals;
  } catch (error) {
    console.error('Error getting current goals:', error);
    return getDefaultGoals();
  }
}

/**
 * Get user's recent achievements
 */
export async function getRecentAchievements(): Promise<Achievement[]> {
  // Return cached data if available and fresh
  if (achievementsCache && (Date.now() - achievementsCache.timestamp < CACHE_DURATION)) {
    console.log('Returning cached achievements data');
    return achievementsCache.data;
  }
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError || !user) {
      return [];
    }

    const stats = await getUserStats();
    const achievements: Achievement[] = [];

    // Check for milestone achievements
    if (stats.totalPolls >= 1 && stats.totalPolls < 2) {
      achievements.push({
        id: 'first_poll',
        title: 'First Steps',
        description: 'Created your first poll!',
        unlockedAt: new Date(),
        icon: '🎉',
        rarity: 'common'
      });
    }

    if (stats.totalPolls >= 10) {
      achievements.push({
        id: 'poll_creator',
        title: 'Poll Creator',
        description: 'Created 10 polls',
        unlockedAt: new Date(),
        icon: '🏆',
        rarity: 'rare'
      });
    }

    if (stats.totalResponses >= 100) {
      achievements.push({
        id: 'response_magnet',
        title: 'Response Magnet',
        description: 'Collected 100+ responses',
        unlockedAt: new Date(),
        icon: '🧲',
        rarity: 'epic'
      });
    }

    const recentAchievements = achievements.slice(-3); // Return last 3 achievements

    // Store in cache for future requests
    achievementsCache = {
      data: recentAchievements,
      timestamp: Date.now()
    };

    return recentAchievements;
  } catch (error) {
    console.error('Error getting achievements:', error);
    return [];
  }
}

/**
 * Get curated resource categories
 */
export async function getResourceCategories(): Promise<ResourceCategory[]> {
  // Return cached data if available and fresh
  if (resourcesCache && (Date.now() - resourcesCache.timestamp < CACHE_DURATION)) {
    console.log('Returning cached resources data');
    return resourcesCache.data;
  }

  const resources: ResourceCategory[] = [
    {
      id: 'getting_started',
      title: 'Getting Started',
      description: 'Essential guides for new poll creators',
      icon: '🚀',
      items: [
        {
          id: 'poll_basics',
          title: 'Poll Creation Best Practices',
          description: 'Learn how to create effective polls that get responses',
          type: 'guide' as const,
          estimatedTime: '5 min',
          difficulty: 'beginner' as const,
          tags: ['basics', 'creation', 'best-practices']
        },
        {
          id: 'question_types',
          title: 'Choosing the Right Question Type',
          description: 'Understand when to use multiple choice, rating scales, and open text',
          type: 'guide' as const,
          estimatedTime: '3 min',
          difficulty: 'beginner' as const,
          tags: ['questions', 'types', 'design']
        }
      ]
    },
    {
      id: 'analytics',
      title: 'Analytics & Insights',
      description: 'Make sense of your poll data',
      icon: '📊',
      items: [
        {
          id: 'reading_results',
          title: 'Understanding Your Results',
          description: 'How to interpret poll analytics and find actionable insights',
          type: 'guide' as const,
          estimatedTime: '7 min',
          difficulty: 'intermediate' as const,
          tags: ['analytics', 'insights', 'interpretation']
        },
        {
          id: 'export_data',
          title: 'Exporting and Sharing Data',
          description: 'Learn how to export results for reports and presentations',
          type: 'guide' as const,
          estimatedTime: '4 min',
          difficulty: 'beginner' as const,
          tags: ['export', 'sharing', 'reports']
        }
      ]
    },
    {
      id: 'advanced',
      title: 'Advanced Features',
      description: 'Unlock the full potential of PollGPT',
      icon: '⚡',
      items: [
        {
          id: 'ai_extraction',
          title: 'AI Content Extraction',
          description: 'Extract poll questions from any document or webpage automatically',
          type: 'guide' as const,
          url: '/dashboard/create',
          estimatedTime: '6 min',
          difficulty: 'intermediate' as const,
          tags: ['ai', 'automation', 'extraction']
        },
        {
          id: 'simulation',
          title: 'Poll Simulation Preview',
          description: 'Preview how your poll will perform before publishing',
          type: 'tool' as const,
          estimatedTime: '3 min',
          difficulty: 'advanced' as const,
          tags: ['simulation', 'preview', 'testing']
        }
      ]
    }
  ];

  // Store in cache for future requests
  resourcesCache = {
    data: resources,
    timestamp: Date.now()
  };

  return resources;
}

/**
 * Helper functions
 */
async function getUserStats(): Promise<UserStats> {
  const { user } = await getCurrentUser();
  if (!user) {
    return {
      totalPolls: 0,
      totalResponses: 0,
      averageResponseRate: 0,
      mostPopularPollType: 'multiple_choice',
      totalViews: 0,
      accountAge: 0,
      streak: 0,
      lastActiveDate: new Date()
    };
  }

  // Get poll count
  const { count: pollCount } = await supabase
    .from('polls')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id);

  // Get response count
  const { data: responseData } = await supabase
    .from('responses')
    .select('id, polls!inner(user_id)')
    .eq('polls.user_id', user.id);

  // Get views and other stats
  const { data: pollsData } = await supabase
    .from('polls')
    .select('views, created_at')
    .eq('user_id', user.id);

  const totalViews = pollsData?.reduce((sum, poll) => sum + (poll.views || 0), 0) || 0;
  const totalResponses = responseData?.length || 0;
  const averageResponseRate = pollCount && pollCount > 0 ? (totalResponses / pollCount) * 100 : 0;

  return {
    totalPolls: pollCount || 0,
    totalResponses,
    averageResponseRate,
    mostPopularPollType: 'multiple_choice',
    totalViews,
    accountAge: 30, // Calculate from user creation date
    streak: 1,
    lastActiveDate: new Date()
  };
}

async function getWeeklyPollCount(): Promise<number> {
  const { user } = await getCurrentUser();
  if (!user) return 0;

  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const { count } = await supabase
    .from('polls')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .gte('created_at', oneWeekAgo.toISOString());

  return count || 0;
}

async function getActivePollsCount(): Promise<number> {
  const { user } = await getCurrentUser();
  if (!user) return 0;

  const { count } = await supabase
    .from('polls')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .eq('status', 'active');

  return count || 0;
}

async function checkIfUserUsedFeature(): Promise<boolean> {
  // This would check user's activity logs for specific features
  // For now, return false to encourage feature usage
  return false;
}

function getDefaultTips(): Tip[] {
  return [
    {
      id: 'welcome_tip',
      type: 'quick_tip',
      title: 'Welcome to PollGPT!',
      description: 'Create your first poll using our conversational AI assistant.',
      actionText: 'Get Started',
      actionUrl: '/dashboard/create/conversational',
      priority: 10,
      isPersonalized: false,
      icon: '👋',
      gradient: 'from-blue-500 to-purple-600',
      category: 'creation'
    }
  ];
}

function getDefaultGoals(): Goal[] {
  return [
    {
      id: 'first_poll',
      title: 'Create Your First Poll',
      description: 'Get started with PollGPT',
      current: 0,
      target: 1,
      unit: 'poll',
      period: 'weekly',
      icon: '🎯',
      color: 'blue'
    }
  ];
}

async function getGeneralTips(): Promise<Tip[]> {
  return [
    {
      id: 'question_clarity',
      type: 'best_practice',
      title: 'Write Clear Questions',
      description: 'Use simple, specific language to get better responses.',
      priority: 6,
      isPersonalized: false,
      icon: '💡',
      gradient: 'from-yellow-500 to-orange-600',
      category: 'strategy'
    },
    {
      id: 'timing_matters',
      type: 'best_practice',
      title: 'Timing Matters',
      description: 'Share polls when your audience is most active for better engagement.',
      priority: 5,
      isPersonalized: false,
      icon: '⏰',
      gradient: 'from-indigo-500 to-blue-600',
      category: 'strategy'
    }
  ];
}
