import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { redisCacheService } from '.';
import { createHash } from 'crypto';

/**
 * Specialized cache service for simulation data
 * Provides optimized caching for both individual and batch simulations
 */
export class SimulationCacheService {
  private readonly NAMESPACE = 'simulation';
  private readonly VERSION = 'v2';
  private readonly DEFAULT_TTL = 7 * 24 * 60 * 60; // 7 days in seconds
  
  /**
   * Generate a cache key for a simulation based on its parameters
   */
  generateSimulationKey(
    pollQuestion: string,
    pollOptions: string[],
    demographicGroup: string,
    sampleSize: number
  ): string {
    // Create a deterministic identifier from the simulation parameters
    const identifier = `${pollQuestion}:${pollOptions.join(',')}:${demographicGroup}:${sampleSize}`;
    return redisCacheService.generateKey(this.NAMESPACE, identifier, this.VERSION);
  }
  
  /**
   * Generate a cache key for a batch simulation
   */
  generateBatchKey(
    pollQuestion: string,
    pollOptions: string[],
    demographics: Array<{ group: string; size: number }>
  ): string {
    // Sort demographics for consistent key generation
    const sortedDemographics = [...demographics].sort((a, b) => 
      a.group.localeCompare(b.group) || a.size - b.size
    );
    
    // Create a deterministic identifier from the batch parameters
    const demographicsString = sortedDemographics.map(d => `${d.group}:${d.size}`).join('|');
    const identifier = `batch:${pollQuestion}:${pollOptions.join(',')}:${demographicsString}`;
    
    return redisCacheService.generateKey(this.NAMESPACE, identifier, this.VERSION);
  }
  
  /**
   * Get a cached simulation response
   */
  async getSimulation(
    pollQuestion: string,
    pollOptions: string[],
    demographicGroup: string,
    sampleSize: number
  ): Promise<SimulationResponse | null> {
    const key = this.generateSimulationKey(pollQuestion, pollOptions, demographicGroup, sampleSize);
    return redisCacheService.get<SimulationResponse>(key);
  }
  
  /**
   * Cache a simulation response
   */
  async cacheSimulation(
    pollQuestion: string,
    pollOptions: string[],
    demographicGroup: string,
    sampleSize: number,
    response: SimulationResponse,
    ttl: number = this.DEFAULT_TTL
  ): Promise<boolean> {
    const key = this.generateSimulationKey(pollQuestion, pollOptions, demographicGroup, sampleSize);
    return redisCacheService.set<SimulationResponse>(key, response, ttl);
  }
  
  /**
   * Get multiple cached simulations at once
   */
  async getBatchSimulations(
    pollQuestion: string,
    pollOptions: string[],
    demographics: Array<{ group: string; size: number }>
  ): Promise<Record<string, SimulationResponse>> {
    // Generate keys for each demographic
    const keys = demographics.map(d => 
      this.generateSimulationKey(pollQuestion, pollOptions, d.group, d.size)
    );
    
    // Batch get all simulations
    return redisCacheService.batchGet<SimulationResponse>(keys);
  }
  
  /**
   * Get a cached batch simulation result
   */
  async getBatchResult(
    pollQuestion: string,
    pollOptions: string[],
    demographics: Array<{ group: string; size: number }>
  ): Promise<BatchSimulationResult | null> {
    const key = this.generateBatchKey(pollQuestion, pollOptions, demographics);
    return redisCacheService.get<BatchSimulationResult>(key);
  }
  
  /**
   * Cache a batch simulation result
   */
  async cacheBatchResult(
    batchResult: BatchSimulationResult,
    ttl: number = this.DEFAULT_TTL
  ): Promise<boolean> {
    const { pollQuestion, pollOptions } = batchResult;
    const demographics = batchResult.results.map(r => ({
      group: r.metadata.demographic,
      size: r.metadata.sampleSize
    }));
    
    const key = this.generateBatchKey(pollQuestion, pollOptions, demographics);
    return redisCacheService.set<BatchSimulationResult>(key, batchResult, ttl);
  }
  
  /**
   * Invalidate simulation cache for a specific poll question
   */
  async invalidateSimulationCache(pollQuestion?: string, demographicGroup?: string): Promise<void> {
    let pattern: string;
    
    if (pollQuestion && demographicGroup) {
      // Create a hash of the specific combination
      const hash = createHash('md5').update(`${pollQuestion}:*:${demographicGroup}:*`).digest('hex');
      pattern = `pollgpt:${this.VERSION}:${this.NAMESPACE}:*${hash}*`;
    } else if (pollQuestion) {
      // Create a hash of just the poll question
      const hash = createHash('md5').update(`${pollQuestion}:*`).digest('hex');
      pattern = `pollgpt:${this.VERSION}:${this.NAMESPACE}:*${hash}*`;
    } else if (demographicGroup) {
      // Create a hash that would match the demographic
      const hash = createHash('md5').update(`*:${demographicGroup}:*`).digest('hex');
      pattern = `pollgpt:${this.VERSION}:${this.NAMESPACE}:*${hash}*`;
    } else {
      // Invalidate all simulation cache
      pattern = `pollgpt:${this.VERSION}:${this.NAMESPACE}:*`;
    }
    
    await redisCacheService.deletePattern(pattern);
  }
  
  /**
   * Get cache statistics for simulations
   */
  async getSimulationCacheStats(): Promise<{
    totalCached: number;
    hitRate: number;
    avgResponseTime: number;
  }> {
    // This would be implemented with actual metrics in a production system
    return {
      totalCached: 0,
      hitRate: 0,
      avgResponseTime: 0
    };
  }
}

// Export singleton instance
export const simulationCacheService = new SimulationCacheService();
