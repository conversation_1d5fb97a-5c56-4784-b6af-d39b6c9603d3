export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      polls: {
        Row: {
          id: string
          created_at: string
          title: string
          description: string | null
          user_id: string
          is_published: boolean
          is_public: boolean
          slug: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          title: string
          description?: string | null
          user_id: string
          is_published?: boolean
          is_public?: boolean
          slug?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          title?: string
          description?: string | null
          user_id?: string
          is_published?: boolean
          is_public?: boolean
          slug?: string | null
          updated_at?: string | null
        }
      }
      questions: {
        Row: {
          id: string
          created_at: string
          poll_id: string
          question_text: string
          question_type: string
          options: Json | null
          required: boolean
          order: number
        }
        Insert: {
          id?: string
          created_at?: string
          poll_id: string
          question_text: string
          question_type: string
          options?: Json | null
          required?: boolean
          order: number
        }
        Update: {
          id?: string
          created_at?: string
          poll_id?: string
          question_text?: string
          question_type?: string
          options?: Json | null
          required?: boolean
          order?: number
        }
      }
      responses: {
        Row: {
          id: string
          created_at: string
          poll_id: string
          user_id: string | null
          respondent_info: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          poll_id: string
          user_id?: string | null
          respondent_info?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          poll_id?: string
          user_id?: string | null
          respondent_info?: Json | null
        }
      }
      answers: {
        Row: {
          id: string
          created_at: string
          response_id: string
          question_id: string
          answer_value: Json
        }
        Insert: {
          id?: string
          created_at?: string
          response_id: string
          question_id: string
          answer_value: Json
        }
        Update: {
          id?: string
          created_at?: string
          response_id?: string
          question_id?: string
          answer_value?: Json
        }
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          email: string
          name: string | null
          avatar_url: string | null
        }
        Insert: {
          id: string
          created_at?: string
          email: string
          name?: string | null
          avatar_url?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          email?: string
          name?: string | null
          avatar_url?: string | null
        }
      }
      poll_simulations: {
        Row: {
          id: string
          poll_id: string
          demographic_group: string
          sample_size: number
          simulation_data: Json
          confidence_score: number | null
          citations: string[] | null
          created_at: string
          created_by: string | null
        }
        Insert: {
          id?: string
          poll_id: string
          demographic_group: string
          sample_size: number
          simulation_data: Json
          confidence_score?: number | null
          citations?: string[] | null
          created_at?: string
          created_by?: string | null
        }
        Update: {
          id?: string
          poll_id?: string
          demographic_group?: string
          sample_size?: number
          simulation_data?: Json
          confidence_score?: number | null
          citations?: string[] | null
          created_at?: string
          created_by?: string | null
        }
      }
      simulation_cache: {
        Row: {
          id: string
          question_hash: string
          demographic_key: string
          cached_result: Json
          cache_expires_at: string
          created_at: string
        }
        Insert: {
          id?: string
          question_hash: string
          demographic_key: string
          cached_result: Json
          cache_expires_at: string
          created_at?: string
        }
        Update: {
          id?: string
          question_hash?: string
          demographic_key?: string
          cached_result?: Json
          cache_expires_at?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Helper types for Supabase tables
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for convenience
export type Poll = Tables<'polls'>
export type Question = Tables<'questions'>
export type Response = Tables<'responses'>
export type Answer = Tables<'answers'>
export type Profile = Tables<'profiles'>
export type PollSimulation = Tables<'poll_simulations'>
export type SimulationCache = Tables<'simulation_cache'>