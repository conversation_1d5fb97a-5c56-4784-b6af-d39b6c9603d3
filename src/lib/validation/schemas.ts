import { z } from 'zod';

// Question Types - updated to match actual DB values
export const QuestionTypeEnum = z.enum(['single', 'multiple', 'open']);
export type QuestionType = z.infer<typeof QuestionTypeEnum>;

// Question Option Schema
export const QuestionOptionSchema = z.object({
  id: z.string(),
  text: z.string(),
  value: z.string(),
});
export type QuestionOption = z.infer<typeof QuestionOptionSchema>;

// Poll Question Schema
export const PollQuestionSchema = z.object({
  id: z.string(),
  text: z.string(),
  type: QuestionTypeEnum,
  options: z.array(QuestionOptionSchema).optional(),
  required: z.boolean(),
  order: z.number(),
});
export type PollQuestion = z.infer<typeof PollQuestionSchema>;

// Poll Status
export const PollStatusEnum = z.enum(['draft', 'active', 'completed']);
export type PollStatus = z.infer<typeof PollStatusEnum>;

// Main Poll Schema
export const PollSchema = z.object({
  id: z.string(),
  title: z.string(),
  slug: z.string().nullable(),
  description: z.string().nullable().optional(),
  questions: z.array(PollQuestionSchema),
  questions_count: z.number(),
  createdAt: z.string(),
  updatedAt: z.string().nullable(),
  expiresAt: z.string().nullable(),
  userId: z.string(),
  status: PollStatusEnum,
  responses_count: z.number(),
  views_count: z.number(),
  is_public: z.boolean(),
  access_code: z.string().nullable().optional(),
  source_url: z.string().nullable().optional(),
  source_type: z.enum(['url', 'pdf', 'website']).nullable().optional(),
  source_filename: z.string().nullable().optional(),
  show_source: z.boolean().optional().default(true),
  context: z.string().nullable().optional(),
});
export type Poll = z.infer<typeof PollSchema>;

// Partial Poll Schema for updates
export const PollUpdateSchema = PollSchema.partial().omit({
  id: true,
  createdAt: true,
  userId: true,
});
export type PollUpdate = z.infer<typeof PollUpdateSchema>;

// Poll Response Schema
export const PollResponseSchema = z.object({
  id: z.string(),
  pollId: z.string(),
  responses: z.record(z.string(), z.union([z.string(), z.array(z.string())])),
  submittedAt: z.string(),
  respondentInfo: z.object({
    deviceType: z.string(),
    browser: z.string(),
    os: z.string(),
    region: z.string().optional(),
  }).optional(),
  deviceType: z.string(),
  browser: z.string(),
  os: z.string(),
  region: z.string().optional(),
});
export type PollResponse = z.infer<typeof PollResponseSchema>;

// Database Response Schema (from responses table)
export const DbResponseSchema = z.object({
  id: z.string(),
  created_at: z.string().nullable(),
  poll_id: z.string(),
  user_id: z.string().nullable(),
  respondent_info: z.object({
    os: z.string(),
    browser: z.string(),
    deviceType: z.string(),
  }).nullable(),
});
export type DbResponse = z.infer<typeof DbResponseSchema>;

// Database Answer Schema (from answers table)
export const DbAnswerSchema = z.object({
  id: z.string(),
  created_at: z.string().nullable(),
  response_id: z.string(),
  question_id: z.string(),
  answer_value: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]),
});
export type DbAnswer = z.infer<typeof DbAnswerSchema>;

// Paginated Polls Schema
export const PaginatedPollsSchema = z.object({
  polls: z.array(PollSchema),
  totalCount: z.number(),
  currentPage: z.number(),
  totalPages: z.number(),
  pageSize: z.number(),
});
export type PaginatedPolls = z.infer<typeof PaginatedPollsSchema>;

// Validation helper functions
export const validatePoll = (data: unknown): Poll => {
  return PollSchema.parse(data);
};

export const validatePollSafe = (data: unknown): { success: true; data: Poll } | { success: false; error: z.ZodError } => {
  const result = PollSchema.safeParse(data);
  return result;
};

export const validatePollUpdate = (data: unknown): PollUpdate => {
  return PollUpdateSchema.parse(data);
};

export const validatePaginatedPolls = (data: unknown): PaginatedPolls => {
  return PaginatedPollsSchema.parse(data);
};

// Additional validation helpers
export const validateDbPoll = (data: unknown): DbPoll => {
  return DbPollSchema.parse(data);
};

export const validateDbQuestion = (data: unknown): z.infer<typeof DbQuestionShapeSchema> => {
  return DbQuestionShapeSchema.parse(data);
};

export const validateDbResponse = (data: unknown): DbResponse => {
  return DbResponseSchema.parse(data);
};

export const validateDbAnswer = (data: unknown): DbAnswer => {
  return DbAnswerSchema.parse(data);
};

// Safe parsing helpers
export const safeParseDbPoll = (data: unknown) => {
  return DbPollSchema.safeParse(data);
};

export const safeParseDbQuestion = (data: unknown) => {
  return DbQuestionShapeSchema.safeParse(data);
};

// Database-specific schemas for parsing data from Supabase
export const DbQuestionShapeSchema = z.object({
  id: z.string(),
  created_at: z.string().nullable(),
  poll_id: z.string(),
  question_text: z.string(),
  question_type: QuestionTypeEnum,
  options: z.array(QuestionOptionSchema).nullable(),
  required: z.boolean().nullable(),
  order: z.number(),
});

// Database Poll Schema (from polls table)
export const DbPollSchema = z.object({
  id: z.string(),
  created_at: z.string().nullable(),
  title: z.string(),
  description: z.string().nullable(),
  user_id: z.string(),
  is_public: z.boolean().nullable(),
  slug: z.string().nullable(),
  updated_at: z.string().nullable(),
  expires_at: z.string().nullable(),
  views: z.number().nullable(),
  access_code: z.string().nullable(),
  source_url: z.string().nullable(),
  source_type: z.enum(['url', 'pdf', 'website']).nullable(),
  source_filename: z.string().nullable(),
  show_source: z.boolean().nullable(),
  context: z.string().nullable(), // MISSING FIELD ADDED!
  status: PollStatusEnum,
});
export type DbPoll = z.infer<typeof DbPollSchema>;

// Transform DB question to app question
export const transformDbQuestionToApp = (dbQuestion: z.infer<typeof DbQuestionShapeSchema>): PollQuestion => {
  return {
    id: dbQuestion.id,
    text: dbQuestion.question_text,
    type: dbQuestion.question_type,
    options: dbQuestion.options || [],
    required: dbQuestion.required ?? true,
    order: dbQuestion.order,
  };
};

// Transform DB poll to app poll
export const transformDbPollToApp = (dbPoll: DbPoll, questions: PollQuestion[] = [], responsesCount: number = 0): Poll => {
  return {
    id: dbPoll.id,
    title: dbPoll.title,
    slug: dbPoll.slug,
    description: dbPoll.description || '',
    questions,
    questions_count: questions.length,
    createdAt: dbPoll.created_at || new Date().toISOString(),
    updatedAt: dbPoll.updated_at || new Date().toISOString(),
    expiresAt: dbPoll.expires_at,
    userId: dbPoll.user_id,
    status: dbPoll.status,
    responses_count: responsesCount,
    views_count: dbPoll.views || 0,
    is_public: dbPoll.is_public ?? false,
    access_code: dbPoll.access_code,
    source_url: dbPoll.source_url,
    source_type: dbPoll.source_type,
    source_filename: dbPoll.source_filename,
    show_source: dbPoll.show_source ?? true,
    context: dbPoll.context, // MISSING FIELD ADDED!
  };
};

// Flexible validation for data coming from the API
export const FlexiblePollSchema = z.object({
  id: z.string(),
  title: z.string(),
  slug: z.string().nullable().optional(),
  description: z.string().nullable().optional().default(''),
  questions: z.union([
    z.array(PollQuestionSchema),
    z.array(DbQuestionShapeSchema),
    z.undefined(),
  ]).optional().default([]),
  questions_count: z.number().optional().default(0),
  createdAt: z.string().optional().default(''),
  updatedAt: z.string().optional().default(''),
  expiresAt: z.string().nullable().optional(),
  userId: z.string().optional().default(''),
  status: z.union([PollStatusEnum, z.string()]).optional().default('draft'),
  responses_count: z.number().optional().default(0),
  views_count: z.number().optional().default(0),
  is_public: z.boolean().optional().default(true),
  access_code: z.string().nullable().optional(),
  source_url: z.string().nullable().optional(),
  source_type: z.enum(['url', 'pdf', 'website']).nullable().optional(),
  source_filename: z.string().nullable().optional(),
  show_source: z.boolean().optional(),
  context: z.string().nullable().optional(), // MISSING FIELD ADDED!
});

// Transform flexible poll to strict poll
export const transformFlexiblePoll = (data: unknown): Poll => {
  const parsed = FlexiblePollSchema.parse(data);

  // Transform questions if they're in DB format
  let questions: PollQuestion[] = [];
  if (Array.isArray(parsed.questions)) {
    questions = parsed.questions.map((q: unknown) => {
      // Check if it's a DB question shape
      if (typeof q === 'object' && q !== null && 'question_text' in q) {
        return transformDbQuestionToApp(q as z.infer<typeof DbQuestionShapeSchema>);
      }
      // Otherwise assume it's already in app format
      return q as PollQuestion;
    });
  }

  return {
    id: parsed.id,
    title: parsed.title,
    slug: parsed.slug ?? null,
    description: parsed.description ?? '',
    questions,
    questions_count: questions.length,
    createdAt: parsed.createdAt || new Date().toISOString(),
    updatedAt: parsed.updatedAt || new Date().toISOString(),
    expiresAt: parsed.expiresAt ?? null,
    userId: parsed.userId || '',
    status: (typeof parsed.status === 'string' && ['draft', 'active', 'completed'].includes(parsed.status))
      ? parsed.status as PollStatus
      : 'draft',
    responses_count: parsed.responses_count || 0,
    views_count: parsed.views_count || 0,
    is_public: parsed.is_public ?? true,
    access_code: parsed.access_code ?? null,
    source_url: parsed.source_url ?? null,
    source_type: parsed.source_type ?? null,
    source_filename: parsed.source_filename ?? null,
    show_source: parsed.show_source ?? true,
    context: parsed.context ?? null, // MISSING FIELD ADDED!
  };
};

// Chart and Analysis Types
export const ChartDataPointSchema = z.object({
  name: z.string(),
  value: z.number(),
  fill: z.string().optional(),
});
export type ChartDataPoint = z.infer<typeof ChartDataPointSchema>;

export const OpenQuestionResponseSchema = z.object({
  id: z.number(),
  response: z.string(),
});
export type OpenQuestionResponse = z.infer<typeof OpenQuestionResponseSchema>;

export const ProcessedQuestionSchema = PollQuestionSchema.omit({ options: true }).extend({
  options: z.array(QuestionOptionSchema).optional(),
  results: z.union([z.array(ChartDataPointSchema), z.array(OpenQuestionResponseSchema)]),
  npsData: z.array(ChartDataPointSchema).optional(),
  npsScore: z.number().optional(),
});
export type ProcessedQuestion = z.infer<typeof ProcessedQuestionSchema>;

export const ResponseByDateSchema = z.object({
  date: z.string(),
  count: z.number(),
});
export type ResponseByDate = z.infer<typeof ResponseByDateSchema>;

// Chart and UI-specific types for components
export interface ProcessedPollData {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  completedResponses: number;
  partialResponses: number;
  totalViews: number;
  completionRate: string;
  averageTimeToComplete: string;
  questions: ProcessedQuestion[];
  responsesByDate: ResponseByDate[];
  demographics: {
    devices: ChartDataPoint[];
    regions: ChartDataPoint[];
  };
}
