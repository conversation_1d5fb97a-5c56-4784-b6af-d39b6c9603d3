import { useState, useEffect, useMemo } from 'react';
import { SimulationResponse } from '../types/simulation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

interface SimulationOptions {
  pollId?: string;
  questionId?: string;
  pollQuestion: string;
  pollOptions: string[];
  demographic: {
    group: string;
    size: number;
    context?: string;
  };
  responseFormat: 'distribution' | 'individual' | 'both';
  questionType: 'multiple_choice' | 'open_ended';
  specialInstructions?: string;
  pollContext?: string;
}

interface SimulationState {
  demographic: string;
  sampleSize: number;
  specialInstructions: string;
  simulation: SimulationResponse | null;
  timestamp: number;
}

// Helper functions to work with localStorage
const getStoredSimulationData = (key: string): SimulationState | null => {
  if (typeof window === 'undefined') return null;

  const saved = localStorage.getItem(key);
  if (!saved) return null;

  try {
    return JSON.parse(saved) as SimulationState;
  } catch (e) {
    console.error('Error parsing saved simulation data:', e);
    return null;
  }
};

const saveSimulationData = (key: string, data: SimulationState): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (e) {
    console.error('Error saving simulation data:', e);
  }
};

export function useSimulation(pollId?: string, questionId?: string) {
  // Create a unique key for storing simulation data that includes both poll and question ID
  const storageKey = useMemo(() => pollId
    ? questionId
      ? `simulation_data_${pollId}_question_${questionId}`
      : `simulation_data_${pollId}`
    : 'simulation_data_temp',
  [pollId, questionId]);

  // Create a cache key for React Query
  const queryKey = useMemo(() => ['simulation', pollId, questionId], [pollId, questionId]);

  // Access the query client for cache manipulation
  const queryClient = useQueryClient();

  // Use React Query to fetch and cache simulation data
  const {
    data: simulationData,
    isLoading: isLoadingQuery,
    error: queryError
  } = useQuery({
    queryKey,
    queryFn: () => getStoredSimulationData(storageKey),
    // If not in cache, try to load from localStorage
    initialData: () => getStoredSimulationData(storageKey),
    staleTime: Infinity, // This data doesn't go stale until we explicitly invalidate it
  });

  // Set up state from the query data
  const [demographic, setDemographic] = useState<string>(
    simulationData?.demographic || 'college_students'
  );

  const [sampleSize, setSampleSize] = useState<number>(
    simulationData?.sampleSize || 100
  );

  const [specialInstructions, setSpecialInstructions] = useState<string>(
    simulationData?.specialInstructions || ''
  );

  const simulation = simulationData?.simulation || null;
  const [error, setError] = useState<string | undefined>(
    queryError ? String(queryError) : undefined
  );

  // Define a mutation for starting simulations
  const { mutate, isPending } = useMutation({
    mutationFn: async (options: SimulationOptions) => {
      const response = await fetch("/api/simulate-poll", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          pollId: options.pollId,
          questionId: options.questionId,
          pollQuestion: options.pollQuestion,
          pollOptions: options.pollOptions || [],
          demographic: {
            group: options.demographic.group,
            size: options.demographic.size,
            context: options.demographic.context || "",
          },
          responseFormat: options.responseFormat,
          questionType: options.questionType,
          specialInstructions: options.specialInstructions,
          pollContext: options.pollContext,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to simulate poll');
      }

      const data = await response.json();
      if (!data.success || !data.simulation) {
        throw new Error('Invalid response format from simulation API');
      }

      return data.simulation as SimulationResponse;
    },
    onSuccess: (simulationResponse) => {
      // Update the cache with new simulation data
      const newData: SimulationState = {
        demographic,
        sampleSize,
        specialInstructions,
        simulation: simulationResponse,
        timestamp: Date.now()
      };

      queryClient.setQueryData(queryKey, newData);

      // Also save to localStorage for persistence
      saveSimulationData(storageKey, newData);
    },
    onError: (err) => {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    },
  });

  // Loading state combining query loading and mutation loading
  const isLoading = isPending || isLoadingQuery;

  // Update local storage when simulation settings change
  useEffect(() => {
    // Only update if we already have simulation data or settings have changed from defaults
    if (simulationData || demographic !== 'college_students' || sampleSize !== 100 || specialInstructions !== '') {
      const updatedData: SimulationState = {
        demographic,
        sampleSize,
        specialInstructions,
        simulation: simulation,
        timestamp: Date.now()
      };

      // Update React Query cache
      queryClient.setQueryData(queryKey, updatedData);

      // Update localStorage
      saveSimulationData(storageKey, updatedData);
    }
  }, [demographic, sampleSize, specialInstructions, storageKey, queryClient, queryKey, simulation, simulationData]);

  const startSimulation = async (options: SimulationOptions, onComplete?: (simulation: SimulationResponse) => void) => {
    setError(undefined);
    mutate(options, {
      onSuccess: (data) => {
        if (onComplete) {
          onComplete(data);
        }
      }
    });
  };

  const resetSimulation = () => {
    // Clear from React Query cache
    queryClient.setQueryData(queryKey, null);

    // Clear from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(storageKey);
    }
  };

  return {
    demographic,
    setDemographic,
    sampleSize,
    setSampleSize,
    specialInstructions,
    setSpecialInstructions,
    simulation,
    isLoading,
    error,
    startSimulation,
    resetSimulation,
  };
}
