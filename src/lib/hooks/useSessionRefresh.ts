// useSessionRefresh.ts
// Hook to help with auth session refreshing

import { useState } from 'react';
import { supabase } from '@/lib/supabase';

// Maximum number of refresh attempts to prevent infinite loops
const MAX_REFRESH_ATTEMPTS = 3;

/**
 * Hook that provides session refresh functionality
 * @returns Functions and state for session refresh
 */
export const useSessionRefresh = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [refreshAttempts, setRefreshAttempts] = useState(0);

  /**
   * Attempts to refresh the auth session
   * @returns Promise that resolves to true if refresh was successful, false otherwise
   */
  const refreshSession = async (): Promise<boolean> => {
    if (refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
      console.error("Maximum refresh attempts reached");
      return false;
    }

    setRefreshing(true);
    setRefreshAttempts(prev => prev + 1);

    try {
      console.log(`Attempting to refresh session (attempt ${refreshAttempts + 1})`);
      const { data, error } = await supabase.auth.refreshSession();

      if (error || !data?.session) {
        console.error("Failed to refresh auth session:", error);
        setRefreshing(false);
        return false;
      }

      console.log("Auth session refreshed successfully");
      setRefreshing(false);
      return true;
    } catch (err) {
      console.error("Unexpected error during session refresh:", err);
      setRefreshing(false);
      return false;
    }
  };

  /**
   * Resets the refresh attempts counter
   */
  const resetRefreshAttempts = () => {
    setRefreshAttempts(0);
  };

  return {
    refreshSession,
    refreshing,
    refreshAttempts,
    resetRefreshAttempts,
    maxAttemptsReached: refreshAttempts >= MAX_REFRESH_ATTEMPTS
  };
};
