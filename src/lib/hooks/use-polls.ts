// Custom hooks for polls data fetching with React Query
import { useQuery } from '@tanstack/react-query';
import { getPolls } from '@/lib/services/polls';
import { Poll } from '@/lib/validation/schemas';
import { useMemo } from 'react';

/**
 * Hook that fetches polls with React Query caching
 * @param page Current page number
 * @param pageSize Number of items per page
 * @param enabled Whether the query should run automatically
 * @returns Query result object with polls data
 */
export const usePolls = (
  page: number = 1,
  pageSize: number = 10,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: ['polls', page, pageSize],
    queryFn: async () => getPolls(page, pageSize),
    enabled,
  });
};

/**
 * Hook that provides filtered polls based on search and status filters
 * Uses memoization to prevent unnecessary filtering operations
 */
export const useFilteredPolls = (
  polls: Poll[] | undefined,
  searchQuery: string,
  statusFilter: string | null
) => {
  return useMemo(() => {
    if (!polls) return [];

    return polls.filter(poll => {
      const matchesSearch = poll.title.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter ? poll.status === statusFilter : true;
      return matchesSearch && matchesStatus;
    });
  }, [polls, searchQuery, statusFilter]);
};

// Named exports are preferred for hooks
// We'll also provide a named module export for backward compatibility
const pollHooks = {
  usePolls,
  useFilteredPolls
};

export default pollHooks;
