import { useCompletion } from 'ai/react';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { z } from 'zod';
import { SimulationRequest, SimulationResponse } from '@/lib/types/simulation';
import { getFinalFeatureFlags } from '@/lib/ai/feature-flags';

// Schema for structured simulation creation
const SimulationConfigSchema = z.object({
  pollQuestion: z.string(),
  pollOptions: z.array(z.string()),
  demographic: z.object({
    group: z.string(),
    size: z.number(),
    context: z.string().optional()
  }),
  specialInstructions: z.string().optional(),
  useEnhancedEngine: z.boolean().default(true)
});

// Hook for enhanced poll simulation with AI SDK
export function useEnhancedSimulation(pollId?: string) {
  const [progress, setProgress] = useState(0);
  const [currentDemographic, setCurrentDemographic] = useState<string>('');

  // Get feature flags
  const featureFlags = getFinalFeatureFlags();

  // Enhanced simulation mutation
  const enhancedSimulation = useMutation({
    mutationFn: async (request: SimulationRequest & { useEnhancedEngine?: boolean }) => {
      const response = await fetch('/api/ai/enhanced-simulation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          pollId,
          useEnhancedEngine: request.useEnhancedEngine ?? true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Enhanced simulation failed');
      }

      const data = await response.json();
      return data.simulation as SimulationResponse;
    },
    onSuccess: (data) => {
      console.log('Enhanced simulation completed:', data);
    },
    onError: (error) => {
      console.error('Enhanced simulation error:', error);
    }
  });

  // Batch simulation mutation
  const batchSimulation = useMutation({
    mutationFn: async (params: {
      pollQuestion: string;
      pollOptions: string[];
      demographics: string[];
    }) => {
      const response = await fetch('/api/ai/enhanced-simulation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...params,
          pollId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Batch simulation failed');
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('Batch simulation completed:', data);
      setProgress(100);
    },
    onError: (error) => {
      console.error('Batch simulation error:', error);
      setProgress(0);
    }
  });

  return {
    // Single simulation
    runSimulation: enhancedSimulation.mutate,
    isRunning: enhancedSimulation.isPending,
    simulation: enhancedSimulation.data,
    simulationError: enhancedSimulation.error,

    // Batch simulation
    runBatchSimulation: batchSimulation.mutate,
    isBatchRunning: batchSimulation.isPending,
    batchResult: batchSimulation.data,
    batchError: batchSimulation.error,

    // Progress tracking
    progress,
    currentDemographic,

    // Utility methods
    reset: () => {
      enhancedSimulation.reset();
      batchSimulation.reset();
      setProgress(0);
      setCurrentDemographic('');
    },

    // Feature availability
    isEnhancedAvailable: featureFlags.enhancedSimulation,
    isBatchAvailable: featureFlags.batchSimulationProcessing
  };
}

// Hook for real-time simulation streaming
export function useSimulationStreaming(pollId: string) {
  // Get feature flags
  const featureFlags = getFinalFeatureFlags();

  const { completion, complete, isLoading, error } = useCompletion({
    api: '/api/ai/stream-simulation-insights',
    onFinish: () => {
      console.log('Simulation insights streaming completed');
    }
  });

  const streamInsights = (simulationData: SimulationResponse) => {
    if (!featureFlags.simulationInsights) {
      console.warn('Simulation insights streaming is disabled');
      return;
    }

    const prompt = JSON.stringify({
      pollId,
      simulation: simulationData,
      requestType: 'insights_generation'
    });

    complete(prompt);
  };

  return {
    streamInsights,
    insights: completion,
    isStreaming: isLoading,
    error,
    isAvailable: featureFlags.simulationInsights
  };
}

// Hook for structured simulation configuration
export function useSimulationConfig() {
  const [config, setConfig] = useState<z.infer<typeof SimulationConfigSchema> | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Get feature flags
  const featureFlags = getFinalFeatureFlags();

  const { complete, isLoading } = useCompletion({
    api: '/api/ai/generate-simulation-config',
    onFinish: (_prompt, completion) => {
      try {
        const parsed = JSON.parse(completion);
        const validated = SimulationConfigSchema.parse(parsed);
        setConfig(validated);
      } catch (error) {
        console.error('Failed to parse simulation config:', error);
      }
      setIsGenerating(false);
    }
  });

  const generateConfig = (params: {
    pollDescription: string;
    targetAudience: string;
    goals: string[];
  }) => {
    if (!featureFlags.structuredGeneration) {
      console.warn('Structured generation is disabled');
      return;
    }

    setIsGenerating(true);
    const prompt = JSON.stringify({
      ...params,
      requestType: 'simulation_config'
    });
    complete(prompt);
  };

  return {
    generateConfig,
    config,
    isGenerating: isGenerating || isLoading,
    isAvailable: featureFlags.structuredGeneration
  };
}

// Hook for demographic analysis
export function useDemographicAnalysis(simulationResults: SimulationResponse[]) {
  // Get feature flags
  const featureFlags = getFinalFeatureFlags();

  const { completion, complete, isLoading } = useCompletion({
    api: '/api/ai/analyze-demographics',
    onFinish: () => {
      console.log('Demographic analysis completed');
    }
  });

  const analyzeResults = () => {
    if (!featureFlags.demographicAnalysis || !simulationResults?.length) {
      console.warn('Demographic analysis is disabled or no results available');
      return;
    }

    const analysisPrompt = JSON.stringify({
      results: simulationResults,
      analysisType: 'cross_demographic_comparison'
    });

    complete(analysisPrompt);
  };

  return {
    analyzeResults,
    analysis: completion,
    isAnalyzing: isLoading,
    isAvailable: featureFlags.demographicAnalysis && simulationResults?.length > 0
  };
}

// Hook for simulation comparison
export function useSimulationComparison() {
  const [comparisons, setComparisons] = useState<SimulationResponse[]>([]);

  const addSimulation = (simulation: SimulationResponse) => {
    setComparisons(prev => [...prev, simulation]);
  };

  const removeSimulation = (simulationId: string) => {
    setComparisons(prev => prev.filter(sim => sim.simulationId !== simulationId));
  };

  const clearComparisons = () => {
    setComparisons([]);
  };

  // Generate comparison analysis
  const { completion, complete, isLoading } = useCompletion({
    api: '/api/ai/compare-simulations',
    onFinish: () => {
      console.log('Simulation comparison completed');
    }
  });

  const compareSimulations = () => {
    if (comparisons.length < 2) {
      console.warn('Need at least 2 simulations to compare');
      return;
    }

    const comparisonPrompt = JSON.stringify({
      simulations: comparisons,
      comparisonType: 'detailed_analysis'
    });

    complete(comparisonPrompt);
  };

  return {
    // Comparison management
    addSimulation,
    removeSimulation,
    clearComparisons,
    comparisons,

    // Analysis
    compareSimulations,
    comparisonAnalysis: completion,
    isComparing: isLoading,

    // State
    canCompare: comparisons.length >= 2,
    count: comparisons.length
  };
}

// Enhanced hook that combines all simulation features
export function useAdvancedSimulation(pollId?: string) {
  const simulation = useEnhancedSimulation(pollId);
  const streaming = useSimulationStreaming(pollId || '');
  const config = useSimulationConfig();
  const comparison = useSimulationComparison();

  // Get feature flags
  const featureFlags = getFinalFeatureFlags();

  // Auto-stream insights when simulation completes
  const runWithInsights = async (request: SimulationRequest) => {
    const result = await new Promise<SimulationResponse>((resolve, reject) => {
      simulation.runSimulation(request, {
        onSuccess: resolve,
        onError: reject
      });
    });

    // Auto-generate insights if available
    if (streaming.isAvailable && result) {
      streaming.streamInsights(result);
    }

    return result;
  };

  return {
    // Core simulation
    ...simulation,
    runWithInsights,

    // Streaming insights
    insights: streaming.insights,
    isStreamingInsights: streaming.isStreaming,

    // Configuration
    generateConfig: config.generateConfig,
    generatedConfig: config.config,
    isGeneratingConfig: config.isGenerating,

    // Comparison
    addToComparison: comparison.addSimulation,
    compareAll: comparison.compareSimulations,
    comparisonAnalysis: comparison.comparisonAnalysis,
    comparisonCount: comparison.count,

    // Feature availability
    features: {
      enhanced: featureFlags.enhancedSimulation,
      batch: featureFlags.batchSimulationProcessing,
      insights: featureFlags.simulationInsights,
      demographic: featureFlags.demographicAnalysis,
      structured: featureFlags.structuredGeneration
    }
  };
}
