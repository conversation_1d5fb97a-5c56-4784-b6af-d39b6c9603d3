import { BatchSimulationProgress } from '@/lib/types/simulation';

// In-memory store for simulation progress
// In a production app, this would be replaced with Redis or another distributed store
const progressStore: Record<string, BatchSimulationProgress> = {};

/**
 * Update progress for a specific simulation
 * @param id Simulation ID
 * @param progress Progress data
 */
export function updateProgress(id: string, progress: BatchSimulationProgress) {
  progressStore[id] = progress;
  
  // Clean up completed or failed simulations after 5 minutes
  if (progress.status === 'completed' || progress.status === 'failed') {
    setTimeout(() => {
      delete progressStore[id];
    }, 5 * 60 * 1000);
  }
}

/**
 * Get progress for a specific simulation
 * @param id Simulation ID
 * @returns Progress data or null if not found
 */
export function getProgress(id: string): BatchSimulationProgress | null {
  return progressStore[id] || null;
}
