/**
 * Statistical measures for analyzing poll data
 */
interface StatisticalMeasures {
  mean: number;
  median: number;
  mode: { value: string; count: number }[];
  standardDeviation: number;
  variance: number;
  range: number;
  entropy: number;
  giniCoefficient: number;
}

/**
 * Result item with a value property
 */
interface ResultItem {
  value: number;
  [key: string]: unknown;
}

/**
 * Calculates basic statistical measures for numerical data
 * @param data Array of numerical values
 * @returns Statistical measures
 */
const calculateStatistics = (data: number[]): StatisticalMeasures => {
  // Ensure we have data to analyze
  if (!data.length) {
    return {
      mean: 0,
      median: 0,
      mode: [],
      standardDeviation: 0,
      variance: 0,
      range: 0,
      entropy: 0,
      giniCoefficient: 0
    };
  }

  // Sort the data for calculations
  const sortedData = [...data].sort((a, b) => a - b);

  // Calculate mean (average)
  const sum = data.reduce((acc, val) => acc + val, 0);
  const mean = sum / data.length;

  // Calculate median (middle value)
  const middle = Math.floor(sortedData.length / 2);
  const median = sortedData.length % 2 === 0
    ? (sortedData[middle - 1] + sortedData[middle]) / 2
    : sortedData[middle];

  // Calculate mode (most frequent value(s))
  const frequency: Record<string, number> = {};
  data.forEach(value => {
    frequency[value] = (frequency[value] || 0) + 1;
  });

  let maxFrequency = 0;
  const mode: { value: string; count: number }[] = [];

  Object.entries(frequency).forEach(([value, count]) => {
    if (count > maxFrequency) {
      maxFrequency = count;
      mode.length = 0;
      mode.push({ value, count });
    } else if (count === maxFrequency) {
      mode.push({ value, count });
    }
  });

  // Calculate variance and standard deviation
  const squaredDifferences = data.map(value => Math.pow(value - mean, 2));
  const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / data.length;
  const standardDeviation = Math.sqrt(variance);

  // Calculate range (difference between max and min)
  const range = sortedData[sortedData.length - 1] - sortedData[0];

  // Calculate entropy (measure of uncertainty)
  let entropy = 0;
  const total = data.length;

  Object.values(frequency).forEach(count => {
    const probability = count / total;
    if (probability > 0) {
      entropy -= probability * Math.log2(probability);
    }
  });

  // Calculate Gini coefficient (measure of inequality)
  let absoluteDifferencesSum = 0;
  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < data.length; j++) {
      absoluteDifferencesSum += Math.abs(data[i] - data[j]);
    }
  }

  const giniCoefficient = absoluteDifferencesSum / (2 * data.length * data.length * mean);

  return {
    mean,
    median,
    mode,
    standardDeviation,
    variance,
    range,
    entropy,
    giniCoefficient
  };
};

/**
 * Generates statistical insights based on response data
 */
export const generateStatisticalInsights = (
  results: ResultItem[],
  totalResponses: number,
  questionType: string
): string[] => {
  const insights: string[] = [];

  // Extract values for statistical analysis
  const values = results.map(r => r.value);
  if (!values.length) return insights;

  // Calculate statistics
  const stats = calculateStatistics(values);

  // Determine distribution characteristics
  const isSkewed = Math.abs(stats.mean - stats.median) > stats.standardDeviation * 0.5;
  const isHighVariance = stats.standardDeviation > stats.mean * 0.5;
  const isBimodal = stats.mode.length > 1;
  const isFlat = stats.entropy > Math.log2(results.length) * 0.8;
  const isConcentrated = stats.giniCoefficient > 0.6;

  // Generate insights based on distribution characteristics
  if (questionType !== 'open') {
    // Distribution insights
    if (isConcentrated) {
      insights.push("Responses are concentrated around a few popular options.");
    } else if (isFlat) {
      insights.push("Responses are fairly evenly distributed across all options.");
    }

    if (isSkewed) {
      const direction = stats.mean > stats.median ? "higher" : "lower";
      insights.push(`The distribution is skewed with some ${direction} values affecting the average.`);
    }

    if (isHighVariance) {
      insights.push("There's significant variability in the responses, indicating diverse opinions.");
    }

    if (isBimodal) {
      insights.push("Multiple distinct groups of respondents with different preferences are apparent.");
    }
  }

  return insights;
};

/**
 * Identifies outliers in the dataset using IQR method
 */
export const identifyOutliers = (results: ResultItem[]): number[] => {
  const values = results.map(r => r.value).sort((a, b) => a - b);
  if (values.length < 4) return [];

  const q1Index = Math.floor(values.length * 0.25);
  const q3Index = Math.floor(values.length * 0.75);

  const q1 = values[q1Index];
  const q3 = values[q3Index];

  const iqr = q3 - q1;
  const lowerBound = q1 - iqr * 1.5;
  const upperBound = q3 + iqr * 1.5;

  const outlierIndices: number[] = [];
  results.forEach((result, index) => {
    if (result.value < lowerBound || result.value > upperBound) {
      outlierIndices.push(index);
    }
  });

  return outlierIndices;
};
