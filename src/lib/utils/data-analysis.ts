import { ChartDataPoint, ProcessedQuestion } from '@/lib/validation/schemas';

/**
 * Poll data interface
 */
interface PollDataForAnalysis {
  completedResponses: number;
  completionRate: string;
  demographics?: {
    devices?: Array<{name: string; value: number}>;
    regions?: Array<{name: string; value: number}>;
  };
  responsesByDate?: Array<{ date: string; count: number }>;
  [key: string]: unknown;
}

/**
 * Analyzes question data to extract insights
 */
export function analyzeQuestionData(question: ProcessedQuestion, totalResponses: number): string[] {
  const insights: string[] = [];

  if (question.type === 'open') {
    return []; // Open-ended question analysis requires NLP, skipping for now
  }

  const data = question.results as ChartDataPoint[];

  // Early return if no meaningful data
  if (!data || data.length === 0 || totalResponses === 0) {
    return [];
  }

  // Find highest and lowest values
  const sortedData = [...data].sort((a, b) => b.value - a.value);
  const highest = sortedData[0];

  // Calculate percentages
  const highestPercentage = ((highest.value / totalResponses) * 100).toFixed(1);

  // Check for majority (>50%)
  if (parseFloat(highestPercentage) > 50) {
    insights.push(`A majority (${highestPercentage}%) selected "${highest.name}".`);
  } else if (parseFloat(highestPercentage) > 30) {
    insights.push(`"${highest.name}" is the most popular choice at ${highestPercentage}%.`);
  }

  // Check for close results (within 5%)
  if (data.length >= 2) {
    const topTwo = sortedData.slice(0, 2);
    const diff = ((topTwo[0].value - topTwo[1].value) / totalResponses) * 100;

    if (diff < 5) {
      insights.push(
        `"${topTwo[0].name}" and "${topTwo[1].name}" are very close (${diff.toFixed(1)}% difference).`
      );
    }
  }

  // Check for low engagement options
  const lowOptions = data.filter(
    d => (d.value / totalResponses) < 0.1 && d.value > 0
  );

  if (lowOptions.length > 0 && lowOptions.length < data.length / 2) {
    if (lowOptions.length === 1) {
      insights.push(`"${lowOptions[0].name}" received minimal selection (${((lowOptions[0].value / totalResponses) * 100).toFixed(1)}%).`);
    } else {
      insights.push(`${lowOptions.length} options received less than 10% selection each.`);
    }
  }

  // Check for even distribution
  const mean = totalResponses / data.length;
  const deviation = data.reduce((sum, d) => sum + Math.abs(d.value - mean), 0) / data.length;
  const normalizedDeviation = deviation / mean;

  if (normalizedDeviation < 0.2 && data.length > 2) {
    insights.push('Responses are fairly evenly distributed across all options.');
  }

  // Special insights for NPS questions
  if (question.npsScore !== undefined) {
    const npsScore = question.npsScore;

    if (npsScore >= 50) {
      insights.push(`Strong NPS score of ${npsScore}, showing positive sentiment.`);
    } else if (npsScore >= 0) {
      insights.push(`Moderate NPS score of ${npsScore}, room for improvement.`);
    } else {
      insights.push(`Negative NPS score of ${npsScore}, indicates potential concerns.`);
    }
  }

  return insights;
}

/**
 * Cleans and preprocesses open-ended responses
 */
export function preprocessOpenEndedResponses(responses: string[]): string[] {
  if (!responses || responses.length === 0) return [];

  return responses.map(response => {
    // Basic preprocessing steps
    let cleaned = response.trim();

    // Remove excessive spaces
    cleaned = cleaned.replace(/\s+/g, ' ');

    // Remove common special characters not needed
    cleaned = cleaned.replace(/[^\w\s.,!?;:'"()]/g, '');

    return cleaned;
  });
}

/**
 * Extracts common themes from open-ended responses
 * Note: This is a simplified version - a real implementation would use NLP techniques
 */
export function extractCommonThemes(responses: string[]): { theme: string, count: number }[] {
  if (!responses || responses.length === 0) return [];

  // Simple keyword-based approach
  const keywords = [
    'price', 'cost', 'expensive', 'cheap',
    'quality', 'good', 'great', 'excellent', 'poor', 'bad',
    'service', 'support', 'help',
    'feature', 'functionality',
    'easy', 'difficult', 'hard', 'simple',
    'recommend', 'suggestion'
  ];

  const themes: Record<string, number> = {};

  // Count occurrences of keywords
  responses.forEach(response => {
    const lowercased = response.toLowerCase();

    keywords.forEach(keyword => {
      if (lowercased.includes(keyword)) {
        // Map to theme groups
        let theme = keyword;
        if (['price', 'cost', 'expensive', 'cheap'].includes(keyword)) {
          theme = 'Price/Cost';
        } else if (['quality', 'good', 'great', 'excellent', 'poor', 'bad'].includes(keyword)) {
          theme = 'Quality';
        } else if (['service', 'support', 'help'].includes(keyword)) {
          theme = 'Service/Support';
        } else if (['easy', 'difficult', 'hard', 'simple'].includes(keyword)) {
          theme = 'Ease of Use';
        } else if (['feature', 'functionality'].includes(keyword)) {
          theme = 'Features';
        } else if (['recommend', 'suggestion'].includes(keyword)) {
          theme = 'Recommendations';
        }

        themes[theme] = (themes[theme] || 0) + 1;
      }
    });
  });

  // Convert to array and sort
  return Object.entries(themes)
    .map(([theme, count]) => ({ theme, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Detects trends in time-series data
 */
export function detectTrends(timeSeriesData: Array<{ date: string, count: number }>): string[] {
  const insights: string[] = [];

  if (!timeSeriesData || timeSeriesData.length < 3) {
    return insights;
  }

  // Sort by date
  const sortedData = [...timeSeriesData].sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Check for overall trend
  let increases = 0;
  let decreases = 0;

  for (let i = 1; i < sortedData.length; i++) {
    if (sortedData[i].count > sortedData[i-1].count) {
      increases++;
    } else if (sortedData[i].count < sortedData[i-1].count) {
      decreases++;
    }
  }

  const totalChanges = increases + decreases;

  if (totalChanges > 0) {
    // Determine overall trend
    if (increases / totalChanges > 0.7) {
      insights.push('Response rate shows a strong upward trend over time.');
    } else if (increases / totalChanges > 0.5) {
      insights.push('Response rate shows a general upward trend over time.');
    } else if (decreases / totalChanges > 0.7) {
      insights.push('Response rate shows a significant downward trend over time.');
    } else if (decreases / totalChanges > 0.5) {
      insights.push('Response rate shows a slight downward trend over time.');
    } else {
      insights.push('Response rate fluctuates without a clear trend.');
    }
  }

  // Check for peaks
  const max = Math.max(...sortedData.map(d => d.count));
  const maxDateIndex = sortedData.findIndex(d => d.count === max);

  if (maxDateIndex !== -1) {
    const maxDate = new Date(sortedData[maxDateIndex].date);
    insights.push(`Peak response rate occurred on ${maxDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}.`);
  }

  // Check for recency effect
  const recent = sortedData.slice(-3);
  const recentAvg = recent.reduce((sum, d) => sum + d.count, 0) / recent.length;

  const earlier = sortedData.slice(0, -3);

  if (earlier.length > 0) {
    const earlierAvg = earlier.reduce((sum, d) => sum + d.count, 0) / earlier.length;

    if (recentAvg > earlierAvg * 1.5) {
      insights.push('Recent days show significantly higher response rates than earlier periods.');
    } else if (recentAvg < earlierAvg * 0.5) {
      insights.push('Response rates have declined substantially in recent days.');
    }
  }

  return insights;
}

/**
 * Generates advanced AI insights from poll data
 */
export function generateAdvancedInsights(pollData: PollDataForAnalysis): string[] {
  const insights: string[] = [];

  // Add device insights
  if (pollData.demographics?.devices && pollData.demographics.devices.length > 0) {
    const devices = pollData.demographics.devices;
    const mostCommonDevice = [...devices].sort((a, b) => b.value - a.value)[0];
    const mobileDevices = devices.filter(d =>
      ['mobile', 'phone', 'android', 'ios', 'iphone'].includes(d.name.toLowerCase())
    );
    const desktopDevices = devices.filter(d =>
      ['desktop', 'pc', 'mac', 'laptop', 'computer'].includes(d.name.toLowerCase())
    );

    const mobileTotal = mobileDevices.reduce((sum, d) => sum + d.value, 0);
    const desktopTotal = desktopDevices.reduce((sum, d) => sum + d.value, 0);

    if (mostCommonDevice) {
      insights.push(`Most participants (${((mostCommonDevice.value / pollData.completedResponses) * 100).toFixed(1)}%) used ${mostCommonDevice.name} devices.`);
    }

    if (mobileTotal > 0 && desktopTotal > 0) {
      if (mobileTotal > desktopTotal) {
        insights.push(`Mobile users (${((mobileTotal / pollData.completedResponses) * 100).toFixed(1)}%) outnumber desktop users (${((desktopTotal / pollData.completedResponses) * 100).toFixed(1)}%).`);
      } else {
        insights.push(`Desktop users (${((desktopTotal / pollData.completedResponses) * 100).toFixed(1)}%) outnumber mobile users (${((mobileTotal / pollData.completedResponses) * 100).toFixed(1)}%).`);
      }
    }
  }

  // Add completion rate insights
  if (pollData.completionRate) {
    const completionRateValue = parseInt(pollData.completionRate);

    if (completionRateValue > 80) {
      insights.push(`Excellent completion rate of ${pollData.completionRate} indicates strong engagement.`);
    } else if (completionRateValue < 40) {
      insights.push(`Low completion rate of ${pollData.completionRate} suggests potential issues with poll length or complexity.`);
    }
  }

  // Add regional insights
  if (pollData.demographics?.regions && pollData.demographics.regions.length > 0) {
    const regions = pollData.demographics.regions;
    const topRegions = [...regions]
      .sort((a, b) => b.value - a.value)
      .slice(0, 2);

    if (topRegions.length >= 2) {
      const combinedPercentage = ((topRegions[0].value + topRegions[1].value) / pollData.completedResponses * 100).toFixed(1);

      if (parseFloat(combinedPercentage) > 70) {
        insights.push(`${topRegions[0].name} and ${topRegions[1].name} account for ${combinedPercentage}% of all responses.`);
      }
    }
  }

  // Add time-based insights
  if (pollData.responsesByDate && pollData.responsesByDate.length > 0) {
    const trendInsights = detectTrends(pollData.responsesByDate);
    insights.push(...trendInsights);
  }

  return insights;
}
