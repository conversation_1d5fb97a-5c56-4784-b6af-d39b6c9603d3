/**
 * Chart theme management for consistent colors across visualizations
 */

// Define theme color palettes
type ThemePalette = {
  name: string;
  colors: string[];
  description: string;
};

export const CHART_THEMES: Record<string, ThemePalette> = {
  default: {
    name: "Default",
    colors: ['#4285F4', '#34A853', '#8A4BFF', '#FBBC05', '#EA4335', '#22d3ee', '#0ea5e9', '#4f46e5'],
    description: "Google/Material inspired palette"
  },
  pastels: {
    name: "Pastels",
    colors: ['#9DB4FF', '#96E9C6', '#DBC4FF', '#FFF599', '#FFB499', '#94EBFF', '#A9F8FB', '#D5F2B6'],
    description: "Soft pastel colors"
  },
  ocean: {
    name: "Ocean",
    colors: ['#086788', '#07A0C3', '#0ACDFF', '#6699CC', '#24345C', '#00A8E8', '#0077B6', '#03045E'],
    description: "Blue and teal ocean theme"
  },
  sunset: {
    name: "Sunset",
    colors: ['#F9A826', '#E76F51', '#F4845F', '#F79D65', '#F9C74F', '#ED7B84', '#F18805', '#D62828'],
    description: "Warm orange and red hues"
  },
  forest: {
    name: "Forest",
    colors: ['#2D6A4F', '#52B788', '#74C69D', '#40916C', '#95D5B2', '#1B4332', '#386641', '#588157'],
    description: "Green forest-inspired palette"
  },
  berry: {
    name: "Berry",
    colors: ['#9D4EDD', '#C77DFF', '#7B2CBF', '#5A189A', '#3C096C', '#FF5A5F', '#B5179E', '#480CA8'],
    description: "Purple and pink berry tones"
  },
  contrast: {
    name: "High Contrast",
    colors: ['#0466C8', '#F48C06', '#D00000', '#03071E', '#4CC9F0', '#7209B7', '#6A994E', '#1B263B'],
    description: "High contrast palette for accessibility"
  }
};

// Default theme to use
let currentThemeName = 'default';

/**
 * Get the current theme's color palette
 */
export function getThemeColors(): string[] {
  return CHART_THEMES[currentThemeName].colors;
}

/**
 * Change the current theme by name
 */
export function setChartTheme(themeName: string): boolean {
  if (CHART_THEMES[themeName]) {
    currentThemeName = themeName;
    return true;
  }
  return false;
}

/**
 * Get the current theme name
 */
export function getCurrentTheme(): string {
  return currentThemeName;
}

/**
 * Get all available theme names
 */
export function getAvailableThemes(): { name: string; description: string }[] {
  return Object.values(CHART_THEMES).map(theme => ({
    name: theme.name,
    description: theme.description
  }));
}

/**
 * Generate a consistent color for a specific category across charts
 * This ensures that the same category always gets the same color
 */
export function getCategoryColor(categoryName: string | undefined | null): string {
  // Handle undefined, null, or empty categoryName
  if (!categoryName || typeof categoryName !== 'string') {
    // Return first color as default for invalid input
    const colors = getThemeColors();
    return colors[0];
  }

  // Use hash function to get a consistent index for the category
  const hash = categoryName.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);

  const colors = getThemeColors();
  const index = Math.abs(hash) % colors.length;
  return colors[index];
}

/**
 * Apply a theme-appropriate gradient to a chart
 */
export function getThemeGradient(baseColor: string, id: string): { id: string; stops: { offset: number; color: string; opacity: number }[] } {
  return {
    id,
    stops: [
      { offset: 0.05, color: baseColor, opacity: 0.8 },
      { offset: 0.95, color: baseColor, opacity: 0.1 }
    ]
  };
}
