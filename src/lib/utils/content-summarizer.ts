import { google } from '@ai-sdk/google';
import { generateText } from 'ai';

export interface SummarizeOptions {
  maxLength?: number;
  focus?: 'general' | 'poll-creation' | 'key-points';
  sourceType?: 'pdf' | 'url' | 'website' | 'document';
}

export async function summarizeContent(
  content: string,
  options: SummarizeOptions = {}
): Promise<string> {
  const { maxLength = 150, focus = 'poll-creation', sourceType = 'document' } = options;

  if (!content || content.trim().length < 50) {
    return content.trim();
  }

  try {
    const prompt = `Summarize the following ${sourceType} content in ${maxLength} words or less. Focus on ${focus === 'poll-creation' ? 'key topics and themes that would be useful for creating survey questions' : 'the main points and key information'}:

${content.substring(0, 4000)}

Summary:`;

    const { text } = await generateText({
      model: google('gemini-2.0-flash'),
      prompt,
      maxTokens: <PERSON>.ceil(maxLength * 1.5),
      temperature: 0.3,
    });

    return text.trim();
  } catch (error) {
    console.error('Failed to summarize content:', error);
    // Fallback: return first 150 words
    const words = content.split(' ').slice(0, Math.ceil(maxLength / 6));
    return words.join(' ') + (words.length < content.split(' ').length ? '...' : '');
  }
}
