import puppeteer from 'puppeteer';

/**
 * Scrapes content from a URL using Puppeteer
 * @param url The URL to scrape
 * @param selector Optional CSS selector to target specific content
 * @returns The scraped content
 */
export async function scrapeWithPuppeteer(url: string, selector?: string): Promise<string> {
  const browser = await puppeteer.launch({
    headless: true,
  });

  try {
    const page = await browser.newPage();

    // Set viewport size
    await page.setViewport({ width: 1280, height: 800 });

    // Navigate to the URL
    await page.goto(url, { waitUntil: 'networkidle0' });

    // Extract content based on selector or get entire body content
    let content: string;
    if (selector) {
      // Wait for the selector to be available
      await page.waitForSelector(selector);
      content = await page.$eval(selector, el => el.textContent || '');
    } else {
      content = await page.content();
    }

    return content;
  } finally {
    await browser.close();
  }
}

/**
 * Takes a screenshot of a webpage using Puppeteer
 * @param url The URL to take a screenshot of
 * @param outputPath The path to save the screenshot to (must end with .png, .jpeg, or .webp)
 * @param fullPage Whether to take a full page screenshot
 */
export async function takeScreenshotWithPuppeteer(
  url: string,
  outputPath: `${string}.png` | `${string}.jpeg` | `${string}.webp`,
  fullPage: boolean = false
): Promise<void> {
  const browser = await puppeteer.launch({
    headless: true,
  });

  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });
    await page.goto(url, { waitUntil: 'networkidle0' });
    await page.screenshot({ path: outputPath, fullPage });
  } finally {
    await browser.close();
  }
}

/**
 * Generates a PDF of a webpage using Puppeteer
 * @param url The URL to generate a PDF of
 * @param outputPath The path to save the PDF to
 */
export async function generatePdf(url: string, outputPath: string): Promise<void> {
  const browser = await puppeteer.launch({
    headless: true,
  });

  try {
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });
    await page.pdf({ path: outputPath, format: 'A4' });
  } finally {
    await browser.close();
  }
}

/**
 * Executes a script on a webpage and returns the result
 * @param url The URL to execute the script on
 * @param script The script to execute
 * @returns The result of the script execution
 */
export async function executeScript<T>(url: string, script: string): Promise<T> {
  const browser = await puppeteer.launch({
    headless: true,
  });

  try {
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });
    const result = await page.evaluate(script);
    return result as T;
  } finally {
    await browser.close();
  }
}