// session-manager.ts
// Centralized utility for session management across the application
import { toast } from "sonner";
import { refreshAuthSession, isSessionValid } from "./auth-refresh";
import { authRateTracker } from "./auth-rate-tracker";
import { supabase } from "@/lib/supabase";

/**
 * Options for session validation
 */
interface SessionValidationOptions {
  forceRefresh?: boolean;
  showToasts?: boolean;
  redirectOnFailure?: boolean;
}

/**
 * Session expiration pre-check
 * Helps prevent authentication failures by proactively refreshing session
 * @param options Configuration options for session validation
 * @returns Promise resolving to boolean indicating if session is valid and ready for operations
 */
export async function ensureValidSession(options?: SessionValidationOptions): Promise<boolean> {
  const opts = {
    forceRefresh: false,
    showToasts: true,
    redirectOnFailure: true,
    ...options
  };

  try {
    // If not forcing refresh, first check if we have a valid session
    if (!opts.forceRefresh && await isSessionValid()) {
      return true;
    }

    // No session, expired session, or force refresh requested
    console.log("Session refresh needed, refreshing...");

    // Show toast only if enabled
    if (opts.showToasts) {
      toast.loading("Refreshing your session...");
    }

    const refreshResult = await refreshAuthSession();

    if (!refreshResult.success) {
      console.warn("Session refresh failed:", refreshResult.error);

      if (opts.showToasts) {
        toast.error("Your session has expired. Please log in again.");
      }

      // Redirect to login after short delay if enabled
      if (opts.redirectOnFailure) {
        setTimeout(() => {
          window.location.href = "/login";
        }, 2000);
      }

      return false;
    }

    // Success
    if (opts.showToasts) {
      toast.success("Session refreshed successfully");
    }

    return true;
  } catch (error) {
    console.error("Error ensuring session validity:", error);

    if (opts.showToasts) {
      toast.error("Unable to verify your session. Please refresh the page.");
    }

    return false;
  }
}

/**
 * Safely executes an API operation with automatic session refresh on auth failures
 * @param operation Function that performs the API operation
 * @param maxRetries Maximum number of retries (default 1)
 * @returns Promise resolving to the operation result
 */
export async function withAuthRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 1
): Promise<T> {
  // First try the operation directly
  try {
    return await operation();
  } catch (error) {
    // Check if this appears to be an auth error
    const err = error as Error;
    const isAuthError =
      err.message?.includes("auth") ||
      err.message?.includes("session") ||
      err.message?.includes("JWT") ||
      err.message?.includes("token") ||
      err.message?.includes("unauthorized") ||
      err.message?.includes("permission denied");

    if (!isAuthError || maxRetries <= 0) {
      // Not an auth error or no retries left, rethrow
      throw error;
    }

    // Attempt to refresh the session
    console.log("Auth error detected, refreshing session before retry:", err.message);
    const { success } = await refreshAuthSession();

    if (!success) {
      // Session refresh failed, rethrow original error
      throw error;
    }

    // Retry the operation with decremented retry count
    return withAuthRetry(operation, maxRetries - 1);
  }
}

/**
 * Get current user information with error handling
 * @returns Object containing user data or null and any error
 */
export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  } catch (error) {
    console.error("Error getting current user:", error);
    return { user: null, error: error as Error };
  }
}

/**
 * Check if rate limits are being approached
 * @returns Information about current rate limit status
 */
export function checkRateLimits() {
  const refreshLimit = authRateTracker.checkRefreshRateLimit();
  const verifyLimit = authRateTracker.checkVerifyRateLimit();

  return {
    refreshLimit,
    verifyLimit,
    canRefresh: authRateTracker.canRefreshToken(),
    timeUntilNextRefresh: authRateTracker.getTimeUntilNextAllowedRefresh()
  };
}
