/**
 * Utility function to get the correct OAuth redirect URL
 * Handles both development and production environments
 */

export function getOAuthRedirectUrl(path = '/auth/callback'): string {
  // Client-side: use current origin
  if (typeof window !== 'undefined') {
    return `${window.location.origin}${path}`;
  }

  // Server-side: check environment variables
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  if (siteUrl) {
    return `${siteUrl}${path}`;
  }

  // Fallback based on environment
  const isProduction = process.env.NODE_ENV === 'production';
  const defaultUrl = isProduction
    ? 'https://www.pollgpt.com'
    : 'http://localhost:3000';

  return `${defaultUrl}${path}`;
}

/**
 * Get base site URL for email redirects and other purposes
 */
export function getSiteUrl(): string {
  // Client-side: use current origin
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Server-side: check environment variables
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  if (siteUrl) {
    return siteUrl;
  }

  // Fallback based on environment
  const isProduction = process.env.NODE_ENV === 'production';
  return isProduction
    ? 'https://www.pollgpt.com'
    : 'http://localhost:3000';
}
