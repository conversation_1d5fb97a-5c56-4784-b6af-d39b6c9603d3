/**
 * Single Source of Truth for User ID Access
 * Implements the multi-layered storage approach for instant user ID access
 */

import { supabase } from '@/lib/supabase';

// Global user ID storage for instant access
let globalUserId: string | null = null;
let globalUserEmail: string | null = null;

// Performance tracking
let authCallCount = 0;
let lastAuthCall = 0;

/**
 * Get user ID with priority order lookup
 * 1. Global variable (fastest)
 * 2. localStorage
 * 3. sessionStorage
 * 4. Auth API call (slowest)
 */
export const getUserId = (): string | null => {
  // 1. Check global variable first (fastest)
  if (globalUserId) {
    return globalUserId;
  }

  // Check if we're on the client side
  if (typeof window === 'undefined') {
    return null; // SSR - no browser APIs available
  }

  // 2. Check localStorage
  try {
    const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
    const storedAuth = localStorage.getItem(authKey);
    if (storedAuth) {
      const parsed = JSON.parse(storedAuth);
      if (parsed?.currentSession?.user?.id) {
        globalUserId = parsed.currentSession.user.id;
        globalUserEmail = parsed.currentSession.user.email;
        return globalUserId;
      }
    }
  } catch (e) {
    console.warn('Error parsing localStorage auth:', e);
  }

  // 3. Check sessionStorage
  try {
    const sessionAuth = sessionStorage.getItem('sb-sumruaeyfidjlssrmfrm-auth-token');
    if (sessionAuth) {
      const parsed = JSON.parse(sessionAuth);
      if (parsed?.currentSession?.user?.id) {
        globalUserId = parsed.currentSession.user.id;
        globalUserEmail = parsed.currentSession.user.email;
        return globalUserId;
      }
    }
  } catch (e) {
    console.warn('Error parsing sessionStorage auth:', e);
  }

  // 4. No sync access available
  return null;
};

/**
 * Get user ID with async fallback to Auth API
 */
export const getUserIdAsync = async (): Promise<string | null> => {
  // Try sync methods first
  const syncUserId = getUserId();
  if (syncUserId) return syncUserId;

  // Fallback to Auth API
  try {
    authCallCount++;
    lastAuthCall = Date.now();

    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      console.error('Error fetching user:', error);
      return null;
    }

    if (user?.id) {
      globalUserId = user.id;
      globalUserEmail = user.email;
      return user.id;
    }
  } catch (e) {
    console.error('Exception fetching user:', e);
  }

  return null;
};

/**
 * Get user email with same priority order
 */
export const getUserEmail = (): string | null => {
  if (globalUserEmail) return globalUserEmail;

  // Trigger ID lookup which will also set email
  getUserId();
  return globalUserEmail;
};

/**
 * Set user ID and email (called by auth provider)
 */
export const setUserData = (userId: string | null, userEmail: string | null) => {
  globalUserId = userId;
  globalUserEmail = userEmail;

  // Also store in window for cross-tab access
  if (typeof window !== 'undefined') {
    (window as typeof window & { __pollgpt_user_id?: string | null; __pollgpt_user_email?: string | null }).__pollgpt_user_id = userId;
    (window as typeof window & { __pollgpt_user_id?: string | null; __pollgpt_user_email?: string | null }).__pollgpt_user_email = userEmail;
  }
};

/**
 * Clear user data (called on logout)
 */
export const clearUserData = () => {
  globalUserId = null;
  globalUserEmail = null;

  if (typeof window !== 'undefined') {
    const win = window as typeof window & { __pollgpt_user_id?: string | null; __pollgpt_user_email?: string | null };
    delete win.__pollgpt_user_id;
    delete win.__pollgpt_user_email;
  }
};

/**
 * Check if user is authenticated (sync)
 */
export const isAuthenticated = (): boolean => {
  return getUserId() !== null;
};

/**
 * Get performance metrics
 */
export const getAuthPerformanceMetrics = () => {
  return {
    authCallCount,
    lastAuthCall,
    hasGlobalUserId: !!globalUserId,
    timeSinceLastAuth: Date.now() - lastAuthCall,
  };
};

/**
 * Strategic auth method usage helper
 */
export const getAuthStrategy = (operation: 'read' | 'write' | 'sensitive') => {
  switch (operation) {
    case 'read':
      // Use getSession for read operations (faster)
      return 'getSession';
    case 'write':
    case 'sensitive':
      // Use getUser for write/sensitive operations (more secure)
      return 'getUser';
    default:
      return 'getSession';
  }
};
