/**
 * Rate limiting utility for simulation API endpoints
 * Helps manage API usage and prevent abuse
 */

// In-memory store for rate limiting
// In a production app, this would be replaced with Redis or another distributed store
const rateLimitStore: Record<string, { count: number, resetAt: number }> = {};

/**
 * Check if a user has exceeded their rate limit for simulations
 * @param userId User ID to check
 * @param limit Maximum number of requests allowed in the time window
 * @param windowSeconds Time window in seconds
 * @returns Boolean indicating if the request is allowed
 */
export async function checkSimulationRateLimit(
  userId: string,
  limit: number = 5,
  windowSeconds: number = 3600 // 1 hour default
): Promise<boolean> {
  const key = `simulation_${userId}`;
  const now = Date.now();
  
  // Get or initialize rate limit data
  const rateData = rateLimitStore[key] || { count: 0, resetAt: now + (windowSeconds * 1000) };
  
  // Reset if window has expired
  if (now > rateData.resetAt) {
    rateData.count = 0;
    rateData.resetAt = now + (windowSeconds * 1000);
  }
  
  // Check if limit exceeded
  if (rateData.count >= limit) {
    return false;
  }
  
  // Increment count and update store
  rateData.count++;
  rateLimitStore[key] = rateData;
  
  return true;
}

/**
 * Get remaining requests for a user
 * @param userId User ID to check
 * @param limit Maximum number of requests allowed in the time window
 * @returns Number of remaining requests and reset time
 */
export function getRemainingSimulationRequests(
  userId: string,
  limit: number = 5
): { remaining: number, resetAt: Date } {
  const key = `simulation_${userId}`;
  const now = Date.now();
  const rateData = rateLimitStore[key] || { count: 0, resetAt: now + 3600000 };
  
  // Reset if window has expired
  if (now > rateData.resetAt) {
    return { 
      remaining: limit, 
      resetAt: new Date(now + 3600000)
    };
  }
  
  return {
    remaining: Math.max(0, limit - rateData.count),
    resetAt: new Date(rateData.resetAt)
  };
}
