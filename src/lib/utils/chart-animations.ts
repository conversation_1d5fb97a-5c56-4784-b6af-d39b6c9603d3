/**
 * Chart animation utilities for enhancing visual presentation
 */

/**
 * Configuration for chart animations
 */
interface AnimationConfig {
  animate: boolean;
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  delay: number;
}

/**
 * Default animation configuration
 */
const defaultAnimationConfig: AnimationConfig = {
  animate: true,
  duration: 800,
  easing: 'ease',
  delay: 0
};

/**
 * Get appropriate animation config based on data size and device capabilities
 * @param dataSize Number of data points
 * @param isMobile Whether the device is mobile
 * @returns Animation configuration
 */
export const getAnimationConfig = (
  dataSize: number,
  isMobile = false
): AnimationConfig => {
  // Disable animations on mobile devices with large datasets to improve performance
  if (isMobile && dataSize > 20) {
    return { ...defaultAnimationConfig, animate: false };
  }

  // For very large datasets, reduce animation duration
  if (dataSize > 50) {
    return {
      ...defaultAnimationConfig,
      duration: 500,
      easing: 'linear'
    };
  }

  // For medium datasets, use standard animations
  if (dataSize > 10) {
    return defaultAnimationConfig;
  }

  // For small datasets, use slightly longer animations for better effect
  return {
    ...defaultAnimationConfig,
    duration: 1200,
    easing: 'ease-out'
  };
};

/**
 * Determine if staggered animations should be used based on chart type
 * @param chartType Type of chart
 * @param dataSize Number of data points
 * @returns Whether to use staggered animations
 */
export const useStaggeredAnimations = (
  chartType: string,
  dataSize: number
): boolean => {
  // Bar and pie charts benefit from staggered animations when not too many data points
  if ((chartType === 'bar' || chartType === 'pie') && dataSize <= 10) {
    return true;
  }

  return false;
};

/**
 * Calculate stagger delay for animations
 * @param index Index of the data point
 * @param total Total number of data points
 * @returns Delay in milliseconds
 */
export const calculateStaggerDelay = (index: number, total: number): number => {
  // Maximum total stagger time (ms)
  const maxTotalStagger = 500;

  // Calculate delay based on position in sequence
  return (index / total) * maxTotalStagger;
};

/**
 * Generate responsive animation settings based on viewport size
 * @returns Animation settings adjusted for viewport
 */
export const getResponsiveAnimationSettings = (): AnimationConfig => {
  // Only run this in browser environment
  if (typeof window === 'undefined') {
    return defaultAnimationConfig;
  }

  const viewportWidth = window.innerWidth;
  const isMobile = viewportWidth < 768;
  const isTablet = viewportWidth >= 768 && viewportWidth < 1024;

  if (isMobile) {
    // Simplified animations for mobile
    return {
      animate: true,
      duration: 500,
      easing: 'ease-out',
      delay: 0
    };
  }

  if (isTablet) {
    // Moderate animations for tablets
    return {
      animate: true,
      duration: 700,
      easing: 'ease-in-out',
      delay: 0
    };
  }

  // Full animations for desktop
  return {
    animate: true,
    duration: 1000,
    easing: 'ease-out', // Using standard easing instead of cubic-bezier for type compatibility
    delay: 100
  };
};
