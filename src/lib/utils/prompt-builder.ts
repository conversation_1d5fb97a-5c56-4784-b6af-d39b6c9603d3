import { SimulationPromptData, PromptTemplate } from '../types/simulation';

// Generate a hash for caching simulation results
export async function generateSimulationHash(question: string, options: string[], demographic: string): Promise<string> {
  const content = `${question}|${options.join(',')}|${demographic}`;
  const encoder = new TextEncoder();
  const data = encoder.encode(content);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Generate demographic key for caching
export async function generateDemographicKey(group: string, size: number, context?: string): Promise<string> {
  let contextSuffix = '';
  if (context) {
    const encoder = new TextEncoder();
    const data = encoder.encode(context);
    const hashBuffer = await crypto.subtle.digest('SHA-1', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    contextSuffix = `_${hashHex.slice(0, 8)}`;
  }
  return `${group.toLowerCase().replace(/\s+/g, '_')}_${size}${contextSuffix}`;
}

// Main simulation prompt template
export const SIMULATION_PROMPT_TEMPLATE: PromptTemplate = {
  system: `You are a polling research expert conducting a simulated poll. Based on academic research and demographic data, simulate realistic poll responses for specific population groups.

Your simulations must:
1. Be based on real demographic research and statistical patterns
2. Include realistic variation within the demographic group
3. Account for known biases and response patterns
4. Provide academic citations with URLs when possible for your assumptions
5. Include confidence levels for your simulation
6. Reference credible sources like academic papers, government studies, or reputable research organizations

When providing citations, include URLs to the actual sources whenever possible. Use real academic papers, studies, and data sources that support your demographic analysis.

Always format responses as valid JSON that can be parsed programmatically.`,

  user: `Please simulate a poll for the following scenario:

**Poll Question:** {question}
**Answer Options:** {options}
**Target Demographic:** {demographic}
**Sample Size:** {sampleSize}
{context}

**Instructions:**
1. Simulate realistic response patterns based on research about this demographic
2. Provide percentage distribution across all answer options
3. Use the exact answer option text as the key in the distribution object (not generic names like option1/option2)
4. Include 3-5 individual response examples with brief reasoning
5. Cite 2-3 academic sources or studies that inform your simulation
6. Provide a confidence score (0.0-1.0) for your simulation accuracy

**Required JSON Format:**
{responseFormat}`,

  responseFormat: `{
  "simulationId": "unique_simulation_id",
  "metadata": {
    "demographic": "demographic description",
    "sampleSize": number,
    "confidence": 0.85,
    "citations": [
      "Author (Year). Title. Journal/Source.",
      "Author (Year). Title. Journal/Source."
    ]
  },
  "results": {
    "distribution": {
      "Very Satisfied": percentage,
      "Somewhat Satisfied": percentage,
      "Neutral": percentage,
      "Dissatisfied": percentage,
      "Very Dissatisfied": percentage
    },
    "individuals": [
      {
        "responseId": "resp_1",
        "selectedOptions": ["Very Satisfied"],
        "reasoning": "Brief explanation for this choice",
        "demographicContext": "Relevant demographic context"
      }
    ],
    "analysis": "Brief analysis of the simulation results and demographic patterns"
  }
}`
};

// Build the complete prompt for simulation
export function buildSimulationPrompt(data: SimulationPromptData): string {
  let userPrompt = SIMULATION_PROMPT_TEMPLATE.user;

  // Replace placeholders with actual data
  userPrompt = userPrompt.replace('{question}', data.question);
  userPrompt = userPrompt.replace('{options}', data.options.map((opt, idx) => `${idx + 1}. ${opt}`).join('\n'));
  userPrompt = userPrompt.replace('{demographic}', data.demographic);
  userPrompt = userPrompt.replace('{sampleSize}', data.sampleSize.toString());

  // Add context information
  let contextInfo = '';
  if (data.context) {
    contextInfo += `\n**Additional Context:** ${data.context}`;
  }
  if (data.pollContext) {
    contextInfo += `\n**Poll Context:** ${data.pollContext}`;
  }
  if (data.specialInstructions) {
    contextInfo += `\n**Special Instructions:** ${data.specialInstructions}`;
  }
  userPrompt = userPrompt.replace('{context}', contextInfo);

  userPrompt = userPrompt.replace('{responseFormat}', SIMULATION_PROMPT_TEMPLATE.responseFormat);

  return userPrompt;
}

// Validation functions for simulation prompts
export function validateSimulationRequest(question: string, options: string[], demographic: string, sampleSize: number): string[] {
  const errors: string[] = [];

  if (!question || question.trim().length < 10) {
    errors.push('Poll question must be at least 10 characters long');
  }

  if (!options || options.length < 2) {
    errors.push('At least 2 poll options are required');
  }

  if (options && options.some(opt => !opt || opt.trim().length < 1)) {
    errors.push('All poll options must be non-empty');
  }

  if (!demographic || demographic.trim().length < 3) {
    errors.push('Demographic group must be specified (at least 3 characters)');
  }

  if (!sampleSize || sampleSize < 10 || sampleSize > 1000) {
    errors.push('Sample size must be between 10 and 1000');
  }

  return errors;
}

// Export validateSimulationRequest as default export
export default validateSimulationRequest;

// Generate unique simulation ID
export function generateSimulationId(): string {
  return `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// --- Enhanced JSON cleaning and parsing helpers ---

function cleanJsonString(jsonString: string): string {
  if (!jsonString) return '{}';

  let cleaned = jsonString;

  // Step 1: Find the outermost JSON object if there's surrounding text
  const firstBrace = cleaned.indexOf('{');
  const lastBrace = cleaned.lastIndexOf('}');
  if (firstBrace > -1 && lastBrace > -1 && lastBrace > firstBrace) {
    cleaned = cleaned.substring(firstBrace, lastBrace + 1);
  }

  // Step 2: Fix unquoted property names - multiple passes with different patterns
  const propertyNamePatterns = [
    /([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g,
    /([{,]\s*)([^"'\s][^:\s]*[^"'\s])(\s*:)/g,
    /([{,]\s*)([^"'\s][^:]*[^"'\s])(\s*:)/g
  ];
  for (let i = 0; i < 3; i++) {
    propertyNamePatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '$1"$2"$3');
    });
  }
  cleaned = cleaned.replace(/:\s*([^"{}\[\],\s][^,}\]]*[^"{}\[\],\s])(\s*[,}\]])/g, ':"$1"$2');
  cleaned = cleaned.replace(/:\s*True(\s*[,}\]])/gi, ':true$1');
  cleaned = cleaned.replace(/:\s*False(\s*[,}\]])/gi, ':false$1');
  cleaned = cleaned.replace(/:\s*None(\s*[,}\]])/gi, ':null$1');
  return cleaned;
}

// Type guard for error property
function hasError(obj: unknown): obj is { error: unknown } {
  return typeof obj === 'object' && obj !== null && 'error' in obj;
}

// Type guard for demographic request
function isDemographicRequest(request: unknown): request is { demographic: { group?: string; size?: number } } {
  return (
    typeof request === 'object' &&
    request !== null &&
    'demographic' in request &&
    typeof (request as { demographic: unknown }).demographic === 'object' &&
    (request as { demographic: unknown }).demographic !== null
  );
}

function getDemographicFromRequest(request: unknown): string {
  if (isDemographicRequest(request) && typeof request.demographic.group === 'string') {
    return request.demographic.group;
  }
  return 'Unknown';
}

function getSampleSizeFromRequest(request: unknown): number {
  if (isDemographicRequest(request) && typeof request.demographic.size === 'number') {
    return request.demographic.size;
  }
  return 0;
}

function safeJsonParse(jsonString: string): unknown {
  if (!jsonString) return {};
  let parsedResponse: unknown;
  try {
    return JSON.parse(jsonString);
  } catch {
    const cleanedJson = cleanJsonString(jsonString);
    try {
      parsedResponse = JSON.parse(cleanedJson);
      if (hasError(parsedResponse)) {
        throw new Error(String(parsedResponse.error));
      }
      return parsedResponse;
    } catch {
      try {
        let deepCleaned = cleanedJson;
        deepCleaned = deepCleaned.replace(/:\s*(null|undefined)(\s*[,}])/gi, ':null$2');
        deepCleaned = deepCleaned.replace(/,(\s*[}\]])/g, '$1');
        parsedResponse = JSON.parse(deepCleaned);
        if (hasError(parsedResponse)) {
          throw new Error(String(parsedResponse.error));
        }
        return parsedResponse;
      } catch {
        try {
          const contentMatch = cleanedJson.match(/{([\s\S]*?)}/);
          if (!contentMatch) {
            throw new Error("Could not find valid JSON structure");
          }
          const funcBody = `try { return { ${contentMatch[1]} }; } catch(e) { return { error: e.message }; }`;
          const fallbackParse = new Function(funcBody);
          const result = fallbackParse();
          if (hasError(result)) {
            throw new Error(String(result.error));
          }
          return result;
        } catch {
          // Final fallback: static structure, no request context
          return {
            error: "Failed to parse JSON response",
            simulationId: "error_" + Date.now(),
            metadata: {
              demographic: "Unknown",
              sampleSize: 0,
              confidence: 0,
              citations: ["Error parsing response"]
            },
            results: {
              distribution: {},
              individuals: [],
              analysis: "Failed to parse simulation results"
            }
          };
        }
      }
    }
  }
}

// Define a type for the expected simulation response structure
interface SimulationResponseFallback {
  simulationId: string;
  metadata: {
    demographic: string;
    sampleSize: number;
    confidence: number;
    citations: string[];
  };
  results: {
    distribution: Record<string, number>;
    individuals: unknown[];
    analysis: string;
  };
}

export function parseSimulationResponse(response: string, request: unknown = null): unknown {
  try {
    let jsonContent = response;
    const jsonBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      jsonContent = jsonBlockMatch[1].trim();
    }
    const possibleJsonStart = jsonContent.indexOf('{');
    const possibleJsonEnd = jsonContent.lastIndexOf('}');
    if (possibleJsonStart !== -1 && possibleJsonEnd !== -1 && possibleJsonEnd > possibleJsonStart) {
      jsonContent = jsonContent.substring(possibleJsonStart, possibleJsonEnd + 1);
    }
    const cleanedJson = cleanJsonString(jsonContent);
    let parsedResponse: unknown = safeJsonParse(cleanedJson);
    try {
      const cleaned = cleanJsonString(jsonContent);
      try {
        parsedResponse = JSON.parse(cleaned);
      } catch {
        parsedResponse = JSON.parse(jsonContent);
      }
    } catch {
      let cleanedJson = jsonContent;
      let propertyNameRegex = /([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g;
      cleanedJson = cleanedJson.replace(propertyNameRegex, '$1"$2"$3');
      propertyNameRegex = /([{,]\s*)([^"'\s][^:\s]*[^"'\s])(\s*:)/g;
      cleanedJson = cleanedJson.replace(propertyNameRegex, '$1"$2"$3');
      cleanedJson = cleanedJson.replace(/:\s*'([^']*)'/g, ': "$1"');
      cleanedJson = cleanedJson.replace(/([,\[{]\s*)"(.*?)([^\\])"(\s*[,\]}])/g, '$1"$2$3"$4');
      cleanedJson = cleanedJson.replace(/}\s*{/g, '},{');
      cleanedJson = cleanedJson.replace(/,\s*[}\]]/g, '$1');
      cleanedJson = cleanedJson.replace(/:\s*"([^\"]*)\\n([^\"]*)"/g, ': "$1\\\n$2"');
      cleanedJson = cleanedJson.replace(/([{,]\s*)(\d+)(\s*:)/g, '$1"$2"$3');
      try {
        parsedResponse = JSON.parse(cleanedJson);
      } catch {
        try {
          let superCleanedJson = cleanedJson;
          for (let i = 0; i < 3; i++) {
            superCleanedJson = superCleanedJson.replace(/({|\,)[ \t\r\n]*([a-zA-Z_][a-zA-Z0-9_]*)[ \t\r\n]*:/g, '$1"$2":');
          }
          superCleanedJson = superCleanedJson.replace(/'([^']*)'(\s*:)/g, '"$1"$2');
          parsedResponse = JSON.parse(superCleanedJson);
        } catch {
          try {
            const extractSimulationId = jsonContent.match(/"simulationId"\s*:\s*"([^"]+)"/);
            const extractDemographic = jsonContent.match(/"demographic"\s*:\s*"([^"]+)"/);
            const extractSampleSize = jsonContent.match(/"sampleSize"\s*:\s*(\d+)/);
            const extractConfidence = jsonContent.match(/"confidence"\s*:\s*(0?\.\d+)/);
            const extractAnalysis = jsonContent.match(/"analysis"\s*:\s*"([^"]+)"/);
            const distributionSection = jsonContent.match(/"distribution"\s*:\s*\{([^}]+)\}/);

            // Extract citations using regex
            const citationsSection = jsonContent.match(/"citations"\s*:\s*\[([^\]]+)\]/);
            const citations: string[] = [];
            if (citationsSection && citationsSection[1]) {
              const citationMatches = citationsSection[1].match(/"([^"]+)"/g);
              if (citationMatches) {
                citationMatches.forEach((citation) => {
                  citations.push(citation.replace(/^"|"$/g, ''));
                });
              }
            }
            const distribution: Record<string, number> = {};
            if (distributionSection && distributionSection[1]) {
              const pairs = distributionSection[1].match(/"([^"]+)"\s*:\s*(\d+)/g) || [];
              pairs.forEach((pairStr: string) => {
                const parts = pairStr.split(':');
                if (parts.length >= 2) {
                  const [key, value] = parts.map(s => s.trim());
                  if (key && value) {
                    const cleanKey = key.replace(/^"/, '').replace(/"$/, '');
                    distribution[cleanKey] = parseInt(value, 10);
                  }
                }
              });
            }
            const fallback: SimulationResponseFallback = {
              simulationId: extractSimulationId ? extractSimulationId[1] : `sim_manual_${Date.now()}`,
              metadata: {
                demographic: extractDemographic ? extractDemographic[1] : getDemographicFromRequest(request),
                sampleSize: extractSampleSize ? parseInt(extractSampleSize[1], 10) : getSampleSizeFromRequest(request),
                confidence: extractConfidence ? parseFloat(extractConfidence[1]) : 0.5,
                citations: citations.length > 0 ? citations : ['Manually reconstructed from parsing error']
              },
              results: {
                distribution: distribution,
                individuals: [],
                analysis: extractAnalysis ? extractAnalysis[1] : 'Response parsing failed, results reconstructed manually'
              }
            };
            return fallback;
          } catch {
            const fallback: SimulationResponseFallback = {
              simulationId: `sim_fallback_${Date.now()}`,
              metadata: {
                demographic: getDemographicFromRequest(request),
                sampleSize: getSampleSizeFromRequest(request),
                confidence: 0.1,
                citations: ['Error: Failed to parse AI response']
              },
              results: {
                distribution: {},
                individuals: [],
                analysis: 'Error: Failed to parse simulation response properly'
              }
            };
            return fallback;
          }
        }
      }
    }
    // Type guard for parsedResponse
    const pr = parsedResponse as Partial<SimulationResponseFallback>;
    if (!pr.metadata) {
      pr.metadata = {
        demographic: 'Unknown demographic',
        sampleSize: 0,
        confidence: 0.1,
        citations: ['Error: Missing metadata in simulation response']
      };
    }
    if (!pr.results) {
      pr.results = {
        distribution: {},
        individuals: [],
        analysis: 'Error: Missing results data in simulation response'
      };
    }
    if (!pr.results.distribution) {
      pr.results.distribution = {};
    }
    if (!pr.simulationId) {
      pr.simulationId = generateSimulationId();
    }
    return pr;
  } catch {
    let demographic = 'Unknown';
    let confidence = 0.1;
    let analysis = 'Failed to parse simulation response';
    const demographicMatch = response.match(/demographic.*?[""':]([^""',}]+)/i);
    if (demographicMatch && demographicMatch[1]) {
      demographic = demographicMatch[1].trim();
    }
    const confidenceMatch = response.match(/confidence.*?([0-9.]+)/i);
    if (confidenceMatch && confidenceMatch[1]) {
      const confValue = parseFloat(confidenceMatch[1]);
      if (!isNaN(confValue) && confValue >= 0 && confValue <= 1) {
        confidence = confValue;
      }
    }
    const analysisMatch = response.match(/analysis.*?[""':]([^"']+)/i);
    if (analysisMatch && analysisMatch[1]) {
      analysis = analysisMatch[1].trim();
    }
    const fallback: SimulationResponseFallback = {
      simulationId: generateSimulationId(),
      metadata: {
        demographic: demographic,
        sampleSize: 0,
        confidence: confidence,
        citations: ['Error parsing simulation response']
      },
      results: {
        distribution: {},
        individuals: [],
        analysis: analysis || 'Failed to parse simulation response'
      }
    };
    return fallback;
  }
}
