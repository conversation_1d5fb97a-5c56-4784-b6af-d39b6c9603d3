import { chromium } from 'playwright';

/**
 * Scrapes content from a URL using <PERSON>wright
 * @param url The URL to scrape
 * @param selector Optional CSS selector to target specific content
 * @returns The scraped content
 */
export async function scrapeWithPlaywright(url: string, selector?: string): Promise<string> {
  const browser = await chromium.launch({
    headless: true,
  });

  try {
    const context = await browser.newContext();
    const page = await context.newPage();

    // Navigate to the URL
    await page.goto(url, { waitUntil: 'networkidle' });

    // Extract content based on selector or get entire body content
    let content: string;
    if (selector) {
      const element = await page.$(selector);
      content = element ? await element.innerText() : '';
    } else {
      content = await page.content();
    }

    return content;
  } finally {
    await browser.close();
  }
}

/**
 * Takes a screenshot of a webpage using Playwright
 * @param url The URL to take a screenshot of
 * @param outputPath The path to save the screenshot to
 * @param fullPage Whether to take a full page screenshot
 */
export async function takeScreenshot(
  url: string,
  outputPath: string,
  fullPage: boolean = false
): Promise<void> {
  const browser = await chromium.launch({
    headless: true,
  });

  try {
    const context = await browser.newContext();
    const page = await context.newPage();

    await page.goto(url, { waitUntil: 'networkidle' });
    await page.screenshot({ path: outputPath, fullPage });
  } finally {
    await browser.close();
  }
}

/**
 * Extracts all links from a webpage using Playwright
 * @param url The URL to extract links from
 * @returns An array of link objects with text and href
 */
export async function extractLinks(url: string): Promise<{ text: string; href: string }[]> {
  const browser = await chromium.launch({
    headless: true,
  });

  try {
    const context = await browser.newContext();
    const page = await context.newPage();

    await page.goto(url, { waitUntil: 'networkidle' });

    const links = await page.evaluate(() => {
      const anchors = Array.from(document.querySelectorAll('a'));
      return anchors.map(anchor => ({
        text: anchor.textContent?.trim() || '',
        href: anchor.href
      }));
    });

    return links;
  } finally {
    await browser.close();
  }
}