
/**
 * Helper function to check if the application has network connectivity
 * @returns {Promise<{online: boolean, latency?: number}>} Object containing online status and latency
 */
export async function checkNetworkStatus(): Promise<{online: boolean, latency?: number}> {
  try {
    // Use multiple reliable endpoints that support CORS to check internet connectivity
    // Google's network connectivity check endpoint
    const endpoints = [
      'https://www.google.com/generate_204',
      'https://www.cloudflare.com/cdn-cgi/trace',
      'https://*******/cdn-cgi/trace'
    ];

    const startTime = Date.now();

    // Fetch with AbortController to ensure we can timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    // Try each endpoint until one succeeds
    for (const endpoint of endpoints) {
      try {
        // We don't need to store the response for no-cors mode, just check if the fetch succeeds
        await fetch(endpoint, {
          method: 'HEAD',
          mode: 'no-cors', // Use no-cors to avoid CORS issues
          cache: 'no-cache',
          signal: controller.signal
        });

        // With no-cors, we can't check response.ok, but if we got here without
        // throwing an error, network is likely online
        clearTimeout(timeoutId);
        const latency = Date.now() - startTime;

        return {
          online: true,
          latency
        };
      } catch {
        // Try the next endpoint
        console.log(`Endpoint ${endpoint} failed, trying next one`);
      }
    }

    // If all endpoints failed, we're offline
    clearTimeout(timeoutId);
    return {
      online: false
    };
  } catch {
    // If we get a timeout or any other error, we're offline or having connectivity issues
    return {
      online: false
    };
  }
}

/**
 * Check if the client has connectivity issues
 * @returns {Promise<string|null>} Human-readable connectivity issue or null if no issues
 */
export async function checkConnectivityIssue(): Promise<string|null> {
  try {
    // First check browser's navigator.onLine property for quick offline detection
    if (typeof window !== 'undefined' && !navigator.onLine) {
      return "You appear to be offline according to your browser. Please check your internet connection.";
    }

    // Then do a more thorough check by trying to reach external servers
    const { online, latency } = await checkNetworkStatus();

    if (!online) {
      return "You appear to be offline. Please check your internet connection.";
    }

    if (latency && latency > 3000) {
      return `Network latency is very high (${latency}ms). This may significantly affect application performance.`;
    }

    if (latency && latency > 1500) {
      // Only warn about moderate latency but don't consider it an error
      console.warn(`Network latency is moderate (${latency}ms).`);
    }

    return null;
  } catch (error) {
    console.error("Error checking connectivity:", error);
    return "Unable to determine network status. Please ensure you're connected to the internet.";
  }
}
