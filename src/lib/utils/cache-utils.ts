import { QueryClient } from '@tanstack/react-query';
import { pollKeys } from '@/lib/api/query-keys';

/**
 * Utility functions for managing React Query cache
 * These functions help optimize performance by providing granular cache invalidation
 */

/**
 * Invalidate all poll-related queries
 * Use this when you want to refresh all poll data
 */
export function invalidateAllPolls(queryClient: QueryClient): Promise<void> {
  return queryClient.invalidateQueries({ queryKey: pollKeys.all });
}

/**
 * Invalidate only poll lists, not individual poll details
 * Use this when a new poll is created or a poll is deleted
 */
export function invalidatePollLists(queryClient: QueryClient): Promise<void> {
  return queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
}

/**
 * Invalidate a specific poll by ID
 * Use this when a specific poll is updated
 */
export function invalidatePoll(queryClient: QueryClient, pollId: string): Promise<void> {
  return queryClient.invalidateQueries({ queryKey: pollKeys.detail(pollId) });
}

/**
 * Optimistically update the cache when a poll is updated
 * This makes the UI feel more responsive by immediately showing the change
 * @param queryClient The React Query client
 * @param pollId The ID of the poll being updated
 * @param updater A function that takes the old poll data and returns the updated poll data
 */
export function optimisticallyUpdatePoll<T>(
  queryClient: QueryClient, 
  pollId: string, 
  updater: (oldData: T | undefined) => T
): void {
  // Update the poll in the cache
  queryClient.setQueryData(
    pollKeys.detail(pollId),
    (oldData: T | undefined) => updater(oldData)
  );
  
  // Also update the poll in any lists that contain it
  queryClient.setQueriesData<{ data: Array<{ id: string }> } | unknown>(
    { queryKey: pollKeys.lists() },
    (oldData) => {
      if (!oldData || typeof oldData !== 'object' || !('data' in oldData) || !Array.isArray(oldData.data)) return oldData;
      
      return {
        ...oldData,
        data: oldData.data.map((poll) => 
          poll.id === pollId ? updater(poll as unknown as T) as unknown as typeof poll : poll
        )
      };
    }
  );
}

/**
 * Prefetch a poll by ID to improve perceived performance
 * Call this when a user hovers over a link to a poll
 */
export function prefetchPoll(queryClient: QueryClient, pollId: string): Promise<void> {
  return queryClient.prefetchQuery({
    queryKey: pollKeys.detail(pollId),
    // The queryFn will be provided by the query definition
  });
}
