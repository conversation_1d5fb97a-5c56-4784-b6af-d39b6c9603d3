/**
 * Utility to clean up problematic or corrupted session data
 * This can be triggered on demand when experiencing auth issues
 */

import { supabase } from '@/lib/supabase';

interface CleanupResult {
  success: boolean;
  itemsRemoved: number;
  error?: Error | null;
}

/**
 * Clean up all authentication-related storage to resolve session issues
 * This is useful when users experience being logged out unexpectedly
 */
export async function cleanupAuthStorage(): Promise<CleanupResult> {
  console.log('Starting auth storage cleanup...');
  let itemsRemoved = 0;

  try {
    // 1. First try to sign out completely (will attempt to clear cookies/storage)
    try {
      await supabase.auth.signOut({ scope: 'global' });
    } catch (e) {
      console.warn('Error during signOut:', e);
      // Continue with manual cleanup even if signOut fails
    }

    // 2. Clear all Supabase and auth-related localStorage keys
    if (typeof window !== 'undefined' && window.localStorage) {
      const authPrefixes = [
        'sb-',                 // Supabase token
        'supabase',            // Any supabase related data
        'auth',                // Auth specific data
        'token',               // Any token related keys
        'session',             // Session related keys
      ];

      for (const key of Object.keys(window.localStorage)) {
        if (authPrefixes.some(prefix => key.toLowerCase().includes(prefix))) {
          try {
            window.localStorage.removeItem(key);
            itemsRemoved++;
          } catch (e) {
            console.warn(`Failed to remove localStorage key ${key}:`, e);
          }
        }
      }
    }

    // 3. Clear sessionStorage too
    if (typeof window !== 'undefined' && window.sessionStorage) {
      for (const key of Object.keys(window.sessionStorage)) {
        if (key.toLowerCase().includes('auth') ||
            key.toLowerCase().includes('token') ||
            key.toLowerCase().includes('supabase') ||
            key.toLowerCase().includes('session')) {
          try {
            window.sessionStorage.removeItem(key);
            itemsRemoved++;
          } catch (e) {
            console.warn('Error clearing sessionStorage:', e);
          }
        }
      }
    }

    // 4. Clear cookies that might be related to auth
    if (typeof document !== 'undefined') {
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=');
        if (name && (
            name.toLowerCase().includes('auth') ||
            name.toLowerCase().includes('token') ||
            name.toLowerCase().includes('supabase') ||
            name.toLowerCase().includes('sb-'))
        ) {
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
          itemsRemoved++;
        }
      });
    }

    console.log(`Auth storage cleanup completed successfully: ${itemsRemoved} items removed`);
    return { success: true, itemsRemoved };
  } catch (error) {
    console.error('Error during session cleanup:', error);
    return {
      success: false,
      itemsRemoved,
      error: error instanceof Error ? error : new Error('Unknown error')
    };
  }
}

/**
 * Add a button to the UI to easily clean up session data
 * This is useful when users experience auth issues
 */
export function createCleanupButton(buttonText = 'Fix Login Issues'): HTMLButtonElement {
  const button = document.createElement('button');
  button.innerText = buttonText;
  button.style.position = 'fixed';
  button.style.bottom = '10px';
  button.style.right = '10px';
  button.style.zIndex = '9999';
  button.style.padding = '8px 16px';
  button.style.backgroundColor = '#4f46e5';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';

  button.onclick = async () => {
    button.disabled = true;
    button.innerText = 'Fixing...';

    try {
      const result = await cleanupAuthStorage();
      if (result.success) {
        button.innerText = 'Fixed! Redirecting...';
        setTimeout(() => {
          window.location.href = '/login';
        }, 1000);
      } else {
        button.innerText = 'Error! Try again';
        setTimeout(() => {
          button.disabled = false;
          button.innerText = buttonText;
        }, 2000);
      }
    } catch (error) {
      console.error('Error executing cleanup:', error);
      button.innerText = 'Failed';
      setTimeout(() => {
        button.disabled = false;
        button.innerText = buttonText;
      }, 2000);
    }
  };

  return button;
}

/**
 * Detect if user is experiencing auth issues and offer to help
 * This can be added to the app's error handling
 */
export function detectAuthIssues(): boolean {
  if (typeof window === 'undefined') return false;

  try {
    // Check for signs of corrupted auth state
    const hasAuthToken = !!localStorage.getItem('sb-sumruaeyfidjlssrmfrm-auth-token');
    const isOnAuthPage = window.location.pathname.includes('/login') ||
                          window.location.pathname.includes('/register');
    const hasAuthErrorParam = window.location.search.includes('authError') ||
                              window.location.search.includes('error');

    // If we have token but we're on login page, something might be wrong
    if (hasAuthToken && isOnAuthPage) {
      return true;
    }

    // If we're seeing explicit auth errors, that's a sign
    if (hasAuthErrorParam) {
      return true;
    }

    return false;
  } catch {
    // Catch without a variable parameter to avoid the unused variable warning
    return false;
  }
}
