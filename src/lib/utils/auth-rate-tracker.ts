// auth-rate-tracker.ts
// Utility to track and manage authentication rate limits

/**
 * Rate limit tracker for Supabase authentication
 * Helps avoid hitting Supabase's rate limits:
 * - Token refresh: 150 per 5 minutes per IP
 * - Token reuse: 10-second minimum interval
 * - Token verification: 30 per 5 minutes per IP
 */

// Track refresh attempts within a sliding window
interface RateLimitWindow {
  timestamp: number;
  count: number;
}

// State management for rate tracking
class AuthRateTracker {
  // Last successful refresh time
  private lastRefreshTime = 0;

  // Sliding window for tracking refresh rate (5-minute window)
  private refreshWindows: RateLimitWindow[] = [];

  // Sliding window for tracking verification rate (5-minute window)
  private verifyWindows: RateLimitWindow[] = [];

  // Constants
  private readonly REFRESH_WINDOW_MS = 5 * 60 * 1000; // 5 minutes
  private readonly REFRESH_LIMIT = 150; // 150 per 5 minutes
  private readonly VERIFY_LIMIT = 30; // 30 per 5 minutes
  private readonly TOKEN_REUSE_INTERVAL_MS = 11000; // 11 seconds (just over Supabase's 10s)

  /**
   * Record a refresh attempt
   */
  recordRefresh(success = true): void {
    // Clean up old windows first
    this.cleanupWindows();

    const now = Date.now();

    // Record the successful refresh time
    if (success) {
      this.lastRefreshTime = now;
    }

    // Add to the current window or create a new one
    const currentWindow = this.refreshWindows.find(
      w => now - w.timestamp < this.REFRESH_WINDOW_MS
    );

    if (currentWindow) {
      currentWindow.count++;
    } else {
      this.refreshWindows.push({ timestamp: now, count: 1 });
    }
  }

  /**
   * Record a verification attempt
   */
  recordVerify(): void {
    // Clean up old windows first
    this.cleanupWindows();

    const now = Date.now();

    // Add to the current window or create a new one
    const currentWindow = this.verifyWindows.find(
      w => now - w.timestamp < this.REFRESH_WINDOW_MS
    );

    if (currentWindow) {
      currentWindow.count++;
    } else {
      this.verifyWindows.push({ timestamp: now, count: 1 });
    }
  }

  /**
   * Check if we're approaching the refresh rate limit
   * @returns Object with isApproaching flag and percentage of limit used
   */
  checkRefreshRateLimit(): { isApproaching: boolean; percentUsed: number } {
    this.cleanupWindows();

    // Count total refreshes in the current window
    const totalRefreshes = this.refreshWindows.reduce((sum, window) => sum + window.count, 0);
    const percentUsed = (totalRefreshes / this.REFRESH_LIMIT) * 100;

    return {
      isApproaching: totalRefreshes > this.REFRESH_LIMIT * 0.8, // 80% of limit
      percentUsed
    };
  }

  /**
   * Check if we're approaching the verify rate limit
   * @returns Object with isApproaching flag and percentage of limit used
   */
  checkVerifyRateLimit(): { isApproaching: boolean; percentUsed: number } {
    this.cleanupWindows();

    // Count total verifies in the current window
    const totalVerifies = this.verifyWindows.reduce((sum, window) => sum + window.count, 0);
    const percentUsed = (totalVerifies / this.VERIFY_LIMIT) * 100;

    return {
      isApproaching: totalVerifies > this.VERIFY_LIMIT * 0.8, // 80% of limit
      percentUsed
    };
  }

  /**
   * Check if we can refresh based on the token reuse interval
   * @returns Boolean indicating if enough time has passed since last refresh
   */
  canRefreshToken(): boolean {
    const now = Date.now();
    const timeSinceLastRefresh = now - this.lastRefreshTime;

    return timeSinceLastRefresh >= this.TOKEN_REUSE_INTERVAL_MS;
  }

  /**
   * Get the time remaining until next allowed refresh
   * @returns Number of milliseconds until next allowed refresh
   */
  getTimeUntilNextAllowedRefresh(): number {
    if (this.canRefreshToken()) return 0;

    const now = Date.now();
    const timeSinceLastRefresh = now - this.lastRefreshTime;

    return Math.max(0, this.TOKEN_REUSE_INTERVAL_MS - timeSinceLastRefresh);
  }

  /**
   * Clean up old windows that are outside the tracking period
   */
  private cleanupWindows(): void {
    const now = Date.now();

    // Remove refresh windows older than the window period
    this.refreshWindows = this.refreshWindows.filter(
      w => now - w.timestamp < this.REFRESH_WINDOW_MS
    );

    // Remove verify windows older than the window period
    this.verifyWindows = this.verifyWindows.filter(
      w => now - w.timestamp < this.REFRESH_WINDOW_MS
    );
  }
}

// Export a singleton instance
export const authRateTracker = new AuthRateTracker();
