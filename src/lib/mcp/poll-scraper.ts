import puppeteer from 'puppeteer';

// Define types locally to avoid dependency issues
interface MCPToolParameter {
  type: string;
  description?: string;
  properties?: Record<string, MCPToolParameter>;
  required?: string[];
  items?: MCPToolParameter;
}

interface MCPTool {
  name: string;
  description: string;
  parameters: MCPToolParameter;
  handler: MCPFunctionHandler;
}

interface MCPServer {
  name: string;
  description: string;
  getTools(): Promise<MCPTool[]>;
}

type MCPFunctionHandler = (options: Record<string, unknown>) => Promise<unknown>;

class PollScraperServer implements MCPServer {
  name = 'poll-scraper';
  description = 'Scrapes poll data from websites';

  async getTools() {
    return [
      {
        name: this.name,
        description: this.description,
        parameters: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL of the page containing the poll',
            },
            pollSelector: {
              type: 'string',
              description: 'CSS selector for the poll container',
            },
            questionSelector: {
              type: 'string',
              description: 'CSS selector for poll questions',
            },
            optionsSelector: {
              type: 'string',
              description: 'CSS selector for poll options',
            },
          },
          required: ['url'],
        },
        handler: this.scrapePoll.bind(this) as MCPFunctionHandler,
      },
    ];
  }

  async scrapePoll(options: {
    url: string;
    pollSelector?: string;
    questionSelector?: string;
    optionsSelector?: string;
  }) {
    const { url, pollSelector, questionSelector, optionsSelector } = options;

    const browser = await puppeteer.launch({
      headless: true,
    });

    try {
      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle0' });

      // Get page title as poll title if no specific poll selector
      const title = await page.title();

      const questions: Array<{
        text: string;
        options: string[];
      }> = [];

      // If specific selectors are provided, use them to extract poll data
      if (questionSelector) {
        const questionElements = await page.$$(questionSelector);

        for (const questionEl of questionElements) {
          const questionText = await questionEl.evaluate(el => (el as HTMLElement).textContent?.trim() || '');
          let options: string[] = [];

          if (optionsSelector) {
            // Look for options within the question element if possible
            try {
              const optionElements = await questionEl.$$(optionsSelector);
              if (optionElements.length > 0) {
                options = await Promise.all(
                  optionElements.map(async optEl =>
                    await optEl.evaluate(el => (el as HTMLElement).textContent?.trim() || '')
                  )
                );
              }
            } catch {
              // If options not found within question, look globally
              const optionElements = await page.$$(optionsSelector);
              options = await Promise.all(
                optionElements.map(async optEl =>
                  await optEl.evaluate(el => (el as HTMLElement).textContent?.trim() || '')
                )
              );
            }
          }

          questions.push({
            text: questionText,
            options: options.filter(opt => opt.length > 0), // Filter out empty options
          });
        }
      } else if (pollSelector) {
        // If only poll selector is provided, get its text and try to parse it
        const pollElement = await page.$(pollSelector);
        if (pollElement) {
          const pollText = await pollElement.evaluate(el => (el as HTMLElement).textContent?.trim() || '');

          // Simple heuristic: Split by question marks and new lines
          const questionTexts = pollText
            .split(/\?|\n{2,}/)
            .map(q => q.trim())
            .filter(q => q.length > 10); // Filter out short fragments

          for (const questionText of questionTexts) {
            questions.push({
              text: questionText.endsWith('?') ? questionText : `${questionText}?`,
              options: [],
            });
          }
        }
      }

      return {
        title: pollSelector ? await page.evaluate((selector) => {
          const el = document.querySelector(selector);
          return el ? el.textContent?.trim() : undefined;
        }, pollSelector) : title,
        questions,
        source: url,
      };
    } finally {
      await browser.close();
    }
  }
}

const pollScraperInstance = new PollScraperServer();
export default pollScraperInstance;