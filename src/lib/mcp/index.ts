// Mock SDK class for Next.js build compatibility
class MockSDK {
  constructor() {}
  register() {}
  getTools() { return []; }
  async invoke(toolName: string, params: Record<string, unknown>) {
    console.log(`Mock invoke called for ${toolName} with params:`, params);
    return { results: [] };
  }
}

// Mock implementation for Next.js build
export const sdk = typeof window === 'undefined' && process.env.NODE_ENV !== 'production'
  ? undefined  // This will be replaced by the actual SDK in the standalone script
  : new MockSDK();

// Mock setup function for Next.js build
export const setupMcpServers = () => {
  console.log('MCP servers are only available in the standalone script');
  return sdk;
};

// These exports are needed for TypeScript compatibility
export class BraveSearchServer {
  constructor() {}
}

export class SequentialThinkingServer {
  constructor() {}
}

export class PostgresServer {
  constructor() {}
}

export class PuppeteerMcpServer {
  constructor() {}
}