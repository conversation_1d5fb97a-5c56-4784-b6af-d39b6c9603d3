import { SDK } from '@modelcontextprotocol/sdk';
import { PostgresServer } from '@modelcontextprotocol/server-postgres';

export function setupMcpServers() {
  const sdk = new SDK();

  // Initialize PostgreSQL server
  const postgresServer = new PostgresServer({
    connectionString: process.env.DATABASE_URL,
    poolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
    idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000')
  });

  // Register the PostgreSQL server
  sdk.registerServer('postgresql-pollgpt', postgresServer);

  return sdk;
}