import { NextRequest, NextResponse } from 'next/server';

/**
 * SEO middleware function that enhances responses with proper headers for SEO
 * This can be used in middleware.ts or in specific route handlers
 */
export function seoMiddleware(req: NextRequest, res: NextResponse): NextResponse {
  const url = req.nextUrl;
  const pathname = url.pathname;
  const response = res;

  // Add appropriate headers based on file type
  if (pathname.endsWith('.xml')) {
    // For XML files like sitemap
    response.headers.set('Content-Type', 'application/xml; charset=utf-8');
    response.headers.set('X-Content-Type-Options', 'nosniff');
  } else if (pathname.endsWith('.json') || pathname.endsWith('.jsonld')) {
    // For JSON/JSON-LD files like schema.org data
    response.headers.set('Content-Type', 'application/ld+json; charset=utf-8');
    response.headers.set('X-Content-Type-Options', 'nosniff');
  }

  // Add caching headers for static resources
  if (pathname.match(/\.(xml|json|jsonld|txt)$/)) {
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
  }

  // Handle canonical URLs for trailing slashes
  // Could either add or remove trailing slashes depending on preferred format
  if (pathname !== '/' && pathname.endsWith('/')) {
    // Remove trailing slashes
    const withoutTrailingSlash = pathname.slice(0, -1);
    const newUrl = new URL(withoutTrailingSlash, url);

    return NextResponse.redirect(newUrl, 301);
  }

  return response;
}
