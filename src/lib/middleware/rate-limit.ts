/**
 * Rate Limiting Middleware for Next.js API Routes
 *
 * Integrates the rate limiter service with Next.js API routes
 * Provides flexible rate limiting with different strategies for different endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { rateLimiter } from '@/lib/services/rate-limiter';
// If the above import fails, uncomment the following line and comment the above one
// import rateLimiter from '@/lib/services/rate-limiter';

export interface RateLimitOptions {
  identifier?: (req: NextRequest) => string;
  configName?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  customHeaders?: Record<string, string>;
  onLimitReached?: (identifier: string, req: NextRequest) => void;
}

/**
 * Rate limiting middleware factory
 */
export function withRateLimit(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: RateLimitOptions = {}
) {
  return async function rateLimitedHandler(req: NextRequest): Promise<NextResponse> {
    const {
      identifier = defaultIdentifier,
      configName = 'moderate',
      customHeaders = {},
      onLimitReached
    } = options;

    try {
      // Get identifier for rate limiting
      const id = identifier(req);

      // Check rate limit
      const result = await rateLimiter.checkLimit(id, configName);

      // Set rate limit headers
      const headers = new Headers({
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
        ...customHeaders
      });

      if (result.retryAfter) {
        headers.set('Retry-After', result.retryAfter.toString());
      }

      if (!result.allowed) {
        // Rate limit exceeded
        onLimitReached?.(id, req);

        return new NextResponse(
          JSON.stringify({
            error: 'Too Many Requests',
            message: 'Rate limit exceeded. Please try again later.',
            retryAfter: result.retryAfter
          }),
          {
            status: 429,
            headers
          }
        );
      }

      // Execute the handler
      const response = await handler(req);

      // Add rate limit headers to successful response
      if (result.limit) {
        response.headers.set('X-RateLimit-Limit', result.limit.toString());
      }
      if (result.remaining !== undefined) {
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
      }
      response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());

      return response;

    } catch (err) {
      console.error('Rate limiting error:', err);

      // If rate limiting fails, allow the request to proceed
      return handler(req);
    }
  };
}

/**
 * Default identifier function (uses IP address)
 */
function defaultIdentifier(req: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Get IP from headers since req.ip is not available in NextRequest
  return req.headers.get('x-forwarded-for')?.split(',')[0] || 
         req.headers.get('x-real-ip') || 
         'unknown';
}

/**
 * User-based identifier function
 */
export function userIdentifier(req: NextRequest): string {
  // Extract user ID from JWT token or session
  const authorization = req.headers.get('authorization');

  if (authorization?.startsWith('Bearer ')) {
    try {
      // This would normally decode JWT to get user ID
      // For now, use a simplified approach
      const token = authorization.substring(7);
      return `user:${token.substring(0, 10)}`; // Use first 10 chars as identifier
    } catch {
      // Fallback to IP if token is invalid
      return `ip:${defaultIdentifier(req)}`;
    }
  }

  return `ip:${defaultIdentifier(req)}`;
}

/**
 * API key based identifier function
 */
export function apiKeyIdentifier(req: NextRequest): string {
  const apiKey = req.headers.get('x-api-key');

  if (apiKey) {
    return `api:${apiKey}`;
  }

  return `ip:${defaultIdentifier(req)}`;
}

/**
 * Combined identifier function (user + IP)
 */
export function combinedIdentifier(req: NextRequest): string {
  const userID = userIdentifier(req);
  const ip = defaultIdentifier(req);

  return `${userID}:${ip}`;
}

/**
 * Route-specific rate limiting configurations
 */
export const routeConfigs = {
  // Authentication routes
  '/api/auth/signin': {
    configName: 'auth',
    identifier: combinedIdentifier,
    onLimitReached: (id: string) => {
      console.warn(`Authentication rate limit exceeded for ${id}`);
    }
  },

  // Simulation routes
  '/api/simulations': {
    configName: 'simulation',
    identifier: userIdentifier,
    onLimitReached: (id: string) => {
      console.warn(`Simulation rate limit exceeded for ${id}`);
    }
  },

  // AI-powered routes
  '/api/ai/generate': {
    configName: 'ai_requests',
    identifier: userIdentifier,
    customHeaders: {
      'X-AI-Rate-Limited': 'true'
    }
  },

  // General API routes
  '/api/polls': {
    configName: 'moderate',
    identifier: defaultIdentifier
  },

  // High-frequency routes
  '/api/stats': {
    configName: 'lenient',
    identifier: defaultIdentifier
  },

  // Admin routes
  '/api/admin': {
    configName: 'strict',
    identifier: userIdentifier,
    onLimitReached: (id: string) => {
      console.error(`Admin rate limit exceeded for ${id} - potential security issue`);
    }
  }
};

/**
 * Smart rate limiter that automatically selects configuration based on route
 */
export function withSmartRateLimit(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async function smartRateLimitedHandler(req: NextRequest): Promise<NextResponse> {
    const pathname = new URL(req.url).pathname;

    // Find matching route configuration
    let config = routeConfigs[pathname];

    if (!config) {
      // Try to find a pattern match
      for (const [route, routeConfig] of Object.entries(routeConfigs)) {
        if (pathname.startsWith(route)) {
          config = routeConfig;
          break;
        }
      }
    }

    // Use default configuration if no match found
    if (!config) {
      config = {
        configName: 'moderate',
        identifier: defaultIdentifier
      };
    }

    return withRateLimit(handler, config)(req);
  };
}

/**
 * Burst protection middleware
 * Provides additional protection against sudden spikes in traffic
 */
export function withBurstProtection(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    windowMs?: number;
    maxBurst?: number;
    identifier?: (req: NextRequest) => string;
  } = {}
) {
  const {
    windowMs = 1000, // 1 second window
    maxBurst = 10,   // 10 requests per second max
    identifier = defaultIdentifier
  } = options;

  return async function burstProtectedHandler(req: NextRequest): Promise<NextResponse> {
    const id = identifier(req);

    // Use token bucket strategy for burst protection
    const burstConfig = {
      windowMs,
      maxRequests: maxBurst,
      strategy: 'token_bucket' as const,
      capacity: maxBurst,
      refillRate: maxBurst / (windowMs / 1000) // Refill rate per second
    };

    // Set temporary burst configuration
    rateLimiter.setConfig('burst_protection', burstConfig);

    const result = await rateLimiter.checkLimit(id, 'burst_protection');

    if (!result.allowed) {
      return new NextResponse(
        JSON.stringify({
          error: 'Burst Limit Exceeded',
          message: 'Too many requests in a short period. Please slow down.',
          retryAfter: result.retryAfter
        }),
        {
          status: 429,
          headers: {
            'X-Burst-Limit': maxBurst.toString(),
            'X-Burst-Remaining': result.remaining.toString(),
            'Retry-After': (result.retryAfter || 1).toString()
          }
        }
      );
    }

    return handler(req);
  };
}

/**
 * Global rate limiting middleware for Next.js middleware.ts
 */
export async function globalRateLimit(req: NextRequest): Promise<NextResponse | void> {
  // Skip rate limiting for static files and internal routes
  if (
    req.nextUrl.pathname.startsWith('/_next') ||
    req.nextUrl.pathname.startsWith('/static') ||
    req.nextUrl.pathname.includes('.')
  ) {
    return;
  }

  // Apply global rate limiting
  const identifier = defaultIdentifier(req);
  const result = await rateLimiter.checkLimit(identifier, 'global_ip');

  if (!result.allowed) {
    return new NextResponse(
      JSON.stringify({
        error: 'Global Rate Limit Exceeded',
        message: 'Too many requests from your IP address. Please try again later.',
        retryAfter: result.retryAfter
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': result.limit.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
          'Retry-After': (result.retryAfter || 60).toString()
        }
      }
    );
  }

  // Continue to the next middleware or route
  return;
}

/**
 * Rate limiting utilities
 */
export const rateLimitUtils = {
  /**
   * Check if request is from a trusted source
   */
  isTrustedSource(req: NextRequest): boolean {
    const trustedIPs = process.env.TRUSTED_IPS?.split(',') || [];
    const ip = defaultIdentifier(req);
    return trustedIPs.includes(ip);
  },

  /**
   * Get rate limit info for monitoring
   */
  async getRateLimitInfo(identifier: string, configName: string = 'moderate') {
    return rateLimiter.getUsageStats(identifier, configName);
  },

  /**
   * Reset rate limit for specific identifier (admin function)
   */
  async resetRateLimit(identifier: string, configName?: string) {
    return rateLimiter.resetLimit(identifier, configName);
  },

  /**
   * Get rate limiting statistics
   */
  async getGlobalStats() {
    // This would aggregate stats from Redis
    return {
      totalRequests: 0,
      rateLimitedRequests: 0,
      topLimitedIPs: [],
      averageRequestRate: 0
    };
  }
};

export default withRateLimit;
