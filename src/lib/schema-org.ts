// src/lib/schema-org.ts
import { Organization, WithContext } from 'schema-dts';

export function generateOrganizationSchema(): WithContext<Organization> {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'PollGPT',
    alternateName: ['Poll GPT', 'PollGPT AI', 'Poll GPT AI'],
    url: 'https://pollgpt.com',
    logo: 'https://pollgpt.com/pollgpt-icon.svg',
    description: 'AI-Powered Polling Platform for creating and analyzing polls',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '5 Parv. Alan Turing',
      addressLocality: 'Paris',
      postalCode: '75013',
      addressCountry: 'France'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Support',
      email: '<EMAIL>'
    },
    sameAs: [
      'https://x.com/pollgpt',
      'https://linkedin.com/company/pollgpt'
    ]
  };
}
