import { generateObject, streamText } from 'ai';
import { aiProviders, validateAIProviders } from './providers';
import {
  PollSchema,
  ContentExtractionSchema,
  DocumentAnalysisSchema,
  PollCreationInput,
  type Poll,
  type ContentExtraction,
  type DocumentAnalysis
} from './schemas';
import {
  generatePollQuestions as existingGeneratePoll,
  type GeneratedPoll
} from '../services/perplexity-ai';
import { cachedGeneration } from './cache-service';

export class UnifiedAIService {
  private isAISDKEnabled: boolean;

  constructor() {
    // Check if AI SDK providers are properly configured
    this.isAISDKEnabled = validateAIProviders();

    if (!this.isAISDKEnabled) {
      console.warn('AI SDK providers not fully configured, falling back to existing services');
    }
  }

  /**
   * Enhanced poll generation with structured output
   * Falls back to existing Perplexity service if AI SDK fails
   */
  async generatePoll(input: PollCreationInput): Promise<GeneratedPoll> {
    const cacheKey = `poll_${JSON.stringify(input)}`;

    return cachedGeneration(cacheKey, async () => {
      if (this.isAISDKEnabled) {
        try {
          const { object } = await generateObject({
            model: aiProviders.contentExtraction, // Using Mistral for poll generation
            schema: PollSchema,
            prompt: this.buildPollPrompt(input),
            temperature: 0.3
          });

          // Convert AI SDK format to existing format for compatibility
          return this.convertToExistingFormat(object, input);
        } catch (error) {
          console.warn('AI SDK poll generation failed, falling back:', error);
          return this.fallbackToExistingService(input);
        }
      }

      return this.fallbackToExistingService(input);
    });
  }

  /**
   * Enhanced content extraction with structured output
   */
  async extractContent(content: string, type: 'text' | 'poll-questions' | 'key-themes' = 'text'): Promise<ContentExtraction> {
    const cacheKey = `content_${type}_${content.substring(0, 100)}`;

    return cachedGeneration(cacheKey, async () => {
      if (this.isAISDKEnabled) {
        try {
          const { object } = await generateObject({
            model: aiProviders.contentExtraction,
            schema: ContentExtractionSchema,
            prompt: this.buildContentExtractionPrompt(content, type),
            temperature: 0.3
          });

          return object;
        } catch (error) {
          console.warn('AI SDK content extraction failed:', error);
          // Return basic extraction as fallback
          return {
            extractedText: content,
            keyThemes: [],
            suggestedQuestions: [],
            confidence: 0.5
          };
        }
      }

      // Basic fallback when AI SDK is not available
      return {
        extractedText: content,
        keyThemes: [],
        suggestedQuestions: [],
        confidence: 0.5
      };
    });
  }

  /**
   * Document analysis with AI SDK
   */
  async analyzeDocument(content: string): Promise<DocumentAnalysis> {
    if (this.isAISDKEnabled) {
      try {
        const { object } = await generateObject({
          model: aiProviders.contentExtraction,
          schema: DocumentAnalysisSchema,
          prompt: this.buildDocumentAnalysisPrompt(content),
          temperature: 0.3
        });

        return object;
      } catch (error) {
        console.warn('AI SDK document analysis failed:', error);
      }
    }

    // Fallback to basic analysis
    return {
      summary: content.substring(0, 200) + '...',
      mainTopics: [],
      pollSuggestions: [],
      extractionQuality: 'low' as const
    };
  }

  /**
   * Streaming poll analysis for real-time insights
   */
  async streamPollAnalysis(pollData: Record<string, unknown>) {
    if (this.isAISDKEnabled) {
      try {
        return streamText({
          model: aiProviders.contentExtraction,
          prompt: this.buildAnalysisPrompt(pollData),
          temperature: 0.3
        });
      } catch (error) {
        console.warn('AI SDK streaming failed:', error);
      }
    }

    // Fallback to existing insights service
    throw new Error('Streaming not available, use existing insights service');
  }

  /**
   * Build prompts for different AI operations
   */
  private buildPollPrompt(input: PollCreationInput): string {
    return `
I need to create a poll about "${input.topic}" for an audience of "${input.audience}".
The poll title is: "${input.title}"
${input.additionalInfo ? `Additional context: ${input.additionalInfo}` : ''}
${input.content ? `Content to analyze: ${input.content}` : ''}

Please generate a poll with 4-5 well-designed questions that would provide meaningful insights.

The poll should include:
1. A brief description of the poll's purpose
2. A mix of question types (single choice, multiple choice, Likert scale, and open-ended)
3. Appropriate options for each question
4. Clear, unbiased question wording

Return the result in the specified JSON schema format.
    `.trim();
  }

  private buildContentExtractionPrompt(content: string, type: string): string {
    return `
Analyze the following content and extract key information:

Content: ${content}

Extraction type: ${type}

Please provide:
1. Clean extracted text
2. Key themes and topics (3-5 main themes)
3. Suggested poll questions based on the content (3-5 questions)
4. Confidence level in the extraction quality (0-1)

Focus on creating poll questions that would gather meaningful insights about the content's main topics.
    `.trim();
  }

  private buildDocumentAnalysisPrompt(content: string): string {
    return `
Perform a comprehensive analysis of this document:

Content: ${content}

Please provide:
1. A concise summary of the main content
2. List of main topics/themes
3. Suggested poll questions with reasoning for each
4. Assessment of extraction quality (high/medium/low)

Focus on identifying content that would make for engaging and insightful poll questions.
    `.trim();
  }

  private buildAnalysisPrompt(pollData: Record<string, unknown>): string {
    return `
Analyze the following poll data and provide insights:

${JSON.stringify(pollData, null, 2)}

Please provide:
1. Key patterns and trends in the responses
2. Demographic insights if available
3. Satisfaction levels and sentiment analysis
4. Actionable recommendations based on the data
5. Any notable correlations or outliers

Format the analysis in a clear, structured way that would be valuable for decision-making.
    `.trim();
  }

  /**
   * Convert AI SDK poll format to existing GeneratedPoll format
   */
  private convertToExistingFormat(aiPoll: Poll, input: PollCreationInput): GeneratedPoll {
    return {
      title: input.title,
      description: aiPoll.description,
      questions: aiPoll.questions.map((q, index) => ({
        text: q.text,
        type: q.type,
        options: q.options?.map(opt => ({ text: opt.text, value: opt.value })) || [],
        required: q.required,
        order: q.order || index + 1
      }))
    };
  }

  /**
   * Fallback to existing Perplexity service
   */
  private async fallbackToExistingService(input: PollCreationInput): Promise<GeneratedPoll> {
    return existingGeneratePoll(input);
  }

  /**
   * Check if AI SDK features are available
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isFeatureAvailable(feature: 'structured' | 'streaming' | 'tools'): boolean {
    // For now, all features are available if AI SDK is enabled
    // In the future, we could check specific feature availability
    return this.isAISDKEnabled;
  }
}

// Export singleton instance
export const unifiedAI = new UnifiedAIService();

// Export helper functions for direct use
export async function generatePollWithAI(input: PollCreationInput): Promise<GeneratedPoll> {
  return unifiedAI.generatePoll(input);
}

export async function extractContentWithAI(content: string): Promise<ContentExtraction> {
  return unifiedAI.extractContent(content);
}

export async function analyzeDocumentWithAI(content: string): Promise<DocumentAnalysis> {
  return unifiedAI.analyzeDocument(content);
}
