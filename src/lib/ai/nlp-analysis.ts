/**
 * Phase 2: Advanced Sentiment Analysis and NLP Services
 * Provides comprehensive text analysis for poll content enhancement
 */

// Lazy import natural to avoid issues with server-side rendering
import { generateObject } from 'ai';
import { aiProviders } from './providers';
import { z } from 'zod';

// Lazy loading function for natural library
async function loadNatural() {
  if (typeof window !== 'undefined') {
    // Client-side: return a mock for now
    return null;
  }

  try {
    // Server-side: dynamically import natural
    const natural = await import('natural');
    return natural.default;
  } catch (error) {
    // Silently fail if natural can't be loaded
    console.warn('Natural library not available, falling back to basic analysis:', error);
    return null;
  }
}

// Sentiment analysis types
export interface SentimentAnalysis {
  score: number; // -1 to 1 (negative to positive)
  magnitude: number; // 0 to 1 (intensity)
  label: 'positive' | 'negative' | 'neutral';
  confidence: number;
  emotions?: EmotionAnalysis;
  subjectivity: number; // 0 to 1 (objective to subjective)
}

export interface EmotionAnalysis {
  joy: number;
  anger: number;
  fear: number;
  sadness: number;
  surprise: number;
  disgust: number;
}

export interface TopicAnalysis {
  topics: Topic[];
  themes: string[];
  categories: string[];
  readabilityScore: number;
  complexity: 'simple' | 'moderate' | 'complex';
}

export interface Topic {
  name: string;
  score: number;
  keywords: string[];
  sentences: string[];
}

export interface PollSuggestion {
  question: string;
  type: 'single' | 'multiple' | 'likert' | 'open';
  reasoning: string;
  relevanceScore: number;
  options?: string[];
}

export interface ContentQuality {
  clarity: number; // 0 to 1
  engagement: number; // 0 to 1
  informativeness: number; // 0 to 1
  bias: number; // 0 to 1 (higher = more biased)
  overallScore: number; // 0 to 1
  recommendations: string[];
}

// Zod schemas for AI-powered analysis
const SentimentSchema = z.object({
  sentiment: z.object({
    score: z.number().min(-1).max(1),
    label: z.enum(['positive', 'negative', 'neutral']),
    confidence: z.number().min(0).max(1),
    reasoning: z.string()
  }),
  emotions: z.object({
    joy: z.number().min(0).max(1),
    anger: z.number().min(0).max(1),
    fear: z.number().min(0).max(1),
    sadness: z.number().min(0).max(1),
    surprise: z.number().min(0).max(1),
    disgust: z.number().min(0).max(1)
  }),
  subjectivity: z.number().min(0).max(1)
});

const TopicAnalysisSchema = z.object({
  topics: z.array(z.object({
    name: z.string(),
    score: z.number().min(0).max(1),
    keywords: z.array(z.string()),
    description: z.string()
  })),
  themes: z.array(z.string()),
  categories: z.array(z.string()),
  readability: z.object({
    score: z.number().min(0).max(1),
    level: z.enum(['simple', 'moderate', 'complex']),
    explanation: z.string()
  })
});

const PollSuggestionsSchema = z.object({
  suggestions: z.array(z.object({
    question: z.string(),
    type: z.enum(['single', 'multiple', 'likert', 'open']),
    reasoning: z.string(),
    relevanceScore: z.number().min(0).max(1),
    options: z.array(z.string()).optional()
  }))
});

const ContentQualitySchema = z.object({
  quality: z.object({
    clarity: z.number().min(0).max(1),
    engagement: z.number().min(0).max(1),
    informativeness: z.number().min(0).max(1),
    bias: z.number().min(0).max(1),
    overallScore: z.number().min(0).max(1)
  }),
  recommendations: z.array(z.string())
});

/**
 * Advanced Sentiment Analysis Service
 */
export class SentimentAnalysisService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private analyzer: any = null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private stemmer: any = null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private natural: any = null;

  private async initializeNatural() {
    if (this.natural) return;

    try {
      this.natural = await loadNatural();
      if (this.natural) {
        this.analyzer = new this.natural.SentimentAnalyzer('English',
          this.natural.PorterStemmer, 'afinn');
        this.stemmer = this.natural.PorterStemmer;
      }
    } catch (error) {
      console.warn('Failed to initialize Natural.js:', error);
    }
  }

  /**
   * Perform comprehensive sentiment analysis
   */
  async analyzeSentiment(text: string, useAI: boolean = true): Promise<SentimentAnalysis> {
    // Basic Natural.js analysis
    const basicSentiment = await this.performBasicSentiment(text);

    if (!useAI) {
      return basicSentiment;
    }

    try {
      // Enhanced AI-powered sentiment analysis
      const { object } = await generateObject({
        model: aiProviders.contentExtraction,
        schema: SentimentSchema,
        prompt: this.buildSentimentPrompt(text),
        temperature: 0.2
      });

      return {
        score: object.sentiment.score,
        magnitude: Math.abs(object.sentiment.score),
        label: object.sentiment.label,
        confidence: object.sentiment.confidence,
        emotions: {
          joy: object.emotions.joy || 0,
          anger: object.emotions.anger || 0,
          fear: object.emotions.fear || 0,
          sadness: object.emotions.sadness || 0,
          surprise: object.emotions.surprise || 0,
          disgust: object.emotions.disgust || 0
        },
        subjectivity: object.subjectivity
      };
    } catch (error) {
      console.warn('AI sentiment analysis failed, using basic analysis:', error);
      return basicSentiment;
    }
  }

  private async performBasicSentiment(text: string): Promise<SentimentAnalysis> {
    await this.initializeNatural();

    if (!this.natural || !this.analyzer) {
      // Fallback to simple analysis if Natural.js is not available
      return this.performSimpleSentiment(text);
    }

    const tokenizer = new this.natural.WordTokenizer();
    const tokens = tokenizer.tokenize(text.toLowerCase()) || [];
    const stemmedTokens = tokens.map((token: string) => this.stemmer.stem(token));
    const score = this.analyzer.getSentiment(stemmedTokens);

    // Normalize score from [-5, 5] to [-1, 1]
    const normalizedScore = Math.max(-1, Math.min(1, score / 5));

    let label: 'positive' | 'negative' | 'neutral';
    if (normalizedScore > 0.1) label = 'positive';
    else if (normalizedScore < -0.1) label = 'negative';
    else label = 'neutral';

    return {
      score: normalizedScore,
      magnitude: Math.abs(normalizedScore),
      label,
      confidence: 0.7, // Basic analysis confidence
      subjectivity: this.calculateSubjectivity(text)
    };
  }

  private performSimpleSentiment(text: string): SentimentAnalysis {
    // Simple fallback sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'awesome', 'love', 'like', 'happy'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'disappointed'];

    const words = text.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.some(pw => word.includes(pw))).length;
    const negativeCount = words.filter(word => negativeWords.some(nw => word.includes(nw))).length;

    const score = (positiveCount - negativeCount) / words.length * 2;
    const normalizedScore = Math.max(-1, Math.min(1, score));

    let label: 'positive' | 'negative' | 'neutral';
    if (normalizedScore > 0.1) label = 'positive';
    else if (normalizedScore < -0.1) label = 'negative';
    else label = 'neutral';

    return {
      score: normalizedScore,
      magnitude: Math.abs(normalizedScore),
      label,
      confidence: 0.5, // Lower confidence for simple analysis
      subjectivity: this.calculateSubjectivity(text)
    };
  }

  private calculateSubjectivity(text: string): number {
    // Simple subjectivity heuristics
    const subjectiveWords = ['feel', 'think', 'believe', 'opinion', 'should', 'must', 'amazing', 'terrible'];
    const words = text.toLowerCase().split(/\s+/);
    const subjectiveCount = words.filter(word =>
      subjectiveWords.some(sw => word.includes(sw))
    ).length;

    return Math.min(subjectiveCount / words.length * 3, 1);
  }

  private buildSentimentPrompt(text: string): string {
    return `
Analyze the sentiment and emotional content of the following text:

"${text}"

Provide a comprehensive sentiment analysis including:

1. Overall sentiment score (-1 for very negative, 0 for neutral, +1 for very positive)
2. Sentiment label (positive, negative, or neutral)
3. Confidence in the analysis (0 to 1)
4. Emotional breakdown (joy, anger, fear, sadness, surprise, disgust) as scores 0-1
5. Subjectivity level (0 for objective facts, 1 for subjective opinions)

Consider context, tone, and nuanced expressions. Provide clear reasoning for your analysis.
    `.trim();
  }
}

/**
 * Topic Analysis Service
 */
export class TopicAnalysisService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private tfidf: any = null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private natural: any = null;

  private async initializeNatural() {
    if (this.natural) return;

    try {
      this.natural = await loadNatural();
      if (this.natural) {
        this.tfidf = new this.natural.TfIdf();
      }
    } catch (error) {
      console.warn('Failed to initialize Natural.js for topic analysis:', error);
    }
  }

  /**
   * Perform advanced topic analysis
   */
  async analyzeTopics(text: string, useAI: boolean = true): Promise<TopicAnalysis> {
    // Basic topic extraction using TF-IDF
    const basicTopics = this.performBasicTopicAnalysis(text);

    if (!useAI) {
      return basicTopics;
    }

    try {
      // AI-powered topic analysis
      const { object } = await generateObject({
        model: aiProviders.contentExtraction,
        schema: TopicAnalysisSchema,
        prompt: this.buildTopicPrompt(text),
        temperature: 0.3
      });

      return {
        topics: object.topics.map(topic => ({
          name: topic.name,
          score: topic.score,
          keywords: topic.keywords,
          sentences: [] // Could be enhanced to extract relevant sentences
        })),
        themes: object.themes,
        categories: object.categories,
        readabilityScore: object.readability.score,
        complexity: object.readability.level
      };
    } catch (error) {
      console.warn('AI topic analysis failed, using basic analysis:', error);
      return basicTopics;
    }
  }

  private performBasicTopicAnalysis(text: string): TopicAnalysis {
    // Add document to TF-IDF
    this.tfidf.addDocument(text);

    // Extract top terms
    const items: Array<{ term: string; tfidf: number }> = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.tfidf.listTerms(0).forEach((item: any) => {
      if (item.term.length > 3) { // Filter short words
        items.push(item);
      }
    });

    // Convert to topics
    const topics = items.slice(0, 5).map((item) => ({
      name: item.term,
      score: item.tfidf,
      keywords: [item.term],
      sentences: []
    }));

    // Basic readability (simplified)
    const readabilityScore = this.calculateReadability(text);
    const complexity = readabilityScore > 0.7 ? 'simple' :
                      readabilityScore > 0.4 ? 'moderate' : 'complex';

    return {
      topics,
      themes: topics.map(t => t.name),
      categories: [],
      readabilityScore,
      complexity
    };
  }

  private calculateReadability(text: string): number {
    const sentences = text.split(/[.!?]+/).length;
    const words = text.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    const avgCharsPerWord = text.length / words;

    // Simplified readability score (higher = more readable)
    const score = 1 - Math.min(1, (avgWordsPerSentence / 20 + avgCharsPerWord / 10) / 2);
    return Math.max(0, score);
  }

  private buildTopicPrompt(text: string): string {
    return `
Analyze the following text for topics, themes, and readability:

"${text}"

Provide:
1. Main topics with relevance scores (0-1) and key descriptive keywords
2. Overarching themes that connect the topics
3. Content categories this text belongs to
4. Readability analysis (score 0-1, complexity level, explanation)

Focus on identifying themes that would be valuable for creating polls or surveys about this content.
    `.trim();
  }
}

/**
 * Poll Suggestion Service
 */
export class PollSuggestionService {
  /**
   * Generate intelligent poll suggestions based on content
   */
  async generatePollSuggestions(text: string, audience?: string): Promise<PollSuggestion[]> {
    try {
      const { object } = await generateObject({
        model: aiProviders.pollGeneration,
        schema: PollSuggestionsSchema,
        prompt: this.buildSuggestionPrompt(text, audience),
        temperature: 0.4
      });

      return object.suggestions.map(suggestion => ({
        question: suggestion.question || "What is your opinion on this topic?",
        type: suggestion.type || "single",
        reasoning: suggestion.reasoning || "General feedback collection",
        relevanceScore: suggestion.relevanceScore || 0.5,
        options: suggestion.options
      }));
    } catch (error) {
      console.warn('AI poll suggestions failed, using basic suggestions:', error);
      return this.generateBasicSuggestions(text);
    }
  }

  private generateBasicSuggestions(text: string): PollSuggestion[] {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _ = text; // Reserved for future enhancement
    // Basic fallback suggestions
    return [
      {
        question: "What is your overall opinion about this topic?",
        type: "likert",
        reasoning: "General opinion assessment",
        relevanceScore: 0.7,
        options: ["Strongly Agree", "Agree", "Neutral", "Disagree", "Strongly Disagree"]
      },
      {
        question: "What aspects of this topic interest you most?",
        type: "multiple",
        reasoning: "Interest-based segmentation",
        relevanceScore: 0.6,
        options: ["Implementation", "Benefits", "Challenges", "Future prospects"]
      },
      {
        question: "How would you describe your experience with this topic?",
        type: "open",
        reasoning: "Qualitative insights collection",
        relevanceScore: 0.8
      }
    ];
  }

  private buildSuggestionPrompt(text: string, audience?: string): string {
    const audienceContext = audience ? `Target audience: ${audience}` : 'General audience';

    return `
Based on the following content, generate specific poll questions that would gather meaningful insights:

Content: "${text}"
${audienceContext}

For each suggestion, provide:
1. A clear, unbiased question
2. Appropriate question type (single, multiple, likert, open)
3. Reasoning for why this question would be valuable
4. Relevance score (0-1) based on content alignment
5. Suggested options for choice-based questions

Focus on questions that would:
- Capture different perspectives on the topic
- Provide actionable insights
- Engage the target audience
- Avoid leading or biased phrasing
- Cover both quantitative and qualitative aspects

Generate 4-6 diverse, high-quality suggestions.
    `.trim();
  }
}

/**
 * Content Quality Assessment Service
 */
export class ContentQualityService {
  /**
   * Assess content quality for poll creation
   */
  async assessContentQuality(text: string): Promise<ContentQuality> {
    try {
      const { object } = await generateObject({
        model: aiProviders.contentExtraction,
        schema: ContentQualitySchema,
        prompt: this.buildQualityPrompt(text),
        temperature: 0.2
      });

      return {
        clarity: object.quality.clarity,
        engagement: object.quality.engagement,
        informativeness: object.quality.informativeness,
        bias: object.quality.bias,
        overallScore: object.quality.overallScore,
        recommendations: object.recommendations
      };
    } catch (error) {
      console.warn('AI quality assessment failed, using basic assessment:', error);
      return this.performBasicQualityAssessment(text);
    }
  }

  private performBasicQualityAssessment(text: string): ContentQuality {
    const words = text.split(/\s+/).length;
    const sentences = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = words / sentences;

    // Basic heuristics
    const clarity = Math.min(1, 1 - (avgWordsPerSentence - 15) / 20);
    const engagement = Math.min(1, text.length / 1000);
    const informativeness = Math.min(1, words / 100);
    const bias = this.detectBasicBias(text);

    const overallScore = (clarity + engagement + informativeness + (1 - bias)) / 4;

    return {
      clarity: Math.max(0, clarity),
      engagement: Math.max(0, engagement),
      informativeness: Math.max(0, informativeness),
      bias,
      overallScore: Math.max(0, overallScore),
      recommendations: this.generateBasicRecommendations(clarity, engagement, informativeness, bias)
    };
  }

  private detectBasicBias(text: string): number {
    const biasWords = ['obviously', 'clearly', 'everyone knows', 'always', 'never', 'must', 'should'];
    const words = text.toLowerCase().split(/\s+/);
    const biasCount = words.filter(word =>
      biasWords.some(bw => word.includes(bw))
    ).length;

    return Math.min(biasCount / words.length * 10, 1);
  }

  private generateBasicRecommendations(clarity: number, engagement: number, informativeness: number, bias: number): string[] {
    const recommendations: string[] = [];

    if (clarity < 0.6) {
      recommendations.push("Consider simplifying sentence structure for better clarity");
    }
    if (engagement < 0.5) {
      recommendations.push("Add more engaging content or examples to capture audience interest");
    }
    if (informativeness < 0.4) {
      recommendations.push("Provide more detailed information to enhance content value");
    }
    if (bias > 0.3) {
      recommendations.push("Review content for potential bias and use more neutral language");
    }

    return recommendations;
  }

  private buildQualityPrompt(text: string): string {
    return `
Assess the quality of the following content for poll creation purposes:

"${text}"

Evaluate on these dimensions (0-1 scale):
1. Clarity: How clear and understandable is the content?
2. Engagement: How engaging and interesting is it for readers?
3. Informativeness: How much valuable information does it contain?
4. Bias: How much bias or leading language is present? (higher = more biased)
5. Overall score: Combined assessment of content quality

Also provide specific recommendations for improving the content to make it more suitable for poll creation.

Consider factors like:
- Language complexity and readability
- Presence of clear, distinct topics
- Emotional tone and objectivity
- Completeness of information
- Potential for generating meaningful poll questions
    `.trim();
  }
}

/**
 * Comprehensive NLP Analysis Manager
 */
export class NLPAnalysisManager {
  private sentimentService: SentimentAnalysisService;
  private topicService: TopicAnalysisService;
  private pollSuggestionService: PollSuggestionService;
  private qualityService: ContentQualityService;

  constructor() {
    this.sentimentService = new SentimentAnalysisService();
    this.topicService = new TopicAnalysisService();
    this.pollSuggestionService = new PollSuggestionService();
    this.qualityService = new ContentQualityService();
  }

  /**
   * Perform comprehensive content analysis
   */
  async analyzeContent(text: string, options: {
    includeSentiment?: boolean;
    includeTopics?: boolean;
    includePollSuggestions?: boolean;
    includeQuality?: boolean;
    audience?: string;
    useAI?: boolean;
  } = {}): Promise<{
    sentiment?: SentimentAnalysis;
    topics?: TopicAnalysis;
    pollSuggestions?: PollSuggestion[];
    quality?: ContentQuality;
  }> {
    const {
      includeSentiment = true,
      includeTopics = true,
      includePollSuggestions = true,
      includeQuality = true,
      audience,
      useAI = true
    } = options;

    const results: {
      sentiment?: SentimentAnalysis;
      topics?: TopicAnalysis;
      pollSuggestions?: PollSuggestion[];
      quality?: ContentQuality;
    } = {};

    if (includeSentiment) {
      results.sentiment = await this.sentimentService.analyzeSentiment(text, useAI);
    }

    if (includeTopics) {
      results.topics = await this.topicService.analyzeTopics(text, useAI);
    }

    if (includePollSuggestions) {
      results.pollSuggestions = await this.pollSuggestionService.generatePollSuggestions(text, audience);
    }

    if (includeQuality) {
      results.quality = await this.qualityService.assessContentQuality(text);
    }

    return results;
  }
}

// Export singleton instance
export const nlpAnalyzer = new NLPAnalysisManager();
