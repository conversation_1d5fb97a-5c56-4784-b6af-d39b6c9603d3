/**
 * Phase 2: Enhanced Document Processing Services
 * Supports multiple document formats with intelligent content extraction
 */

import PDFParse from 'pdf-parse';
import mammoth from 'mammoth';
import { createWorker } from 'tesseract.js';

// Document processing types
export interface DocumentProcessor {
  process(buffer: Buffer, options?: ProcessingOptions): Promise<ProcessedDocument>;
  supportedMimeTypes: string[];
}

export interface ProcessingOptions {
  enableOCR?: boolean;
  ocrLanguage?: string;
  extractTables?: boolean;
  extractImages?: boolean;
  confidenceThreshold?: number;
}

export interface ProcessedDocument {
  text: string;
  metadata: DocumentMetadata;
  structure: DocumentStructure;
  confidence: number;
  processingTime: number;
}

export interface DocumentMetadata {
  format: string;
  pageCount?: number;
  wordCount: number;
  characterCount: number;
  language?: string;
  author?: string;
  title?: string;
  subject?: string;
  creationDate?: Date;
  modificationDate?: Date;
}

export interface DocumentStructure {
  headings: DocumentHeading[];
  paragraphs: DocumentParagraph[];
  tables?: DocumentTable[];
  images?: DocumentImage[];
  lists?: DocumentList[];
}

export interface DocumentHeading {
  level: number;
  text: string;
  position: number;
}

export interface DocumentParagraph {
  text: string;
  position: number;
  style?: string;
}

export interface DocumentTable {
  headers: string[];
  rows: string[][];
  position: number;
}

export interface DocumentImage {
  alt?: string;
  position: number;
  width?: number;
  height?: number;
  extractedText?: string;
}

export interface DocumentList {
  type: 'ordered' | 'unordered';
  items: string[];
  position: number;
}

/**
 * PDF Document Processor
 */
export class PDFProcessor implements DocumentProcessor {
  supportedMimeTypes = ['application/pdf'];

  async process(buffer: Buffer, _options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    const startTime = Date.now();

    try {
      const pdfData = await PDFParse(buffer, {
        // PDF processing options
        normalizeWhitespace: true,
        max: 0, // No page limit
        // Use OCR settings if provided
        ...(_options.enableOCR && { pagerender: undefined }),
      });

      const text = pdfData.text || '';
      const metadata: DocumentMetadata = {
        format: 'PDF',
        pageCount: pdfData.numpages,
        wordCount: text.split(/\s+/).length,
        characterCount: text.length,
        title: pdfData.info?.Title,
        author: pdfData.info?.Author,
        subject: pdfData.info?.Subject,
        creationDate: pdfData.info?.CreationDate ? new Date(pdfData.info.CreationDate) : undefined,
        modificationDate: pdfData.info?.ModDate ? new Date(pdfData.info.ModDate) : undefined,
      };

      const structure = this.extractStructure(text);
      const confidence = this.calculateConfidence(text, metadata);

      return {
        text,
        metadata,
        structure,
        confidence,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('PDF processing error:', error);
      throw new Error(`PDF processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private extractStructure(text: string): DocumentStructure {
    const lines = text.split('\n');
    const headings: DocumentHeading[] = [];
    const paragraphs: DocumentParagraph[] = [];

    let position = 0;
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      // Simple heading detection (lines that are short and end without punctuation)
      if (trimmed.length < 100 && !trimmed.match(/[.!?]$/)) {
        headings.push({
          level: 1,
          text: trimmed,
          position: position++,
        });
      } else {
        paragraphs.push({
          text: trimmed,
          position: position++,
        });
      }
    }

    return {
      headings,
      paragraphs,
    };
  }

  private calculateConfidence(text: string, metadata: DocumentMetadata): number {
    let confidence = 0.5; // Base confidence

    // Higher confidence for longer texts
    if (text.length > 1000) confidence += 0.2;
    if (text.length > 5000) confidence += 0.1;

    // Higher confidence if metadata is available
    if (metadata.title) confidence += 0.1;
    if (metadata.author) confidence += 0.05;
    if (metadata.pageCount && metadata.pageCount > 0) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }
}

/**
 * Word Document Processor
 */
export class WordProcessor implements DocumentProcessor {
  supportedMimeTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
  ];

  async process(buffer: Buffer, _options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    const startTime = Date.now();

    // Currently, Word processor doesn't use processing options
    // Parameter intentionally unused but kept for interface compatibility
    void _options;

    try {
      const result = await mammoth.extractRawText({ buffer });
      const text = result.value || '';

      const metadata: DocumentMetadata = {
        format: 'Word',
        wordCount: text.split(/\s+/).length,
        characterCount: text.length,
      };

      const structure = this.extractStructure(text);
      const confidence = this.calculateConfidence(text);

      return {
        text,
        metadata,
        structure,
        confidence,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('Word processing error:', error);
      throw new Error(`Word processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private extractStructure(text: string): DocumentStructure {
    const lines = text.split('\n');
    const paragraphs: DocumentParagraph[] = [];

    let position = 0;
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed) {
        paragraphs.push({
          text: trimmed,
          position: position++,
        });
      }
    }

    return {
      headings: [],
      paragraphs,
    };
  }

  private calculateConfidence(text: string): number {
    let confidence = 0.7; // Higher base confidence for Word docs

    if (text.length > 1000) confidence += 0.2;
    if (text.length > 5000) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }
}

/**
 * Image Processor with OCR
 */
export class ImageProcessor implements DocumentProcessor {
  supportedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/bmp',
    'image/tiff',
    'image/webp',
  ];

  async process(buffer: Buffer, _options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    const startTime = Date.now();

    try {
      const ocrResult = await this.performOCR(buffer, _options.ocrLanguage || 'eng');
      const text = ocrResult.text;

      const metadata: DocumentMetadata = {
        format: 'Image',
        wordCount: text.split(/\s+/).filter(w => w.length > 0).length,
        characterCount: text.length,
      };

      const structure: DocumentStructure = {
        headings: [],
        paragraphs: text ? [{
          text,
          position: 0,
        }] : [],
      };

      return {
        text,
        metadata,
        structure,
        confidence: ocrResult.confidence,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('Image processing error:', error);
      throw new Error(`Image processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async performOCR(buffer: Buffer, language: string): Promise<{ text: string; confidence: number }> {
    const worker = await createWorker(language, 1, {
      workerPath: typeof window !== 'undefined' ? '/_next/static/workers/worker.min.js' : undefined,
      corePath: typeof window !== 'undefined' ? '/_next/static/workers/tesseract-core.wasm.js' : undefined,
    });

    try {
      const { data } = await worker.recognize(buffer);
      await worker.terminate();

      return {
        text: data.text || '',
        confidence: data.confidence / 100, // Convert percentage to 0-1
      };
    } catch (error) {
      await worker.terminate();
      throw error;
    }
  }
}

/**
 * Document Processing Factory
 */
export class DocumentProcessorFactory {
  private processors: DocumentProcessor[] = [
    new PDFProcessor(),
    new WordProcessor(),
    new ImageProcessor(),
  ];

  getProcessor(mimeType: string): DocumentProcessor | null {
    return this.processors.find(processor =>
      processor.supportedMimeTypes.includes(mimeType)
    ) || null;
  }

  async processDocument(
    buffer: Buffer,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessedDocument> {
    const processor = this.getProcessor(mimeType);

    if (!processor) {
      throw new Error(`Unsupported document type: ${mimeType}`);
    }

    return processor.process(buffer, options);
  }

  getSupportedMimeTypes(): string[] {
    return this.processors.flatMap(processor => processor.supportedMimeTypes);
  }
}

// Export a default instance
export const documentProcessorFactory = new DocumentProcessorFactory();
