/**
 * Fallback Manager for AI SDK Integration
 * Provides robust error handling and service fallbacks
 */

export interface FallbackOptions {
  maxRetries?: number;
  retryDelay?: number;
  logErrors?: boolean;
  timeoutMs?: number;
}

export class FallbackManager {
  private defaultOptions: Required<FallbackOptions> = {
    maxRetries: 2,
    retryDelay: 1000,
    logErrors: true,
    timeoutMs: 30000
  };

  /**
   * Execute operation with fallback strategy
   */
  async executeWithFallback<T>(
    primaryFn: () => Promise<T>,
    fallbackFn: () => Promise<T>,
    operation: string,
    options: FallbackOptions = {}
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };

    let lastError: Error | null = null;

    // Try primary function with retries
    for (let attempt = 1; attempt <= opts.maxRetries; attempt++) {
      try {
        if (opts.logErrors && attempt > 1) {
          console.log(`${operation} - Attempt ${attempt}/${opts.maxRetries}`);
        }

        const result = await this.withTimeout(primaryFn(), opts.timeoutMs);

        if (opts.logErrors && attempt > 1) {
          console.log(`${operation} - Primary succeeded on attempt ${attempt}`);
        }

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (opts.logErrors) {
          console.warn(`${operation} - Primary attempt ${attempt} failed:`, lastError.message);
        }

        // Wait before retry (except on last attempt)
        if (attempt < opts.maxRetries) {
          await this.delay(opts.retryDelay * attempt);
        }
      }
    }

    // Primary function failed, try fallback
    try {
      if (opts.logErrors) {
        console.warn(`${operation} - Primary failed after ${opts.maxRetries} attempts, using fallback`);
      }

      const result = await this.withTimeout(fallbackFn(), opts.timeoutMs);

      if (opts.logErrors) {
        console.log(`${operation} - Fallback succeeded`);
      }

      return result;
    } catch (fallbackError) {
      if (opts.logErrors) {
        console.error(`${operation} - Both primary and fallback failed`);
        console.error('Primary error:', lastError);
        console.error('Fallback error:', fallbackError);
      }

      // Throw the more specific error
      throw new Error(
        `${operation} failed. Primary: ${lastError?.message}. Fallback: ${fallbackError}`
      );
    }
  }

  /**
   * Execute multiple operations with intelligent fallback routing
   */
  async executeWithMultipleFallbacks<T>(
    operations: Array<{
      name: string;
      fn: () => Promise<T>;
      priority: number;
    }>,
    operationName: string
  ): Promise<T> {
    // Sort by priority (lower number = higher priority)
    const sortedOps = operations.sort((a, b) => a.priority - b.priority);

    let lastError: Error | null = null;

    for (const op of sortedOps) {
      try {
        console.log(`${operationName} - Trying ${op.name}`);
        const result = await this.withTimeout(op.fn(), this.defaultOptions.timeoutMs);
        console.log(`${operationName} - ${op.name} succeeded`);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`${operationName} - ${op.name} failed:`, lastError.message);
      }
    }

    throw new Error(`${operationName} - All operations failed. Last error: ${lastError?.message}`);
  }

  /**
   * Circuit breaker pattern for failing services
   */
  private circuitBreakers = new Map<string, {
    failures: number;
    lastFailure: number;
    isOpen: boolean;
  }>();

  async executeWithCircuitBreaker<T>(
    fn: () => Promise<T>,
    serviceName: string,
    options: {
      failureThreshold?: number;
      resetTimeoutMs?: number;
    } = {}
  ): Promise<T> {
    const { failureThreshold = 5, resetTimeoutMs = 60000 } = options;

    const breaker = this.circuitBreakers.get(serviceName) || {
      failures: 0,
      lastFailure: 0,
      isOpen: false
    };

    // Check if circuit is open and should be reset
    if (breaker.isOpen && Date.now() - breaker.lastFailure > resetTimeoutMs) {
      breaker.isOpen = false;
      breaker.failures = 0;
      console.log(`Circuit breaker for ${serviceName} reset`);
    }

    // If circuit is open, fail fast
    if (breaker.isOpen) {
      throw new Error(`Circuit breaker for ${serviceName} is open`);
    }

    try {
      const result = await fn();

      // Success - reset failure count
      if (breaker.failures > 0) {
        breaker.failures = 0;
        console.log(`Circuit breaker for ${serviceName} - failures reset after success`);
      }

      this.circuitBreakers.set(serviceName, breaker);
      return result;
    } catch (error) {
      breaker.failures++;
      breaker.lastFailure = Date.now();

      if (breaker.failures >= failureThreshold) {
        breaker.isOpen = true;
        console.warn(`Circuit breaker for ${serviceName} opened after ${breaker.failures} failures`);
      }

      this.circuitBreakers.set(serviceName, breaker);
      throw error;
    }
  }

  /**
   * Utility function to add timeout to promises
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get circuit breaker status for monitoring
   */
  getCircuitBreakerStatus(): Record<string, {
    failures: number;
    isOpen: boolean;
    lastFailure: string | null;
  }> {
    const status: Record<string, {
      failures: number;
      isOpen: boolean;
      lastFailure: string | null;
    }> = {};

    for (const [service, breaker] of this.circuitBreakers.entries()) {
      status[service] = {
        failures: breaker.failures,
        isOpen: breaker.isOpen,
        lastFailure: breaker.lastFailure ? new Date(breaker.lastFailure).toISOString() : null
      };
    }

    return status;
  }
}

// Export singleton instance
export const fallbackManager = new FallbackManager();

// Export convenience functions
export async function withFallback<T>(
  primary: () => Promise<T>,
  fallback: () => Promise<T>,
  operation: string,
  options?: FallbackOptions
): Promise<T> {
  return fallbackManager.executeWithFallback(primary, fallback, operation, options);
}

export async function withMultipleFallbacks<T>(
  operations: Array<{ name: string; fn: () => Promise<T>; priority: number }>,
  operationName: string
): Promise<T> {
  return fallbackManager.executeWithMultipleFallbacks(operations, operationName);
}
