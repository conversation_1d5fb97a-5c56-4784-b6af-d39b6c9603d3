/**
 * Mistral OCR API Integration
 * Handles PDF extraction using Mistral's OCR capabilities
 */

import { Mistral } from '@mistralai/mistralai';

export interface MistralOCROptions {
  language?: string;
  confidenceThreshold?: number;
}

export interface MistralOCRResult {
  text: string;
  confidence: number;
  processingTime: number;
}

export class MistralOCRService {
  private client: Mistral;

  constructor() {
    // Use environment variable for API key
    const apiKey = process.env.MISTRAL_API_KEY || process.env.NEXT_PUBLIC_MISTRAL_API_KEY;
    
    if (!apiKey) {
      throw new Error('Mistral API key is not configured');
    }

    this.client = new Mistral({ apiKey });
  }

  /**
   * Extract text from a PDF file using Mistral's OCR capabilities
   */
  async extractTextFromPDF(file: File | Buffer): Promise<MistralOCRResult> {
    const startTime = Date.now();
    
    try {
      // Convert Buffer or File to base64 string
      let base64Data: string;
      
      if (Buffer.isBuffer(file)) {
        base64Data = file.toString('base64');
      } else {
        // Convert File to base64
        const arrayBuffer = await file.arrayBuffer();
        base64Data = Buffer.from(arrayBuffer).toString('base64');
      }

      // Call Mistral OCR API using the ocr.process method with mistral-ocr-latest model
      // This is the correct approach for PDF processing according to Mistral docs
      const ocrResponse = await this.client.ocr.process({
        model: "mistral-ocr-latest",
        document: {
          type: "document_url",
          documentUrl: `data:application/pdf;base64,${base64Data}`
        },
        includeImageBase64: false
      });

      // Process the response
      const processingTime = Date.now() - startTime;
      
      // Extract text from all pages
      let extractedText = '';
      if (ocrResponse.pages && ocrResponse.pages.length > 0) {
        extractedText = ocrResponse.pages.map(page => page.markdown || '').join('\n\n');
      } else {
        extractedText = 'No text extracted from document';
      }
      
      // Return extracted text
      return {
        text: extractedText,
        confidence: 0.95, // Confidence is estimated since OCR is using a dedicated model
        processingTime
      };
    } catch (error) {
      console.error('Mistral OCR extraction failed:', error);
      throw new Error(`Failed to extract text using Mistral OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const mistralOCR = new MistralOCRService();
