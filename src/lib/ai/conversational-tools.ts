import { tool } from 'ai';
import { z } from 'zod';

/**
 * AI Tools for Conversational Poll Creation
 * Implements human-in-the-loop pattern for poll creation
 */

// Tool for suggesting a poll creation (requires user confirmation)
export const suggestPollCreation = tool({
  description: 'Suggest creating a poll based on the conversation. This tool does not create the poll - it only suggests it for user confirmation.',
  parameters: z.object({
    title: z.string().describe('The suggested title for the poll'),
    description: z.string().describe('A brief description of what the poll is about'),
    questions: z.array(z.object({
      text: z.string().describe('The question text'),
      type: z.enum(['single', 'multiple', 'open', 'text']).describe('Type of question'),
      options: z.array(z.string()).optional().describe('Options for single/multiple choice questions'),
      required: z.boolean().describe('Whether this question is required')
    })).describe('Array of suggested questions'),
    reasoning: z.string().describe('Brief explanation of why this poll structure was chosen')
  }),
  // Execute function for AI SDK streaming compatibility - returns structured data for client handling
  execute: async ({ title, description, questions, reasoning }) => {
    return {
      type: 'poll_suggestion',
      title,
      description,
      questions,
      reasoning,
      requiresConfirmation: true,
      timestamp: new Date().toISOString()
    };
  }
});

// Tool for gathering additional information about poll requirements
export const gatherPollRequirements = tool({
  description: 'Ask the user for additional information needed to create a better poll',
  parameters: z.object({
    missingInfo: z.array(z.string()).describe('List of missing information items'),
    clarificationQuestions: z.array(z.string()).describe('Questions to ask the user for clarification'),
    currentUnderstanding: z.string().describe('What we understand so far about their poll needs')
  }),
  // Execute function for AI SDK streaming compatibility - returns structured data for client handling
  execute: async ({ missingInfo, clarificationQuestions, currentUnderstanding }) => {
    return {
      type: 'requirements_gathering',
      missingInfo,
      clarificationQuestions,
      currentUnderstanding,
      requiresUserInput: true,
      timestamp: new Date().toISOString()
    };
  }
});

// Tool for analyzing conversation context to understand poll needs
export const analyzeConversationContext = tool({
  description: 'Analyze the conversation to understand what kind of poll the user wants',
  parameters: z.object({
    detectedTopic: z.string().describe('The main topic or theme detected'),
    detectedAudience: z.string().describe('The intended audience for the poll'),
    detectedPurpose: z.string().describe('The purpose or goal of the poll'),
    confidence: z.number().min(0).max(1).describe('Confidence level in the analysis (0-1)'),
    needsMoreInfo: z.boolean().describe('Whether more information is needed before suggesting a poll')
  }),
  // This tool can auto-execute as it's just analysis
  execute: async ({ detectedTopic, detectedAudience, detectedPurpose, confidence, needsMoreInfo }) => {
    return {
      topic: detectedTopic,
      audience: detectedAudience,
      purpose: detectedPurpose,
      confidence,
      needsMoreInfo,
      timestamp: new Date().toISOString()
    };
  }
});

// Export tools for use in conversational AI
export const conversationalTools = {
  suggestPollCreation,
  gatherPollRequirements,
  analyzeConversationContext
};

// Types for tool responses
export type PollSuggestion = z.infer<typeof suggestPollCreation.parameters>;
export type PollRequirements = z.infer<typeof gatherPollRequirements.parameters>;
export type ConversationAnalysis = z.infer<typeof analyzeConversationContext.parameters>;
