/**
 * Phase 2: Enhanced Document Processing Services
 * Supports multiple document formats with intelligent content extraction
 */

import PDFParse from 'pdf-parse';
import mammoth from 'mammoth';
import { createWorker } from 'tesseract.js';

// Document processing types
export interface DocumentProcessor {
  process(buffer: Buffer, options?: ProcessingOptions): Promise<ProcessedDocument>;
  supportedMimeTypes: string[];
}

export interface ProcessingOptions {
  enableOCR?: boolean;
  ocrLanguage?: string;
  extractTables?: boolean;
  extractImages?: boolean;
  confidenceThreshold?: number;
}

export interface ProcessedDocument {
  text: string;
  metadata: DocumentMetadata;
  structure: DocumentStructure;
  confidence: number;
  processingTime: number;
}

export interface DocumentMetadata {
  format: string;
  pageCount?: number;
  wordCount: number;
  characterCount: number;
  language?: string;
  author?: string;
  title?: string;
  subject?: string;
  creationDate?: Date;
  modificationDate?: Date;
}

export interface DocumentStructure {
  headings: DocumentHeading[];
  paragraphs: DocumentParagraph[];
  tables?: DocumentTable[];
  images?: DocumentImage[];
  lists?: DocumentList[];
}

export interface DocumentHeading {
  level: number;
  text: string;
  position: number;
}

export interface DocumentParagraph {
  text: string;
  position: number;
  style?: string;
}

export interface DocumentTable {
  headers: string[];
  rows: string[][];
  position: number;
}

export interface DocumentImage {
  alt?: string;
  position: number;
  width?: number;
  height?: number;
  extractedText?: string;
}

export interface DocumentList {
  type: 'ordered' | 'unordered';
  items: string[];
  position: number;
}

/**
 * PDF Document Processor
 */
export class PDFProcessor implements DocumentProcessor {
  supportedMimeTypes = ['application/pdf'];

  async process(buffer: Buffer, options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    const startTime = Date.now();

    try {
      const pdfData = await PDFParse(buffer, {
        // PDF processing options
        pagerender: async (pageData) => {
          // Custom page rendering for better text extraction
          return pageData.getTextContent();
        }
      });

      const text = pdfData.text;
      const structure = this.analyzeStructure(text);
      const metadata = this.extractMetadata(pdfData, text);

      // OCR for scanned PDFs if enabled and text extraction is poor
      let finalText = text;
      let confidence = this.calculateConfidence(text);

      if (options.enableOCR && confidence < (options.confidenceThreshold || 0.8)) {
        try {
          const ocrText = await this.performOCR(buffer, options.ocrLanguage || 'eng');
          if (ocrText.confidence > confidence) {
            finalText = ocrText.text;
            confidence = ocrText.confidence;
          }
        } catch (ocrError) {
          console.warn('OCR failed for PDF:', ocrError);
        }
      }

      return {
        text: finalText,
        metadata,
        structure,
        confidence,
        processingTime: Date.now() - startTime
      };
    } catch (error) {
      throw new Error(`PDF processing failed: ${error}`);
    }
  }

  private analyzeStructure(text: string): DocumentStructure {
    const lines = text.split('\n').filter(line => line.trim());
    const structure: DocumentStructure = {
      headings: [],
      paragraphs: []
    };

    let position = 0;
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      // Simple heading detection (could be enhanced)
      if (this.isHeading(trimmed)) {
        structure.headings.push({
          level: this.getHeadingLevel(trimmed),
          text: trimmed,
          position
        });
      } else {
        structure.paragraphs.push({
          text: trimmed,
          position
        });
      }
      position++;
    }

    return structure;
  }

  private extractMetadata(pdfData: { numpages: number; info?: { Title?: string; Author?: string; Subject?: string; CreationDate?: string; ModDate?: string } }, text: string): DocumentMetadata {
    return {
      format: 'PDF',
      pageCount: pdfData.numpages,
      wordCount: text.split(/\s+/).length,
      characterCount: text.length,
      title: pdfData.info?.Title,
      author: pdfData.info?.Author,
      subject: pdfData.info?.Subject,
      creationDate: pdfData.info?.CreationDate ? new Date(pdfData.info.CreationDate) : undefined,
      modificationDate: pdfData.info?.ModDate ? new Date(pdfData.info.ModDate) : undefined
    };
  }

  private isHeading(text: string): boolean {
    // Simple heuristics for heading detection
    return text.length < 100 &&
           (!!(text.match(/^[A-Z][A-Z\s]+$/) ||
            text.match(/^\d+\./) ||
            text.match(/^[IVX]+\./) ||
            text.match(/^[A-Z][a-z]+:$/)));
  }

  private getHeadingLevel(text: string): number {
    if (text.match(/^\d+\./)) return 1;
    if (text.match(/^\d+\.\d+/)) return 2;
    if (text.match(/^\d+\.\d+\.\d+/)) return 3;
    return 1;
  }

  private calculateConfidence(text: string): number {
    // Calculate confidence based on text quality
    const totalChars = text.length;
    if (totalChars === 0) return 0;

    const alphanumeric = (text.match(/[a-zA-Z0-9]/g) || []).length;
    const words = text.split(/\s+/).filter(word => word.length > 1).length;
    const avgWordLength = totalChars / words;

    // Confidence metrics
    const alphanumericRatio = alphanumeric / totalChars;
    const wordLengthScore = Math.min(avgWordLength / 5, 1);
    const textLengthScore = Math.min(totalChars / 1000, 1);

    return (alphanumericRatio * 0.5 + wordLengthScore * 0.3 + textLengthScore * 0.2);
  }

  private async performOCR(buffer: Buffer, language: string): Promise<{ text: string; confidence: number }> {
    // Configure tesseract.js to use local workers to avoid worker script errors
    const worker = await createWorker(language, 1, {
      workerPath: typeof window !== 'undefined' ? '/tesseract.js/worker.min.js' : undefined,
      langPath: typeof window !== 'undefined' ? '/tesseract.js/lang-data' : undefined,
      corePath: typeof window !== 'undefined' ? '/tesseract.js/tesseract-core.wasm.js' : undefined,
    });

    try {
      const { data } = await worker.recognize(buffer);
      await worker.terminate();

      return {
        text: data.text,
        confidence: data.confidence / 100
      };
    } catch (error) {
      await worker.terminate();
      console.error('OCR processing failed:', error);
      throw new Error(`OCR failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * DOCX Document Processor
 */
export class DOCXProcessor implements DocumentProcessor {
  supportedMimeTypes = ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

  async process(buffer: Buffer, options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { enableOCR, ...restOptions } = options;
    const startTime = Date.now();

    try {
      const result = await mammoth.extractRawText({ buffer });
      const text = result.value;
      const structure = this.analyzeStructure(text);
      const metadata = this.extractMetadata(text);

      return {
        text,
        metadata,
        structure,
        confidence: this.calculateConfidence(text, result.messages),
        processingTime: Date.now() - startTime
      };
    } catch (error) {
      throw new Error(`DOCX processing failed: ${error}`);
    }
  }

  private analyzeStructure(text: string): DocumentStructure {
    // Similar to PDF structure analysis but adapted for DOCX
    const lines = text.split('\n').filter(line => line.trim());
    const structure: DocumentStructure = {
      headings: [],
      paragraphs: []
    };

    let position = 0;
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      if (this.isHeading(trimmed)) {
        structure.headings.push({
          level: this.getHeadingLevel(trimmed),
          text: trimmed,
          position
        });
      } else {
        structure.paragraphs.push({
          text: trimmed,
          position
        });
      }
      position++;
    }

    return structure;
  }

  private extractMetadata(text: string): DocumentMetadata {
    return {
      format: 'DOCX',
      wordCount: text.split(/\s+/).length,
      characterCount: text.length
    };
  }

  private isHeading(text: string): boolean {
    return text.length < 100 &&
           (!!(text.match(/^[A-Z][A-Z\s]+$/) ||
            text.match(/^\d+\./) ||
            text.match(/^Chapter \d+/) ||
            text.match(/^Section \d+/)));
  }

  private getHeadingLevel(text: string): number {
    if (text.match(/^Chapter/)) return 1;
    if (text.match(/^Section/)) return 2;
    if (text.match(/^\d+\./)) return this.getNumberLevel(text);
    return 1;
  }

  private getNumberLevel(text: string): number {
    const match = text.match(/^(\d+\.)+/);
    if (!match) return 1;
    return (match[0].match(/\./g) || []).length;
  }

  private calculateConfidence(text: string, messages: { type: string; message: string }[]): number {
    // Base confidence on text quality and conversion messages
    const hasWarnings = messages.some(msg => msg.type === 'warning');
    const baseConfidence = this.calculateTextConfidence(text);

    return hasWarnings ? baseConfidence * 0.9 : baseConfidence;
  }

  private calculateTextConfidence(text: string): number {
    if (text.length === 0) return 0;

    const words = text.split(/\s+/).filter(word => word.length > 1);
    const avgWordLength = text.length / words.length;

    return Math.min(avgWordLength / 5, 1) * 0.8 + Math.min(text.length / 1000, 1) * 0.2;
  }
}

/**
 * Image OCR Processor for standalone images
 */
export class ImageProcessor implements DocumentProcessor {
  supportedMimeTypes = ['image/jpeg', 'image/png', 'image/tiff', 'image/bmp'];

  async process(buffer: Buffer, options: ProcessingOptions = {}): Promise<ProcessedDocument> {
    const startTime = Date.now();

    try {
      const result = await this.performOCR(buffer, options.ocrLanguage || 'eng');
      const structure = this.analyzeStructure(result.text);
      const metadata = this.extractMetadata(result.text);

      return {
        text: result.text,
        metadata,
        structure,
        confidence: result.confidence,
        processingTime: Date.now() - startTime
      };
    } catch (error) {
      throw new Error(`Image processing failed: ${error}`);
    }
  }

  private async performOCR(buffer: Buffer, language: string): Promise<{ text: string; confidence: number }> {
    const worker = await createWorker(language);

    try {
      const { data } = await worker.recognize(buffer);
      await worker.terminate();

      return {
        text: data.text,
        confidence: data.confidence / 100
      };
    } catch (error) {
      await worker.terminate();
      throw error;
    }
  }

  private analyzeStructure(text: string): DocumentStructure {
    const lines = text.split('\n').filter(line => line.trim());
    return {
      headings: [],
      paragraphs: lines.map((line, index) => ({
        text: line.trim(),
        position: index
      }))
    };
  }

  private extractMetadata(text: string): DocumentMetadata {
    return {
      format: 'Image',
      wordCount: text.split(/\s+/).length,
      characterCount: text.length
    };
  }
}

/**
 * Document Processing Manager
 */
export class DocumentProcessingManager {
  private processors: Map<string, DocumentProcessor> = new Map();

  constructor() {
    // Register processors
    const pdfProcessor = new PDFProcessor();
    const docxProcessor = new DOCXProcessor();
    const imageProcessor = new ImageProcessor();

    pdfProcessor.supportedMimeTypes.forEach(type => {
      this.processors.set(type, pdfProcessor);
    });

    docxProcessor.supportedMimeTypes.forEach(type => {
      this.processors.set(type, docxProcessor);
    });

    imageProcessor.supportedMimeTypes.forEach(type => {
      this.processors.set(type, imageProcessor);
    });
  }

  async processDocument(
    buffer: Buffer,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessedDocument> {
    const processor = this.processors.get(mimeType);

    if (!processor) {
      throw new Error(`Unsupported document type: ${mimeType}`);
    }

    return processor.process(buffer, options);
  }

  getSupportedMimeTypes(): string[] {
    return Array.from(this.processors.keys());
  }

  isSupported(mimeType: string): boolean {
    return this.processors.has(mimeType);
  }
}

// Export singleton instance
export const documentProcessor = new DocumentProcessingManager();
