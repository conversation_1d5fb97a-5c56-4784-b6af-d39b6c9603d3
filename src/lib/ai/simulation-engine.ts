import { z } from 'zod';
import { SimulationRequest, SimulationResponse } from '@/lib/types/simulation';
import { simulatePoll } from '@/lib/services/perplexity-ai';
import { FeatureGate } from './feature-flags';

/**
 * AI PROVIDER STRATEGY (see AI-PROVIDER-STRATEGY.md):
 *
 * This simulation engine uses Perplexity AI exclusively for simulation tasks.
 *
 * SIMULATION PROVIDER: Perplexity AI
 * - Service: @/lib/services/perplexity-ai
 * - Model: sonar (current model with search)
 * - Purpose: Poll simulation with real-time web data
 * - Fallback: Direct Perplexity service call
 *
 * Note: AI SDK structured generation is not currently supported for Perplexity.
 * All simulation requests use the existing Perplexity service wrapper.
 */

// Enhanced simulation schema for structured generation
const SimulationSchema = z.object({
  simulationId: z.string(),
  demographics: z.array(z.object({
    group: z.string(),
    percentage: z.number().min(0).max(100),
    responses: z.array(z.object({
      questionId: z.string().optional(),
      response: z.string(),
      confidence: z.number().min(0).max(1),
      reasoning: z.string().optional()
    }))
  })),
  results: z.object({
    distribution: z.record(z.string(), z.number()),
    insights: z.array(z.string()),
    summary: z.string()
  }),
  metadata: z.object({
    accuracy: z.number().min(0).max(1),
    sampleSize: z.number(),
    biasFactors: z.array(z.string()),
    methodology: z.string(),
    confidence: z.number().min(0).max(1),
    demographic: z.string(),
    citations: z.array(z.string()),
    processingTime: z.number()
  })
});

// Types for batch simulation results
interface DemographicComparison {
  demographic: string;
  distribution: Record<string, number>;
  variance: number;
}

interface AggregatedResults {
  overallDistribution: Record<string, number>;
  demographicComparison: DemographicComparison[];
  insights: string[];
  consensus: string[];
  polarization: string[];
}

interface BatchSimulationResult {
  batchId: string;
  simulations: SimulationResponse[];
  aggregatedResults: AggregatedResults;
  metadata: {
    totalSampleSize: number;
    averageConfidence: number;
    processingTime: number;
    cacheHitRate: number;
  };
}

// Note: BatchSimulationSchema defined for potential future validation
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const BatchSimulationSchema = z.object({
  batchId: z.string(),
  simulations: z.array(SimulationSchema),
  aggregatedResults: z.object({
    overallDistribution: z.record(z.string(), z.number()),
    demographicComparison: z.array(z.object({
      demographic: z.string(),
      distribution: z.record(z.string(), z.number()),
      variance: z.number()
    })),
    insights: z.array(z.string()),
    consensus: z.array(z.string()),
    polarization: z.array(z.string())
  }),
  metadata: z.object({
    totalSampleSize: z.number(),
    averageConfidence: z.number(),
    processingTime: z.number(),
    cacheHitRate: z.number()
  })
});

export class EnhancedSimulationEngine {
  private static readonly MODEL_RETRY_ATTEMPTS = 2;
  private static readonly FALLBACK_DELAY = 1000;

  /**
   * Run enhanced poll simulation with AI SDK structured generation
   */
  async runPollSimulation(request: SimulationRequest): Promise<SimulationResponse> {
    // Check if AI SDK structured generation is enabled
    if (!FeatureGate.isEnabled('structuredGeneration')) {
      console.log('AI SDK structured generation disabled, using fallback');
      return await this.fallbackToExistingSimulation(request);
    }

    try {
      console.log('Running enhanced simulation with AI SDK structured generation');

      // Use Perplexity service directly since AI SDK doesn't support Perplexity yet
      // Fall back to existing simulation system which uses Perplexity
      console.log('Using Perplexity service directly for simulation');
      return await this.fallbackToExistingSimulation(request);

    } catch (error) {
      console.warn('AI SDK simulation failed, falling back to existing system:', error);
      return await this.fallbackToExistingSimulation(request);
    }
  }

  /**
   * Multi-demographic batch simulation with AI SDK
   */
  async runBatchSimulation(
    demographics: string[],
    pollQuestion: string,
    pollOptions: string[],
    onProgress?: (progress: number) => void
  ): Promise<BatchSimulationResult> {
    const startTime = Date.now();
    const batchId = this.generateBatchId();

    try {
      if (!FeatureGate.isEnabled('batchProcessing')) {
        throw new Error('Batch processing disabled');
      }

      console.log(`Starting batch simulation for ${demographics.length} demographics`);

      const simulations: SimulationResponse[] = [];
      let completed = 0;

      // Process each demographic with structured generation
      for (const demographic of demographics) {
        try {
          const request: SimulationRequest = {
            pollQuestion,
            pollOptions,
            demographic: {
              group: demographic,
              size: 100, // Default sample size
              context: ''
            },
            responseFormat: 'distribution'
          };

          const simulation = await this.runPollSimulation(request);
          simulations.push(simulation);

          completed++;
          onProgress?.(completed / demographics.length);

          // Small delay to respect rate limits
          if (completed < demographics.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }

        } catch (error) {
          console.error(`Failed to simulate demographic: ${demographic}`, error);
          // Continue with other demographics
        }
      }

      // Generate aggregated analysis using AI SDK
      const aggregatedResults = await this.generateAggregatedAnalysis(
        simulations,
        pollOptions
      );

      return {
        batchId,
        simulations,
        aggregatedResults,
        metadata: {
          totalSampleSize: simulations.reduce((sum, s) => sum + s.metadata.sampleSize, 0),
          averageConfidence: simulations.reduce((sum, s) => sum + s.metadata.confidence, 0) / simulations.length,
          processingTime: Date.now() - startTime,
          cacheHitRate: 0 // Will be calculated by caching layer
        }
      };

    } catch (error) {
      console.error('Batch simulation failed:', error);
      throw error;
    }
  }

  /**
   * Build enhanced simulation prompt for AI SDK
   */
  private buildEnhancedSimulationPrompt(request: SimulationRequest): string {
    const { pollQuestion, pollOptions, demographic } = request;

    return `You are an expert political pollster and demographic analyst. Simulate realistic poll responses for the following scenario:

POLL QUESTION: "${pollQuestion}"

ANSWER OPTIONS: ${pollOptions.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}

DEMOGRAPHIC GROUP: ${demographic.group} (Sample size: ${demographic.size})
${demographic.context ? `Additional context: ${demographic.context}` : ''}

INSTRUCTIONS:
1. Generate realistic response distributions based on this demographic's likely opinions
2. Consider cultural, socioeconomic, and generational factors that would influence responses
3. Include confidence scores reflecting certainty of predictions
4. Provide reasoning for response patterns
5. Identify potential bias factors that might affect accuracy
6. Ensure the distribution adds up to 100% (as decimals)

RESPONSE FORMAT: Provide structured JSON with exact schema compliance:
- simulationId: unique identifier
- demographics: array with group details and individual responses
- results: distribution object with option keys and percentage values
- metadata: accuracy, methodology, confidence metrics

Focus on realism over political correctness. Base responses on actual demographic voting patterns and survey data when possible.`;
  }

  /**
   * Generate aggregated analysis using AI SDK
   */
  private async generateAggregatedAnalysis(
    simulations: SimulationResponse[],
    pollOptions: string[]
  ): Promise<AggregatedResults> {
    try {
      // Use Perplexity service directly for aggregated analysis
      // Since AI SDK doesn't support Perplexity yet, fall back to manual analysis
      console.log('Using fallback aggregation method');
      return this.generateFallbackAggregation(simulations, pollOptions);

    } catch (error) {
      console.error('Failed to generate aggregated analysis:', error);
      return this.generateFallbackAggregation(simulations, pollOptions);
    }
  }

  /**
   * Build aggregation prompt for cross-demographic analysis
   */
  private buildAggregationPrompt(
    simulations: SimulationResponse[],
    pollOptions: string[],
    demographics: string[]
  ): string {
    const distributionSummary = simulations.map((sim, i) => {
      const demo = demographics[i] || sim.metadata.demographic;
      const dist = Object.entries(sim.results.distribution)
        .map(([option, pct]) => `${option}: ${(pct * 100).toFixed(1)}%`)
        .join(', ');
      return `${demo}: ${dist}`;
    }).join('\n');

    return `Analyze these poll simulation results across different demographic groups:

POLL OPTIONS: ${pollOptions.join(', ')}

DEMOGRAPHIC RESULTS:
${distributionSummary}

TASK: Provide comprehensive cross-demographic analysis including:

1. CONSENSUS OPTIONS: Which options show agreement across demographics (variance < 10%)
2. POLARIZING OPTIONS: Which options show high demographic variation (variance > 20%)
3. KEY INSIGHTS: 3-5 actionable insights about demographic differences
4. STRATEGIC IMPLICATIONS: How these patterns might affect messaging/campaigns
5. CONFIDENCE ASSESSMENT: Overall reliability of these predictions

Focus on patterns, outliers, and practical implications for decision-makers.`;
  }

  /**
   * Parse aggregated analysis from AI response
   */
  private parseAggregatedAnalysis(
    analysisText: string,
    simulations: SimulationResponse[],
    pollOptions: string[]
  ): AggregatedResults {
    // Calculate actual demographic comparison data
    const demographicComparison = simulations.map(sim => ({
      demographic: sim.metadata.demographic,
      distribution: sim.results.distribution,
      variance: this.calculateVariance(Object.values(sim.results.distribution))
    }));

    // Calculate overall distribution (average across demographics)
    const overallDistribution: Record<string, number> = {};
    pollOptions.forEach(option => {
      const values = simulations.map(sim => sim.results.distribution[option] || 0);
      overallDistribution[option] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    // Extract insights from AI analysis
    const insights = this.extractInsightsFromText(analysisText);
    const consensus = this.identifyConsensusOptions(demographicComparison, pollOptions);
    const polarization = this.identifyPolarizingOptions(demographicComparison, pollOptions);

    return {
      overallDistribution,
      demographicComparison,
      insights,
      consensus,
      polarization
    };
  }

  /**
   * Enhance simulation response with additional metadata
   */
  private enhanceSimulationResponse(
    object: z.infer<typeof SimulationSchema>,
    request: SimulationRequest
  ): SimulationResponse {
    // Ensure the response matches our existing SimulationResponse interface
    return {
      simulationId: object.simulationId || this.generateSimulationId(),
      results: {
        distribution: object.results.distribution,
        analysis: object.results.summary || 'AI-generated simulation results'
      },
      metadata: {
        demographic: request.demographic.group,
        sampleSize: request.demographic.size,
        confidence: object.metadata.confidence || 0.8,
        citations: object.metadata.citations || []
      }
    };
  }

  /**
   * Fallback to existing simulation system
   */
  private async fallbackToExistingSimulation(request: SimulationRequest): Promise<SimulationResponse> {
    console.log('Using existing simulation system as fallback');
    await new Promise(resolve => setTimeout(resolve, EnhancedSimulationEngine.FALLBACK_DELAY));
    return await simulatePoll(request);
  }

  /**
   * Generate fallback aggregation when AI analysis fails
   */
  private generateFallbackAggregation(
    simulations: SimulationResponse[],
    pollOptions: string[]
  ): AggregatedResults {
    const overallDistribution: Record<string, number> = {};

    pollOptions.forEach(option => {
      const values = simulations.map(sim => sim.results.distribution[option] || 0);
      overallDistribution[option] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    return {
      overallDistribution,
      demographicComparison: simulations.map(sim => ({
        demographic: sim.metadata.demographic,
        distribution: sim.results.distribution,
        variance: this.calculateVariance(Object.values(sim.results.distribution))
      })),
      insights: ['Analysis generated using fallback system'],
      consensus: [],
      polarization: []
    };
  }

  // Utility methods
  private generateSimulationId(): string {
    return `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  }

  private extractInsightsFromText(text: string): string[] {
    // Simple extraction - could be enhanced with more sophisticated parsing
    const lines = text.split('\n').filter(line => line.trim());
    return lines.slice(0, 5); // Take first 5 meaningful lines as insights
  }

  private identifyConsensusOptions(
    demographicComparison: Array<{
      demographic: string;
      distribution: Record<string, number>;
      variance: number;
    }>,
    pollOptions: string[]
  ): string[] {
    return pollOptions.filter(option => {
      const values = demographicComparison.map(demo => demo.distribution[option] || 0);
      const variance = this.calculateVariance(values);
      return variance < 0.01; // Low variance indicates consensus
    });
  }

  private identifyPolarizingOptions(
    demographicComparison: Array<{
      demographic: string;
      distribution: Record<string, number>;
      variance: number;
    }>,
    pollOptions: string[]
  ): string[] {
    return pollOptions.filter(option => {
      const values = demographicComparison.map(demo => demo.distribution[option] || 0);
      const variance = this.calculateVariance(values);
      return variance > 0.04; // High variance indicates polarization
    });
  }
}

// Export singleton instance
export const enhancedSimulationEngine = new EnhancedSimulationEngine();
