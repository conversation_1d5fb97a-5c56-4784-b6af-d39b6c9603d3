/**
 * Feature flags for AI SDK integration
 * Allows gradual rollout and easy rollback of AI SDK features
 */

export interface AISDKFeatureFlags {
  structuredGeneration: boolean;
  streamingAnalytics: boolean;
  toolCalling: boolean;
  batchProcessing: boolean;
  circuitBreaker: boolean;
  caching: boolean;
  multiModal: boolean;
  simulation: boolean;
  // Phase 2 Features
  documentProcessing: boolean;
  ocrProcessing: boolean;
  sentimentAnalysis: boolean;
  batchDocumentProcessing: boolean;
  websocketProgressTracking: boolean;
  advancedNLP: boolean;
  // Phase 3 Features - Enhanced Simulation
  enhancedSimulation: boolean;
  structuredSimulationGeneration: boolean;
  batchSimulationProcessing: boolean;
  demographicAnalysis: boolean;
  simulationInsights: boolean;
  streamingInsights: boolean;
  simulationConfigGeneration: boolean;
  simulationComparison: boolean;
}

// Default feature flag values
const DEFAULT_FLAGS: AISDKFeatureFlags = {
  structuredGeneration: true,
  streamingAnalytics: true,
  toolCalling: true,
  batchProcessing: true,
  circuitBreaker: true,
  caching: true,
  multiModal: false, // Experimental
  simulation: true,
  // Phase 2 Features
  documentProcessing: true,
  ocrProcessing: true,
  sentimentAnalysis: true,
  batchDocumentProcessing: true,
  websocketProgressTracking: false, // Experimental
  advancedNLP: true,
  // Phase 3 Features - Enhanced Simulation
  enhancedSimulation: true,
  structuredSimulationGeneration: true,
  batchSimulationProcessing: true,
  demographicAnalysis: true,
  simulationInsights: true,
  streamingInsights: true,
  simulationConfigGeneration: true,
  simulationComparison: true
};

/**
 * Get feature flags from environment variables with fallbacks
 */
export function getAISDKFeatures(): AISDKFeatureFlags {
  return {
    structuredGeneration: getEnvFlag('ENABLE_AI_SDK_STRUCTURED', DEFAULT_FLAGS.structuredGeneration),
    streamingAnalytics: getEnvFlag('ENABLE_AI_SDK_STREAMING', DEFAULT_FLAGS.streamingAnalytics),
    toolCalling: getEnvFlag('ENABLE_AI_SDK_TOOLS', DEFAULT_FLAGS.toolCalling),
    batchProcessing: getEnvFlag('ENABLE_AI_SDK_BATCH', DEFAULT_FLAGS.batchProcessing),
    circuitBreaker: getEnvFlag('ENABLE_AI_SDK_CIRCUIT_BREAKER', DEFAULT_FLAGS.circuitBreaker),
    caching: getEnvFlag('ENABLE_AI_SDK_CACHING', DEFAULT_FLAGS.caching),
    multiModal: getEnvFlag('ENABLE_AI_SDK_MULTIMODAL', DEFAULT_FLAGS.multiModal),
    simulation: getEnvFlag('ENABLE_AI_SDK_SIMULATION', DEFAULT_FLAGS.simulation),
    // Phase 2 Features
    documentProcessing: getEnvFlag('ENABLE_AI_SDK_DOCUMENT_PROCESSING', DEFAULT_FLAGS.documentProcessing),
    ocrProcessing: getEnvFlag('ENABLE_AI_SDK_OCR_PROCESSING', DEFAULT_FLAGS.ocrProcessing),
    sentimentAnalysis: getEnvFlag('ENABLE_AI_SDK_SENTIMENT_ANALYSIS', DEFAULT_FLAGS.sentimentAnalysis),
    batchDocumentProcessing: getEnvFlag('ENABLE_AI_SDK_BATCH_DOCUMENT_PROCESSING', DEFAULT_FLAGS.batchDocumentProcessing),
    websocketProgressTracking: getEnvFlag('ENABLE_AI_SDK_WEBSOCKET_PROGRESS', DEFAULT_FLAGS.websocketProgressTracking),
    advancedNLP: getEnvFlag('ENABLE_AI_SDK_ADVANCED_NLP', DEFAULT_FLAGS.advancedNLP),
    // Phase 3 Features - Enhanced Simulation
    enhancedSimulation: getEnvFlag('ENABLE_AI_SDK_ENHANCED_SIMULATION', DEFAULT_FLAGS.enhancedSimulation),
    structuredSimulationGeneration: getEnvFlag('ENABLE_AI_SDK_STRUCTURED_SIMULATION_GENERATION', DEFAULT_FLAGS.structuredSimulationGeneration),
    batchSimulationProcessing: getEnvFlag('ENABLE_AI_SDK_BATCH_SIMULATION_PROCESSING', DEFAULT_FLAGS.batchSimulationProcessing),
    demographicAnalysis: getEnvFlag('ENABLE_AI_SDK_DEMOGRAPHIC_ANALYSIS', DEFAULT_FLAGS.demographicAnalysis),
    simulationInsights: getEnvFlag('ENABLE_AI_SDK_SIMULATION_INSIGHTS', DEFAULT_FLAGS.simulationInsights),
    streamingInsights: getEnvFlag('ENABLE_AI_SDK_STREAMING_INSIGHTS', DEFAULT_FLAGS.streamingInsights),
    simulationConfigGeneration: getEnvFlag('ENABLE_AI_SDK_SIMULATION_CONFIG_GENERATION', DEFAULT_FLAGS.simulationConfigGeneration),
    simulationComparison: getEnvFlag('ENABLE_AI_SDK_SIMULATION_COMPARISON', DEFAULT_FLAGS.simulationComparison)
  };
}

/**
 * Check if a specific AI SDK feature is enabled
 */
export function isFeatureEnabled(feature: keyof AISDKFeatureFlags): boolean {
  const flags = getAISDKFeatures();
  return flags[feature];
}

/**
 * Get environment flag with proper type conversion
 */
function getEnvFlag(envVar: string, defaultValue: boolean): boolean {
  const value = process.env[envVar];

  if (value === undefined) {
    return defaultValue;
  }

  // Handle various truthy/falsy representations
  const normalizedValue = value.toLowerCase().trim();

  if (normalizedValue === 'true' || normalizedValue === '1' || normalizedValue === 'yes') {
    return true;
  }

  if (normalizedValue === 'false' || normalizedValue === '0' || normalizedValue === 'no') {
    return false;
  }

  // If we can't parse it, log a warning and use default
  console.warn(`Invalid value for ${envVar}: "${value}". Using default: ${defaultValue}`);
  return defaultValue;
}

/**
 * Development mode feature overrides
 */
export function getDevModeFeatures(): Partial<AISDKFeatureFlags> {
  if (process.env.NODE_ENV !== 'development') {
    return {};
  }

  return {
    // Enable all features in development for testing
    multiModal: true,
    // Add any other dev-specific overrides here
  };
}

/**
 * Get final feature flags with dev mode overrides
 */
export function getFinalFeatureFlags(): AISDKFeatureFlags {
  const baseFlags = getAISDKFeatures();
  const devOverrides = getDevModeFeatures();

  return {
    ...baseFlags,
    ...devOverrides
  };
}

/**
 * Feature flag validation - ensures required dependencies are met
 */
export function validateFeatureFlags(flags: AISDKFeatureFlags): {
  valid: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];

  // Streaming requires structured generation
  if (flags.streamingAnalytics && !flags.structuredGeneration) {
    warnings.push('Streaming analytics requires structured generation to be enabled');
  }

  // Multi-modal requires tool calling
  if (flags.multiModal && !flags.toolCalling) {
    warnings.push('Multi-modal features require tool calling to be enabled');
  }

  // Simulation benefits from caching
  if (flags.simulation && !flags.caching) {
    warnings.push('Simulation performance is improved with caching enabled');
  }

  // Circuit breaker is recommended for production
  if (process.env.NODE_ENV === 'production' && !flags.circuitBreaker) {
    warnings.push('Circuit breaker is recommended for production environments');
  }

  return {
    valid: warnings.length === 0,
    warnings
  };
}

/**
 * Log feature flag status for debugging
 */
export function logFeatureFlagStatus() {
  const flags = getFinalFeatureFlags();
  const validation = validateFeatureFlags(flags);

  console.log('AI SDK Feature Flags:', {
    flags,
    environment: process.env.NODE_ENV,
    validation: validation.valid ? 'VALID' : 'WARNINGS',
    warnings: validation.warnings
  });

  if (!validation.valid) {
    validation.warnings.forEach(warning => {
      console.warn(`Feature Flag Warning: ${warning}`);
    });
  }
}

/**
 * Runtime feature flag checking with fallback
 */
export class FeatureGate {
  private static flags = getFinalFeatureFlags();

  static isEnabled(feature: keyof AISDKFeatureFlags): boolean {
    return this.flags[feature];
  }

  static requireFeature(feature: keyof AISDKFeatureFlags, operation: string): void {
    if (!this.isEnabled(feature)) {
      throw new Error(`${operation} requires feature "${feature}" to be enabled`);
    }
  }

  static whenEnabled<T>(
    feature: keyof AISDKFeatureFlags,
    enabledFn: () => T,
    disabledFn: () => T
  ): T {
    return this.isEnabled(feature) ? enabledFn() : disabledFn();
  }

  static async whenEnabledAsync<T>(
    feature: keyof AISDKFeatureFlags,
    enabledFn: () => Promise<T>,
    disabledFn: () => Promise<T>
  ): Promise<T> {
    return this.isEnabled(feature) ? enabledFn() : disabledFn();
  }

  // Refresh flags (useful for runtime configuration changes)
  static refreshFlags(): void {
    this.flags = getFinalFeatureFlags();
    console.log('Feature flags refreshed:', this.flags);
  }
}

// Initialize and log feature flags on module load
if (process.env.NODE_ENV !== 'test') {
  logFeatureFlagStatus();
}
