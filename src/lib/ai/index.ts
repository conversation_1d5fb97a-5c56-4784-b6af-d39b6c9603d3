import { logFeatureFlagStatus, getFinalFeatureFlags } from './feature-flags';
import { validateAIProviders, checkProviderHealth } from './providers';
import { warmupCache, getCacheStats } from './cache-service';
import { fallbackManager } from './fallback-service';

/**
 * AI SDK Integration - Main Export File
 * Provides a unified interface to all AI SDK functionality
 */

// Core services and providers
export { aiProviders, validateAIProviders, checkProviderHealth } from './providers';
export { unifiedAI, UnifiedAIService, generatePollWithAI, extractContentWithAI, analyzeDocumentWithAI } from './unified-service';

// Schemas and types
export * from './schemas';

// Utility services
export { fallbackManager, FallbackManager, withFallback, withMultipleFallbacks } from './fallback-service';
export { cachedGeneration, generateCacheKey, getCacheStats, clearCacheByPattern, warmupCache, invalidateRelatedCache } from './cache-service';

// Feature management
export {
  getAISDKFeatures,
  isFeatureEnabled,
  getFinalFeatureFlags,
  validateFeatureFlags,
  logFeatureFlagStatus,
  FeatureGate,
  type AISDKFeatureFlags
} from './feature-flags';

// Re-export AI SDK core functions for convenience
export { generateText, generateObject, streamText, tool } from 'ai';

/**
 * Initialize AI SDK services
 * Call this during app startup to warm caches and validate configuration
 */
export async function initializeAISDK() {
  console.log('Initializing AI SDK integration...');

  try {
    // Log feature flag status
    logFeatureFlagStatus();

    // Validate providers
    const providersValid = validateAIProviders();
    if (!providersValid) {
      console.warn('Some AI providers are not properly configured');
    }

    // Warm up caches
    await warmupCache();

    // Check provider health (optional, may fail in development)
    try {
      const health = await checkProviderHealth();
      console.log('AI Provider Health Check:', health);
    } catch (error) {
      console.warn('Provider health check failed:', error);
    }

    console.log('AI SDK initialization completed successfully');
    return true;
  } catch (error) {
    console.error('AI SDK initialization failed:', error);
    return false;
  }
}

/**
 * AI SDK status information for monitoring
 */
export function getAISDKStatus() {
  const flags = getFinalFeatureFlags();
  const cacheStats = getCacheStats();
  const circuitBreakerStatus = fallbackManager.getCircuitBreakerStatus();

  return {
    version: '1.0.0',
    initialized: true,
    features: flags,
    cache: cacheStats,
    circuitBreakers: circuitBreakerStatus,
    providers: {
      mistral: !!process.env.MISTRAL_API_KEY,
      google: !!process.env.GOOGLE_API_KEY,
      openai: !!process.env.OPENAI_API_KEY
    },
    timestamp: new Date().toISOString()
  };
}
