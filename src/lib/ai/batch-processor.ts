/**
 * Phase 2: Batch Processing System for Document Analysis
 * Handles multiple documents with queue management and progress tracking
 */

import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { documentProcessor, ProcessingOptions, ProcessedDocument } from './document-processor';
import { nlp<PERSON><PERSON>yzer, SentimentAnalysis, TopicAnalysis, PollSuggestion, ContentQuality } from './nlp-analysis';
import { getProgressTracker } from './progress-tracker';

// Combined NLP analysis result
export interface NLPAnalysisResult {
  sentiment?: SentimentAnalysis;
  topics?: TopicAnalysis;
  pollSuggestions?: PollSuggestion[];
  quality?: ContentQuality;
}

// Batch processing types
export interface BatchJob {
  id: string;
  documents: DocumentInput[];
  options: BatchProcessingOptions;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  results?: BatchResult[];
  errors?: BatchError[];
  createdAt: Date;
  completedAt?: Date;
  processingTime?: number;
}

export interface DocumentInput {
  id: string;
  name: string;
  buffer: Buffer;
  mimeType: string;
  size: number;
}

export interface BatchProcessingOptions extends ProcessingOptions {
  includeNLP?: boolean;
  includeSentiment?: boolean;
  includeTopics?: boolean;
  includePollSuggestions?: boolean;
  includeQuality?: boolean;
  audience?: string;
  maxConcurrency?: number;
  priority?: number;
}

export interface BatchResult {
  documentId: string;
  document: ProcessedDocument;
  nlpAnalysis?: NLPAnalysisResult;
  processingTime: number;
  success: boolean;
  error?: string;
}

export interface BatchError {
  documentId: string;
  error: string;
  timestamp: Date;
}

export interface BatchProgress {
  jobId: string;
  status: BatchJob['status'];
  progress: number;
  processed: number;
  total: number;
  startTime: Date;
  estimatedCompletion?: Date;
  currentDocument?: string;
}

/**
 * Batch Processing Manager
 */
export class BatchProcessingManager {
  private redis: Redis;
  private documentQueue: Queue;
  private batchQueue: Queue;
  private worker: Worker;
  private batchWorker: Worker;
  private jobs: Map<string, BatchJob> = new Map();

  constructor() {
    // Initialize Redis connection
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    this.redis = new Redis(redisUrl, {
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
      lazyConnect: true
    });

    // Initialize queues
    this.documentQueue = new Queue('document-processing', {
      connection: this.redis
    });

    this.batchQueue = new Queue('batch-processing', {
      connection: this.redis
    });

    // Initialize workers
    this.initializeWorkers();
  }

  private initializeWorkers(): void {
    // Document processing worker
    this.worker = new Worker('document-processing', async (job: Job) => {
      return await this.processDocument(job.data);
    }, {
      connection: this.redis,
      concurrency: parseInt(process.env.DOCUMENT_PROCESSING_CONCURRENCY || '3')
    });

    // Batch processing coordinator worker
    this.batchWorker = new Worker('batch-processing', async (job: Job) => {
      return await this.processBatch(job.data);
    }, {
      connection: this.redis,
      concurrency: 1 // Single batch coordinator
    });

    // Error handling
    this.worker.on('failed', (job, err) => {
      console.error(`Document processing job ${job?.id} failed:`, err);
    });

    this.batchWorker.on('failed', (job, err) => {
      console.error(`Batch processing job ${job?.id} failed:`, err);
    });
  }

  /**
   * Submit a batch of documents for processing
   */
  async submitBatch(documents: DocumentInput[], options: BatchProcessingOptions = {}): Promise<string> {
    const jobId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Validate documents
    this.validateDocuments(documents);

    // Create batch job
    const batchJob: BatchJob = {
      id: jobId,
      documents,
      options,
      status: 'pending',
      progress: 0,
      results: [],
      errors: [],
      createdAt: new Date()
    };

    // Store job
    this.jobs.set(jobId, batchJob);

    // Submit to batch queue
    await this.batchQueue.add('process-batch', {
      jobId,
      documents,
      options
    }, {
      priority: options.priority || 0,
      removeOnComplete: 10,
      removeOnFail: 50
    });

    return jobId;
  }

  /**
   * Process a batch job
   */
  private async processBatch(data: { jobId: string; documents: DocumentInput[]; options: BatchProcessingOptions }): Promise<void> {
    const { jobId, documents, options } = data;
    const job = this.jobs.get(jobId);

    if (!job) {
      throw new Error(`Batch job ${jobId} not found`);
    }

    try {
      // Update job status
      job.status = 'processing';
      const progressTracker = getProgressTracker();

      // Process documents with controlled concurrency
      const concurrency = options.maxConcurrency || 3;
      const results: BatchResult[] = [];
      const errors: BatchError[] = [];

      for (let i = 0; i < documents.length; i += concurrency) {
        const batch = documents.slice(i, i + concurrency);

        // Process batch concurrently
        const batchPromises = batch.map(async (doc) => {
          try {
            const startTime = Date.now();

            // Process document
            const processedDoc = await documentProcessor.processDocument(
              doc.buffer,
              doc.mimeType,
              {
                enableOCR: options.enableOCR,
                ocrLanguage: options.ocrLanguage,
                extractTables: options.extractTables,
                extractImages: options.extractImages,
                confidenceThreshold: options.confidenceThreshold
              }
            );

            let nlpAnalysis: NLPAnalysisResult | undefined;

            // Perform NLP analysis if requested
            if (options.includeNLP && processedDoc.text) {
              nlpAnalysis = await nlpAnalyzer.analyzeContent(processedDoc.text, {
                includeSentiment: options.includeSentiment,
                includeTopics: options.includeTopics,
                includePollSuggestions: options.includePollSuggestions,
                includeQuality: options.includeQuality,
                audience: options.audience
              });
            }

            const processingTime = Date.now() - startTime;

            return {
              documentId: doc.id,
              document: processedDoc,
              nlpAnalysis,
              processingTime,
              success: true
            };

          } catch (error) {
            console.error(`Error processing document ${doc.id}:`, error);

            errors.push({
              documentId: doc.id,
              error: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date()
            });

            return null;
          }
        });

        // Wait for batch completion
        const batchResults = await Promise.allSettled(batchPromises);

        batchResults.forEach((result) => {
          if (result.status === 'fulfilled' && result.value) {
            results.push(result.value);
          }
        });

        // Update progress
        const processed = i + batch.length;
        job.progress = Math.round((processed / documents.length) * 100);
        job.results = results;
        job.errors = errors;

        // Send progress update
        progressTracker.sendBatchProgress(jobId, job.progress, `Processing batch ${Math.floor(i/concurrency) + 1}`);
      }

      // Complete job
      job.status = 'completed';
      job.progress = 100;
      job.completedAt = new Date();
      job.processingTime = job.completedAt.getTime() - job.createdAt.getTime();

      // Final progress update
      progressTracker.sendBatchComplete(jobId, {
        jobId,
        status: job.status,
        results: job.results,
        processingTime: job.processingTime
      });

    } catch (error) {
      console.error(`Batch processing failed for job ${jobId}:`, error);

      job.status = 'failed';
      job.errors = job.errors || [];
      job.errors.push({
        documentId: 'batch',
        error: error instanceof Error ? error.message : 'Batch processing failed',
        timestamp: new Date()
      });

      // Send error progress update
      const progressTracker = getProgressTracker();
      progressTracker.sendBatchError(jobId, error instanceof Error ? error.message : 'Batch processing failed');
    }
  }

  /**
   * Process a single document (worker function)
   */
  private async processDocument(data: { document: DocumentInput; options: BatchProcessingOptions }): Promise<BatchResult> {
    const { document, options } = data;
    const startTime = Date.now();

    try {
      // Process document
      const processedDoc = await documentProcessor.processDocument(
        document.buffer,
        document.mimeType,
        {
          enableOCR: options.enableOCR,
          ocrLanguage: options.ocrLanguage,
          extractTables: options.extractTables,
          extractImages: options.extractImages,
          confidenceThreshold: options.confidenceThreshold
        }
      );

      let nlpAnalysis: NLPAnalysisResult | undefined;

      // Perform NLP analysis if requested
      if (options.includeNLP && processedDoc.text) {
        nlpAnalysis = await nlpAnalyzer.analyzeContent(processedDoc.text, {
          includeSentiment: options.includeSentiment,
          includeTopics: options.includeTopics,
          includePollSuggestions: options.includePollSuggestions,
          includeQuality: options.includeQuality,
          audience: options.audience
        });
      }

      return {
        documentId: document.id,
        document: processedDoc,
        nlpAnalysis,
        processingTime: Date.now() - startTime,
        success: true
      };

    } catch (error) {
      return {
        documentId: document.id,
        document: {
          text: '',
          metadata: {
            format: 'unknown',
            wordCount: 0,
            characterCount: 0
          },
          structure: { headings: [], paragraphs: [] },
          confidence: 0,
          processingTime: Date.now() - startTime
        },
        processingTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get batch processing status
   */
  async getBatchStatus(jobId: string): Promise<BatchProgress | null> {
    const job = this.jobs.get(jobId);
    if (!job) return null;

    return {
      jobId: job.id,
      status: job.status,
      progress: job.progress,
      processed: job.results?.length || 0,
      total: job.documents.length,
      startTime: job.createdAt,
      estimatedCompletion: this.calculateEstimatedCompletion(job),
      currentDocument: this.getCurrentDocument(job)
    };
  }

  /**
   * Get batch processing results
   */
  async getBatchResults(jobId: string): Promise<BatchJob | null> {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Cancel batch processing job
   */
  async cancelBatch(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job || job.status === 'completed' || job.status === 'failed') {
      return false;
    }

    try {
      // Remove from queues
      const batchJobs = await this.batchQueue.getJobs(['waiting', 'active']);
      for (const queueJob of batchJobs) {
        if (queueJob.data.jobId === jobId) {
          await queueJob.remove();
        }
      }

      // Update job status
      job.status = 'failed';
      job.errors = job.errors || [];
      job.errors.push({
        documentId: 'batch',
        error: 'Job cancelled by user',
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      console.error(`Failed to cancel batch job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    documents: { waiting: number; active: number; completed: number; failed: number };
    batches: { waiting: number; active: number; completed: number; failed: number };
  }> {
    const [docStats, batchStats] = await Promise.all([
      this.documentQueue.getJobCounts(),
      this.batchQueue.getJobCounts()
    ]);

    return {
      documents: {
        waiting: docStats.waiting || 0,
        active: docStats.active || 0,
        completed: docStats.completed || 0,
        failed: docStats.failed || 0
      },
      batches: {
        waiting: batchStats.waiting || 0,
        active: batchStats.active || 0,
        completed: batchStats.completed || 0,
        failed: batchStats.failed || 0
      }
    };
  }

  /**
   * Clean up completed jobs
   */
  async cleanup(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> { // 24 hours default
    const now = Date.now();
    const jobsToDelete: string[] = [];

    for (const [jobId, job] of this.jobs.entries()) {
      if (job.completedAt && (now - job.completedAt.getTime()) > maxAge) {
        jobsToDelete.push(jobId);
      }
    }

    jobsToDelete.forEach(jobId => this.jobs.delete(jobId));
  }

  /**
   * Close connections and workers
   */
  async close(): Promise<void> {
    await Promise.all([
      this.worker.close(),
      this.batchWorker.close(),
      this.redis.disconnect()
    ]);
  }

  private validateDocuments(documents: DocumentInput[]): void {
    if (documents.length === 0) {
      throw new Error('No documents provided');
    }

    if (documents.length > 100) {
      throw new Error('Too many documents (max 100)');
    }

    for (const doc of documents) {
      if (!documentProcessor.isSupported(doc.mimeType)) {
        throw new Error(`Unsupported document type: ${doc.mimeType}`);
      }
    }
  }

  private calculateEstimatedCompletion(job: BatchJob): Date | undefined {
    if (job.status !== 'processing' || job.progress === 0) {
      return undefined;
    }

    const elapsed = Date.now() - job.createdAt.getTime();
    const estimatedTotal = elapsed / (job.progress / 100);
    const remaining = estimatedTotal - elapsed;

    return new Date(Date.now() + remaining);
  }

  private getCurrentDocument(job: BatchJob): string | undefined {
    const processed = (job.results?.length || 0) + (job.errors?.length || 0);
    if (processed < job.documents.length) {
      return job.documents[processed].name;
    }
    return undefined;
  }
}

// Export singleton instance
let batchProcessorInstance: BatchProcessingManager | null = null;

export function getBatchProcessor(): BatchProcessingManager {
  if (!batchProcessorInstance) {
    batchProcessorInstance = new BatchProcessingManager();
  }
  return batchProcessorInstance;
}

// Named export for easier importing
export const batchProcessor = getBatchProcessor();

export default BatchProcessingManager;