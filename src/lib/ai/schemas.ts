import { z } from 'zod';

// Schema for poll questions and options
export const QuestionOptionSchema = z.object({
  text: z.string(),
  value: z.string()
});

export const QuestionSchema = z.object({
  text: z.string(),
  type: z.enum(['single', 'multiple', 'open']),
  options: z.array(QuestionOptionSchema).optional(),
  required: z.boolean(),
  order: z.number()
});

// Main poll schema for structured generation
export const PollSchema = z.object({
  title: z.string(),
  description: z.string(),
  questions: z.array(QuestionSchema)
});

// Content extraction schemas
export const ContentExtractionSchema = z.object({
  extractedText: z.string(),
  keyThemes: z.array(z.string()),
  suggestedQuestions: z.array(z.string()),
  confidence: z.number().min(0).max(1)
});

export const DocumentAnalysisSchema = z.object({
  summary: z.string(),
  mainTopics: z.array(z.string()),
  pollSuggestions: z.array(z.object({
    question: z.string(),
    type: z.enum(['single', 'multiple', 'open']),
    reasoning: z.string()
  })),
  extractionQuality: z.enum(['high', 'medium', 'low'])
});

// Simulation schemas
export const SimulationResponseSchema = z.object({
  questionId: z.string(),
  response: z.string(),
  confidence: z.number().min(0).max(1)
});

export const DemographicGroupSchema = z.object({
  group: z.string(),
  percentage: z.number().min(0).max(100),
  responses: z.array(SimulationResponseSchema)
});

export const SimulationSchema = z.object({
  simulationId: z.string(),
  demographics: z.array(DemographicGroupSchema),
  metadata: z.object({
    accuracy: z.number().min(0).max(1),
    sampleSize: z.number(),
    biasFactors: z.array(z.string()),
    methodology: z.string(),
    confidenceLevel: z.number().min(0).max(1)
  })
});

// Analytics schemas
export const InsightSchema = z.object({
  insight: z.string(),
  category: z.enum(['demographic', 'trend', 'satisfaction', 'recommendation']),
  confidence: z.number().min(0).max(1),
  supportingData: z.array(z.string()).optional()
});

export const AnalyticsSchema = z.object({
  insights: z.array(InsightSchema),
  summary: z.string(),
  recommendations: z.array(z.string()),
  keyMetrics: z.object({
    responseRate: z.number().optional(),
    avgSatisfaction: z.number().optional(),
    topCategory: z.string().optional()
  })
});

// Input types for compatibility with existing code
export type PollCreationInput = {
  title: string;
  topic: string;
  audience: string;
  additionalInfo?: string;
  content?: string;
};

export type SimulationRequest = {
  pollId: string;
  questions: Array<{
    id: string;
    text: string;
    type: string;
    options?: Array<{ text: string; value: string }>;
  }>;
  targetDemographics?: string[];
  sampleSize?: number;
  biasCorrection?: boolean;
};

// Type exports for use throughout the app
export type Poll = z.infer<typeof PollSchema>;
export type Question = z.infer<typeof QuestionSchema>;
export type QuestionOption = z.infer<typeof QuestionOptionSchema>;
export type ContentExtraction = z.infer<typeof ContentExtractionSchema>;
export type DocumentAnalysis = z.infer<typeof DocumentAnalysisSchema>;
export type SimulationResponse = z.infer<typeof SimulationSchema>;
export type DemographicGroup = z.infer<typeof DemographicGroupSchema>;
export type Analytics = z.infer<typeof AnalyticsSchema>;
export type Insight = z.infer<typeof InsightSchema>;
