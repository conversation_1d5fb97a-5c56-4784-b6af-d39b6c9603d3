/**
 * WebSocket Server for Real-time Progress Tracking
 * Provides real-time updates for batch processing operations
 */

import { WebSocketServer } from 'ws';
import WebSocket from 'ws';
import { isFeatureEnabled } from './feature-flags';

export interface ProgressUpdate {
  jobId: string;
  type: 'progress' | 'complete' | 'error';
  progress?: number;
  message?: string;
  result?: unknown;
  error?: string;
}

export class ProgressTracker {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, Set<WebSocket>> = new Map();
  private port: number;

  constructor(port: number = 8080) {
    this.port = port;
  }

  /**
   * Initialize WebSocket server
   */
  async initialize(): Promise<void> {
    if (!isFeatureEnabled('websocketProgressTracking')) {
      console.log('WebSocket progress tracking is disabled');
      return;
    }

    try {
      this.wss = new WebSocketServer({ port: this.port });

      this.wss.on('connection', (ws) => {
        console.log('New WebSocket connection established');

        ws.on('message', (message) => {
          try {
            const data = JSON.parse(message.toString());
            if (data.type === 'subscribe' && data.jobId) {
              this.subscribeToJob(data.jobId, ws);
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        });

        ws.on('close', () => {
          this.unsubscribeFromAllJobs(ws);
        });

        ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.unsubscribeFromAllJobs(ws);
        });
      });

      console.log(`WebSocket server listening on port ${this.port}`);
    } catch (error) {
      console.error('Failed to initialize WebSocket server:', error);
      throw error;
    }
  }

  /**
   * Subscribe a WebSocket client to job updates
   */
  private subscribeToJob(jobId: string, ws: WebSocket): void {
    if (!this.clients.has(jobId)) {
      this.clients.set(jobId, new Set());
    }
    this.clients.get(jobId)!.add(ws);

    // Send initial subscription confirmation
    this.sendToClient(ws, {
      jobId,
      type: 'progress',
      progress: 0,
      message: 'Subscribed to job updates'
    });
  }

  /**
   * Unsubscribe a WebSocket client from all jobs
   */
  private unsubscribeFromAllJobs(ws: WebSocket): void {
    for (const [jobId, clients] of this.clients.entries()) {
      clients.delete(ws);
      if (clients.size === 0) {
        this.clients.delete(jobId);
      }
    }
  }

  /**
   * Send progress update to all subscribers of a job
   */
  sendProgressUpdate(jobId: string, update: ProgressUpdate): void {
    if (!isFeatureEnabled('websocketProgressTracking')) {
      return;
    }

    const clients = this.clients.get(jobId);
    if (!clients || clients.size === 0) {
      return;
    }

    const deadClients = new Set<WebSocket>();

    for (const client of clients) {
      try {
        if (client.readyState === WebSocket.OPEN) {
          this.sendToClient(client, update);
        } else {
          deadClients.add(client);
        }
      } catch (error) {
        console.error('Error sending progress update:', error);
        deadClients.add(client);
      }
    }

    // Clean up dead connections
    for (const deadClient of deadClients) {
      clients.delete(deadClient);
    }

    if (clients.size === 0) {
      this.clients.delete(jobId);
    }
  }

  /**
   * Send data to a specific WebSocket client
   */
  private sendToClient(ws: WebSocket, data: ProgressUpdate): void {
    try {
      ws.send(JSON.stringify(data));
    } catch (error) {
      console.error('Error sending data to WebSocket client:', error);
    }
  }

  /**
   * Send progress update for batch job
   */
  sendBatchProgress(jobId: string, progress: number, message: string): void {
    this.sendProgressUpdate(jobId, {
      jobId,
      type: 'progress',
      progress: Math.round(progress * 100) / 100, // Round to 2 decimal places
      message
    });
  }

  /**
   * Send completion notification for batch job
   */
  sendBatchComplete(jobId: string, result: unknown): void {
    this.sendProgressUpdate(jobId, {
      jobId,
      type: 'complete',
      progress: 100,
      message: 'Batch processing completed successfully',
      result
    });
  }

  /**
   * Send error notification for batch job
   */
  sendBatchError(jobId: string, error: string): void {
    this.sendProgressUpdate(jobId, {
      jobId,
      type: 'error',
      message: 'Batch processing failed',
      error
    });
  }

  /**
   * Close WebSocket server
   */
  async close(): Promise<void> {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
      this.clients.clear();
      console.log('WebSocket server closed');
    }
  }

  /**
   * Get active connections count
   */
  getActiveConnections(): number {
    if (!this.wss) return 0;
    return Array.from(this.clients.values()).reduce((total, clients) => total + clients.size, 0);
  }

  /**
   * Get jobs being tracked
   */
  getTrackedJobs(): string[] {
    return Array.from(this.clients.keys());
  }
}

// Singleton instance
let progressTracker: ProgressTracker | null = null;

/**
 * Get the global progress tracker instance
 */
export function getProgressTracker(): ProgressTracker {
  if (!progressTracker) {
    const port = parseInt(process.env.WEBSOCKET_PORT || '8080');
    progressTracker = new ProgressTracker(port);
  }
  return progressTracker;
}

/**
 * Initialize the global progress tracker
 */
export async function initializeProgressTracker(): Promise<void> {
  const tracker = getProgressTracker();
  await tracker.initialize();
}

/**
 * Close the global progress tracker
 */
export async function closeProgressTracker(): Promise<void> {
  if (progressTracker) {
    await progressTracker.close();
    progressTracker = null;
  }
}

export default ProgressTracker;
