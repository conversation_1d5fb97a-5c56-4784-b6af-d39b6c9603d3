/**
 * AI Provider Configuration for PollGPT
 *
 * This file configures AI providers for different PollGPT features.
 * See AI-PROVIDER-STRATEGY.md for detailed strategy documentation.
 *
 * CURRENT MODEL VERSIONS (updated January 2025):
 * - Gemini: gemini-2.0-flash-001 (latest flagship)
 * - Mistral: mistral-large-2411 (latest stable, replaces mistral-large-latest)
 * - Perplexity: sonar (current model with search)
 */

import { mistral } from '@ai-sdk/mistral';
import { google } from '@ai-sdk/google';

// Map your existing environment variable to what Google AI SDK expects
if (process.env.GOOGLE_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  process.env.GOOGLE_GENERATIVE_AI_API_KEY = process.env.GOOGLE_API_KEY;
}

// Note: Perplexity is not yet available as an official AI SDK provider
// We'll continue using our existing Perplexity service for now
// and integrate it through the unified interface

// AI Provider Strategy Configuration
// See AI-PROVIDER-STRATEGY.md for detailed documentation

export const aiProviders = {
  // Content extraction and document processing - ALWAYS Mistral
  contentExtraction: mistral('mistral-large-2411'), // Updated to latest stable

  // Chat UI - User selectable models
  chatGemini: google('gemini-2.0-flash-001'), // Latest stable Gemini 2.0 model
  chatMistral: mistral('mistral-large-2411'), // Updated to latest stable

  // Poll generation and suggestions - Mistral
  pollGeneration: mistral('mistral-large-2411'), // Updated to latest stable

  // Phase 3: Enhanced simulation endpoints
  analysis: google('gemini-2.0-flash-001'), // For streaming insights and comparison analysis
  planning: mistral('mistral-large-2411'), // For config generation and demographic analysis

  // Fallback providers for reliability
  fallback: {
    content: mistral('mistral-large-2411'),
    poll: mistral('mistral-large-2411'),
    chat: mistral('mistral-large-2411'),
    analysis: google('gemini-2.0-flash-001'),
    planning: mistral('mistral-large-2411')
  }
};

// NOTE: Simulation uses Perplexity via our existing service wrapper
// This is handled in src/lib/services/perplexity-ai.ts since Perplexity
// is not yet available as an official AI SDK provider

// Environment variable validation
export function validateAIProviders() {
  const requiredEnvVars = [
    'MISTRAL_API_KEY',
    'GOOGLE_API_KEY'
  ];

  const missing = requiredEnvVars.filter(env => !process.env[env]);

  if (missing.length > 0) {
    console.warn(`Missing AI SDK environment variables: ${missing.join(', ')}`);
    return false;
  }

  return true;
}

// Provider health check
export async function checkProviderHealth() {
  const results = {
    mistral: false,
    google: false
  };

  try {
    // Simple health check for each provider
    const { generateText } = await import('ai');

    // Test Mistral
    try {
      await generateText({
        model: aiProviders.contentExtraction,
        prompt: 'Hello',
        maxTokens: 1
      });
      results.mistral = true;
    } catch (error) {
      console.warn('Mistral provider health check failed:', error);
    }

    // Test Google (using analysis model instead of simulation)
    try {
      await generateText({
        model: aiProviders.analysis,
        prompt: 'Hello',
        maxTokens: 1
      });
      results.google = true;
    } catch (error) {
      console.warn('Google provider health check failed:', error);
    }

  } catch (error) {
    console.error('Provider health check error:', error);
  }

  return results;
}
