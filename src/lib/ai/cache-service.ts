import { LRUCache } from 'lru-cache';

// Create different caches for different types of AI operations
// Using Record<string, unknown> to satisfy LRUCache constraints
const aiCache = new LRUCache<string, Record<string, unknown>>({
  max: 1000,
  ttl: 1000 * 60 * 15, // 15 minutes
  allowStale: false,
  updateAgeOnGet: false,
  updateAgeOnHas: false
});

const longTermCache = new LRUCache<string, Record<string, unknown>>({
  max: 500,
  ttl: 1000 * 60 * 60 * 2, // 2 hours for stable content
  allowStale: true,
  updateAgeOnGet: true
});

/**
 * Cache AI generation results with intelligent key generation
 */
export async function cachedGeneration<T>(
  key: string,
  generator: () => Promise<T>,
  options: {
    ttl?: number;
    longTerm?: boolean;
    skipCache?: boolean;
  } = {}
): Promise<T> {
  const { ttl, longTerm = false, skipCache = false } = options;

  if (skipCache) {
    return generator();
  }

  const cache = longTerm ? longTermCache : aiCache;
  const cachedResult = cache.get(key) as T | undefined;

  if (cachedResult) {
    console.log(`Cache hit for key: ${key.substring(0, 50)}...`);
    return cachedResult;
  }

  console.log(`Cache miss for key: ${key.substring(0, 50)}...`);

  try {
    const result = await generator();

    // Set with custom TTL if provided
    if (ttl) {
      cache.set(key, result as Record<string, unknown>, { ttl });
    } else {
      cache.set(key, result as Record<string, unknown>);
    }

    return result;
  } catch (error) {
    console.error('Error in cached generation:', error);
    throw error;
  }
}

/**
 * Generate a stable cache key from object inputs
 */
export function generateCacheKey(prefix: string, input: Record<string, unknown> | string): string {
  // Create a stable hash from the input
  const inputString = typeof input === 'string' ? input : JSON.stringify(input);
  const hash = simpleHash(inputString);
  return `${prefix}_${hash}`;
}

/**
 * Simple hash function for cache keys
 */
function simpleHash(str: string): string {
  let hash = 0;
  if (str.length === 0) return hash.toString();

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
}

/**
 * Cache statistics for monitoring
 */
export function getCacheStats() {
  return {
    shortTerm: {
      size: aiCache.size,
      max: aiCache.max,
      calculatedSize: aiCache.calculatedSize
    },
    longTerm: {
      size: longTermCache.size,
      max: longTermCache.max,
      calculatedSize: longTermCache.calculatedSize
    }
  };
}

/**
 * Clear specific cache entries by pattern
 */
export function clearCacheByPattern(pattern: string) {
  const regex = new RegExp(pattern);

  // Clear from short-term cache
  for (const key of aiCache.keys()) {
    if (regex.test(key)) {
      aiCache.delete(key);
    }
  }

  // Clear from long-term cache
  for (const key of longTermCache.keys()) {
    if (regex.test(key)) {
      longTermCache.delete(key);
    }
  }
}

/**
 * Warm up cache with common operations
 */
export async function warmupCache() {
  console.log('Starting AI cache warmup...');

  // This would be called during app initialization
  // to pre-populate cache with common poll templates

  const commonTopics = [
    'customer satisfaction',
    'product feedback',
    'employee engagement',
    'market research',
    'user experience'
  ];

  // Pre-generate cache keys for common operations
  for (const topic of commonTopics) {
    const key = generateCacheKey('poll_template', { topic, audience: 'general' });
    console.log(`Pre-generated cache key for ${topic}: ${key}`);
  }

  console.log('AI cache warmup completed');
}

/**
 * Intelligent cache invalidation based on content changes
 */
export function invalidateRelatedCache(pollId: string) {
  const patterns = [
    `poll_${pollId}`,
    `insights_${pollId}`,
    `simulation_${pollId}`,
    `analysis_${pollId}`
  ];

  patterns.forEach(pattern => clearCacheByPattern(pattern));
  console.log(`Invalidated cache for poll ${pollId}`);
}
