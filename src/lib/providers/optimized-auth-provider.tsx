'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { useQuery, useQueryClient, QueryClient } from '@tanstack/react-query';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function OptimizedAuthProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();
  const [initialLoad, setInitialLoad] = useState(true);

  // Optimized auth query with proper caching
  const { data: session, isLoading } = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async () => {
      const { data } = await supabase.auth.getSession();
      return data.session;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - much longer than current 1 minute
    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1,
    enabled: initialLoad, // Only fetch on initial load
  });

  const user = session?.user || null;

  // Set up auth state change listener (only once)
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Update query cache directly instead of refetching
        queryClient.setQueryData(['auth', 'session'], session);

        if (event === 'SIGNED_OUT') {
          // Clear all cached data on sign out
          queryClient.clear();
        } else if (event === 'SIGNED_IN') {
          // Invalidate user-specific data on sign in
          queryClient.invalidateQueries({ queryKey: ['polls'] });
          queryClient.invalidateQueries({ queryKey: ['user'] });
        }
      }
    );

    setInitialLoad(false);
    return () => subscription.unsubscribe();
  }, [queryClient]);

  // Background session refresh every 30 minutes
  useEffect(() => {
    const interval = setInterval(async () => {
      const { data } = await supabase.auth.refreshSession();
      if (data.session) {
        queryClient.setQueryData(['auth', 'session'], data.session);
      }
    }, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, [queryClient]);

  const signOut = async () => {
    await supabase.auth.signOut();
    queryClient.clear();
  };

  return (
    <AuthContext.Provider value={{ user, loading: isLoading, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an OptimizedAuthProvider');
  }
  return context;
};

// Optimized session manager utility
export const sessionManager = {
  getSession: async () => {
    // Check cache first
    const cachedSession = queryClient.getQueryData(['auth', 'session']) as Session | null;
    if (cachedSession && isSessionValid(cachedSession)) {
      return cachedSession;
    }

    // Fetch from server only if cache miss
    const { data } = await supabase.auth.getSession();
    return data.session;
  },

  isSessionValid: (session: Session | null) => {
    if (!session || !session.expires_at) return false;
    return session.expires_at > Date.now() / 1000 + 300; // 5 min buffer
  }
};

function isSessionValid(session: Session | null): boolean {
  if (!session || !session.expires_at) return false;
  return session.expires_at > Date.now() / 1000 + 300; // 5 min buffer
}

// Initialize query client singleton
let queryClient: QueryClient;
if (typeof window !== 'undefined') {
  import('@tanstack/react-query').then(({ QueryClient }) => {
    queryClient = new QueryClient();
  });
}
