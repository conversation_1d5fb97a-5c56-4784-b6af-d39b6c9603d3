'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';

// Optimized Query Client Configuration
function createOptimizedQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Increased from 1 minute to 5 minutes for better performance
        staleTime: 5 * 60 * 1000, // 5 minutes
        // Increased garbage collection time
        gcTime: 10 * 60 * 1000, // 10 minutes
        // Reduce unnecessary refetches
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        // Smart retry logic
        retry: (failureCount, error) => {
          // Don't retry auth errors
          if (error.message?.includes('auth') || error.message?.includes('401')) {
            return false;
          }
          // Retry up to 2 times for other errors
          return failureCount < 2;
        },
        // Add retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      mutations: {
        // Reduced retry for mutations
        retry: 1,
        // Only retry when online
        networkMode: 'online',
        // Add mutation retry delay
        retryDelay: 1000,
      },
    },
  });
}

export function OptimizedQueryClientProvider({ children }: { children: React.ReactNode }) {
  // Create a stable query client instance
  const [queryClient] = useState(() => createOptimizedQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <div id="react-query-devtools" />
      )}
    </QueryClientProvider>
  );
}

// Export optimized query client for use in server components
export const optimizedQueryClient = createOptimizedQueryClient();

// Utility function for cache invalidation patterns
export const cacheInvalidationPatterns = {
  // Clear all user-specific data on auth change
  clearUserData: (queryClient: QueryClient) => {
    queryClient.removeQueries({ queryKey: ['polls'] });
    queryClient.removeQueries({ queryKey: ['user'] });
    queryClient.removeQueries({ queryKey: ['responses'] });
  },

  // Smart invalidation for poll updates
  invalidatePollData: (queryClient: QueryClient, pollId?: string) => {
    queryClient.invalidateQueries({ queryKey: ['polls'] });
    if (pollId) {
      queryClient.invalidateQueries({ queryKey: ['poll', pollId] });
      queryClient.invalidateQueries({ queryKey: ['responses', pollId] });
    }
  },

  // Optimistic updates for better UX
  optimisticPollUpdate: (queryClient: QueryClient, pollId: string, updateData: Record<string, unknown>) => {
    queryClient.setQueryData(['poll', pollId], (old: Record<string, unknown> | undefined) => ({
      ...old,
      ...updateData,
    }));
  },
};

// Cache warming functions
export const cacheWarming = {
  // Preload user polls on login
  preloadUserPolls: async (queryClient: QueryClient, userId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['polls', userId],
      queryFn: async () => {
        const response = await fetch(`/api/polls?userId=${userId}`);
        const data = await response.json();
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  },

  // Preload poll details when hovering over poll cards
  preloadPollDetails: async (queryClient: QueryClient, pollId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['poll', pollId],
      queryFn: async () => {
        const response = await fetch(`/api/polls/${pollId}`);
        const data = await response.json();
        return data;
      },
      staleTime: 5 * 60 * 1000,
    });
  },
};
