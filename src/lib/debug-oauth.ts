/**
 * Debug utility for OAuth PKCE issues
 */

export function debugPKCEStorage() {
  if (typeof window === 'undefined') {
    console.log('Not in browser environment');
    return;
  }

  console.log('=== PKCE Debug Info ===');

  // Check all localStorage items
  const storageItems: Record<string, string> = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key) {
      storageItems[key] = localStorage.getItem(key) || '';
    }
  }

  console.log('All localStorage items:', Object.keys(storageItems));

  // Check for Supabase auth items
  const authKeys = Object.keys(storageItems).filter(key =>
    key.includes('auth') || key.includes('supabase') || key.includes('sb-')
  );

  console.log('Auth-related keys:', authKeys);

  // Check for PKCE-specific items
  const pkceKeys = Object.keys(storageItems).filter(key =>
    key.includes('code-verifier') || key.includes('pkce')
  );

  console.log('PKCE-related keys:', pkceKeys);

  // Show values for PKCE keys
  pkceKeys.forEach(key => {
    const value = storageItems[key];
    console.log(`${key}: ${value ? 'present' : 'empty'}`);
  });

  // Check cookies too
  if (document.cookie) {
    const cookies = document.cookie.split(';').map(c => c.trim());
    const authCookies = cookies.filter(c =>
      c.includes('auth') || c.includes('supabase') || c.includes('sb-')
    );
    console.log('Auth-related cookies:', authCookies.map(c => c.split('=')[0]));
  }

  console.log('=== End PKCE Debug ===');
}

export function clearAllAuthStorage() {
  if (typeof window === 'undefined') return;

  console.log('Clearing all auth storage...');

  // Clear localStorage items
  const keysToRemove: string[] = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('auth') || key.includes('supabase') || key.includes('sb-'))) {
      keysToRemove.push(key);
    }
  }

  keysToRemove.forEach(key => localStorage.removeItem(key));
  console.log('Removed localStorage keys:', keysToRemove);

  // Clear auth cookies
  document.cookie.split(';').forEach(cookie => {
    const eqPos = cookie.indexOf('=');
    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
    if (name.includes('auth') || name.includes('supabase') || name.includes('sb-')) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      console.log('Cleared cookie:', name);
    }
  });
}
