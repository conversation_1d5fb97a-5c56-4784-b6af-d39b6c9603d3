import { supabase, refreshSession } from '@/lib/supabase';

/**
 * Ensures a fresh authentication token before performing critical operations
 * This helps prevent auth token expiration during longer operations
 * @param operation A function that performs an authenticated operation
 * @returns The result of the operation function
 */
export async function withFreshAuth<T>(operation: () => Promise<T>): Promise<T> {
  try {
    // First try to refresh the session to ensure we have a fresh token
    const refreshed = await refreshSession();
    
    if (refreshed) {
      console.log('Session refreshed successfully before operation');
    } else {
      // If refresh failed, check if we're still authenticated
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required. Please log in again.');
      } else {
        console.log('Using existing session (refresh not needed or failed)');
      }
    }
    
    // Now perform the operation with a fresh token
    return await operation();
  } catch (error) {
    console.error('Auth error during operation:', error);
    throw error;
  }
}

/**
 * Checks if the current error is related to authentication
 * @param error The error to check
 * @returns True if the error is auth-related
 */
export function isAuthError(error: unknown): boolean {
  if (!error) return false;
  
  const errorMessage = error instanceof Error 
    ? error.message.toLowerCase() 
    : String(error).toLowerCase();
  
  return (
    errorMessage.includes('auth') ||
    errorMessage.includes('authenticated') ||
    errorMessage.includes('login') ||
    errorMessage.includes('session') ||
    errorMessage.includes('permission denied') ||
    errorMessage.includes('not authorized') ||
    errorMessage.includes('jwt') ||
    errorMessage.includes('token')
  );
}

/**
 * Handles authentication errors with appropriate messaging and actions
 * @param error The error to handle
 * @param onAuthError Optional callback for auth errors
 */
export function handleAuthError(error: unknown, onAuthError?: () => void): void {
  if (isAuthError(error)) {
    console.warn('Authentication error detected:', error);
    
    // Store retry info in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('pollgpt_retry_after_refresh', 'true');
    }
    
    // Call the optional callback
    if (onAuthError) {
      onAuthError();
    }
    
    // Suggest session refresh
    if (typeof window !== 'undefined' && window.confirm('Your session has expired. Reload the page to refresh your session?')) {
      window.location.reload();
    }
  }
}
