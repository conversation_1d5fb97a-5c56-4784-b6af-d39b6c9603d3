/**
 * Query keys for React Query
 * These keys are used to identify and manage cached data
 */

// Define filter types for better type safety
export interface PollFilters {
  page?: number;
  pageSize?: number;
  searchQuery?: string;
  statusFilter?: string | null;
  userId?: string;
}

export const pollKeys = {
  all: ['polls'] as const,
  lists: () => [...pollKeys.all, 'list'] as const,
  list: (filters: PollFilters = {}) => [...pollKeys.lists(), { filters }] as const,
  details: () => [...pollKeys.all, 'detail'] as const,
  detail: (id: string) => [...pollKeys.details(), id] as const,
};

export const responseKeys = {
  all: ['responses'] as const,
  lists: () => [...responseKeys.all, 'list'] as const,
  list: (pollId: string) => [...responseKeys.lists(), pollId] as const,
  details: () => [...responseKeys.all, 'detail'] as const,
  detail: (id: string) => [...responseKeys.details(), id] as const,
};

export const sessionKeys = {
  all: ['session'] as const,
  current: () => [...sessionKeys.all, 'current'] as const,
};

export const userKeys = {
  all: ['user'] as const,
  profile: () => [...userKeys.all, 'profile'] as const,
};
