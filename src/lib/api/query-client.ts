// React Query client configuration
import { QueryClient } from '@tanstack/react-query';

/**
 * Configured QueryClient with optimized settings for PollGPT
 * - 5 minute stale time: data considered fresh for 5 minutes
 * - 10 minute cache time: data kept in memory for 10 minutes
 * - Auto retry failures up to 2 times
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime in v4)
      retry: 2, // Retry failed queries twice
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff
    },
  },
});

export default queryClient;
