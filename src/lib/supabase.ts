import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/lib/database.types'

// Enhanced storage adapter that handles cookies AND localStorage with proper decoding
const createEnhancedStorage = () => {
  // Helper to safely decode base64 (handles both regular and URL-safe base64)
  const safeBase64Decode = (str: string): string | null => {
    try {
      // Remove base64- prefix if present
      const cleanStr = str.replace(/^base64-/, '')

      // Handle URL-safe base64
      const normalizedStr = cleanStr.replace(/-/g, '+').replace(/_/g, '/')

      // Add padding if needed
      const paddedStr = normalizedStr + '='.repeat((4 - normalizedStr.length % 4) % 4)

      return atob(paddedStr)
    } catch (error) {
      console.warn('Failed to decode base64 string:', error)
      return null
    }
  }

  // Helper to read chunked cookies
  const readChunkedCookies = (baseKey: string): string | null => {
    if (typeof document === 'undefined') return null

    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=')
      if (key && value) {
        try {
          acc[key] = decodeURIComponent(value)
        } catch {
          acc[key] = value // fallback if decodeURIComponent fails
        }
      }
      return acc
    }, {} as Record<string, string>)

    // Check for base key first
    if (cookies[baseKey]) {
      return cookies[baseKey]
    }

    // Check for chunked cookies (.0, .1, .2, etc.)
    const chunks: Array<{ index: number; value: string }> = []
    Object.keys(cookies).forEach(key => {
      const match = key.match(new RegExp(`^${baseKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\.(\\d+)$`))
      if (match) {
        chunks.push({ index: parseInt(match[1]), value: cookies[key] })
      }
    })

    if (chunks.length === 0) return null

    // Sort chunks by index and concatenate
    chunks.sort((a, b) => a.index - b.index)
    return chunks.map(chunk => chunk.value).join('')
  }

  // Helper to parse and validate session data
  const parseSessionData = (rawData: string): string | null => {
    try {
      // First, try to decode if it looks like base64
      let decodedData = rawData
      if (rawData.startsWith('base64-') || /^[A-Za-z0-9+/]+=*$/.test(rawData)) {
        const decoded = safeBase64Decode(rawData)
        if (decoded) {
          decodedData = decoded
        }
      }

      // Try to parse as JSON to validate
      const parsed = JSON.parse(decodedData)

      // Validate it looks like a Supabase session
      if (parsed && (parsed.access_token || parsed.user)) {
        return decodedData // return the valid JSON string
      }

      return null
    } catch (error) {
      console.warn('Failed to parse session data:', error)
      return null
    }
  }

  // Helper to write chunked cookies
  const writeChunkedCookies = (key: string, value: string) => {
    if (typeof document === 'undefined') return

    const maxCookieSize = 4000 // Conservative limit

    if (value.length <= maxCookieSize) {
      // Single cookie
      document.cookie = `${key}=${encodeURIComponent(value)}; path=/; SameSite=Lax; Secure=${location.protocol === 'https:'}`
    } else {
      // Split into chunks
      const chunks: string[] = []
      for (let i = 0; i < value.length; i += maxCookieSize) {
        chunks.push(value.slice(i, i + maxCookieSize))
      }

      chunks.forEach((chunk, index) => {
        document.cookie = `${key}.${index}=${encodeURIComponent(chunk)}; path=/; SameSite=Lax; Secure=${location.protocol === 'https:'}`
      })
    }
  }

  // Helper to remove chunked cookies
  const removeChunkedCookies = (baseKey: string) => {
    if (typeof document === 'undefined') return

    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key] = cookie.trim().split('=')
      if (key) acc[key] = true
      return acc
    }, {} as Record<string, boolean>)

    // Remove base key
    if (cookies[baseKey]) {
      document.cookie = `${baseKey}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
    }

    // Remove chunked cookies
    Object.keys(cookies).forEach(key => {
      if (key.match(new RegExp(`^${baseKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\.(\\d+)$`))) {
        document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
      }
    })
  }

  return {
    getItem: (key: string) => {
      try {
        if (typeof window === 'undefined') return null

        // First check localStorage
        const localStorageValue = window.localStorage.getItem(key)
        if (localStorageValue) {
          console.log(`Storage getItem for key ${key}: found in localStorage`)
          return localStorageValue
        }

        // Then check cookies (for OAuth callback scenarios)
        const cookieValue = readChunkedCookies(key)
        if (cookieValue) {
          console.log(`Storage getItem for key ${key}: found in cookies, transferring to localStorage`)

          // Parse and validate the session data
          const parsedValue = parseSessionData(cookieValue)
          if (parsedValue) {
            // Transfer to localStorage for future reads
            window.localStorage.setItem(key, parsedValue)
            return parsedValue
          } else {
            console.warn(`Invalid session data in cookie for key ${key}`)
            return null
          }
        }

        console.log(`Storage getItem for key ${key}: not found`)
        return null
      } catch (error) {
        console.error(`Error in storage.getItem for key ${key}:`, error)
        return null
      }
    },

    setItem: (key: string, value: string) => {
      try {
        if (typeof window === 'undefined') return

        console.log(`Storage setItem for key ${key}`)

        // Validate the value is proper JSON
        try {
          JSON.parse(value)
        } catch {
          console.warn(`Invalid JSON value for key ${key}, skipping storage`)
          return
        }

        // Set in localStorage
        window.localStorage.setItem(key, value)

        // Also set in cookies for OAuth callback persistence (but don't base64 encode)
        writeChunkedCookies(key, value)
      } catch (error) {
        console.error(`Error in storage.setItem for key ${key}:`, error)
      }
    },

    removeItem: (key: string) => {
      try {
        if (typeof window === 'undefined') return

        console.log(`Storage removeItem for key ${key}`)

        // Remove from localStorage
        window.localStorage.removeItem(key)

        // Remove from cookies
        removeChunkedCookies(key)
      } catch (error) {
        console.error(`Error in storage.removeItem for key ${key}:`, error)
      }
    }
  }
}

// This helper ensures we don't get runtime errors during SSR or static generation
let supabaseInstance: ReturnType<typeof createRealClient> | ReturnType<typeof createDummyClient> | null = null

// Function to create a real Supabase client using SSR-compatible browser client
function createRealClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn('Supabase URL or Anon Key is missing. Using dummy client instead.')
    return createDummyClient()
  }

  return createBrowserClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        storageKey: 'sb-sumruaeyfidjlssrmfrm-auth-token',
        flowType: 'pkce',
        debug: false,
        storage: createEnhancedStorage()
      }
    }
  )
}

// Function to create a dummy client for SSR/static generation or missing env vars
function createDummyClient() {
  return {
    from: () => ({
      select: () => ({ data: null, error: null }),
      insert: () => ({ data: null, error: null }),
      update: () => ({ data: null, error: null }),
      delete: () => ({ data: null, error: null }),
      eq: () => ({ data: null, error: null }),
      order: () => ({ data: null, error: null }),
      limit: () => ({ data: null, error: null }),
      single: () => ({ data: null, error: null }),
      maybeSingle: () => Promise.resolve({ data: null, error: null }),
    }),
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      signOut: () => Promise.resolve({ error: null }),
      signInWithPassword: () => Promise.resolve({ data: { user: null, session: null }, error: null }),
      signUp: () => Promise.resolve({ data: { user: null, session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
      refreshSession: () => Promise.resolve({ data: { session: null }, error: null }),
      resetPasswordForEmail: () => Promise.resolve({ data: null, error: null }),
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any
}

// Get the Supabase client instance with React 19 compatible session restoration
export function getSupabase() {
  // For browser environments, always create a real client
  if (typeof window !== 'undefined') {
    return createRealClient()
  }

  // For server environments, use a singleton pattern
  if (!supabaseInstance) {
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'phase-production-build') {
      supabaseInstance = createDummyClient()
    } else {
      supabaseInstance = createRealClient()
    }
  }

  return supabaseInstance
}

// Create and export the Supabase client
export const supabase = getSupabase()

// Enhanced session restoration for React 19 compatibility
export const restoreSession = async (): Promise<boolean> => {
  try {
    if (typeof window === 'undefined') return false

    console.log('🔄 Starting session restoration...')

    // Get current session
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error('❌ Session restoration error:', error)
      return false
    }

    if (data.session) {
      console.log('✅ Session restored successfully:', data.session.user?.id)
      return true
    }

    console.log('ℹ️ No session to restore')
    return false
  } catch (err) {
    console.error('❌ Session restoration exception:', err)
    return false
  }
}

// Helper function to refresh the session
export const refreshSession = async () => {
  try {
    if (typeof window === 'undefined') return false

    const { data, error } = await supabase.auth.refreshSession()

    if (error) {
      console.error("Error refreshing session:", error)
      return false
    }

    if (!data.session) {
      console.warn("No session returned when refreshing")
      return false
    }

    return true
  } catch (err) {
    console.error("Exception during session refresh:", err)
    return false
  }
}

// Helper function to check if user is authenticated
export const isAuthenticated = async () => {
  try {
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error("Error checking authentication:", error)
      return false
    }

    return !!data.session
  } catch (err) {
    console.error("Exception during auth check:", err)
    return false
  }
}

export type { Database }
export * from '@/lib/database.types'
