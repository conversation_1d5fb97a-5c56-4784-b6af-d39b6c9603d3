import { useCompletion } from 'ai/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { pollKeys } from '@/hooks/use-polls';
import { useState, useCallback } from 'react';
import type { PollCreationInput } from '@/lib/ai/schemas';

// Define types for poll data
interface PollData {
  id?: string;
  title: string;
  description?: string;
  questions?: Array<{
    text: string;
    type: string;
    options?: Array<{ text: string; value: string }>;
    required?: boolean;
    order?: number;
  }>;
}

/**
 * Enhanced hook for AI-powered poll generation with real-time features
 * Uses AI SDK's useCompletion for structured poll generation
 */
export function useAIPollGeneration() {
  const queryClient = useQueryClient();
  const [isStreaming, setIsStreaming] = useState(false);
  const [generatedPoll, setGeneratedPoll] = useState<PollData | null>(null);

  // State for poll generation
  const [isGeneratingPoll, setIsGeneratingPoll] = useState(false);
  const [pollGenerationError, setPollGenerationError] = useState<Error | null>(null);

  // Direct fetch for structured poll generation (not using useCompletion)
  const submitPollGeneration = useCallback(async (input: PollCreationInput) => {
    setIsGeneratingPoll(true);
    setPollGenerationError(null);

    try {
      const response = await fetch('/api/ai/generate-poll', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        throw new Error(`Poll generation failed: ${response.status}`);
      }

      const completion = await response.text();

      try {
        const parsed = JSON.parse(completion);
        setGeneratedPoll(parsed);
      } catch {
        // If parsing fails, create a basic poll structure
        setGeneratedPoll({
          title: 'Generated Poll',
          description: completion,
          questions: []
        });
      }
    } catch (error) {
      setPollGenerationError(error as Error);
      throw error;
    } finally {
      setIsGeneratingPoll(false);
    }
  }, []);

  // AI SDK useCompletion hook for real-time content analysis
  const {
    completion: analysisText,
    complete: analyzeContent,
    isLoading: isAnalyzing,
    error: analysisError,
  } = useCompletion({
    api: '/api/ai/analyze-content',
    onFinish: () => setIsStreaming(false),
  });

  // React Query mutation for saving generated polls
  const savePollMutation = useMutation({
    mutationFn: async (pollData: PollData) => {
      const response = await fetch('/api/polls', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(pollData),
      });

      if (!response.ok) {
        throw new Error('Failed to save poll');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate polls cache to refresh UI
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });

  // Enhanced poll generation with content analysis
  const generatePollWithAnalysis = useCallback(async (input: PollCreationInput) => {
    try {
      // Start content analysis if content is provided
      if (input.content) {
        setIsStreaming(true);
        analyzeContent(input.content);
      }

      // Generate structured poll
      await submitPollGeneration(input);
    } catch (error) {
      console.error('Poll generation with analysis failed:', error);
      throw error;
    }
  }, [submitPollGeneration, analyzeContent]);

  // Save generated poll to database
  const saveGeneratedPoll = useCallback(async () => {
    if (!generatedPoll) {
      throw new Error('No poll to save');
    }

    return savePollMutation.mutateAsync(generatedPoll);
  }, [generatedPoll, savePollMutation]);

  // Real-time content analysis
  const streamContentAnalysis = useCallback(async (content: string) => {
    setIsStreaming(true);
    return analyzeContent(content);
  }, [analyzeContent]);

  return {
    // Generated poll data
    generatedPoll,
    analysisText,

    // Loading states
    isGeneratingPoll,
    isAnalyzing,
    isStreaming,
    isSavingPoll: savePollMutation.isPending,

    // Error states
    pollGenerationError,
    analysisError,
    saveError: savePollMutation.error,

    // Actions
    generatePoll: (input: PollCreationInput) => submitPollGeneration(input),
    generatePollWithAnalysis,
    analyzeContent: streamContentAnalysis,
    saveGeneratedPoll,

    // Success states
    pollSaved: savePollMutation.isSuccess,

    // Reset functionality
    resetGeneration: () => {
      setIsStreaming(false);
      setGeneratedPoll(null);
      savePollMutation.reset();
    },
  };
}

/**
 * Hook specifically for streaming poll insights and suggestions
 */
export function useAIPollInsights() {
  const [insights, setInsights] = useState<string[]>([]);

  const {
    completion: currentInsight,
    complete: generateInsight,
    isLoading: isGeneratingInsight,
    error: insightError,
  } = useCompletion({
    api: '/api/ai/stream-poll-insights',
    onFinish: (completion) => {
      // Add completed insight to history
      setInsights(prev => [...prev, completion]);
    },
  });

  const getInsights = useCallback(async (pollData: PollData) => {
    return generateInsight(JSON.stringify(pollData));
  }, [generateInsight]);

  const clearInsights = useCallback(() => {
    setInsights([]);
  }, []);

  return {
    insights,
    currentInsight,
    isGeneratingInsight,
    insightError,
    getInsights,
    clearInsights,
  };
}

/**
 * Hook for real-time poll optimization suggestions
 */
export function useAIPollOptimization() {
  const {
    completion: optimizationSuggestions,
    complete: getOptimizations,
    isLoading: isOptimizing,
    error: optimizationError,
  } = useCompletion({
    api: '/api/ai/optimize-poll',
  });

  const optimizePoll = useCallback(async (pollData: PollData) => {
    return getOptimizations(JSON.stringify({
      ...pollData,
      requestType: 'optimization',
    }));
  }, [getOptimizations]);

  return {
    optimizationSuggestions,
    isOptimizing,
    optimizationError,
    optimizePoll,
  };
}

export type { PollCreationInput };
