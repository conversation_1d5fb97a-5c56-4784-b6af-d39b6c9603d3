/**
 * Advanced Prefetching System for PollGPT
 *
 * Implements intelligent prefetching strategies for instant navigation
 * and improved user experience
 */

import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuthContext } from '@/components/providers/auth-provider-optimized';
import { getUserId } from '@/lib/utils/user-id-manager';
import { pollKeys } from './use-polls-optimized';
import { supabase } from '@/lib/supabase';

// Prefetch priorities
type PrefetchPriority = 'low' | 'medium' | 'high';

// Prefetch strategies
type PrefetchStrategy = 'immediate' | 'hover' | 'focus' | 'idle' | 'route-change';

interface PrefetchOptions {
  priority?: PrefetchPriority;
  strategy?: PrefetchStrategy;
  delay?: number;
  maxAge?: number;
  enabled?: boolean;
}

/**
 * Hook for intelligent prefetching based on user behavior
 */
export function useIntelligentPrefetch() {
  const queryClient = useQueryClient();
  const { userId } = useAuthContext();
  const prefetchedRoutes = useRef<Set<string>>(new Set());
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Prefetch critical user data with instant user ID access
   */
  const prefetchUserData = useCallback(async (options: PrefetchOptions = {}) => {
    const { priority = 'high', enabled = true } = options;

    if (!enabled) return;

    // Use instant user ID access
    const currentUserId = userId || (typeof window !== 'undefined' ? getUserId() : null);
    if (!currentUserId) return;

    // Prefetch user profile
    await queryClient.prefetchQuery({
      queryKey: ['auth', 'profile', currentUserId],
      queryFn: async () => {
        const { data } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', currentUserId)
          .single();
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Prefetch user's recent polls
    await queryClient.prefetchQuery({
      queryKey: pollKeys.list({ userId: currentUserId, page: 1, pageSize: 10 }),
      queryFn: async () => {
        const { data } = await supabase
          .from('polls')
          .select('*')
          .eq('user_id', currentUserId)
          .order('created_at', { ascending: false })
          .limit(10);
        return data;
      },
      staleTime: 3 * 60 * 1000, // 3 minutes
    });

    console.log(`Prefetched user data for ${currentUserId} with priority: ${priority}`);
  }, [queryClient, userId]);

  /**
   * Prefetch dashboard data with optimized user ID access
   */
  const prefetchDashboard = useCallback(async (options: PrefetchOptions = {}) => {
    const { priority = 'medium', enabled = true } = options;

    if (!enabled) return;

    // Use instant user ID access
    const currentUserId = userId || (typeof window !== 'undefined' ? getUserId() : null);
    if (!currentUserId) return;

    // Prefetch dashboard polls
    await queryClient.prefetchQuery({
      queryKey: pollKeys.list({ userId: currentUserId, page: 1, pageSize: 20 }),
      queryFn: async () => {
        const { data } = await supabase
          .from('polls')
          .select('*')
          .eq('user_id', currentUserId)
          .order('created_at', { ascending: false })
          .limit(20);
        return data;
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    });

    // Prefetch poll statistics
    await queryClient.prefetchQuery({
      queryKey: ['dashboard', 'stats', currentUserId],
      queryFn: async () => {
        const { data } = await supabase
          .from('polls')
          .select('id, responses_count, views_count, created_at')
          .eq('user_id', currentUserId);
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });

    console.log(`Prefetched dashboard data for ${currentUserId} with priority: ${priority}`);
  }, [queryClient, userId]);

  /**
   * Prefetch specific poll data
   */
  const prefetchPoll = useCallback(async (pollId: string, options: PrefetchOptions = {}) => {
    const { priority = 'medium', enabled = true } = options;

    if (!enabled || !pollId) return;

    // Prefetch poll details
    await queryClient.prefetchQuery({
      queryKey: pollKeys.detail(pollId),
      queryFn: async () => {
        const { data } = await supabase
          .from('polls')
          .select('*')
          .eq('id', pollId)
          .single();
        return data;
      },
      staleTime: 3 * 60 * 1000, // 3 minutes
    });

    // Prefetch poll responses
    await queryClient.prefetchQuery({
      queryKey: ['poll', pollId, 'responses'],
      queryFn: async () => {
        const { data } = await supabase
          .from('poll_responses')
          .select('*')
          .eq('poll_id', pollId);
        return data;
      },
      staleTime: 1 * 60 * 1000, // 1 minute
    });

    console.log(`Prefetched poll ${pollId} with priority: ${priority}`);
  }, [queryClient]);

  /**
   * Route-based prefetching
   */
  const prefetchRoute = useCallback(async (route: string, options: PrefetchOptions = {}) => {
    const { priority = 'low', enabled = true } = options;

    if (!enabled || prefetchedRoutes.current.has(route)) return;

    prefetchedRoutes.current.add(route);

    // Determine what to prefetch based on route
    if (route.includes('/dashboard')) {
      await prefetchDashboard({ priority });
    } else if (route.includes('/polls/')) {
      const pollId = route.split('/polls/')[1]?.split('/')[0];
      if (pollId) {
        await prefetchPoll(pollId, { priority });
      }
    } else if (route.includes('/profile')) {
      await prefetchUserData({ priority });
    }

    console.log(`Prefetched route: ${route} with priority: ${priority}`);
  }, [prefetchDashboard, prefetchPoll, prefetchUserData]);

  /**
   * Hover-based prefetching
   */
  const prefetchOnHover = useCallback((element: HTMLElement | null, route: string) => {
    if (!element) return;

    const handleMouseEnter = () => {
      prefetchRoute(route, { priority: 'medium', strategy: 'hover' });
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    return () => element.removeEventListener('mouseenter', handleMouseEnter);
  }, [prefetchRoute]);

  /**
   * Focus-based prefetching
   */
  const prefetchOnFocus = useCallback((element: HTMLElement | null, route: string) => {
    if (!element) return;

    const handleFocus = () => {
      prefetchRoute(route, { priority: 'medium', strategy: 'focus' });
    };

    element.addEventListener('focus', handleFocus);
    return () => element.removeEventListener('focus', handleFocus);
  }, [prefetchRoute]);

  /**
   * Idle-time prefetching
   */
  const prefetchOnIdle = useCallback((callback: () => Promise<void>, delay = 2000) => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }

    idleTimeoutRef.current = setTimeout(async () => {
      await callback();
    }, delay);
  }, []);

  /**
   * Background prefetching for critical data with instant user ID access
   */
  const backgroundPrefetch = useCallback(async () => {
    // Use instant user ID access
    const currentUserId = userId || (typeof window !== 'undefined' ? getUserId() : null);
    if (!currentUserId) return;

    // Prefetch user data in the background
    await prefetchUserData({ priority: 'low', strategy: 'idle' });

    // Prefetch dashboard data if not already cached
    const dashboardData = queryClient.getQueryData(pollKeys.list({ userId: currentUserId, page: 1 }));
    if (!dashboardData) {
      await prefetchDashboard({ priority: 'low', strategy: 'idle' });
    }
  }, [userId, prefetchUserData, prefetchDashboard, queryClient]);

  /**
   * Cleanup prefetch timeouts
   */
  useEffect(() => {
    return () => {
      if (idleTimeoutRef.current) {
        clearTimeout(idleTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Auto-prefetch on route changes
   */
  useEffect(() => {
    const handleRouteChange = () => {
      // Clear prefetched routes on navigation
      prefetchedRoutes.current.clear();
    };

    // Listen for route changes
    // Note: This is a simplified implementation, actual implementation would depend on Next.js routing
    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, []);

  return {
    prefetchUserData,
    prefetchDashboard,
    prefetchPoll,
    prefetchRoute,
    prefetchOnHover,
    prefetchOnFocus,
    prefetchOnIdle,
    backgroundPrefetch,
  };
}

/**
 * Hook for link prefetching
 */
export function useLinkPrefetch() {
  const { prefetchRoute } = useIntelligentPrefetch();

  const prefetchLink = useCallback((href: string, options: PrefetchOptions = {}) => {
    const { strategy = 'hover', delay = 100 } = options;

    return (element: HTMLElement | null) => {
      if (!element) return;

      const handleInteraction = () => {
        setTimeout(() => {
          prefetchRoute(href, { ...options, strategy });
        }, delay);
      };

      if (strategy === 'hover') {
        element.addEventListener('mouseenter', handleInteraction);
        return () => element.removeEventListener('mouseenter', handleInteraction);
      } else if (strategy === 'focus') {
        element.addEventListener('focus', handleInteraction);
        return () => element.removeEventListener('focus', handleInteraction);
      }
    };
  }, [prefetchRoute]);

  return { prefetchLink };
}

/**
 * Hook for automatic prefetching on component mount
 */
export function useAutoPrefetch(routes: string[], options: PrefetchOptions = {}) {
  const { prefetchRoute } = useIntelligentPrefetch();

  useEffect(() => {
    const { strategy = 'idle', delay = 1000 } = options;

    const prefetchRoutes = async () => {
      for (const route of routes) {
        await prefetchRoute(route, { ...options, strategy });
      }
    };

    if (strategy === 'immediate') {
      prefetchRoutes();
    } else if (strategy === 'idle') {
      const timeout = setTimeout(prefetchRoutes, delay);
      return () => clearTimeout(timeout);
    }
  }, [routes, prefetchRoute, options]);
}

/**
 * Hook for prefetching based on user behavior patterns
 */
export function useBehaviorBasedPrefetch() {
  const { prefetchRoute } = useIntelligentPrefetch();
  const visitedRoutes = useRef<string[]>([]);

  const trackVisit = useCallback((route: string) => {
    visitedRoutes.current.push(route);

    // Keep only last 10 visited routes
    if (visitedRoutes.current.length > 10) {
      visitedRoutes.current = visitedRoutes.current.slice(-10);
    }
  }, []);

  const predictNextRoute = useCallback((): string | null => {
    const routes = visitedRoutes.current;
    if (routes.length < 2) return null;

    // Simple pattern detection - if user visited dashboard then polls, likely to visit polls again
    const lastRoute = routes[routes.length - 1];
    const secondLastRoute = routes[routes.length - 2];

    if (lastRoute.includes('/dashboard') && secondLastRoute.includes('/polls')) {
      return '/dashboard/polls';
    }

    return null;
  }, []);

  const prefetchPredictedRoute = useCallback(() => {
    const predictedRoute = predictNextRoute();
    if (predictedRoute) {
      prefetchRoute(predictedRoute, { priority: 'low', strategy: 'idle' });
    }
  }, [predictNextRoute, prefetchRoute]);

  return {
    trackVisit,
    predictNextRoute,
    prefetchPredictedRoute,
  };
}
