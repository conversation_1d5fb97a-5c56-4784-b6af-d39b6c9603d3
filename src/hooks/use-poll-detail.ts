import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { pollKeys } from './use-polls';

// Define complete Poll and PollQuestion interfaces to fix type errors
interface PollQuestion {
  id: string;
  text: string;
  type: string;
  options: string[];
  required: boolean;
  order: number;
}

interface Poll {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  userId: string;
  status: string;
  questions: PollQuestion[];
  questionsCount: number;
  responsesCount: number;
  viewsCount: number;
  is_public: boolean;
  access_code?: string;
}

/**
 * Hook for fetching a single poll detail with caching
 * @param id The ID of the poll to fetch
 */
export function usePollDetail(id: string | undefined) {
  return useQuery<Poll, Error>({
    queryKey: id ? pollKeys.detail(id) : [],
    queryFn: async (): Promise<Poll> => {
      if (!id) {
        throw new Error('Poll ID is required');
      }
      
      // Check for a valid session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Session not found');
      }
      
      // Fetch poll detail
      const { data, error } = await supabase
        .from('polls')
        .select(`
          *,
          questions (*)
        `)
        .eq('id', id)
        .single();
        
      if (error) {
        console.error(`Error fetching poll ${id}:`, error);
        throw new Error(error.message);
      }
      
      if (!data) {
        throw new Error('Poll not found');
      }
      
      // Get response count
      const { count: responsesCount, error: responseCountError } = await supabase
        .from('responses')
        .select('id', { count: 'exact', head: true })
        .eq('poll_id', id);
        
      if (responseCountError) {
        console.error('Error fetching response count:', responseCountError);
      }
      
      // Transform data to match Poll interface
      const poll: Poll = {
        id: data.id,
        title: data.title,
        description: data.description || '',
        createdAt: data.created_at,
        updatedAt: data.updated_at || data.created_at,
        expiresAt: data.expires_at,
        userId: data.user_id,
        status: data.status || 'active',
        questions: data.questions.map((q: {
          id: string;
          question_text: string;
          question_type: string;
          options: string[];
          required: boolean;
          order: number;
        }) => ({
          id: q.id,
          text: q.question_text,
          type: q.question_type,
          options: q.options || [],
          required: q.required || false,
          order: q.order || 0,
        })),
        questionsCount: data.questions.length,
        responsesCount: responsesCount || 0,
        viewsCount: data.views || 0,
        is_public: data.is_public !== undefined ? data.is_public : true,
        access_code: data.access_code,
      };
      
      return poll;
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: !!id, // Only run the query if we have an ID
  });
}

/**
 * Hook for updating a poll
 */
export function useUpdatePoll() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Poll> }) => {
      // Transform the data for the database
      const updateData = {
        title: data.title,
        description: data.description,
        status: data.status,
        is_public: data.is_public,
        expires_at: data.expiresAt,
        access_code: data.access_code,
      };
      
      // Update the poll
      const { data: updatedPoll, error } = await supabase
        .from('polls')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating poll:', error);
        throw new Error(error.message);
      }
      
      // Update questions if provided
      if (data.questions && data.questions.length > 0) {
        // First delete existing questions
        const { error: deleteError } = await supabase
          .from('questions')
          .delete()
          .eq('poll_id', id);
        
        if (deleteError) {
          console.error('Error deleting questions:', deleteError);
          throw new Error(deleteError.message);
        }
        
        // Then insert new questions
        const questionData = data.questions.map((q, index) => ({
          poll_id: id,
          question_text: q.text,
          question_type: q.type,
          options: q.options,
          required: q.required,
          order: q.order || index,
        }));
        
        const { error: insertError } = await supabase
          .from('questions')
          .insert(questionData);
        
        if (insertError) {
          console.error('Error inserting questions:', insertError);
          throw new Error(insertError.message);
        }
      }
      
      return updatedPoll;
    },
    onSuccess: (_, variables) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: pollKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });
}

/**
 * Hook for creating a new poll
 */
export function useCreatePoll() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (pollData: Partial<Poll>) => {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Not authenticated');
      }
      
      // Create the poll
      const pollPayload = {
        title: pollData.title || 'Untitled Poll',
        description: pollData.description || '',
        status: pollData.status || 'draft',
        is_public: pollData.is_public !== undefined ? pollData.is_public : true,
        user_id: session.user.id,
        expires_at: pollData.expiresAt,
        access_code: pollData.access_code,
      };
      
      const { data: newPoll, error: pollError } = await supabase
        .from('polls')
        .insert(pollPayload)
        .select()
        .single();
      
      if (pollError) {
        console.error('Error creating poll:', pollError);
        throw new Error(pollError.message);
      }
      
      // Create questions if provided
      if (pollData.questions && pollData.questions.length > 0) {
        const questionData = pollData.questions.map((q, index) => ({
          poll_id: newPoll.id,
          question_text: q.text,
          question_type: q.type,
          options: q.options,
          required: q.required,
          order: q.order || index,
        }));
        
        const { error: questionError } = await supabase
          .from('questions')
          .insert(questionData);
        
        if (questionError) {
          console.error('Error creating questions:', questionError);
          throw new Error(questionError.message);
        }
      }
      
      return newPoll;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });
}
