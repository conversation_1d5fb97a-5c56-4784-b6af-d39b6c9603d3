import { useCompletion, useChat } from 'ai/react';
import { useState, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

// Define types for better type safety
interface PollData {
  id?: string;
  title?: string;
  description?: string;
  questions?: Array<{
    text: string;
    type: string;
    options?: Array<{ text: string; value: string }>;
  }>;
}

interface ResponseData {
  id: string;
  created_at: string;
  poll_id: string;
  [key: string]: unknown;
}

/**
 * Hook for real-time streaming analytics with AI insights
 */
export function useStreamingAnalytics(pollData?: PollData) {
  const [analyticsHistory, setAnalyticsHistory] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  // AI SDK useCompletion for streaming analytics
  const {
    completion: currentAnalysis,
    complete: runAnalysis,
    isLoading: isAnalyzing,
    error: analysisError,
    stop: stopAnalysis,
  } = useCompletion({
    api: '/api/ai/stream-analytics',
    onFinish: (completion) => {
      setAnalyticsHistory(prev => [...prev, completion]);
      setIsConnected(false);
    },
    onError: () => setIsConnected(false),
  });

  // Auto-analyze when poll data changes
  useEffect(() => {
    if (pollData && Object.keys(pollData).length > 0) {
      setIsConnected(true);
      runAnalysis(JSON.stringify({
        pollData,
        analysisType: 'comprehensive',
        timestamp: new Date().toISOString(),
      }));
    }
  }, [pollData, runAnalysis]);

  const analyzeSpecific = useCallback(async (data: PollData, analysisType: string) => {
    setIsConnected(true);
    return runAnalysis(JSON.stringify({
      pollData: data,
      analysisType,
      timestamp: new Date().toISOString(),
    }));
  }, [runAnalysis]);

  const clearHistory = useCallback(() => {
    setAnalyticsHistory([]);
  }, []);

  return {
    // Current streaming analysis
    currentAnalysis,

    // Analysis history
    analyticsHistory,

    // Connection state
    isConnected,
    isAnalyzing,

    // Error handling
    analysisError,

    // Actions
    runAnalysis: analyzeSpecific,
    stopAnalysis,
    clearHistory,

    // Quick analysis shortcuts
    analyzePerformance: (data: PollData) => analyzeSpecific(data, 'performance'),
    analyzeDemographics: (data: PollData) => analyzeSpecific(data, 'demographics'),
    analyzeTrends: (data: PollData) => analyzeSpecific(data, 'trends'),
    analyzeSentiment: (data: PollData) => analyzeSpecific(data, 'sentiment'),
  };
}

/**
 * Hook for real-time poll response analysis as responses come in
 */
export function useRealTimeResponseAnalysis(pollId: string) {
  const [lastAnalysisTime, setLastAnalysisTime] = useState<Date>(new Date());
  const [insights, setInsights] = useState<Array<{
    id: string;
    type: string;
    message: string;
    timestamp: Date;
    confidence: number;
  }>>([]);

  // Streaming analysis for incoming responses
  const {
    completion: responseInsight,
    complete: analyzeNewResponse,
    isLoading: isAnalyzingResponse,
  } = useCompletion({
    api: '/api/ai/analyze-response',
    onFinish: (completion) => {
      try {
        const insight = JSON.parse(completion);
        setInsights(prev => [{
          id: Date.now().toString(),
          ...insight,
          timestamp: new Date(),
        }, ...prev].slice(0, 10)); // Keep only last 10 insights
      } catch {
        // If parsing fails, treat as plain text insight
        setInsights(prev => [{
          id: Date.now().toString(),
          type: 'general',
          message: completion,
          timestamp: new Date(),
          confidence: 0.8,
        }, ...prev].slice(0, 10));
      }
    },
  });

  // Fetch current poll responses
  const { data: responses, refetch: refetchResponses } = useQuery({
    queryKey: ['poll-responses', pollId],
    queryFn: async () => {
      const response = await fetch(`/api/polls/${pollId}/responses`);
      if (!response.ok) throw new Error('Failed to fetch responses');
      return response.json();
    },
    refetchInterval: 5000, // Poll every 5 seconds for new responses
  });

  // Analyze new responses when they come in
  useEffect(() => {
    if (responses && responses.length > 0) {
      const newResponses = responses.filter((r: ResponseData) =>
        new Date(r.created_at) > lastAnalysisTime
      );

      if (newResponses.length > 0) {
        analyzeNewResponse(JSON.stringify({
          pollId,
          newResponses,
          totalResponses: responses.length,
          analysisType: 'incremental',
        }));
        setLastAnalysisTime(new Date());
      }
    }
  }, [responses, lastAnalysisTime, analyzeNewResponse, pollId]);

  const clearInsights = useCallback(() => {
    setInsights([]);
  }, []);

  return {
    // Current state
    insights,
    responseInsight,
    responses,

    // Loading states
    isAnalyzingResponse,

    // Actions
    refetchResponses,
    clearInsights,

    // Stats
    totalResponses: responses?.length || 0,
    newResponsesCount: responses?.filter((r: ResponseData) =>
      new Date(r.created_at) > lastAnalysisTime
    ).length || 0,
  };
}

/**
 * Hook for AI-powered chat interface for poll discussions
 */
export function useAIPollChat(pollId: string) {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isChatLoading,
    error: chatError,
    reload,
    stop,
  } = useChat({
    api: '/api/ai/poll-chat',
    initialMessages: [{
      id: 'welcome',
      role: 'assistant',
      content: `Hi! I'm here to help you discuss and analyze this poll. What would you like to know?`,
    }],
    body: {
      pollId,
    },
  });

  const askAboutPoll = useCallback((question: string) => {
    const syntheticEvent = {
      preventDefault: () => {},
    } as React.FormEvent<HTMLFormElement>;

    handleInputChange({
      target: { value: question },
    } as React.ChangeEvent<HTMLInputElement>);

    // Submit after a brief delay to ensure input is set
    setTimeout(() => handleSubmit(syntheticEvent), 10);
  }, [handleInputChange, handleSubmit]);

  return {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isChatLoading,
    chatError,
    reload,
    stop,
    askAboutPoll,
  };
}

/**
 * Hook for live poll performance monitoring
 */
export function useLivePollMonitoring(pollId: string) {
  const [metrics, setMetrics] = useState({
    responseRate: 0,
    completionRate: 0,
    averageTime: 0,
    dropOffPoints: [] as string[],
    engagement: 0,
  });

  const {
    completion: performanceInsight,
    complete: analyzePerformance,
    isLoading: isAnalyzingPerformance,
  } = useCompletion({
    api: '/api/ai/monitor-performance',
    onFinish: (completion) => {
      try {
        const newMetrics = JSON.parse(completion);
        setMetrics(prev => ({ ...prev, ...newMetrics }));
      } catch {
        console.warn('Failed to parse performance metrics');
      }
    },
  });

  // Fetch live metrics
  const { data: liveData } = useQuery({
    queryKey: ['live-metrics', pollId],
    queryFn: async () => {
      const response = await fetch(`/api/polls/${pollId}/live-metrics`);
      if (!response.ok) throw new Error('Failed to fetch live metrics');
      return response.json();
    },
    refetchInterval: 3000, // Update every 3 seconds
  });

  // Trigger AI analysis when data updates
  useEffect(() => {
    if (liveData) {
      analyzePerformance(JSON.stringify({
        pollId,
        metrics: liveData,
        timestamp: new Date().toISOString(),
      }));
    }
  }, [liveData, pollId, analyzePerformance]);

  return {
    metrics: { ...metrics, ...(liveData || {}) } as typeof metrics,
    performanceInsight,
    isAnalyzingPerformance,
    isLoading: !liveData,
  };
}
