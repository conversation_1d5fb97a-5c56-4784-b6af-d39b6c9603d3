/**
 * Intelligent Query Invalidation System
 *
 * Provides smart invalidation patterns for related queries,
 * dependency-based invalidation, and optimistic updates
 */

import { useQueryClient } from '@tanstack/react-query';
import { authKeys } from './use-auth-optimized';
import { pollKeys } from './use-polls-optimized';

// Define query dependencies and relationships
const QUERY_DEPENDENCIES = {
  // Auth-related invalidations
  auth: {
    signin: [authKeys.all, pollKeys.all],
    signout: [authKeys.all, pollKeys.all],
    signup: [authKeys.all],
    profile: [authKeys.profile()],
  },
  // Poll-related invalidations
  polls: {
    create: [pollKeys.all],
    update: [pollKeys.all],
    delete: [pollKeys.all],
    vote: [pollKeys.details(), pollKeys.all],
    simulation: [pollKeys.details(), pollKeys.all],
  },
  // User-related invalidations
  user: {
    profile: [authKeys.profile()],
    settings: [authKeys.profile()],
  },
} as const;

/**
 * Hook for intelligent query invalidation
 */
export function useIntelligentInvalidation() {
  const queryClient = useQueryClient();

  /**
   * Invalidate queries based on action type
   */
  const invalidateByAction = async (action: string) => {
    const [domain, operation] = action.split('.');

    if (!domain || !operation) {
      console.warn('Invalid action format. Use "domain.operation" format.');
      return;
    }

    const dependencies = QUERY_DEPENDENCIES[domain as keyof typeof QUERY_DEPENDENCIES];
    if (!dependencies) {
      console.warn(`Unknown domain: ${domain}`);
      return;
    }

    const queryKeys = dependencies[operation as keyof typeof dependencies] as readonly unknown[][];
    if (!queryKeys || !Array.isArray(queryKeys)) {
      console.warn(`Unknown operation: ${operation} for domain: ${domain}`);
      return;
    }

    // Invalidate all related queries
    await Promise.all(
      queryKeys.map(async (key) => {
        await queryClient.invalidateQueries({ queryKey: key });
      })
    );

    // Log for debugging
    console.log(`Invalidated queries for action: ${action}`, queryKeys);
  };

  /**
   * Invalidate queries with smart batching
   */
  const batchInvalidate = async (actions: string[]) => {
    const allKeys = new Set<readonly unknown[]>();

    // Collect all query keys to invalidate
    actions.forEach(action => {
      const [domain, operation] = action.split('.');
      const dependencies = QUERY_DEPENDENCIES[domain as keyof typeof QUERY_DEPENDENCIES];
      if (dependencies) {
        const queryKeys = dependencies[operation as keyof typeof dependencies] as readonly unknown[][];
        if (queryKeys && Array.isArray(queryKeys)) {
          queryKeys.forEach(key => allKeys.add(key));
        }
      }
    });

    // Batch invalidate all unique keys
    await Promise.all(
      Array.from(allKeys).map(async (key) => {
        await queryClient.invalidateQueries({ queryKey: key });
      })
    );

    console.log(`Batch invalidated queries for actions: ${actions.join(', ')}`);
  };

  /**
   * Invalidate queries with conditions
   */
  const conditionalInvalidate = async (
    condition: () => boolean,
    action: string
  ) => {
    if (condition()) {
      await invalidateByAction(action);
    }
  };

  /**
   * Invalidate stale queries (older than threshold)
   */
  const invalidateStaleQueries = async (maxAge: number = 5 * 60 * 1000) => {
    const now = Date.now();
    const queries = queryClient.getQueryCache().getAll();

    const staleQueries = queries.filter(query => {
      const lastUpdated = query.state.dataUpdatedAt;
      return lastUpdated && (now - lastUpdated) > maxAge;
    });

    await Promise.all(
      staleQueries.map(async (query) => {
        await queryClient.invalidateQueries({ queryKey: query.queryKey });
      })
    );

    console.log(`Invalidated ${staleQueries.length} stale queries`);
  };

  /**
   * Background refresh for critical queries
   */
  const backgroundRefresh = async (keys: readonly (readonly unknown[])[] = [authKeys.session(), pollKeys.all]) => {
    await Promise.all(
      keys.map(async (key) => {
        await queryClient.refetchQueries({
          queryKey: key,
          type: 'active' // Only refetch active queries
        });
      })
    );
  };

  /**
   * Optimistic update helper
   */
  const optimisticUpdate = async <T>(
    queryKey: readonly unknown[],
    updater: (old: T | undefined) => T,
    action: () => Promise<T>
  ) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey });

    // Snapshot previous value
    const previousData = queryClient.getQueryData<T>(queryKey);

    // Optimistically update
    queryClient.setQueryData(queryKey, updater);

    try {
      // Perform actual mutation
      const result = await action();

      // Update with real data
      queryClient.setQueryData(queryKey, result);

      return result;
    } catch (error) {
      // Rollback on error
      queryClient.setQueryData(queryKey, previousData);
      throw error;
    }
  };

  /**
   * Smart prefetch based on user behavior
   */
  const smartPrefetch = async <T>(
    queryKey: readonly unknown[],
    queryFn: () => Promise<T>,
    options: {
      condition?: () => boolean;
      delay?: number;
      priority?: 'low' | 'high';
    } = {}
  ) => {
    const { condition = () => true, delay = 0, priority = 'low' } = options;

    if (!condition()) return;

    // Add delay for low priority prefetches
    if (priority === 'low' && delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Check if data is already cached and fresh
    const existingData = queryClient.getQueryData(queryKey);
    if (existingData) {
      const query = queryClient.getQueryCache().find({ queryKey });
      if (query && query.state.dataUpdatedAt) {
        const age = Date.now() - query.state.dataUpdatedAt;
        if (age < 2 * 60 * 1000) { // Skip if data is less than 2 minutes old
          return;
        }
      }
    }

    // Prefetch the data
    await queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  return {
    invalidateByAction,
    batchInvalidate,
    conditionalInvalidate,
    invalidateStaleQueries,
    backgroundRefresh,
    optimisticUpdate,
    smartPrefetch,
  };
}

/**
 * Hook for managing query dependencies
 */
export function useQueryDependencies() {
  const queryClient = useQueryClient();

  /**
   * Add dependency relationship between queries
   */
  const addDependency = (parentKey: readonly unknown[], childKey: readonly unknown[]) => {
    // When parent changes, invalidate child
    queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' &&
          event.query.queryKey.toString() === parentKey.toString()) {
        queryClient.invalidateQueries({ queryKey: childKey });
      }
    });
  };

  /**
   * Remove all dependencies for a query
   */
  const removeDependencies = (queryKey: readonly unknown[]) => {
    // Implementation would depend on maintaining dependency registry
    // For now, we'll just invalidate the query
    queryClient.invalidateQueries({ queryKey });
  };

  return {
    addDependency,
    removeDependencies,
  };
}

/**
 * Performance monitoring for invalidation
 */
export function useInvalidationMetrics() {
  const queryClient = useQueryClient();

  const getInvalidationMetrics = () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    const metrics = {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      cacheSize: cache.getAll().length,
      oldestQuery: Math.min(...queries.map(q => q.state.dataUpdatedAt || Date.now())),
      newestQuery: Math.max(...queries.map(q => q.state.dataUpdatedAt || 0)),
    };

    return metrics;
  };

  const logMetrics = () => {
    const metrics = getInvalidationMetrics();
    console.log('Query Cache Metrics:', metrics);
    return metrics;
  };

  return {
    getInvalidationMetrics,
    logMetrics,
  };
}
