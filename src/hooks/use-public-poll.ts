import { useQuery, useMutation } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import {
  Poll,
  DbQuestionShapeSchema,
  transformDbQuestionToApp,
  validateDbQuestion
} from '@/lib/validation/schemas';
import { z } from 'zod';

/**
 * Public Poll type that doesn't require all the fields of the Poll interface
 */
type PublicPoll = Omit<Poll, 'updatedAt' | 'expiresAt' | 'userId'> & {
  status?: 'draft' | 'active' | 'completed';
  is_public?: boolean;
}

/**
 * Database question type (inferred from Zod schema)
 */
type DbQuestion = z.infer<typeof DbQuestionShapeSchema>;

// Query keys for React Query
export const publicPollKeys = {
  all: ['publicPolls'] as const,
  lists: () => [...publicPollKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...publicPollKeys.lists(), filters] as const,
  details: () => [...publicPollKeys.all, 'detail'] as const,
  detail: (id: string) => [...publicPollKeys.details(), id] as const,
  bySlug: (slug: string) => [...publicPollKeys.all, 'slug', slug] as const,
};

/**
 * Hook for fetching a public poll by slug
 * @param slug The slug of the poll to fetch
 */
export function usePublicPollBySlug(slug: string | undefined) {
  return useQuery<PublicPoll, Error>({
    queryKey: slug ? publicPollKeys.bySlug(slug) : [],
    queryFn: async () => {
      if (!slug) {
        throw new Error('Slug is required');
      }

      // Fetch poll with questions
      const { data, error } = await supabase
        .from('polls')
        .select(`
          *,
          questions (*)
        `)
        .eq('slug', slug)
        .eq('is_public', true)
        .single();

      if (error) {
        console.error('Error fetching public poll:', error);
        throw new Error(error.message);
      }

      if (!data) {
        throw new Error('Poll not found');
      }

      // Sort questions by order and validate
      const sortedQuestions = data.questions
        .map((q: unknown) => {
          try {
            return validateDbQuestion(q);
          } catch (error) {
            console.warn('Invalid question data:', q, error);
            return null;
          }
        })
        .filter((q): q is DbQuestion => q !== null)
        .sort((a, b) => (a.order || 0) - (b.order || 0));

      // Transform to match Poll interface
      return {
        id: data.id,
        title: data.title,
        description: data.description || '',
        createdAt: data.created_at,
        status: data.status || 'active',
        is_public: true,
        slug: data.slug,
        questions: sortedQuestions.map(transformDbQuestionToApp),
        questions_count: sortedQuestions.length,
        responses_count: data.responses_count || 0,
        views_count: data.views || 0,
        // Add required source fields
        source_url: data.source_url || null,
        source_type: data.source_type || null,
        source_filename: data.source_filename || null,
        show_source: data.show_source ?? true,
      };
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: !!slug, // Only run the query if we have a slug
  });
}

/**
 * Hook for fetching a public poll by ID
 * @param id The ID of the poll to fetch
 */
export function usePublicPoll(id: string | undefined) {
  return useQuery<PublicPoll, Error>({
    queryKey: id ? publicPollKeys.detail(id) : [],
    queryFn: async () => {
      if (!id) {
        throw new Error('Poll ID is required');
      }

      // Fetch poll with questions
      const { data, error } = await supabase
        .from('polls')
        .select(`
          *,
          questions (*)
        `)
        .eq('id', id)
        .eq('is_public', true)
        .single();

      if (error) {
        console.error('Error fetching public poll:', error);
        throw new Error(error.message);
      }

      if (!data) {
        throw new Error('Poll not found');
      }

      // Sort questions by order and validate
      const sortedQuestions = data.questions
        .map((q: unknown) => {
          try {
            return validateDbQuestion(q);
          } catch (error) {
            console.warn('Invalid question data:', q, error);
            return null;
          }
        })
        .filter((q): q is DbQuestion => q !== null)
        .sort((a, b) => (a.order || 0) - (b.order || 0));

      // Transform to match Poll interface
      return {
        id: data.id,
        title: data.title,
        description: data.description || '',
        createdAt: data.created_at,
        status: data.status || 'active',
        is_public: true,
        slug: data.slug,
        questions: sortedQuestions.map(transformDbQuestionToApp),
        questions_count: sortedQuestions.length,
        responses_count: data.responses_count || 0,
        views_count: data.views || 0,
        // Add required source fields
        source_url: data.source_url || null,
        source_type: data.source_type || null,
        source_filename: data.source_filename || null,
        show_source: data.show_source ?? true,
      };
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: !!id, // Only run the query if we have an ID
  });
}

/**
 * Hook for submitting a response to a public poll
 */
export function useSubmitPublicPollResponse() {
  return useMutation({
    mutationFn: async ({
      pollId,
      responses,
      respondentInfo
    }: {
      pollId: string;
      responses: Record<string, string | string[]>;
      respondentInfo?: {
        deviceType?: string;
        browser?: string;
        os?: string;
        region?: string;
        timeToComplete?: number;
      };
    }) => {
      // Get anonymous session if available
      const { data: { session } } = await supabase.auth.getSession();

      // Create response record
      const { data: responseData, error: responseError } = await supabase
        .from('responses')
        .insert({
          poll_id: pollId,
          user_id: session?.user?.id, // May be null for anonymous responses
          respondent_info: respondentInfo || {}
        })
        .select('id')
        .single();

      if (responseError) {
        console.error('Error creating response:', responseError);
        throw new Error(responseError.message);
      }

      // Create answer records for each question response
      const answerRecords = Object.entries(responses).map(([questionId, value]) => ({
        response_id: responseData.id,
        question_id: questionId,
        answer_value: value
      }));

      const { error: answersError } = await supabase
        .from('answers')
        .insert(answerRecords);

      if (answersError) {
        console.error('Error creating answers:', answersError);
        throw new Error(answersError.message);
      }

      // Increment view count via RPC if configured
      try {
        await supabase.rpc('increment_poll_views', { poll_id_param: pollId });
      } catch (viewError) {
        // Non-critical error, just log it
        console.warn('Failed to increment view count:', viewError);
      }

      return { success: true, responseId: responseData.id };
    }
  });
}

/**
 * Hook for checking if a poll requires access code
 * @param pollId The ID of the poll to check
 */
export function usePollAccessCheck(pollId: string | undefined) {
  return useQuery<{ requiresCode: boolean; codeHash?: string }, Error>({
    queryKey: pollId ? [...publicPollKeys.detail(pollId), 'access'] : [],
    queryFn: async () => {
      if (!pollId) {
        throw new Error('Poll ID is required');
      }

      const { data, error } = await supabase
        .from('polls')
        .select('access_code_hash')
        .eq('id', pollId)
        .single();

      if (error) {
        console.error('Error checking poll access:', error);
        throw new Error(error.message);
      }

      return {
        requiresCode: !!data.access_code_hash,
        codeHash: data.access_code_hash
      };
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: !!pollId
  });
}
