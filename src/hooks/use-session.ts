import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Session } from '@supabase/supabase-js';
import { useEffect } from 'react';

// Query keys for React Query
export const sessionKeys = {
  all: ['session'] as const,
  current: () => [...sessionKeys.all, 'current'] as const,
};

/**
 * Optimized hook for managing Supabase sessions with React Query
 * Uses stale-while-revalidate strategy to reduce network requests
 */
export function useSession() {
  return useQuery<Session | null, Error>({
    queryKey: sessionKeys.current(),
    queryFn: async () => {
      // First try to get session from memory
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        // Check if session is still valid
        const expiresAt = new Date(session.expires_at || 0);
        if (expiresAt > new Date()) {
          return session;
        }
      }
      
      return null;
    },
    staleTime: 60 * 1000, // 1 minute - balance between performance and freshness
    refetchOnWindowFocus: false, // Don't refetch on window focus unless changed by user action
    retry: false, // Don't retry as auth errors should be handled directly
  });
}

/**
 * Hook for refreshing the Supabase session
 * This is useful when you detect session issues and need to actively refresh
 */
export function useRefreshSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (): Promise<Session | null> => {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error("Failed to refresh session:", error);
        throw new Error(error.message);
      }
      
      return data.session;
    },
    onSuccess: (newSession) => {
      // Update the session in the cache
      queryClient.setQueryData(sessionKeys.current(), newSession);
      
      // Invalidate any queries that may depend on the session
      // Only invalidate if we actually got a new session
      if (newSession) {
        queryClient.invalidateQueries({ queryKey: ['polls'] });
        queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
      }
    },
  });
}

/**
 * Hook that handles session expiration with automatic refresh
 * This can be used in a provider component to keep the session fresh
 */
export function useSessionManager(options: {
  refreshInterval?: number;
} = {}) {
  const { 
    refreshInterval = 10 * 60 * 1000 // 10 minutes
  } = options;
  
  const { data: session } = useSession();
  const { mutate: refreshSession } = useRefreshSession();
  const queryClient = useQueryClient();
  
  // Effect to periodically check and refresh the session
  useEffect(() => {
    if (!session) return;
    
    // Calculate time until session expiration
    const expiresAt = new Date(session.expires_at || 0);
    const timeUntilExpiry = expiresAt.getTime() - Date.now();
    
    // If session is about to expire (within refreshInterval), refresh it
    if (timeUntilExpiry < refreshInterval) {
      refreshSession();
      return;
    }
    
    // Set up a timer to refresh the session before it expires
    const timer = setTimeout(() => {
      refreshSession();
    }, timeUntilExpiry - refreshInterval); // Refresh before expiry
    
    return () => clearTimeout(timer);
  }, [session, refreshSession, refreshInterval, queryClient]);
  
  // Return session info and manual refresh function
  return {
    session,
    isAuthenticated: !!session,
    refreshSession,
  };
}
