/**
 * Optimized use<PERSON>olls hook with React Query integration
 *
 * Provides efficient polling data fetching with intelligent caching
 * and dependency management for optimal performance
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthContext } from '@/components/providers/auth-provider-optimized';
import { supabase } from '@/lib/supabase';
import { Poll } from '@/lib/validation/schemas';
import { useIntelligentInvalidation } from './use-intelligent-invalidation';

// Query keys for consistent caching
export const pollKeys = {
  all: ['polls'] as const,
  lists: () => [...pollKeys.all, 'list'] as const,
  list: (filters: PollFilters) => [...pollKeys.lists(), filters] as const,
  details: () => [...pollKeys.all, 'detail'] as const,
  detail: (id: string) => [...pollKeys.details(), id] as const,
};

export interface PollFilters {
  page?: number;
  pageSize?: number;
  searchQuery?: string;
  statusFilter?: string | null;
  userId?: string;
}

export interface PaginatedPolls {
  data: Poll[];
  totalCount: number;
  page: number;
  pageSize: number;
}

// Optimized poll fetcher
async function fetchPolls(filters: PollFilters): Promise<PaginatedPolls> {
  const {
    page = 1,
    pageSize = 10,
    searchQuery = '',
    statusFilter = null,
    userId,
  } = filters;

  if (!userId) {
    throw new Error('User ID is required to fetch polls');
  }

  console.log(`Fetching polls for user: ${userId}, page: ${page}`);

  const { data, error } = await supabase.rpc('get_polls_with_counts', {
    user_id_param: userId,
    page_number: page,
    page_size: pageSize,
    search_query_param: searchQuery || '',
    status_filter_param: statusFilter || null,
    fetch_all: false,
  });

  if (error) {
    console.error('Error fetching polls:', error);
    throw new Error(`Failed to fetch polls: ${error.message}`);
  }

  if (!data) {
    return {
      data: [],
      totalCount: 0,
      page,
      pageSize,
    };
  }

  const totalCount = data.length > 0 ? data[0].total_count || 0 : 0;

  // Transform data to match Poll interface
  const transformedData: Poll[] = data.map((poll: Record<string, unknown>) => ({
    id: poll.id,
    title: poll.title,
    slug: poll.slug || `poll-${poll.id}`,
    description: poll.description || '',
    createdAt: poll.created_at,
    updatedAt: poll.updated_at,
    expiresAt: poll.expires_at,
    userId: poll.user_id,
    questions: poll.questions || [],
    questions_count: poll.questions_count || 0,
    responses_count: poll.response_count || 0,
    views_count: poll.view_count || 0,
    status: poll.status || 'active',
    is_public: poll.is_public || false,
    is_published: poll.is_published || false,
  }));

  return {
    data: transformedData,
    totalCount,
    page,
    pageSize,
  };
}

/**
 * Main usePolls hook with optimized caching and dependency management
 */
export function usePolls(filters: PollFilters = {}) {
  const { user, isAuthenticated } = useAuthContext();

  const effectiveFilters = {
    ...filters,
    userId: user?.id,
  };

  return useQuery({
    queryKey: pollKeys.list(effectiveFilters),
    queryFn: () => fetchPolls(effectiveFilters),
    enabled: isAuthenticated && !!user?.id,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      if (error?.message?.includes('auth') || error?.message?.includes('permission')) {
        return false;
      }
      return failureCount < 2;
    },
  });
}

/**
 * Hook for fetching a single poll by ID
 */
export function usePoll(pollId: string) {
  const { user, isAuthenticated } = useAuthContext();

  return useQuery({
    queryKey: pollKeys.detail(pollId),
    queryFn: async () => {
      if (!pollId || !user?.id) return null;

      const { data, error } = await supabase
        .from('polls')
        .select('*')
        .eq('id', pollId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching poll:', error);
        throw new Error(`Failed to fetch poll: ${error.message}`);
      }

      return data;
    },
    enabled: isAuthenticated && !!user?.id && !!pollId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
}

/**
 * Mutation for deleting a poll with optimistic updates
 */
export function useDeletePoll() {
  const { user } = useAuthContext();
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async (pollId: string) => {
      const { error } = await supabase
        .from('polls')
        .delete()
        .eq('id', pollId)
        .eq('user_id', user?.id);

      if (error) {
        throw new Error(`Failed to delete poll: ${error.message}`);
      }
    },
    onSuccess: async () => {
      // Use intelligent invalidation
      await invalidateByAction('polls.delete');
    },
  });
}

/**
 * Mutation for creating a new poll
 */
export function useCreatePoll() {
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async (pollData: Partial<Poll>) => {
      const { data, error } = await supabase
        .from('polls')
        .insert([pollData])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create poll: ${error.message}`);
      }

      return data;
    },
    onSuccess: async () => {
      // Use intelligent invalidation
      await invalidateByAction('polls.create');
    },
  });
}

/**
 * Mutation for updating a poll
 */
export function useUpdatePoll() {
  const queryClient = useQueryClient();
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async ({ pollId, updates }: { pollId: string; updates: Partial<Poll> }) => {
      const { data, error } = await supabase
        .from('polls')
        .update(updates)
        .eq('id', pollId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update poll: ${error.message}`);
      }

      return data;
    },
    onSuccess: async (data) => {
      // Update the specific poll in cache
      queryClient.setQueryData(pollKeys.detail(data.id), data);

      // Use intelligent invalidation
      await invalidateByAction('polls.update');
    },
  });
}
