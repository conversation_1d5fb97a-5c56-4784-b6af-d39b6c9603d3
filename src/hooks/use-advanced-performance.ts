/**
 * Advanced Performance Integration Hook
 *
 * Combines intelligent invalidation, prefetching, and memory management
 * into a single comprehensive performance optimization system
 */

import { useEffect, useMemo } from 'react';
import { useIntelligentInvalidation } from './use-intelligent-invalidation';
import { useIntelligentPrefetch } from './use-intelligent-prefetch';
import { useMemoryManagement } from './use-memory-management';
import { useAuthContext } from '@/components/providers/auth-provider-optimized';

interface PerformanceConfig {
  enableIntelligentInvalidation?: boolean;
  enablePrefetching?: boolean;
  enableMemoryManagement?: boolean;
  enableAutoPrefetch?: boolean;
  enableRouteBasedCleanup?: boolean;
  enableBackgroundOptimization?: boolean;
  debug?: boolean;
}

const DEFAULT_CONFIG: PerformanceConfig = {
  enableIntelligentInvalidation: true,
  enablePrefetching: true,
  enableMemoryManagement: true,
  enableAutoPrefetch: true,
  enableRouteBasedCleanup: true,
  enableBackgroundOptimization: true,
  debug: false,
};

/**
 * Master performance optimization hook
 */
export function useAdvancedPerformanceOptimization(config: PerformanceConfig = {}, passedUser?: { id: string } | null) {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);

  // Always call useAuthContext (hooks must be called unconditionally)
  let authContextUser: { id: string } | null = null;
  try {
    const authContext = useAuthContext();
    authContextUser = authContext.user;
  } catch {
    // Context not available (e.g., during provider initialization)
    authContextUser = null;
  }

  // Use passed user or context user
  const user = passedUser !== undefined ? passedUser : authContextUser;

  // Initialize performance modules
  const invalidation = useIntelligentInvalidation();
  const prefetch = useIntelligentPrefetch();
  const memory = useMemoryManagement();

  /**
   * Initialize performance optimization on user authentication
   */
  useEffect(() => {
    if (!user || !finalConfig.enableAutoPrefetch) return;

    const initializeOptimizations = async () => {
      if (finalConfig.debug) {
        console.log('🚀 Initializing advanced performance optimizations for user:', user.id);
      }

      // 1. Prefetch critical user data
      if (finalConfig.enablePrefetching) {
        await prefetch.prefetchUserData({ priority: 'high' });
      }

      // 2. Monitor memory usage
      if (finalConfig.enableMemoryManagement) {
        const metrics = memory.getMemoryMetrics();
        if (finalConfig.debug) {
          console.log('📊 Initial memory metrics:', metrics);
        }
      }

      // 3. Background optimization
      if (finalConfig.enableBackgroundOptimization) {
        setTimeout(() => {
          prefetch.backgroundPrefetch();
        }, 2000);
      }
    };

    initializeOptimizations();
  }, [user, finalConfig, prefetch, memory]);

  /**
   * Auto-cleanup on route changes
   */
  useEffect(() => {
    if (!finalConfig.enableRouteBasedCleanup) return;

    const handleRouteChange = () => {
      memory.routeBasedCleanup(window.location.pathname);
    };

    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, [finalConfig.enableRouteBasedCleanup, memory]);

  /**
   * Periodic optimization tasks
   */
  useEffect(() => {
    if (!finalConfig.enableBackgroundOptimization) return;

    const interval = setInterval(async () => {
      // Memory monitoring
      if (finalConfig.enableMemoryManagement) {
        const metrics = memory.monitorMemoryUsage();

        if (finalConfig.debug && metrics.memoryUsagePercent > 70) {
          console.log('⚠️ High memory usage detected:', metrics);
        }
      }

      // Background prefetch
      if (finalConfig.enablePrefetching && user) {
        await prefetch.backgroundPrefetch();
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [finalConfig, memory, prefetch, user]);

  /**
   * Performance monitoring and reporting
   */
  const getPerformanceReport = () => {
    const memoryMetrics = memory.getMemoryMetrics();
    const recommendations = memory.getCacheOptimizationRecommendations();

    return {
      timestamp: new Date().toISOString(),
      config: finalConfig,
      memory: {
        metrics: memoryMetrics,
        recommendations,
        status: memoryMetrics.memoryUsagePercent > 90 ? 'critical' :
                memoryMetrics.memoryUsagePercent > 70 ? 'warning' : 'good',
      },
      optimizations: {
        intelligentInvalidation: finalConfig.enableIntelligentInvalidation,
        prefetching: finalConfig.enablePrefetching,
        memoryManagement: finalConfig.enableMemoryManagement,
        backgroundOptimization: finalConfig.enableBackgroundOptimization,
      },
    };
  };

  /**
   * Manual performance optimization trigger
   */
  const optimizePerformance = async () => {
    if (finalConfig.debug) {
      console.log('🔧 Manual performance optimization triggered');
    }

    // Clean up memory
    if (finalConfig.enableMemoryManagement) {
      await memory.smartCleanup();
    }

    // Invalidate stale queries
    if (finalConfig.enableIntelligentInvalidation) {
      await invalidation.invalidateStaleQueries();
    }

    // Prefetch critical data
    if (finalConfig.enablePrefetching && user) {
      await prefetch.prefetchUserData({ priority: 'high' });
    }

    if (finalConfig.debug) {
      console.log('✅ Performance optimization completed');
    }
  };

  /**
   * Emergency performance recovery
   */
  const emergencyOptimization = async () => {
    console.warn('🚨 Emergency performance optimization triggered');

    // Aggressive memory cleanup
    await memory.emergencyCleanup();

    // Clear all stale queries
    await invalidation.invalidateStaleQueries(1 * 60 * 1000); // 1 minute threshold

    // Refresh critical data only
    if (user) {
      await prefetch.prefetchUserData({ priority: 'high' });
    }

    console.log('🔄 Emergency optimization completed');
  };

  /**
   * Debug information
   */
  const getDebugInfo = () => {
    return {
      config: finalConfig,
      memoryMetrics: memory.getMemoryMetrics(),
      performanceReport: getPerformanceReport(),
    };
  };

  return {
    // Core functionality
    invalidation,
    prefetch,
    memory,

    // Combined operations
    getPerformanceReport,
    optimizePerformance,
    emergencyOptimization,
    getDebugInfo,

    // Configuration
    config: finalConfig,
  };
}

/**
 * Quick performance optimization for specific scenarios
 */
export function useQuickPerformanceBoost() {
  const { optimizePerformance } = useAdvancedPerformanceOptimization();

  const boostForDashboard = async () => {
    await optimizePerformance();
    // Additional dashboard-specific optimizations could go here
  };

  const boostForPolls = async () => {
    await optimizePerformance();
    // Additional polls-specific optimizations could go here
  };

  return {
    boostForDashboard,
    boostForPolls,
    generalBoost: optimizePerformance,
  };
}

/**
 * Performance monitoring hook for development
 */
export function usePerformanceMonitoring() {
  const { getPerformanceReport, getDebugInfo } = useAdvancedPerformanceOptimization({
    debug: true,
  });

  useEffect(() => {
    // Log performance metrics periodically in development
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        const report = getPerformanceReport();
        if (report.memory.status !== 'good') {
          console.log('📊 Performance Report:', report);
        }
      }, 60000); // Every minute

      return () => clearInterval(interval);
    }
  }, [getPerformanceReport]);

  return {
    getPerformanceReport,
    getDebugInfo,
  };
}

/**
 * Performance optimization for specific components
 */
export function useComponentPerformanceOptimization(componentName: string) {
  const { prefetch, memory } = useAdvancedPerformanceOptimization();

  useEffect(() => {
    // Component-specific optimizations
    console.log(`🔧 Optimizing performance for component: ${componentName}`);

    // Cleanup when component unmounts
    return () => {
      console.log(`🧹 Cleaning up performance optimization for component: ${componentName}`);
    };
  }, [componentName]);

  const optimizeForComponent = async () => {
    // Component-specific optimization logic
    if (componentName.includes('Dashboard')) {
      await prefetch.prefetchDashboard();
    } else if (componentName.includes('Poll')) {
      // Poll-specific optimizations
      const metrics = memory.getMemoryMetrics();
      if (metrics.memoryUsagePercent > 80) {
        await memory.smartCleanup();
      }
    }
  };

  return {
    optimizeForComponent,
  };
}
