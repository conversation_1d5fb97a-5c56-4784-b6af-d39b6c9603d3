import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

// Define the interfaces that were previously imported
interface PollResponse {
  id: string;
  pollId: string;
  submittedAt: string;
  responses: Record<string, string | string[]>;
  respondentInfo: Record<string, string | number | boolean | null>;
  deviceType?: string;
  browser?: string;
  os?: string;
  region?: string;
}

interface PollResponsePayload {
  pollId: string;
  responses: Record<string, string | string[]>;
  deviceType?: string;
  browser?: string;
  os?: string;
  region?: string;
}

// Query keys for React Query
export const pollResponseKeys = {
  all: ['pollResponses'] as const,
  lists: () => [...pollResponseKeys.all, 'list'] as const,
  list: (pollId: string) => [...pollResponseKeys.lists(), pollId] as const,
  userResponded: (pollId: string) => [...pollResponseKeys.all, 'userResponded', pollId] as const,
};

/**
 * Hook for fetching responses for a specific poll
 * @param pollId The ID of the poll to fetch responses for
 */
export function usePollResponses(pollId: string) {
  return useQuery<PollResponse[], Error>({
    queryKey: pollResponseKeys.list(pollId),
    queryFn: async () => {
      const { data: session } = await supabase.auth.getSession();

      if (!session || !session.session) {
        throw new Error('Not authenticated');
      }

      const { data, error } = await supabase
        .from('responses')
        .select(`
          id,
          created_at,
          poll_id,
          user_id,
          respondent_info,
          answers (
            id,
            question_id,
            answer_value
          )
        `)
        .eq('poll_id', pollId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching poll responses:', error);
        throw new Error(error.message);
      }

      // Transform the data to match the PollResponse interface
      const responses: PollResponse[] = data.map(item => ({
        id: item.id,
        pollId: item.poll_id,
        submittedAt: item.created_at,
        responses: item.answers.reduce((acc: Record<string, string | string[]>, answer: { question_id: string; answer_value: string | string[] }) => {
          acc[answer.question_id] = answer.answer_value;
          return acc;
        }, {}),
        respondentInfo: item.respondent_info || {},
        deviceType: item.respondent_info?.deviceType || 'unknown',
        browser: item.respondent_info?.browser || 'unknown',
        os: item.respondent_info?.os || 'unknown',
        region: item.respondent_info?.region
      }));

      return responses;
    },
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Hook for checking if a user has responded to a specific poll
 * @param pollId The ID of the poll to check
 */
export function useHasUserRespondedToPoll(pollId: string) {
  return useQuery<boolean, Error>({
    queryKey: pollResponseKeys.userResponded(pollId),
    queryFn: async () => {
      const { data: session } = await supabase.auth.getSession();

      if (!session?.session?.user) {
        // If not authenticated, consider as not responded
        return false;
      }

      const userId = session.session.user.id;

      // First check local storage for a faster response
      const localStorageKey = `pollgpt_responded_${pollId}`;
      if (typeof window !== 'undefined' && localStorage.getItem(localStorageKey) === 'true') {
        return true;
      }

      // If not found in local storage, check the database
      const { data, error } = await supabase
        .from('responses')
        .select('id')
        .eq('poll_id', pollId)
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error checking user response:', error);
        throw new Error(error.message);
      }

      const hasResponded = !!data;

      // Cache the result in local storage
      if (typeof window !== 'undefined' && hasResponded) {
        localStorage.setItem(localStorageKey, 'true');
      }

      return hasResponded;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for submitting a response to a poll
 */
export function useAddPollResponse() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: PollResponsePayload) => {
      // Get the current user session
      const { data: sessionData } = await supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;

      // Prepare response data
      const responseData = {
        poll_id: payload.pollId,
        user_id: userId, // Use current user ID if available
        respondent_info: {
          deviceType: payload.deviceType,
          browser: payload.browser,
          os: payload.os,
          region: payload.region
        }
      };

      // Insert the response
      const { data: responseRecord, error: responseError } = await supabase
        .from('responses')
        .insert(responseData)
        .select('id')
        .single();

      if (responseError) {
        console.error('Error adding poll response:', responseError);
        throw new Error(responseError.message);
      }

      // Prepare the answers data
      const answersData = Object.entries(payload.responses).map(([questionId, value]) => ({
        response_id: responseRecord.id,
        question_id: questionId,
        answer_value: value
      }));

      // Insert all answers
      const { error: answersError } = await supabase
        .from('answers')
        .insert(answersData);

      if (answersError) {
        console.error('Error adding answer records:', answersError);
        throw new Error(answersError.message);
      }

      // Store a flag in local storage to indicate user has responded
      if (typeof window !== 'undefined') {
        localStorage.setItem(`pollgpt_responded_${payload.pollId}`, 'true');
      }

      return true;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: pollResponseKeys.list(variables.pollId) });
      queryClient.invalidateQueries({ queryKey: pollResponseKeys.userResponded(variables.pollId) });
    },
  });
}

/**
 * Hook for incrementing poll views
 */
export function useIncrementPollViews() {
  return useMutation({
    mutationFn: async (pollId: string) => {
      const { error } = await supabase.rpc('increment_poll_views', { poll_id_param: pollId });

      if (error) {
        console.error('Error incrementing poll views:', error);
        throw new Error(error.message);
      }

      return true;
    },
  });
}
