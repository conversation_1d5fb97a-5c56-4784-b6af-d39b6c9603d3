import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getPollById, updatePoll, deletePoll } from '@/lib/services/polls';
import { Poll } from '@/lib/validation/schemas';

// Query keys for React Query
export const pollKeys = {
  all: ['poll'] as const,
  details: () => [...pollKeys.all, 'detail'] as const,
  detail: (id: string) => [...pollKeys.details(), id] as const,
};

/**
 * Hook for fetching a single poll by ID
 * @param id The ID of the poll to fetch
 */
export function usePoll(id: string | undefined) {
  return useQuery<Poll | null, Error>({
    queryKey: pollKeys.detail(id || ''),
    queryFn: () => {
      if (!id) throw new Error('Poll ID is required');
      return getPollById(id);
    },
    enabled: !!id,
    staleTime: 30 * 1000, // 30 seconds
    retry: (failureCount, error) => {
      // Don't retry for authentication errors or "not found" errors
      if (error.message.includes('Not authenticated') ||
          error.message.includes('not found') ||
          error.message.includes('permission denied')) {
        return false;
      }
      // Retry twice for other errors
      return failureCount < 2;
    },
    refetchOnMount: true,
    refetchOnReconnect: true,
    // Add placeholderData to prevent flashing
    placeholderData: (previousData) => previousData,
  });
}

/**
 * Hook for updating a poll
 */
export function useUpdatePoll() {
  const queryClient = useQueryClient();

  return useMutation<Poll | null, Error, { id: string; updates: Partial<Poll> }>({
    mutationFn: ({ id, updates }) => updatePoll(id, updates),
    onSuccess: (data, variables) => {
      if (data) {
        // Update the poll in the cache
        queryClient.setQueryData(pollKeys.detail(variables.id), data);

        // Invalidate the polls list to reflect the changes
        queryClient.invalidateQueries({ queryKey: ['polls', 'list'] });
      }
    },
  });
}

/**
 * Hook for deleting a poll
 */
export function useDeletePoll() {
  const queryClient = useQueryClient();

  return useMutation<boolean, Error, string>({
    mutationFn: (id) => deletePoll(id),
    onSuccess: (_, id) => {
      // Remove the poll from the cache
      queryClient.removeQueries({ queryKey: pollKeys.detail(id) });

      // Invalidate the polls list to reflect the changes
      queryClient.invalidateQueries({ queryKey: ['polls', 'list'] });
    },
  });
}
