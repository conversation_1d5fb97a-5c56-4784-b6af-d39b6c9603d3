import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Poll, QuestionType } from '@/lib/validation/schemas';
import { v4 as uuidv4 } from 'uuid';
import { pollKeys } from './use-polls';

/**
 * Extended PollQuestion interface for internal use with additional fields
 */
interface ExtendedPollQuestion {
  id: string;
  text: string;
  type: QuestionType;
  options?: Array<{ id: string; text: string; value: string }>;
  required?: boolean;
  order?: number;
}

/**
 * Hook for fetching a poll for editing
 * @param id The ID of the poll to fetch
 */
export function usePollEditor(id: string) {
  return useQuery<Poll, Error>({
    queryKey: pollKeys.detail(id),
    queryFn: async () => {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Not authenticated');
      }

      // Fetch poll with questions
      const { data, error } = await supabase
        .from('polls')
        .select(`
          *,
          questions (*)
        `)
        .eq('id', id)
        .eq('user_id', session.user.id) // Ensure user owns this poll
        .single();

      if (error) {
        console.error('Error fetching poll:', error);
        throw new Error(error.message);
      }

      if (!data) {
        throw new Error('Poll not found');
      }

      // Sort questions by order
      const sortedQuestions = data.questions.sort(
        (a: { order: number }, b: { order: number }) => a.order - b.order
      );

      // Transform to match Poll interface
      const poll: Poll = {
        id: data.id,
        title: data.title,
        slug: data.slug || null,
        description: data.description || null,
        createdAt: data.created_at,
        updatedAt: data.updated_at || data.created_at,
        expiresAt: data.expires_at || null,
        userId: data.user_id,
        status: (data.status as 'draft' | 'active' | 'completed') || 'draft',
        is_public: data.is_public || false,
        access_code: data.access_code || null,
        questions: sortedQuestions.map((q: {
          id: string;
          question_text: string;
          question_type: QuestionType;
          options?: Array<{ id: string; text: string; value: string }>;
          required?: boolean;
          order?: number;
        }) => ({
          id: q.id,
          text: q.question_text,
          type: q.question_type,
          options: q.options || [],
          required: q.required || false,
          order: q.order || 0,
        })),
        questions_count: sortedQuestions.length,
        responses_count: data.responses_count || 0,
        views_count: data.views || 0,
        // Add required source fields
        source_url: data.source_url || null,
        source_type: data.source_type || null,
        source_filename: data.source_filename || null,
        show_source: data.show_source ?? true,
      };

      return poll;
    },
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Extended Poll interface for internal use with additional fields
 */
interface ExtendedPoll extends Omit<Poll, 'status' | 'is_public'> {
  status?: 'draft' | 'active' | 'completed';
  is_public?: boolean;
}

/**
 * Hook for updating a poll
 */
export function useUpdatePollEditor() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ExtendedPoll> }) => {
      // Update the poll
      const { error: pollError } = await supabase
        .from('polls')
        .update({
          title: data.title,
          description: data.description,
          status: data.status,
          is_public: data.is_public
        })
        .eq('id', id);

      if (pollError) {
        console.error('Error updating poll:', pollError);
        throw new Error(pollError.message);
      }

      // If questions are provided, update them
      if (data.questions && data.questions.length > 0) {
        // First delete existing questions
        const { error: deleteError } = await supabase
          .from('questions')
          .delete()
          .eq('poll_id', id);

        if (deleteError) {
          console.error('Error deleting questions:', deleteError);
          throw new Error(deleteError.message);
        }

        // Then insert new questions
        const questionData = data.questions.map((q, index) => ({
          id: q.id || uuidv4(),
          poll_id: id,
          question_text: q.text,
          question_type: q.type,
          options: q.options || [],
          required: q.required || false,
          order: q.order || index + 1,
        }));

        const { error: insertError } = await supabase
          .from('questions')
          .insert(questionData);

        if (insertError) {
          console.error('Error inserting questions:', insertError);
          throw new Error(insertError.message);
        }
      }

      return { id };
    },
    onSuccess: (result) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: pollKeys.detail(result.id) });
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });
}

/**
 * Hook for adding a new question to a poll
 */
export function useAddQuestion() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      pollId,
      question
    }: {
      pollId: string;
      question: ExtendedPollQuestion;
    }) => {
      // Create a new question
      const newQuestion = {
        id: question.id || uuidv4(),
        poll_id: pollId,
        question_text: question.text,
        question_type: question.type,
        options: question.options || [],
        required: question.required || false,
        order: question.order || 999, // High number to place at end by default
      };

      const { data, error } = await supabase
        .from('questions')
        .insert(newQuestion)
        .select()
        .single();

      if (error) {
        console.error('Error adding question:', error);
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: pollKeys.detail(variables.pollId) });
    },
  });
}

/**
 * Hook for deleting a question from a poll
 */
export function useDeleteQuestion() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ pollId, questionId }: { pollId: string; questionId: string }) => {
      const { error } = await supabase
        .from('questions')
        .delete()
        .eq('id', questionId)
        .eq('poll_id', pollId);

      if (error) {
        console.error('Error deleting question:', error);
        throw new Error(error.message);
      }

      return { pollId, questionId };
    },
    onSuccess: (result) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: pollKeys.detail(result.pollId) });
    },
  });
}

/**
 * Hook for running poll simulations
 */
export function useSimulatePoll() {
  return useMutation({
    mutationFn: async ({
      pollId,
      demographic,
      sampleSize
    }: {
      pollId: string;
      demographic: string;
      sampleSize: number;
    }) => {
      const response = await fetch('/api/simulate-poll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollId,
          demographic,
          sampleSize,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to simulate poll');
      }

      return response.json();
    },
  });
}
