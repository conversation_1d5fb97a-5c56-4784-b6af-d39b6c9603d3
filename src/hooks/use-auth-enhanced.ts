/**
 * Optimized Auth Hook with Strategic Method Usage
 * Implements the getUser vs getSession strategy for better performance
 */

import { useCallback, useEffect } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { getUserId, getUserEmail, setUserData, clearUserData, getAuthStrategy } from '@/lib/utils/user-id-manager';
import { Session, User } from '@supabase/supabase-js';

// Create a single client instance for this module
const supabase = createClient();

// Query keys for consistent caching
export const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  userSecure: () => [...authKeys.all, 'user-secure'] as const,
};

/**
 * Strategic session fetcher - uses getSession for performance
 */
async function fetchSession(): Promise<Session | null> {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Session fetch error:', error);
      return null;
    }

    return data.session;
  } catch (error) {
    console.error('Session fetch exception:', error);
    return null;
  }
}

/**
 * Strategic user fetcher - uses getUser for verification
 */
async function fetchUser(): Promise<User | null> {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      console.error('User fetch error:', error);
      return null;
    }

    return data.user;
  } catch (error) {
    console.error('User fetch exception:', error);
    return null;
  }
}

/**
 * Main auth hook with performance optimization
 */
export function useAuth() {
  const queryClient = useQueryClient();

  // Session query - used for general auth state
  const { data: session, isLoading: isSessionLoading, error: sessionError } = useQuery({
    queryKey: authKeys.session(),
    queryFn: fetchSession,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // User query - used for sensitive operations
  const { data: user, isLoading: isUserLoading, error: userError } = useQuery({
    queryKey: authKeys.user(),
    queryFn: fetchUser,
    enabled: !!session, // Only fetch user if we have a session
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Update global user data when session/user changes
  useEffect(() => {
    if (session?.user) {
      setUserData(session.user.id, session.user.email || null);
    } else {
      clearUserData();
    }
  }, [session?.user]);

  // Strategic auth method selector
  const getAuthData = useCallback((operation: 'read' | 'write' | 'sensitive' = 'read') => {
    const strategy = getAuthStrategy(operation);

    if (strategy === 'getUser') {
      return {
        user: user || null,
        isLoading: isUserLoading,
        error: userError,
      };
    }

    return {
      user: session?.user || null,
      isLoading: isSessionLoading,
      error: sessionError,
    };
  }, [session, user, isSessionLoading, isUserLoading, sessionError, userError]);

  // Instant user ID access (sync) - only on client-side
  const userId = typeof window !== 'undefined' ? getUserId() : null;
  const userEmail = typeof window !== 'undefined' ? getUserEmail() : null;

  // Refresh functions
  const refreshSession = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: authKeys.session() });
  }, [queryClient]);

  const refreshUser = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: authKeys.user() });
  }, [queryClient]);

  // Sign out with cleanup
  const signOut = useCallback(async () => {
    try {
      clearUserData();
      await supabase.auth.signOut();
      queryClient.clear();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }, [queryClient]);

  return {
    // Core auth state
    session,
    user: session?.user || null,
    isAuthenticated: !!session?.user,

    // Loading states
    isLoading: isSessionLoading,
    isUserLoading,

    // Error states
    error: sessionError || userError,

    // Instant access (sync)
    userId,
    userEmail,

    // Strategic data access
    getAuthData,

    // Actions
    refreshSession,
    refreshUser,
    signOut,
  };
}

/**
 * Hook for user ID with instant access
 */
export function useUserId() {
  const userId = typeof window !== 'undefined' ? getUserId() : null;
  const userEmail = typeof window !== 'undefined' ? getUserEmail() : null;

  return {
    userId,
    userEmail,
    isAuthenticated: !!userId,
  };
}

/**
 * Hook for sensitive operations that require user verification
 */
export function useSecureAuth() {
  const { data: user, isLoading: isUserLoading, error } = useQuery({
    queryKey: authKeys.userSecure(),
    queryFn: fetchUser,
    staleTime: 2 * 60 * 1000, // 2 minutes for sensitive operations
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return {
    user,
    isLoading: isUserLoading,
    error,
    isAuthenticated: !!user,
  };
}

/**
 * Performance monitoring hook
 */
export function useAuthPerformance() {
  const queryClient = useQueryClient();

  const getPerformanceReport = useCallback(() => {
    const sessionCache = queryClient.getQueryData(authKeys.session());
    const userCache = queryClient.getQueryData(authKeys.user());

    return {
      hasSessionCache: !!sessionCache,
      hasUserCache: !!userCache,
      globalUserId: typeof window !== 'undefined' ? getUserId() : null,
      globalUserEmail: typeof window !== 'undefined' ? getUserEmail() : null,
    };
  }, [queryClient]);

  return {
    getPerformanceReport,
  };
}

/**
 * Enhanced sign-in hook with optimized performance
 */
export function useSignIn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      // Update global user data
      if (data.user) {
        setUserData(data.user.id, data.user.email || null);
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch auth data
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

/**
 * Enhanced sign-up hook with optimized performance
 */
export function useSignUp() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      email,
      password,
      name
    }: {
      email: string;
      password: string;
      name: string;
    }) => {
      // Sign up user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (authError) {
        throw new Error(authError.message);
      }

      // Update global user data
      if (authData.user) {
        setUserData(authData.user.id, authData.user.email || null);

        // Create profile via API endpoint
        try {
          const profileResponse = await fetch('/api/auth/create-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: authData.user.id,
              username: name,
              fullName: name,
              email: email,
            }),
          });

          if (!profileResponse.ok) {
            console.warn('Profile creation failed, but signup succeeded');
          }
        } catch (profileError) {
          console.warn('Profile creation error:', profileError);
        }
      }

      return authData;
    },
    onSuccess: () => {
      // Invalidate and refetch auth data
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

/**
 * Enhanced Google OAuth hook
 */
export function useGoogleAuth() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const redirectTo = typeof window !== 'undefined'
        ? `${window.location.origin}/auth/callback`
        : 'http://localhost:3000/auth/callback';

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch auth data
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}
