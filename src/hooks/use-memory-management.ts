/**
 * Advanced Memory Management and Cleanup for React Query
 *
 * Implements intelligent memory management, cache size monitoring,
 * and automatic cleanup strategies for optimal performance
 */

import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

// Memory management configuration
const MEMORY_CONFIG = {
  maxCacheSize: 100, // Maximum number of queries to keep in cache
  maxAge: 15 * 60 * 1000, // 15 minutes - queries older than this will be cleaned
  cleanupInterval: 5 * 60 * 1000, // 5 minutes - how often to run cleanup
  emergencyCleanupThreshold: 150, // Trigger emergency cleanup at this many queries
  memoryWarningThreshold: 80, // Warn when cache usage exceeds this percentage
  routeChangeCleanupDelay: 1000, // Delay before cleanup after route change
} as const;

// Query priority levels for cleanup decisions
const QUERY_PRIORITIES = {
  critical: ['auth', 'user', 'session'],
  high: ['polls', 'dashboard'],
  medium: ['profile', 'settings'],
  low: ['cache', 'prefetch', 'background'],
} as const;

interface MemoryMetrics {
  totalQueries: number;
  activeQueries: number;
  staleQueries: number;
  errorQueries: number;
  oldestQueryAge: number;
  averageQueryAge: number;
  memoryUsagePercent: number;
  cacheSize: number;
}

interface CleanupOptions {
  preserveCritical?: boolean;
  maxAge?: number;
  targetSize?: number;
  aggressive?: boolean;
}

/**
 * Hook for intelligent memory management and cleanup
 */
export function useMemoryManagement() {
  const queryClient = useQueryClient();
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const routeChangeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRouteRef = useRef<string>('');

  /**
   * Get comprehensive memory metrics
   */
  const getMemoryMetrics = useCallback((): MemoryMetrics => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const now = Date.now();

    const activeQueries = queries.filter(q => q.getObserversCount() > 0);
    const staleQueries = queries.filter(q => q.isStale());
    const errorQueries = queries.filter(q => q.state.status === 'error');

    const queryAges = queries
      .map(q => q.state.dataUpdatedAt || now)
      .filter(age => age > 0)
      .map(age => now - age);

    const oldestQueryAge = queryAges.length > 0 ? Math.max(...queryAges) : 0;
    const averageQueryAge = queryAges.length > 0 ? queryAges.reduce((a, b) => a + b, 0) / queryAges.length : 0;

    return {
      totalQueries: queries.length,
      activeQueries: activeQueries.length,
      staleQueries: staleQueries.length,
      errorQueries: errorQueries.length,
      oldestQueryAge,
      averageQueryAge,
      memoryUsagePercent: (queries.length / MEMORY_CONFIG.maxCacheSize) * 100,
      cacheSize: queries.length,
    };
  }, [queryClient]);

  /**
   * Determine query priority for cleanup decisions
   */
  const getQueryPriority = useCallback((queryKey: readonly unknown[]): keyof typeof QUERY_PRIORITIES => {
    const keyStr = queryKey.join('').toLowerCase();

    if (QUERY_PRIORITIES.critical.some(p => keyStr.includes(p))) return 'critical';
    if (QUERY_PRIORITIES.high.some(p => keyStr.includes(p))) return 'high';
    if (QUERY_PRIORITIES.medium.some(p => keyStr.includes(p))) return 'medium';
    return 'low';
  }, []);

  /**
   * Smart cleanup based on query priority and age
   */
  const smartCleanup = useCallback(async (options: CleanupOptions = {}) => {
    const {
      preserveCritical = true,
      maxAge = MEMORY_CONFIG.maxAge,
      targetSize = MEMORY_CONFIG.maxCacheSize,
      aggressive = false,
    } = options;

    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const now = Date.now();

    // Separate queries by priority
    const queryGroups = {
      critical: [] as typeof queries,
      high: [] as typeof queries,
      medium: [] as typeof queries,
      low: [] as typeof queries,
    };

    queries.forEach(query => {
      const priority = getQueryPriority(query.queryKey);
      queryGroups[priority].push(query);
    });

    // Sort each group by age (oldest first)
    Object.values(queryGroups).forEach(group => {
      group.sort((a, b) => (a.state.dataUpdatedAt || 0) - (b.state.dataUpdatedAt || 0));
    });

    let removedCount = 0;
    const removalReasons: string[] = [];

    // 1. Remove error queries first
    const errorQueries = queries.filter(q => q.state.status === 'error');
    for (const query of errorQueries) {
      await queryClient.removeQueries({ queryKey: query.queryKey });
      removedCount++;
    }
    if (errorQueries.length > 0) {
      removalReasons.push(`${errorQueries.length} error queries`);
    }

    // 2. Remove very old queries
    const oldQueries = queries.filter(q => {
      const age = now - (q.state.dataUpdatedAt || now);
      return age > maxAge;
    });
    for (const query of oldQueries) {
      const priority = getQueryPriority(query.queryKey);
      if (!preserveCritical || priority !== 'critical') {
        await queryClient.removeQueries({ queryKey: query.queryKey });
        removedCount++;
      }
    }
    if (oldQueries.length > 0) {
      removalReasons.push(`${oldQueries.length} old queries`);
    }

    // 3. Remove excess queries by priority (lowest priority first)
    const remainingQueries = queries.length - removedCount;
    if (remainingQueries > targetSize) {
      const toRemove = remainingQueries - targetSize;
      let removed = 0;

      // Remove from low priority first
      const priorities = aggressive ? ['low', 'medium', 'high'] : ['low', 'medium'];

      for (const priority of priorities) {
        const group = queryGroups[priority];
        for (const query of group) {
          if (removed >= toRemove) break;

          // Don't remove active queries unless aggressive
          if (!aggressive && query.getObserversCount() > 0) continue;

          await queryClient.removeQueries({ queryKey: query.queryKey });
          removed++;
          removedCount++;
        }
        if (removed >= toRemove) break;
      }
    }

    const metrics = getMemoryMetrics();
    console.log(`Smart cleanup completed: removed ${removedCount} queries (${removalReasons.join(', ')})`, metrics);

    return { removedCount, metrics };
  }, [queryClient, getQueryPriority, getMemoryMetrics]);

  /**
   * Emergency cleanup when memory usage is too high
   */
  const emergencyCleanup = useCallback(async () => {
    const metrics = getMemoryMetrics();

    if (metrics.totalQueries >= MEMORY_CONFIG.emergencyCleanupThreshold) {
      console.warn('Emergency cleanup triggered - high memory usage detected');

      await smartCleanup({
        preserveCritical: true,
        maxAge: MEMORY_CONFIG.maxAge * 0.5, // More aggressive age threshold
        targetSize: MEMORY_CONFIG.maxCacheSize * 0.7, // More aggressive target size
        aggressive: true,
      });
    }
  }, [getMemoryMetrics, smartCleanup]);

  /**
   * Route-based cleanup
   */
  const routeBasedCleanup = useCallback(async (currentRoute: string) => {
    if (lastRouteRef.current === currentRoute) return;

    const previousRoute = lastRouteRef.current;
    lastRouteRef.current = currentRoute;

    // Clear route-specific caches when navigating away
    if (previousRoute) {
      // Clear poll detail caches when leaving specific poll pages
      if (previousRoute.includes('/polls/') && !currentRoute.includes('/polls/')) {
        await queryClient.removeQueries({
          queryKey: ['polls', 'detail'],
          exact: false
        });
      }

      // Clear dashboard caches when leaving dashboard
      if (previousRoute.includes('/dashboard') && !currentRoute.includes('/dashboard')) {
        await queryClient.removeQueries({
          queryKey: ['dashboard'],
          exact: false
        });
      }
    }

    console.log(`Route-based cleanup completed for navigation: ${previousRoute} -> ${currentRoute}`);
  }, [queryClient]);

  /**
   * Periodic cleanup task
   */
  const schedulePeriodicCleanup = useCallback(() => {
    if (cleanupIntervalRef.current) {
      clearInterval(cleanupIntervalRef.current);
    }

    cleanupIntervalRef.current = setInterval(async () => {
      const metrics = getMemoryMetrics();

      // Check if cleanup is needed
      if (metrics.totalQueries > MEMORY_CONFIG.maxCacheSize * 0.8) {
        await smartCleanup();
      }

      // Check for emergency cleanup
      if (metrics.totalQueries >= MEMORY_CONFIG.emergencyCleanupThreshold) {
        await emergencyCleanup();
      }
    }, MEMORY_CONFIG.cleanupInterval);
  }, [getMemoryMetrics, smartCleanup, emergencyCleanup]);

  /**
   * Memory monitoring and warnings
   */
  const monitorMemoryUsage = useCallback(() => {
    const metrics = getMemoryMetrics();

    if (metrics.memoryUsagePercent > MEMORY_CONFIG.memoryWarningThreshold) {
      console.warn('Memory usage warning:', metrics);

      // Log detailed breakdown
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();
      const breakdown = queries.reduce((acc, query) => {
        const priority = getQueryPriority(query.queryKey);
        acc[priority] = (acc[priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log('Query breakdown by priority:', breakdown);
    }

    return metrics;
  }, [getMemoryMetrics, queryClient, getQueryPriority]);

  /**
   * Cleanup specific query patterns
   */
  const cleanupPattern = useCallback(async (pattern: string) => {
    await queryClient.removeQueries({
      predicate: (query) => {
        const keyStr = query.queryKey.join('').toLowerCase();
        return keyStr.includes(pattern.toLowerCase());
      },
    });

    console.log(`Cleaned up queries matching pattern: ${pattern}`);
  }, [queryClient]);

  /**
   * Get cache optimization recommendations
   */
  const getCacheOptimizationRecommendations = useCallback(() => {
    const metrics = getMemoryMetrics();
    const recommendations: string[] = [];

    if (metrics.staleQueries > metrics.totalQueries * 0.3) {
      recommendations.push('Consider increasing staleTime for frequently accessed queries');
    }

    if (metrics.errorQueries > 0) {
      recommendations.push('Remove error queries from cache');
    }

    if (metrics.oldestQueryAge > 30 * 60 * 1000) {
      recommendations.push('Clean up very old queries');
    }

    if (metrics.memoryUsagePercent > 90) {
      recommendations.push('Urgent: Implement aggressive cleanup to prevent memory issues');
    }

    return recommendations;
  }, [getMemoryMetrics]);

  // Setup periodic cleanup and monitoring
  useEffect(() => {
    schedulePeriodicCleanup();

    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
    };
  }, [schedulePeriodicCleanup]);

  // Cleanup on unmount
  useEffect(() => {
    const routeTimeout = routeChangeTimeoutRef.current;
    const cleanupInterval = cleanupIntervalRef.current;

    return () => {
      if (routeTimeout) {
        clearTimeout(routeTimeout);
      }
      if (cleanupInterval) {
        clearInterval(cleanupInterval);
      }
    };
  }, []);

  return {
    getMemoryMetrics,
    smartCleanup,
    emergencyCleanup,
    routeBasedCleanup,
    monitorMemoryUsage,
    cleanupPattern,
    getCacheOptimizationRecommendations,
  };
}

/**
 * Hook for automatic route-based cleanup
 */
export function useRouteBasedCleanup() {
  const { routeBasedCleanup } = useMemoryManagement();

  useEffect(() => {
    const handleRouteChange = (url: string) => {
      setTimeout(() => {
        routeBasedCleanup(url);
      }, MEMORY_CONFIG.routeChangeCleanupDelay);
    };

    // Listen for route changes
    const currentUrl = window.location.pathname;
    routeBasedCleanup(currentUrl);

    // Note: This is a simplified implementation
    // In a real Next.js app, you'd use router.events or the navigation API
    window.addEventListener('popstate', () => {
      handleRouteChange(window.location.pathname);
    });

    return () => {
      window.removeEventListener('popstate', () => {
        handleRouteChange(window.location.pathname);
      });
    };
  }, [routeBasedCleanup]);
}

/**
 * Hook for memory monitoring dashboard
 */
export function useMemoryMonitoring() {
  const { getMemoryMetrics, getCacheOptimizationRecommendations } = useMemoryManagement();

  const getDetailedMemoryReport = useCallback(() => {
    const metrics = getMemoryMetrics();
    const recommendations = getCacheOptimizationRecommendations();

    return {
      metrics,
      recommendations,
      status: metrics.memoryUsagePercent > 90 ? 'critical' :
              metrics.memoryUsagePercent > 70 ? 'warning' : 'good',
      timestamp: new Date().toISOString(),
    };
  }, [getMemoryMetrics, getCacheOptimizationRecommendations]);

  return {
    getDetailedMemoryReport,
  };
}
