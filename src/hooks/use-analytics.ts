import { useQuery } from '@tanstack/react-query';
import { AnalyticsData, getAnalyticsData } from '@/lib/services/analytics';

// Query keys for React Query
export const analyticsKeys = {
  all: ['analytics'] as const,
  timeRange: (timeRange: '7d' | '30d' | '90d' | '1y') => [...analyticsKeys.all, timeRange] as const,
};

/**
 * Hook for fetching analytics data with React Query
 * @param timeRange The time range for the analytics data
 */
export function useAnalyticsData(timeRange: '7d' | '30d' | '90d' | '1y' = '30d') {
  return useQuery<AnalyticsData, Error>({
    queryKey: analyticsKeys.timeRange(timeRange),
    queryFn: () => getAnalyticsData(timeRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}
