/**
 * Optimized Authentication Hook
 *
 * Provides a single source of truth for authentication state
 * with intelligent caching and minimal network requests
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Session } from '@supabase/supabase-js';
import { useIntelligentInvalidation } from './use-intelligent-invalidation';

// Query keys for consistent caching
export const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
};

// Session validation helper
function isSessionValid(session: Session | null): boolean {
  if (!session) return false;

  const now = Math.floor(Date.now() / 1000);
  const expiresAt = session.expires_at;

  // Session is valid if it expires more than 5 minutes from now
  return expiresAt ? expiresAt > now + 300 : false;
}

// Optimized session fetcher
async function getSession(): Promise<Session | null> {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Session fetch error:', error);
      return null;
    }

    return data.session;
  } catch (error) {
    console.error('Session fetch exception:', error);
    return null;
  }
}

/**
 * Primary authentication hook
 * Provides session, user, and loading states with optimal caching
 */
export function useAuth() {
  // Session query with intelligent caching
  const sessionQuery = useQuery({
    queryKey: authKeys.session(),
    queryFn: getSession,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: (failureCount, error) => {
      // Don't retry auth errors
      if (error?.message?.includes('auth') || error?.message?.includes('JWT')) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Derived user data from session
  const user = sessionQuery.data?.user || null;
  const isAuthenticated = !!sessionQuery.data && isSessionValid(sessionQuery.data);

  // User profile query (only if authenticated)
  const profileQuery = useQuery({
    queryKey: authKeys.profile(),
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Profile fetch error:', error);
        return null;
      }

      return data;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });

  return {
    // Core auth state
    session: sessionQuery.data,
    user,
    profile: profileQuery.data,
    isAuthenticated,

    // Loading states
    isLoading: sessionQuery.isLoading,
    isLoadingProfile: profileQuery.isLoading,

    // Error states
    error: sessionQuery.error,
    profileError: profileQuery.error,

    // Utility functions
    refetch: sessionQuery.refetch,
    refetchProfile: profileQuery.refetch,
  };
}

/**
 * Sign up mutation
 */
export function useSignUp() {
  const queryClient = useQueryClient();
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async ({ email, password, name }: { email: string; password: string; name: string }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Update session cache if user is immediately signed in
      if (data.session) {
        queryClient.setQueryData(authKeys.session(), data.session);
      }
      // Use intelligent invalidation
      await invalidateByAction('auth.signup');
    },
  });
}

/**
 * Sign in mutation with optimized cache updates
 */
export function useSignIn() {
  const queryClient = useQueryClient();
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Update session cache
      queryClient.setQueryData(authKeys.session(), data.session);
      // Use intelligent invalidation
      await invalidateByAction('auth.signin');
    },
  });
}

/**
 * Sign out mutation with cache cleanup
 */
export function useSignOut() {
  const queryClient = useQueryClient();
  const { invalidateByAction } = useIntelligentInvalidation();

  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: async () => {
      // Use intelligent invalidation
      await invalidateByAction('auth.signout');
      // Also clear all auth-related cache
      queryClient.removeQueries({ queryKey: ['auth'] });
    },
  });
}

/**
 * Session refresh mutation for background updates
 */
export function useRefreshSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      return data.session;
    },
    onSuccess: (session) => {
      // Update session cache
      queryClient.setQueryData(authKeys.session(), session);
    },
  });
}

/**
 * Reset password mutation
 */
export function useResetPassword() {
  return useMutation({
    mutationFn: async ({ email }: { email: string }) => {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
    },
  });
}
