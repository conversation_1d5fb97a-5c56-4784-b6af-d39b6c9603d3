/**
 * Unified Auth Hook - Combines best practices from both branches
 * Fixes PKCE issues and race conditions while maintaining simplicity
 */

import { useCallback } from 'react';
import { useQuery, useQueryClient, useMutation, QueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { createOAuthClient } from '@/lib/supabase-oauth';
import { getOAuthRedirectUrl } from '@/lib/utils/redirect-utils';
import { Session } from '@supabase/supabase-js';
import { toast } from 'sonner';

// Interface for user profile
export interface UserProfile {
  id: string;
  email: string | undefined;
  name: string | null;
  avatar_url: string | null;
  created_at: string | null;
}
export const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
};

/**
 * Session fetcher with error handling
 */
async function fetchSession(): Promise<Session | null> {
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Session fetch error:', error);
      return null;
    }
    return data.session;
  } catch (error) {
    console.error('Session fetch exception:', error);
    return null;
  }
}

/**
 * Main auth hook - simplified but robust
 */
export function useAuth() {
  const queryClient = useQueryClient();

  const { data: session, isLoading, error } = useQuery({
    queryKey: authKeys.session(),
    queryFn: fetchSession,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const refreshAuth = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: authKeys.all });
  }, [queryClient]);

  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut();
      queryClient.clear();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }, [queryClient]);

  return {
    user: session?.user || null,
    session,
    isLoading,
    error,
    isAuthenticated: !!session?.user,
    refreshAuth,
    signOut,
  };
}

/**
 * Email sign in hook (preserve existing working implementation)
 */
export function useSignIn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

/**
 * Email sign up hook
 */
export function useSignUp() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      email,
      password,
      name
    }: {
      email: string;
      password: string;
      name: string;
    }) => {
      // Sign up user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (authError) {
        throw new Error(authError.message);
      }

      // Create profile via API endpoint if user was created
      if (authData.user && !authData.user.email_confirmed_at) {
        try {
          const profileResponse = await fetch('/api/auth/create-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: authData.user.id,
              username: name,
              fullName: name,
              email: email,
            }),
          });

          if (!profileResponse.ok) {
            console.warn('Profile creation failed, but signup succeeded');
          }
        } catch (profileError) {
          console.warn('Profile creation error:', profileError);
        }
      }

      return authData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

/**
 * Simplified Google OAuth hook - fixes PKCE issues and preserves redirect intent
 */
export function useGoogleAuth() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      // Use the OAuth-specific client for proper PKCE handling
      const supabaseOAuth = createOAuthClient();

      // Use the proper redirect utility to get the correct URL for current environment
      let redirectToUrl = getOAuthRedirectUrl('/auth/callback');

      // Check if there's a redirect parameter we need to preserve
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const redirectParam = urlParams.get('redirect');

        if (redirectParam) {
          // Include the redirect parameter in the callback URL
          redirectToUrl += `?redirect=${encodeURIComponent(redirectParam)}`;
          console.log('Preserving redirect intent:', redirectParam);
        }
      }

      console.log('Starting Google OAuth with redirect:', redirectToUrl);

      const { data, error } = await supabaseOAuth.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectToUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('Google OAuth error:', error);
        throw new Error(error.message);
      }

      if (!data.url) {
        throw new Error('No redirect URL returned from OAuth');
      }

      return data;
    },
    onSuccess: () => {
      // Set up monitoring for OAuth completion
      if (typeof window !== 'undefined') {
        setupOAuthCompletionMonitoring(queryClient);
      }
    },
    onError: (error) => {
      console.error('Google sign-in error:', error);
      toast.error('Failed to start Google sign-in');
    },
  });
}

/**
 * OAuth completion monitoring - simplified from oauth-enhancements
 */
function setupOAuthCompletionMonitoring(queryClient: QueryClient) {
  if (typeof window === 'undefined') return;

  const checkOAuthCompletion = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const hasOAuthParams = urlParams.has('code') ||
                          urlParams.has('error') ||
                          urlParams.has('oauth_completed') ||
                          document.cookie.includes('oauth_completed=true');

    if (hasOAuthParams) {
      console.log('OAuth redirect detected, refreshing auth state');

      // Immediate refresh
      queryClient.invalidateQueries({ queryKey: authKeys.all });

      // Delayed refresh to ensure Supabase has processed the session
      setTimeout(() => {
        console.log('Delayed auth refresh after OAuth');
        queryClient.invalidateQueries({ queryKey: authKeys.all });
      }, 1500);

      // Clean up URL parameters but preserve important ones
      const newUrl = window.location.pathname;
      if (window.history && window.history.replaceState) {
        window.history.replaceState({}, '', newUrl);
      }

      // Clean up oauth_completed cookie
      if (document.cookie.includes('oauth_completed=true')) {
        document.cookie = 'oauth_completed=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      }
    }
  };

  // Check immediately
  checkOAuthCompletion();

  // Set up listeners for various scenarios
  const cleanup = () => {
    window.removeEventListener('focus', checkOAuthCompletion);
    window.removeEventListener('pageshow', checkOAuthCompletion);
  };

  window.addEventListener('focus', checkOAuthCompletion);
  window.addEventListener('pageshow', checkOAuthCompletion);

  // Also check periodically for a short time after setup
  const periodicCheck = setInterval(checkOAuthCompletion, 2000);
  setTimeout(() => clearInterval(periodicCheck), 10000); // Stop after 10 seconds

  return cleanup;
}

/**
 * Hook for profile management
 */
export function useUserProfile() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async (): Promise<UserProfile | null> => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        throw new Error(error.message);
      }

      return {
        id: data.id,
        email: user.email,
        name: data.name,
        avatar_url: data.avatar_url,
        created_at: data.created_at,
      };
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for updating user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (profile: { name?: string; avatar_url?: string }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(profile)
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      if (user?.id) {
        queryClient.invalidateQueries({
          queryKey: ['profile', user.id]
        });
      }
    },
  });
}
