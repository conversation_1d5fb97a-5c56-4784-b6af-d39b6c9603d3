"use client";

import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { ChartDataPoint } from "@/lib/validation/schemas";

interface OpenQuestionResponse {
  id: number;
  response: string;
}

interface ExportPollQuestion {
  id: string;
  text: string;
  type: string;
  results: ChartDataPoint[] | OpenQuestionResponse[];
  npsData?: ChartDataPoint[];
  npsScore?: number;
}

interface ProcessedPollData {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  completedResponses: number;
  partialResponses: number;
  totalViews: number;
  completionRate: string;
  averageTimeToComplete: string;
  questions: ExportPollQuestion[];
  responsesByDate: Array<{ date: string; count: number }>;
  demographics: {
    devices: ChartDataPoint[];
    regions: ChartDataPoint[];
  };
}

interface ExportButtonProps {
  pollData: ProcessedPollData;
  className?: string;
}

export function ExportButton({ pollData, className = "" }: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);

  // Function to handle exporting results
  const handleExport = async (format: string) => {
    try {
      setIsExporting(true);

      // Prepare data for export
      const exportData = {
        pollTitle: pollData.title,
        pollDescription: pollData.description,
        createdAt: new Date(pollData.createdAt).toLocaleString(),
        totalResponses: pollData.completedResponses,
        completionRate: pollData.completionRate,
        questions: pollData.questions.map((q: ExportPollQuestion) => ({
          questionText: q.text,
          questionType: q.type,
          responses: q.type === 'open'
            ? (q.results as Array<{id: number, response: string}>).map(r => r.response)
            : (q.results as Array<{name: string, value: number}>).map(r =>
                `${r.name}: ${r.value} (${((r.value / pollData.completedResponses) * 100).toFixed(1)}%)`)
        })),
        demographics: {
          devices: pollData.demographics?.devices?.map((d: {name: string, value: number}) =>
            `${d.name}: ${d.value} (${((d.value / pollData.completedResponses) * 100).toFixed(1)}%)`),
          regions: pollData.demographics?.regions?.map((r: {name: string, value: number}) =>
            `${r.name}: ${r.value} (${((r.value / pollData.completedResponses) * 100).toFixed(1)}%)`)
        }
      };

      switch(format) {
        case 'CSV':
          // Create CSV content
          let csvContent = `"Poll: ${exportData.pollTitle}"\n`;
          csvContent += `"Description: ${exportData.pollDescription}"\n`;
          csvContent += `"Created: ${exportData.createdAt}"\n`;
          csvContent += `"Total Responses: ${exportData.totalResponses}"\n\n`;

          // Add questions and responses
          exportData.questions.forEach((q: { questionText: string; questionType: string; responses: string[] }, i: number) => {
            csvContent += `"Question ${i + 1}: ${q.questionText} (${q.questionType})"\n`;
            q.responses.forEach((r: string) => {
              csvContent += `"${r}"\n`;
            });
            csvContent += '\n';
          });

          // Demographics
          if (exportData.demographics?.devices?.length > 0) {
            csvContent += '"Device Breakdown"\n';
            exportData.demographics.devices.forEach((d: string) => {
              csvContent += `"${d}"\n`;
            });
          }

          if (exportData.demographics?.regions?.length > 0) {
            csvContent += '\n"Regional Distribution"\n';
            exportData.demographics.regions.forEach((r: string) => {
              csvContent += `"${r}"\n`;
            });
          }

          // Create download link
          const csvBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const csvUrl = URL.createObjectURL(csvBlob);
          const link = document.createElement('a');
          link.href = csvUrl;
          link.setAttribute('download', `${pollData.title.replace(/\s+/g, '_')}_results.csv`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          toast.success(`Results exported as CSV`);
          break;

        case 'Excel':
          // In a real implementation, would use libraries like xlsx
          toast.info("Excel export will be available in the next update!");
          break;

        case 'PDF':
          // In a real implementation, would use libraries like jspdf
          toast.info("PDF export will be available in the next update!");
          break;
      }
    } catch (err) {
      console.error('Error exporting results:', err);
      toast.error('Failed to export results');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={`bg-[#4285F4] text-white hover:bg-[#3b77db] hover:text-white flex items-center gap-2 ${className}`}
          disabled={isExporting}
        >
          {isExporting ? (
            <div className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Exporting...
            </div>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="7 10 12 15 17 10" />
                <line x1="12" x2="12" y1="15" y2="3" />
              </svg>
              Export Results
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleExport('PDF')} className="flex items-center gap-2 cursor-pointer">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14 2 14 8 20 8" />
            <path d="M9 15v-2h6v2" />
            <path d="M12 13v5" />
            <path d="M9 19h6" />
          </svg>
          Export as PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('CSV')} className="flex items-center gap-2 cursor-pointer">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="8 17 12 21 16 17" />
            <path d="M12 12v9" />
            <path d="M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29" />
          </svg>
          Export as CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('Excel')} className="flex items-center gap-2 cursor-pointer">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14 2 14 8 20 8" />
            <line x1="8" y1="13" x2="16" y2="13" />
            <line x1="8" y1="17" x2="16" y2="17" />
            <line x1="10" y1="9" x2="12" y2="9" />
          </svg>
          Export as Excel
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
