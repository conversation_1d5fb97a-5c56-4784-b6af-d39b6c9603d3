import React from 'react';
import { Button } from '@/components/ui/button';
import { ChartDataPoint } from '@/lib/validation/schemas';

interface ChartAccessibilityProps {
  chartTitle: string;
  chartType: string;
  data: ChartDataPoint[];
  totalResponses: number;
}

const ChartAccessibility: React.FC<ChartAccessibilityProps> = ({
  chartTitle,
  chartType,
  data,
  totalResponses
}) => {
  // Create a text description of the chart data
  const createTextDescription = (): string => {
    if (!data || data.length === 0) {
      return `No data available for ${chartTitle}.`;
    }

    const sortedData = [...data].sort((a, b) => b.value - a.value);
    const total = totalResponses || sortedData.reduce((sum, item) => sum + item.value, 0);

    let description = `${chartTitle} (${chartType}): `;

    if (sortedData.length === 1) {
      description += `100% of responses (${sortedData[0].value} total) selected "${sortedData[0].name}".`;
      return description;
    }

    // Describe the top 3 items or all if less than 3
    const topItems = sortedData.slice(0, Math.min(3, sortedData.length));

    description += `Out of ${total} total responses, `;

    topItems.forEach((item, index) => {
      const percentage = total ? ((item.value / total) * 100).toFixed(1) : '0';

      if (index === 0) {
        description += `"${item.name}" received the most with ${item.value} responses (${percentage}%)`;
      } else if (index === topItems.length - 1 && topItems.length > 1) {
        description += ` and "${item.name}" with ${item.value} responses (${percentage}%)`;
      } else {
        description += `, "${item.name}" with ${item.value} responses (${percentage}%)`;
      }
    });

    // If there are more items beyond the top 3, mention how many more
    if (sortedData.length > 3) {
      const remainingCount = sortedData.length - 3;
      const remainingResponses = sortedData.slice(3).reduce((sum, item) => sum + item.value, 0);
      const remainingPercentage = total ? ((remainingResponses / total) * 100).toFixed(1) : '0';

      description += `. The remaining ${remainingCount} options account for ${remainingResponses} responses (${remainingPercentage}%)`;
    }

    description += '.';
    return description;
  };

  // Create a table representation of the data
  const createDataTable = () => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm border-collapse mt-2">
          <caption className="sr-only">{chartTitle} data table</caption>
          <thead>
            <tr className="bg-muted">
              <th className="p-2 text-left border">Option</th>
              <th className="p-2 text-right border">Responses</th>
              <th className="p-2 text-right border">Percentage</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => {
              const percentage = totalResponses ? ((item.value / totalResponses) * 100).toFixed(1) : '0';

              return (
                <tr key={index} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/50'}>
                  <td className="p-2 border">{item.name}</td>
                  <td className="p-2 text-right border">{item.value}</td>
                  <td className="p-2 text-right border">{percentage}%</td>
                </tr>
              );
            })}
          </tbody>
          <tfoot>
            <tr className="font-medium">
              <td className="p-2 border">Total</td>
              <td className="p-2 text-right border">{totalResponses}</td>
              <td className="p-2 text-right border">100%</td>
            </tr>
          </tfoot>
        </table>
      </div>
    );
  };

  const [showTable, setShowTable] = React.useState(false);

  return (
    <div className="mt-2 space-y-2">
      <div className="sr-only" aria-live="polite">
        {createTextDescription()}
      </div>

      <div className="flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowTable(!showTable)}
          className="text-xs flex items-center gap-1"
        >
          {showTable ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>
              Hide Data Table
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="3" y1="15" x2="21" y2="15"></line><line x1="9" y1="3" x2="9" y2="21"></line><line x1="15" y1="3" x2="15" y2="21"></line></svg>
              Show Data Table
            </>
          )}
        </Button>
      </div>

      {showTable && createDataTable()}
    </div>
  );
};

export default ChartAccessibility;
