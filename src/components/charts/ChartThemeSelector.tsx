"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getAvailableThemes, getCurrentTheme, setChartTheme } from "@/lib/utils/chart-themes";

interface ThemeSelectorProps {
  onThemeChange?: (theme: string) => void;
  className?: string;
}

export function ChartThemeSelector({ onThemeChange, className = "" }: ThemeSelectorProps) {
  const [currentTheme, setCurrentTheme] = useState(getCurrentTheme());
  const themes = getAvailableThemes();

  const handleThemeChange = (themeName: string) => {
    setChartTheme(themeName);
    setCurrentTheme(themeName);

    if (onThemeChange) {
      onThemeChange(themeName);
    }

    // Force re-render of all chart components by dispatching a custom event
    const event = new CustomEvent("chartthemechange", { detail: { theme: themeName } });
    window.dispatchEvent(event);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-xs text-muted-foreground">Chart Theme:</span>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 flex items-center gap-2"
          >
            <span className="w-3 h-3 rounded-full bg-primary"></span>
            {themes.find(t => t.name.toLowerCase() === currentTheme)?.name || "Default"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-2" align="end">
          <div className="space-y-1">
            {themes.map((theme) => (
              <Button
                key={theme.name.toLowerCase()}
                variant={theme.name.toLowerCase() === currentTheme ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start"
                onClick={() => handleThemeChange(theme.name.toLowerCase())}
              >
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 rounded-full bg-primary"></span>
                  <span>{theme.name}</span>
                </div>
              </Button>
            ))}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
