import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export type LoaderVariant = "default" | "spinner" | "dots" | "minimal";
export type LoaderSize = "xs" | "sm" | "md" | "lg" | "xl";

interface LoaderProps {
  /**
   * Visual variant of the loader
   * @default "default"
   */
  variant?: LoaderVariant;

  /**
   * Size of the loader
   * @default "md"
   */
  size?: LoaderSize;

  /**
   * Text to display next to the loader
   */
  text?: string;

  /**
   * Additional className to apply
   */
  className?: string;

  /**
   * Whether to render the loader in a full-page container
   * @default false
   */
  fullPage?: boolean;

  /**
   * Whether to center the loader in its container
   * @default false
   */
  centered?: boolean;
}

const sizeClasses = {
  xs: "w-3 h-3",
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-8 h-8",
  xl: "w-12 h-12",
};

const textSizeClasses = {
  xs: "text-xs",
  sm: "text-sm",
  md: "text-base",
  lg: "text-lg",
  xl: "text-xl",
};

const gapSizeClasses = {
  xs: "gap-1",
  sm: "gap-1.5",
  md: "gap-2",
  lg: "gap-3",
  xl: "gap-4",
};

/**
 * Loader component for displaying loading states throughout the application
 */
export function Loader({
  variant = "default",
  size = "md",
  text,
  className,
  fullPage = false,
  centered = false,
}: LoaderProps) {
  const sizeClass = sizeClasses[size];
  const textSizeClass = textSizeClasses[size];
  const gapSizeClass = gapSizeClasses[size];

  const containerClasses = cn(
    "flex flex-col items-center",
    gapSizeClass,
    {
      "justify-center": centered || fullPage,
      "fixed inset-0 z-50 bg-transparent": fullPage, // Removed background and blur
    },
    className
  );

  const renderLoader = () => {
    switch (variant) {
      case "dots":
        return (
          <div className="flex space-x-1">
            <div className={cn("animate-bounce rounded-full bg-primary", sizeClass)} style={{ animationDelay: "0ms" }} />
            <div className={cn("animate-bounce rounded-full bg-primary", sizeClass)} style={{ animationDelay: "150ms" }} />
            <div className={cn("animate-bounce rounded-full bg-primary", sizeClass)} style={{ animationDelay: "300ms" }} />
          </div>
        );
      case "spinner":
        return (
          <div className={cn("border-4 rounded-full border-muted", sizeClass)}>
            <div className="animate-spin w-full h-full rounded-full border-t-4 border-primary" />
          </div>
        );
      case "minimal":
        return <Loader2 className={cn("animate-spin text-primary", sizeClass)} aria-hidden="true" />;
      case "default":
      default:
        return <Loader2 className={cn("animate-spin text-primary", sizeClass)} aria-hidden="true" />;
    }
  };

  return (
    <div className={containerClasses} role="status" aria-live="polite">
      {renderLoader()}
      {text && <span className={cn("text-muted-foreground text-center mt-2", textSizeClass)}>{text}</span>}
      <span className="sr-only">Loading...</span>
    </div>
  );
}
