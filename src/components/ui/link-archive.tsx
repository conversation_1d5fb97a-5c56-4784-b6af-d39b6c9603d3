// src/components/ui/link-archive.tsx
import React from 'react';
import Link from 'next/link';

/**
 * Link Archive Component creates a network of internal links to help search engines understand
 * the site structure and improve ranking for targeted keywords. This is hidden from users
 * but visible to search engines.
 */
export function LinkArchive() {
  const links = [
    { href: '/', label: 'PollGPT Home' },
    { href: '/free-ai-polls', label: 'Free AI Polls' },
    { href: '/ai-poll-generator', label: 'AI Poll Generator' },
    { href: '/login', label: 'PollGPT Login' },
    { href: '/register', label: 'Create PollGPT Account' },
    { href: '/dashboard/polls', label: 'Polls Dashboard' },
  ];

  return (
    <div className="sr-only" aria-hidden="true">
      <div>
        <div>
          <div>
            <h4>Quick Links</h4>
            <ul>
              {links.map((link) => (
                <li key={link.href}>
                  <Link href={link.href}>{link.label}</Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4>PollGPT Features</h4>
            <ul>
              <li>
                <span>AI Question Generation</span>
              </li>
              <li>
                <span>Survey Analytics</span>
              </li>
              <li>
                <span>Poll GPT Results Dashboard</span>
              </li>
              <li>
                <span>Multilingual Support</span>
              </li>
            </ul>
          </div>
          <div>
            <h4>Why PollGPT</h4>
            <ul>
              <li>
                <span>#1 in AI Survey Creation</span>
              </li>
              <li>
                <span>Free Poll GPT Account</span>
              </li>
              <li>
                <span>Advanced Analytics</span>
              </li>
              <li>
                <span>Trusted by Professionals</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
