import { Loader, LoaderSize } from "./loader";

interface LoadingSpinnerProps {
  className?: string;
  size?: number;
}

// Map numeric sizes to our new size system
const mapSizeToPreset = (size?: number): LoaderSize => {
  if (!size) return "md";
  if (size <= 16) return "xs";
  if (size <= 24) return "sm";
  if (size <= 32) return "md";
  if (size <= 48) return "lg";
  return "xl";
};

export const LoadingSpinner = ({ className, size = 24 }: LoadingSpinnerProps) => {
  return (
    <Loader
      variant="default"
      size={mapSizeToPreset(size)}
      centered={true}
      className={className}
    />
  );
};

export const FullPageLoader = () => {
  return (
    <Loader
      variant="default"
      size="xl"
      fullPage={true}
    />
  );
}
