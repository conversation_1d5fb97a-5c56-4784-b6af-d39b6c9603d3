import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Loader, LoaderSize, LoaderVariant } from "@/components/ui/loader";

export default function LoaderShowcase() {
  const variants: LoaderVariant[] = ["default", "spinner", "dots", "minimal"];
  const sizes: LoaderSize[] = ["xs", "sm", "md", "lg", "xl"];

  return (
    <div className="container mx-auto py-10">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Loading UI Components</h1>
          <p className="text-muted-foreground">
            These are the available loading UI components in the application.
          </p>
        </div>

        <Tabs defaultValue="variants" className="bg-transparent">
          <TabsList className="bg-transparent">
            <TabsTrigger value="variants">Variants</TabsTrigger>
            <TabsTrigger value="sizes">Sizes</TabsTrigger>
            <TabsTrigger value="text">With Text</TabsTrigger>
          </TabsList>

          <TabsContent value="variants" className="bg-transparent">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6 bg-transparent">
              {variants.map((variant) => (
                <Card key={variant} className="bg-transparent">
                  <CardHeader>
                    <CardTitle className="capitalize">{variant}</CardTitle>
                    <CardDescription>
                      <code>variant=&quot;{variant}&quot;</code>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex justify-center items-center min-h-[100px] bg-transparent">
                    <Loader variant={variant} size="md" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="sizes" className="bg-transparent">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mt-6 bg-transparent">
              {sizes.map((size) => (
                <Card key={size} className="bg-transparent">
                  <CardHeader>
                    <CardTitle className="capitalize">{size}</CardTitle>
                    <CardDescription>
                      <code>size=&quot;{size}&quot;</code>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex justify-center items-center min-h-[100px] bg-transparent">
                    <Loader variant="default" size={size} />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="text" className="bg-transparent">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6 bg-transparent">
              {variants.map((variant) => (
                <Card key={variant} className="bg-transparent">
                  <CardHeader>
                    <CardTitle className="capitalize">{variant} with text</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col space-y-6 bg-transparent">
                    {sizes.map((size) => (
                      <div key={`${variant}-${size}`} className="border p-4 rounded-md bg-transparent">
                        <Loader
                          variant={variant}
                          size={size}
                          text={`Loading ${size} size...`}
                        />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <Card className="bg-transparent">
          <CardHeader>
            <CardTitle>Full Page Loader</CardTitle>
            <CardDescription>
              Example of a full-page loading state. Click the button to preview.
            </CardDescription>
          </CardHeader>
          <CardContent className="bg-transparent">
            <code>
              {'<Loader fullPage={true} size="lg" text="Loading application..." />'}
            </code>
          </CardContent>
          <CardFooter>
            <p className="text-sm text-muted-foreground">
              Use this sparingly for initial application load or transitions between major views.
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
