"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Shield, Users, TrendingUp, Clock, CheckCircle, BookOpen, DollarSign, Monitor } from "lucide-react";

export function AiSimulationShowcase() {
  return (
    <div className="w-full py-12 md:py-16 lg:py-24 relative z-10 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50/30 via-white to-amber-50/30 dark:from-slate-950/50 dark:via-slate-900/30 dark:to-slate-950/50"></div>
      <div className="absolute top-0 left-1/4 w-48 h-48 md:w-72 md:h-72 bg-orange-400/8 dark:bg-orange-500/12 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-64 h-64 md:w-96 md:h-96 bg-amber-400/6 dark:bg-amber-500/10 rounded-full blur-3xl"></div>

      <div className="container px-4 md:px-6 mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-12 md:mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-600 to-amber-600 text-gray-100 px-3 py-1.5 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Shield className="w-3 h-3 md:w-4 md:h-4" />
            Enterprise-Grade AI Simulation
          </div>
          <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight mb-4 md:mb-6">
            Predict Audience Response with{" "}
            <span className="bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
              Advanced AI Simulation
            </span>
          </h2>
          <p className="text-base md:text-lg lg:text-xl text-muted-foreground max-w-2xl md:max-w-3xl mx-auto leading-relaxed px-4 md:px-0">
            Test your polls before they go live with our cutting-edge AI simulation engine.
            Get realistic demographic breakdowns, predict response patterns, and optimize your strategy
            with enterprise-grade accuracy.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center mb-12 md:mb-16">
          {/* Interactive Simulation Demo */}
          <div className="relative order-2 lg:order-1">
            <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm rounded-xl md:rounded-2xl shadow-xl md:shadow-2xl border border-border/50 dark:border-slate-700/50 overflow-hidden">
              {/* Demo Header */}
              <div className="bg-gradient-to-r from-orange-600 to-amber-600 p-3 md:p-4 text-white">
                <div className="flex items-center gap-2 md:gap-3">
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-white/30 rounded-full"></div>
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-white/30 rounded-full"></div>
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-white/30 rounded-full"></div>
                  <span className="ml-auto text-xs md:text-sm font-medium text-white">AI Simulation Dashboard</span>
                </div>
              </div>

              {/* Demo Content */}
              <div className="p-4 md:p-6 space-y-4 md:space-y-6 bg-gradient-to-br from-white to-slate-50/50 dark:from-slate-900 dark:to-slate-800/50">
                <div className="space-y-2 md:space-y-3">
                  <h4 className="font-semibold text-base md:text-lg text-foreground">Which work arrangement do you prefer?</h4>
                  <div className="flex flex-wrap gap-1.5 md:gap-2">
                    <span className="px-2 py-1 md:px-3 md:py-1 bg-slate-100 dark:bg-slate-700/50 text-slate-700 dark:text-slate-200 rounded-full text-xs md:text-sm border border-slate-200 dark:border-slate-600/50">Remote Work</span>
                    <span className="px-2 py-1 md:px-3 md:py-1 bg-slate-100 dark:bg-slate-700/50 text-slate-700 dark:text-slate-200 rounded-full text-xs md:text-sm border border-slate-200 dark:border-slate-600/50">Hybrid</span>
                    <span className="px-2 py-1 md:px-3 md:py-1 bg-slate-100 dark:bg-slate-700/50 text-slate-700 dark:text-slate-200 rounded-full text-xs md:text-sm border border-slate-200 dark:border-slate-600/50">Office-based</span>
                  </div>
                </div>

                {/* Demographic Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                  <div className="space-y-2">
                    <label className="text-xs md:text-sm font-medium text-foreground">Target Demographics</label>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs md:text-sm text-foreground">
                        <div className="w-2 h-2 md:w-3 md:h-3 bg-orange-500 rounded"></div>
                        <span>Gen Z (18-25)</span>
                        <span className="ml-auto text-slate-600 dark:text-slate-400">25%</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs md:text-sm text-foreground">
                        <div className="w-2 h-2 md:w-3 md:h-3 bg-emerald-600 rounded"></div>
                        <span>Millennials (26-41)</span>
                        <span className="ml-auto text-slate-600 dark:text-slate-400">45%</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs md:text-sm text-foreground">
                        <div className="w-2 h-2 md:w-3 md:h-3 bg-amber-600 rounded"></div>
                        <span>Gen X (42-57)</span>
                        <span className="ml-auto text-slate-600 dark:text-slate-400">30%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs md:text-sm font-medium text-foreground">Confidence Score</label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-slate-200 dark:bg-slate-700/70 rounded-full h-1.5 md:h-2">
                        <div className="bg-emerald-500 dark:bg-emerald-400 h-1.5 md:h-2 rounded-full" style={{width: '87%'}}></div>
                      </div>
                      <span className="text-xs md:text-sm font-medium text-emerald-600 dark:text-emerald-400">87%</span>
                    </div>
                    <p className="text-xs text-slate-600 dark:text-slate-400">Based on 15+ data sources</p>
                  </div>
                </div>

                {/* Results Preview */}
                <div className="space-y-2 md:space-y-3">
                  <h5 className="font-medium text-sm md:text-base text-foreground">Predicted Results</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs md:text-sm text-foreground">
                      <span>Remote Work</span>
                      <span className="font-medium">52%</span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700/70 rounded-full h-1.5 md:h-2">
                      <div className="bg-emerald-500 dark:bg-emerald-400 h-1.5 md:h-2 rounded-full" style={{width: '52%'}}></div>
                    </div>

                    <div className="flex items-center justify-between text-xs md:text-sm text-foreground">
                      <span>Hybrid</span>
                      <span className="font-medium">31%</span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700/70 rounded-full h-1.5 md:h-2">
                      <div className="bg-emerald-500 dark:bg-emerald-400 h-1.5 md:h-2 rounded-full" style={{width: '31%'}}></div>
                    </div>

                    <div className="flex items-center justify-between text-xs md:text-sm text-foreground">
                      <span>Office-based</span>
                      <span className="font-medium">17%</span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700/70 rounded-full h-1.5 md:h-2">
                      <div className="bg-amber-600 dark:bg-amber-500 h-1.5 md:h-2 rounded-full" style={{width: '17%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating indicators */}
            <div className="absolute -top-2 -right-2 md:-top-4 md:-right-4 bg-emerald-500 text-white p-2 md:p-3 rounded-full shadow-lg">
              <CheckCircle className="w-4 h-4 md:w-5 md:h-5" />
            </div>
            <div className="absolute -bottom-2 -left-2 md:-bottom-4 md:-left-4 bg-white dark:bg-card p-2 md:p-3 rounded-full shadow-lg border border-border/50">
              <Shield className="w-4 h-4 md:w-5 md:h-5 text-orange-600 dark:text-orange-400" />
            </div>
          </div>

          {/* Value Propositions */}
          <div className="space-y-6 md:space-y-8 order-1 lg:order-2">
            <div className="space-y-4 md:space-y-6">
              <div className="flex items-start gap-3 md:gap-4">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-5 h-5 md:w-6 md:h-6 text-orange-600" />
                </div>
                <div>
                  <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2">Multi-Demographic Intelligence</h4>
                  <p className="text-sm md:text-base text-muted-foreground">Simulate responses across 15+ demographic segments including age groups, professional backgrounds, education levels, and geographic regions with 85%+ accuracy.</p>
                </div>
              </div>

              <div className="flex items-start gap-3 md:gap-4">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <TrendingUp className="w-5 h-5 md:w-6 md:h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2">Predictive Analytics</h4>
                  <p className="text-sm md:text-base text-muted-foreground">Identify potential bias, predict response patterns, and discover demographic polarization before launching your actual poll. Save time and budget with data-driven insights.</p>
                </div>
              </div>

              <div className="flex items-start gap-3 md:gap-4">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Clock className="w-5 h-5 md:w-6 md:h-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2">Instant Results</h4>
                  <p className="text-sm md:text-base text-muted-foreground">Get comprehensive simulation results in under 5 minutes. No waiting weeks for real responses - validate your poll strategy immediately and iterate faster.</p>
                </div>
              </div>

              <div className="flex items-start gap-3 md:gap-4">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-stone-100 dark:bg-stone-800/50 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-5 h-5 md:w-6 md:h-6 text-stone-600 dark:text-stone-400" />
                </div>
                <div>
                  <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2">Enterprise Security</h4>
                  <p className="text-sm md:text-base text-muted-foreground">GDPR compliant, SOC 2 ready, and designed for Fortune 500 companies. Your simulation data is encrypted, anonymized, and never stored permanently.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Business Impact Metrics */}
        <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm rounded-xl md:rounded-2xl border border-border/50 dark:border-slate-700/50 p-6 md:p-8 mb-12 md:mb-16 shadow-lg dark:shadow-slate-900/50">
          <div className="text-center mb-6 md:mb-8">
            <h3 className="text-xl md:text-2xl font-bold mb-2 text-foreground">Proven Business Impact</h3>
            <p className="text-sm md:text-base text-muted-foreground">Real results from enterprise customers using AI simulation</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-orange-600 dark:text-orange-300 mb-1 md:mb-2">70%</div>
              <div className="text-xs md:text-sm text-muted-foreground">Faster Decision Making</div>
              <div className="text-xs text-muted-foreground mt-1 hidden md:block">vs traditional polling</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-emerald-600 dark:text-emerald-300 mb-1 md:mb-2">85%</div>
              <div className="text-xs md:text-sm text-muted-foreground">Prediction Accuracy</div>
              <div className="text-xs text-muted-foreground mt-1 hidden md:block">compared to real results</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-amber-600 dark:text-amber-300 mb-1 md:mb-2">60%</div>
              <div className="text-xs md:text-sm text-muted-foreground">Cost Reduction</div>
              <div className="text-xs text-muted-foreground mt-1 hidden md:block">in research budgets</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-stone-600 dark:text-stone-300 mb-1 md:mb-2">15+</div>
              <div className="text-xs md:text-sm text-muted-foreground">Demographics</div>
              <div className="text-xs text-muted-foreground mt-1 hidden md:block">simultaneously analyzed</div>
            </div>
          </div>
        </div>

        {/* Use Cases */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 mb-12 md:mb-16">
          <div className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm rounded-lg md:rounded-xl p-4 md:p-6 border border-border/50 dark:border-slate-700/50 hover:shadow-lg dark:hover:shadow-slate-900/50 hover:border-orange-200 dark:hover:border-orange-800/50 transition-all duration-300">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mb-3 md:mb-4">
              <BookOpen className="w-5 h-5 md:w-6 md:h-6 text-orange-600 dark:text-orange-300" />
            </div>
            <h4 className="text-base md:text-lg font-semibold mb-2 md:mb-3">Market Research</h4>
            <p className="text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">Test product concepts, pricing strategies, and brand positioning across target demographics before expensive market research campaigns.</p>
            <div className="text-xs text-orange-600 dark:text-orange-300 font-medium">Perfect for: Product managers, Brand strategists</div>
          </div>

          <div className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm rounded-lg md:rounded-xl p-4 md:p-6 border border-border/50 dark:border-slate-700/50 hover:shadow-lg dark:hover:shadow-slate-900/50 hover:border-emerald-200 dark:hover:border-emerald-800/50 transition-all duration-300">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mb-3 md:mb-4">
              <DollarSign className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 dark:text-emerald-300" />
            </div>
            <h4 className="text-base md:text-lg font-semibold mb-2 md:mb-3">HR & Employee Engagement</h4>
            <p className="text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">Predict employee sentiment on policy changes, compensation adjustments, and workplace initiatives across different departments and seniority levels.</p>
            <div className="text-xs text-emerald-600 dark:text-emerald-300 font-medium">Perfect for: HR directors, C-suite executives</div>
          </div>

          <div className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm rounded-lg md:rounded-xl p-4 md:p-6 border border-border/50 dark:border-slate-700/50 hover:shadow-lg dark:hover:shadow-slate-900/50 hover:border-amber-200 dark:hover:border-amber-800/50 transition-all duration-300">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center mb-3 md:mb-4">
              <Monitor className="w-5 h-5 md:w-6 md:h-6 text-amber-600 dark:text-amber-300" />
            </div>
            <h4 className="text-base md:text-lg font-semibold mb-2 md:mb-3">Public Policy & Government</h4>
            <p className="text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">Simulate public opinion on policy proposals, budget allocations, and civic initiatives across diverse constituent groups and geographic regions.</p>
            <div className="text-xs text-amber-600 dark:text-amber-300 font-medium">Perfect for: Policy analysts, Government officials</div>
          </div>
        </div>

        {/* Enterprise CTA */}
        <div className="mt-16 md:mt-24 lg:mt-32 max-w-4xl mx-auto">
          <div className="bg-primary rounded-xl md:rounded-2xl p-6 md:p-8 lg:p-12 text-center text-white">
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold">
              Ready to Transform Your Decision-Making?
            </h3>
            <p className="text-sm md:text-lg mb-6 md:mb-8 opacity-90 max-w-xl md:max-w-2xl mx-auto">
              Join leading enterprises who trust PollGPT&apos;s AI simulation to validate strategies,
              reduce risk, and accelerate innovation cycles.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center">
              <Button asChild size="sm" className="bg-white text-orange-600 hover:bg-gray-50 w-full sm:w-auto sm:min-w-40 md:min-w-48 md:h-11">
                <Link href="/register">
                  Start Free Enterprise Trial
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm" className="border-white bg-amber-800 text-gray-300 hover:bg-white/10 w-full sm:w-auto sm:min-w-40 md:min-w-48 md:h-11">
                <Link href="mailto:<EMAIL>">
                  Book Enterprise Demo
                </Link>
              </Button>
            </div>
            <p className="text-xs md:text-sm opacity-75 mt-3 md:mt-4">
              No credit card required • Full feature access • Enterprise support included
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
