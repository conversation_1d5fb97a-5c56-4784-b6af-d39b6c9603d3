'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { cleanupAuthStorage } from "@/lib/utils/session-cleanup";
import { Loader } from "@/components/ui/loader";
import { useRouter } from 'next/navigation';

interface SessionRecoveryButtonProps {
  variant?: 'default' | 'link' | 'destructive' | 'outline' | 'secondary' | 'ghost';
  children?: React.ReactNode;
  className?: string;
}

export function SessionRecoveryButton({
  variant = 'secondary',
  children = 'Fix Login Issues',
  className = '',
}: SessionRecoveryButtonProps) {
  const [status, setStatus] = useState<'idle' | 'cleaning' | 'success' | 'error'>('idle');
  const router = useRouter();

  const handleCleanup = async () => {
    try {
      setStatus('cleaning');
      const result = await cleanupAuthStorage();

      if (result.success) {
        setStatus('success');
        // Redirect after a brief delay
        setTimeout(() => {
          router.push('/login?recovery=true');
        }, 1500);
      } else {
        setStatus('error');
        // Reset after showing error
        setTimeout(() => {
          setStatus('idle');
        }, 3000);
      }
    } catch (error) {
      console.error('Error during session cleanup:', error);
      setStatus('error');
      // Reset after showing error
      setTimeout(() => {
        setStatus('idle');
      }, 3000);
    }
  };

  // Determine button text based on status
  const buttonText = () => {
    switch (status) {
      case 'cleaning':
        return <><Loader size="xs" variant="minimal" className="mr-2" /> Fixing...</>;
      case 'success':
        return 'Fixed! Redirecting...';
      case 'error':
        return 'Error occurred';
      default:
        return children;
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleCleanup}
      disabled={status !== 'idle'}
      className={className}
    >
      {buttonText()}
    </Button>
  );
}
