"use client";

import { useState } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export function PollCreationDemo() {
  const [topic, setTopic] = useState("Remote Work Preferences");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [progress, setProgress] = useState(0);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [aiThoughts, setAiThoughts] = useState<string[]>([]);
  const [currentThoughtIndex, setCurrentThoughtIndex] = useState(0);

  const exampleTopics = [
    "Remote Work Preferences",
    "Product Feedback",
    "Customer Experience",
    "Team Collaboration"
  ];

  // Handle generating poll questions with AI
  const handleGeneratePoll = () => {
    if (!topic.trim()) {
      setTopic(exampleTopics[Math.floor(Math.random() * exampleTopics.length)]);
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setShowResults(false);
    setCurrentThoughtIndex(0);

    // AI thinking process simulation
    const thoughts = [
      "Analyzing topic context and relevance...",
      `Researching best practices for "${topic}" polls...`,
      "Identifying potential biases to avoid...",
      "Formulating neutral, balanced questions...",
      "Optimizing question sequence for better response rates..."
    ];

    setAiThoughts(thoughts);

    // AI suggestions based on topic
    let suggestions: string[] = [];
    const lowercaseTopic = topic.toLowerCase();

    if (lowercaseTopic.includes("remote work")) {
      suggestions = [
        "How many days per week do you prefer to work remotely?",
        "What is your biggest challenge when working remotely?",
        "Which communication tools do you find most effective for remote collaboration?",
        "How has remote work affected your work-life balance?",
        "What would improve your remote work experience the most?"
      ];
    } else if (lowercaseTopic.includes("product")) {
      suggestions = [
        "How likely are you to recommend this product to a friend or colleague?",
        "Which features do you find most valuable?",
        "What improvements would make this product better for you?",
        "How often do you use this product?",
        "How satisfied are you with the customer support?"
      ];
    } else if (lowercaseTopic.includes("customer")) {
      suggestions = [
        "How would you rate your overall experience with our service?",
        "What was the main reason for choosing our product/service?",
        "How easy was it to find what you were looking for?",
        "Did our staff/support team meet your expectations?",
        "What one thing could we improve to better serve you?"
      ];
    } else if (lowercaseTopic.includes("team") || lowercaseTopic.includes("collaboration")) {
      suggestions = [
        "How effectively does your team communicate?",
        "What tools or processes help you collaborate most effectively?",
        "How often do you feel your ideas are valued by the team?",
        "What is the biggest barrier to productive collaboration in your team?",
        "How could meetings be improved to enhance team collaboration?"
      ];
    } else {
      suggestions = [
        `What is your main opinion regarding ${topic}?`,
        `Which aspects of ${topic} are most important to you?`,
        `How would you rate your current satisfaction with ${topic}?`,
        `What changes would you like to see related to ${topic}?`,
        `How has ${topic} impacted your daily life or work?`
      ];
    }

    setAiSuggestions(suggestions);

    // Simulate AI thinking and generation process
    const thoughtInterval = setInterval(() => {
      setCurrentThoughtIndex(prev => {
        if (prev < thoughts.length - 1) {
          return prev + 1;
        }
        clearInterval(thoughtInterval);
        return prev;
      });
    }, 1500);

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 2;
        if (newProgress >= 100) {
          clearInterval(progressInterval);
          setTimeout(() => {
            setIsGenerating(false);
            setShowResults(true);
          }, 500);
          return 100;
        }
        return newProgress;
      });
    }, 100);

    return () => {
      clearInterval(progressInterval);
      clearInterval(thoughtInterval);
    };
  };

  const handleTryAgain = () => {
    setTopic("");
    setShowResults(false);
  };

  return (
    <Card className="w-full max-w-3xl border shadow-xl bg-gradient-to-br from-background to-muted/50">
      <CardContent className="p-6">
        <div className="space-y-6">
          {!isGenerating && !showResults ? (
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-left">Try Our AI Poll Generator</h3>
                <p className="text-muted-foreground text-left">
                  Select one of the topics above and our AI will instantly create engaging, unbiased poll questions.
                </p>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 pt-2 mb-3">
                {exampleTopics.map((exampleTopic) => (
                  <Button
                    key={exampleTopic}
                    variant="outline"
                    size="sm"
                    onClick={() => setTopic(exampleTopic)}
                    className={`text-xs h-auto py-1 px-2 ${
                      topic === exampleTopic
                        ? "bg-primary/10 border-primary"
                        : "border-primary/20 hover:border-primary/50"
                    }`}
                  >
                    {exampleTopic}
                  </Button>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Input
                  placeholder="Enter your own topic or use the selected one above..."
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  className="text-lg flex-1"
                />
                <Button
                  onClick={handleGeneratePoll}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground sm:px-8"
                  disabled={!topic.trim()}
                >
                  Generate with AI
                </Button>
              </div>
            </div>
          ) : isGenerating ? (
            <div className="space-y-8">
              <div className="space-y-2 text-left">
                <h3 className="text-2xl font-bold">Creating &quot;{topic}&quot; Poll</h3>
                <p className="text-muted-foreground">
                  Our AI is analyzing your topic and crafting perfect questions.
                </p>
              </div>

              <div className="space-y-6 py-4">
                {/* AI Thinking Animation */}
                <div className="bg-muted/30 border rounded-lg p-4 min-h-[120px] relative overflow-hidden">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="bg-primary/20 p-1 rounded-md">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M12 2a4 4 0 0 1 4 4c0 1.95-1.4 3.58-3.25 3.93L13 10h3.8l1.61-2.15c.34-.44.26-1.07-.18-1.41a1 1 0 0 0-1.41.18l-.83 1.1a1 1 0 0 1-1.8-.6l.2-1.2c.07-.47-.28-.9-.75-.97a1 1 0 0 0-1.13.77l-.39 1.98-2.12 2.12-2-2.67 1.08-1.08c.87-.87.87-2.28 0-3.15-.87-.87-2.28-.87-3.15 0l-3.08 3.08c-.87.87-.87 2.28 0 3.15l3.5 3.5"></path>
                        <path d="M22 17v-4c0-8-5-12-10-12S2 5 2 13v4"></path>
                        <path d="M18 21a4 4 0 0 1-4-4c0-1.95 1.4-3.58 3.25-3.93L17 13h-3.8l-1.61 2.15c-.34.44-.26 1.07.18 1.41.44.34 1.07.26 1.41-.18l.83-1.1a1 1 0 0 1 1.8.6l-.2 1.2c-.07.47.28.9.75.97a1 1 0 0 0 1.13-.77l.4-1.98 2.11-2.12 2 2.67-1.08 1.08c-.87.87-.87 2.28 0 3.15.87.87 2.28.87 3.15 0l3.08-3.08c.87-.87.87-2.28 0-3.15l-3.5-3.5"></path>
                        <path d="M2 17v4c0 8 5 12 10 12s10-4 10-12v-4"></path>
                      </svg>
                    </div>
                    <span className="font-semibold">PollGPT Thinking</span>
                  </div>
                  {aiThoughts.slice(0, currentThoughtIndex + 1).map((thought, index) => (
                    <div
                      key={index}
                      className={`text-sm py-1 text-left ${index === currentThoughtIndex ? 'animate-pulse' : ''}`}
                    >
                      {thought}
                    </div>
                  ))}
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Analyzing topic</span>
                    <span>Creating questions</span>
                    <span>Optimizing</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div className="text-left">
                  <h3 className="text-2xl font-bold">AI-Generated Poll</h3>
                  <p className="text-muted-foreground text-sm">Topic: {topic}</p>
                </div>
                <Button variant="outline" size="sm" onClick={handleTryAgain}>
                  Try Another Topic
                </Button>
              </div>

              <div className="border rounded-lg p-5 space-y-6 bg-card shadow-sm">
                {aiSuggestions.map((question, index) => (
                  <div key={index} className="space-y-4">
                    <div className="flex gap-3 items-start">
                      <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center shrink-0 mt-0.5">
                        {index + 1}
                      </div>
                      <h4 className="font-medium flex-1 text-left">{question}</h4>
                    </div>

                    {index < 2 && (
                      <div className="ml-9 space-y-2">
                        {index === 0 ? (
                          // Multiple choice for first question
                          <RadioGroup defaultValue="option1" className="space-y-2">
                            {["1-2 days", "3 days", "4 days", "5+ days"].map((option, i) => (
                              <div key={i} className="flex items-center space-x-2">
                                <RadioGroupItem value={`option${i+1}`} id={`option-${index}-${i}`} />
                                <Label htmlFor={`option-${index}-${i}`} className="text-left">{option}</Label>
                              </div>
                            ))}
                          </RadioGroup>
                        ) : (
                          // Rating scale for second question
                          <div className="flex justify-between max-w-md py-2">
                            {[1, 2, 3, 4, 5].map((rating) => (
                              <div key={rating} className="flex flex-col items-center gap-1">
                                <RadioGroup value="3">
                                  <div className="flex flex-col items-center space-y-1">
                                    <RadioGroupItem value={`${rating}`} id={`rating-${rating}`} />
                                    <Label htmlFor={`rating-${rating}`} className="text-sm">{rating}</Label>
                                  </div>
                                </RadioGroup>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {index === 2 && (
                      <div className="ml-9">
                        <Textarea
                          placeholder="Your answer here..."
                          className="h-20 resize-none"
                        />
                      </div>
                    )}

                    {/* Just show the question without inputs for the rest */}
                  </div>
                ))}
              </div>

              <div className="border-t pt-4 flex flex-col sm:flex-row justify-between gap-3">
                <div className="text-sm text-muted-foreground text-left">
                  <p className="font-medium text-foreground">AI-powered capabilities:</p>
                  <ul className="list-disc ml-5 space-y-1 mt-1 text-left">
                    <li>Unbiased question generation</li>
                    <li>Smart answer options</li>
                    <li>Audience-targeted language</li>
                  </ul>
                </div>
                <Button asChild className="sm:self-end">
                  <Link href="/register">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    Sign Up to Create
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}