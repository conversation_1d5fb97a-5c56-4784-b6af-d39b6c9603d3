// src/components/ui/seo-faq.tsx
import React from 'react';

/**
 * SEO FAQ Component - This creates a FAQ section that's structured for search engines but hidden from users
 * It helps improve rankings for key search terms while remaining invisible to regular visitors
 */
export function SEOFaq() {
  return (
    <div className="sr-only" aria-hidden="true">
      <h2>Frequently Asked Questions</h2>

      <section itemScope itemType="https://schema.org/FAQPage">
        <div itemScope itemType="https://schema.org/Question">
          <h3 itemProp="name">What is PollGPT?</h3>
          <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
            <div itemProp="text">
              <p><PERSON><PERSON><PERSON> (also known as Poll GPT) is the #1 AI-powered poll and survey creator that helps you generate, distribute, and analyze polls with artificial intelligence assistance. It&apos;s the fastest way to create professional polls with zero effort.</p>
            </div>
          </div>
        </div>

        <div itemScope itemType="https://schema.org/Question">
          <h3 itemProp="name">Is PollGPT free to use?</h3>
          <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
            <div itemProp="text">
              <p>Yes, PollGPT offers a free tier that allows you to create basic polls and surveys with AI assistance. Premium features are available for more advanced polling needs and analytics capabilities.</p>
            </div>
          </div>
        </div>

        <div itemScope itemType="https://schema.org/Question">
          <h3 itemProp="name">How does Poll GPT compare to other survey tools?</h3>
          <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
            <div itemProp="text">
              <p>There is no difference. PollGPT and Poll GPT refer to the same AI polling platform. PollGPT is simply the brand name written without a space.</p>
            </div>
          </div>
        </div>

        <div itemScope itemType="https://schema.org/Question">
          <h2 itemProp="name">Is Poll GPT free to use?</h2>
          <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
            <div itemProp="text">
              <p>PollGPT offers both free and premium plans. You can create basic polls with the free version of Poll GPT.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
