// src/components/ui/seo-faq-enhanced.tsx
"use client";

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { HelpCircle } from "lucide-react";

/**
 * Enhanced SEO FAQ Component - This creates a visible FAQ section with accordions structured for search engines
 * It improves rankings for key search terms while providing great UX with collapsible sections
 */
export function SEOFaqEnhanced() {
  return (
    <div className="w-full py-16 md:py-20 bg-gradient-to-br from-slate-50/50 via-white to-orange-50/30 dark:from-slate-950/50 dark:via-slate-900/30 dark:to-slate-950/50">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12 md:mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-600 to-amber-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
              <HelpCircle className="w-4 h-4" />
              Frequently Asked Questions
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4">
              Everything You Need to Know About{" "}
              <span className="bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                PollGPT
              </span>
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
              Get answers to common questions about our AI-powered polling platform
            </p>
          </div>

          {/* FAQ Accordion with Schema.org markup */}
          <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm rounded-xl md:rounded-2xl border border-border/50 dark:border-slate-700/50 shadow-xl p-6 md:p-8">
            <section itemScope itemType="https://schema.org/FAQPage">
              <Accordion type="single" collapsible className="w-full space-y-2">
                <AccordionItem value="what-is-pollgpt" className="border-b border-border/50 dark:border-slate-700/50" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    What is PollGPT?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>PollGPT (also known as Poll GPT) is the #1 AI-powered poll and survey creator that helps you generate, distribute, and analyze polls with artificial intelligence assistance. It&apos;s the fastest way to create professional polls with zero effort.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="is-pollgpt-free" className="border-b border-border/50 dark:border-slate-700/50" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    Is PollGPT free to use?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>Yes, PollGPT offers a free tier that allows you to create basic polls and surveys with AI assistance. Premium features are available for more advanced polling needs and analytics capabilities.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="poll-gpt-comparison" className="border-b border-border/50 dark:border-slate-700/50" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    How does Poll GPT compare to other survey tools?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>Unlike traditional survey tools, Poll GPT uses artificial intelligence to generate unbiased questions, analyze responses automatically, and provide actionable insights in seconds. This makes it 10× more efficient than manual survey creation tools.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="pollgpt-login" className="border-b border-border/50 dark:border-slate-700/50" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    Do I need a PollGPT login to use the service?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>While you can view and respond to public polls without an account, creating a free PollGPT login allows you to create your own polls, save templates, and access analytics for the responses you collect.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="pollgpt-app" className="border-b border-border/50 dark:border-slate-700/50" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    Is there a PollGPT app available?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>PollGPT is available as a web application that works seamlessly on all devices. Our responsive design means you can create and manage polls on desktop, tablet, or mobile without needing to download a separate app.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="best-ai-poll-generator" className="border-b-0" itemScope itemType="https://schema.org/Question">
                  <AccordionTrigger className="text-left text-base md:text-lg font-semibold hover:text-orange-600 dark:hover:text-orange-400 transition-colors" itemProp="name">
                    What makes PollGPT the best AI poll generator?
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground leading-relaxed" itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
                    <div itemProp="text">
                      <p>PollGPT stands out as the leading AI poll generator because it combines state-of-the-art language models with intuitive design. You can create polls from text prompts, documents, or websites in seconds, with AI handling the heavy lifting of question formulation, answer options, and bias removal.</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
