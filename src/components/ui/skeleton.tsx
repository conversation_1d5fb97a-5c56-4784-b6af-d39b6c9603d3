"use client"

import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-md bg-muted",
        "after:absolute after:inset-0 after:-translate-x-full",
        "after:animate-[shimmer_2s_infinite]",
        "after:bg-gradient-to-r after:from-transparent after:via-white/10 after:to-transparent",
        className
      )}
      style={{
        animation: props.style?.animation,
      }}
      {...props}
    />
  )
}

// Define shimmer keyframes as inline styles - will be injected into the document if not available from Tailwind
if (typeof document !== 'undefined' && !document.getElementById('skeleton-keyframes')) {
  const style = document.createElement('style')
  style.id = 'skeleton-keyframes'
  style.innerHTML = `
    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(100%); }
      100% { transform: translateX(100%); }
    }
  `
  document.head.appendChild(style)
}

export { Skeleton }
