"use client"

import Image from "next/image"
import { useTheme } from "@/components/providers/theme-provider"
import { useEffect, useState } from "react"

interface ThemeLogoProps {
  type?: "icon" | "logo"
  width?: number
  height?: number
  className?: string
}

export function ThemeLogo({
  type = "icon",
  width = 32,
  height = 32,
  className = ""
}: ThemeLogoProps) {
  const { theme } = useTheme()
  // Add client-side only rendering to prevent hydration mismatch
  const [mounted, setMounted] = useState(false)

  // Only show the UI when mounted on the client
  useEffect(() => {
    setMounted(true)
  }, [])

  // Choose the appropriate logo files based on type
  const lightSrc = `/${type === "logo" ? "pollgpt-logo-light" : "pollgpt-icon-light"}.svg`
  const darkSrc = `/${type === "logo" ? "pollgpt-logo-dark" : "pollgpt-icon-dark"}.svg`

  // Return a single consistent image initially to prevent hydration issues
  // Always use light logo for server-side rendering to avoid hydration mismatch
  if (!mounted) {
    // Use style to hide element that doesn't match current theme instead of CSS classes
    // This prevents hydration mismatch issues with dark:hidden and dark:block classes
    return (
      <Image
        src={lightSrc}
        alt="PollGPT"
        width={width}
        height={height}
        className={className}
        priority
      />
    )
  }

  // Determine theme after client-side mounting
  const isDarkMode = theme === "dark"
  const isSystemTheme = theme === "system"

  if (isSystemTheme) {
    // For system preference, we need to handle dark/light mode with CSS
    // but ensure we use the same approach for both client and server rendering
    return (
      <>
        <Image
          src={lightSrc}
          alt="PollGPT"
          width={width}
          height={height}
          className={className}
          style={{ display: 'var(--display-light-logo, block)' }}
          priority
        />
        <Image
          src={darkSrc}
          alt="PollGPT"
          width={width}
          height={height}
          className={className}
          style={{ display: 'var(--display-dark-logo, none)' }}
          priority
        />
      </>
    )
  }

  // For explicitly set themes, just use the appropriate logo
  return (
    <Image
      src={isDarkMode ? darkSrc : lightSrc}
      alt="PollGPT"
      width={width}
      height={height}
      className={className}
      priority
    />
  )
}