"use client";

import { useState, useEffect, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Twitter, Facebook, Mail } from "lucide-react";

// AI-Assisted Creation Feature Preview
export function AiCreationPreview() {
  const [step, setStep] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [typedText, setTypedText] = useState("");

  const topicExample = "Customer Satisfaction Survey";
  const aiThoughts = useMemo(() => [
    "Analyzing topic context...",
    "Researching best practices...",
    "Formulating balanced questions...",
    "Optimizing for engagement..."
  ], []);

  const generatedQuestions = useMemo(() => [
    "How satisfied are you with our product?",
    "What features do you find most valuable?",
    "How likely are you to recommend us?",
    "What improvements would you suggest?"
  ], []);

  useEffect(() => {
    const interval = setInterval(() => {
      setStep((prev) => (prev + 1) % (aiThoughts.length + generatedQuestions.length + 1));
    }, 3000);

    return () => clearInterval(interval);
  }, [aiThoughts.length, generatedQuestions.length]);

  useEffect(() => {
    if (step < aiThoughts.length) {
      typeText(aiThoughts[step]);
    }
  }, [step, aiThoughts]);

  const typeText = (text: string) => {
    setIsTyping(true);
    setTypedText("");

    let i = 0;
    const typing = setInterval(() => {
      if (i < text.length) {
        setTypedText(() => text.substring(0, i + 1)); // Removed unused 'prev' parameter
        i++;
      } else {
        clearInterval(typing);
        setIsTyping(false);
      }
    }, 30);
  };

  return (
    <Card className="w-full h-[320px] overflow-hidden shadow-lg border-primary/20 hover:border-primary/50 transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-green-500"></div>
            <span className="text-sm font-medium">AI Assistant</span>
          </div>
          <Badge variant="outline" className="bg-primary/10 text-primary text-xs">
            Creating: {topicExample}
          </Badge>
        </div>

        <div className="h-[240px] bg-muted/30 rounded-md p-3 overflow-y-auto relative">
          {step < aiThoughts.length ? (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">AI is thinking...</div>
              <div className="flex items-start gap-3">
                <div className="h-6 w-6 min-w-6 rounded-full bg-primary/20 flex items-center justify-center text-primary text-xs flex-shrink-0">
                  AI
                </div>
                <div className="text-sm pt-0.5">
                  {typedText}
                  {isTyping && <span className="animate-pulse">|</span>}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">Generated questions:</div>
              <ul className="space-y-1.5 text-sm">
                {generatedQuestions.map((q, i) => (
                  <li
                    key={i}
                    className={`flex items-center gap-2 ${step - aiThoughts.length <= i ? 'animate-fadeIn' : ''}`}
                    style={{ animationDelay: `${i * 0.1}s` }}
                  >
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xs">
                      {i + 1}
                    </div>
                    {q}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Easy Distribution Feature Preview
export function DistributionPreview() {
  const [activeTab, setActiveTab] = useState(0);
  const pollUrl = "pollgpt.com/poll/abc123";

  const tabs = useMemo(() => ["Link", "QR Code", "Embed"], []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % tabs.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [tabs.length]);

  return (
    <Card className="w-full h-[320px] overflow-hidden shadow-lg border-primary/20 hover:border-primary/50 transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-sm font-medium">Share Your Poll</div>
          <div className="flex gap-1">
            {tabs.map((tab, i) => (
              <Button
                key={i}
                variant={activeTab === i ? "default" : "ghost"}
                size="sm"
                className={`text-xs px-2 py-1 h-7 ${activeTab === i ? 'bg-primary text-primary-foreground' : ''}`}
                onClick={() => setActiveTab(i)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        <div className="h-[240px] bg-muted/30 rounded-md p-3 overflow-y-auto">
          {activeTab === 0 && (
            <div
              key="link"
              className="h-full flex flex-col justify-between animate-fadeIn"
            >
              <div className="flex items-center gap-2 bg-background rounded border p-2">
                <div className="text-sm font-mono truncate flex-1">{pollUrl}</div>
                <Button size="sm" variant="outline" className="h-7">Copy</Button>
              </div>
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Send via:</div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                    <Twitter className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                    <Facebook className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                    <Mail className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 1 && (
            <div
              key="qr"
              className="h-full flex flex-col items-center justify-center animate-fadeIn"
            >
              <div className="w-24 h-24 bg-background p-2 rounded-md border flex items-center justify-center">
                <div className="w-20 h-20 grid grid-cols-5 grid-rows-5 gap-0.5">
                  {Array.from({ length: 25 }).map((_, i) => (
                    <div
                      key={i}
                      className={`${
                        [0, 1, 2, 3, 4, 5, 9, 10, 14, 15, 19, 20, 21, 22, 23, 24].includes(i) ||
                        [6, 8, 16, 18].includes(i) ||
                        i === 12
                          ? 'bg-foreground'
                          : 'bg-transparent'
                      }`}
                    />
                  ))}
                </div>
              </div>
              <div className="text-xs text-center mt-3 text-muted-foreground">
                Scan with your camera
              </div>
            </div>
          )}

          {activeTab === 2 && (
            <div
              key="embed"
              className="h-full flex flex-col justify-between animate-fadeIn"
            >
              <div className="bg-background rounded border p-2 font-mono text-xs overflow-x-auto">
                <pre className="text-muted-foreground">
                  {`<iframe
  src="${pollUrl}"
  width="100%"
  height="500"
  frameborder="0"
></iframe>`}
                </pre>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-xs text-muted-foreground">Embed on your website</div>
                <Button size="sm" variant="outline" className="h-7">Copy Code</Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Real-time Responses Feature Preview
export function RealTimeResponsesPreview() {
  const [activeTab, setActiveTab] = useState(0);
  const [responses, setResponses] = useState(94);

  const tabs = useMemo(() => ["Live Responses", "Active"], []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % tabs.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [tabs.length]);

  useEffect(() => {
    const interval = setInterval(() => {
      setResponses((prev) => prev + 1);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Card className="w-full h-[320px] overflow-hidden shadow-lg border-primary/20 hover:border-primary/50 transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-sm font-medium">Live Responses</div>
          <div className="flex gap-1">
            {tabs.map((tab, i) => (
              <Button
                key={i}
                variant={activeTab === i ? "default" : "ghost"}
                size="sm"
                className={`text-xs px-2 py-1 h-7 ${activeTab === i ? 'bg-primary text-primary-foreground' : ''}`}
                onClick={() => setActiveTab(i)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        <div className="h-[240px] bg-muted/30 rounded-md p-3 overflow-y-auto">
          {activeTab === 0 && (
            <div className="animate-fadeIn">
              <div className="bg-background rounded-md border p-3 mb-3">
                <div className="flex justify-between items-center mb-1">
                  <div className="font-medium text-sm">Total Responses</div>
                  <div className="text-primary font-medium text-sm">+4 today</div>
                </div>
                <div className="flex justify-between items-end">
                  <div className="text-3xl font-bold">{responses}</div>
                  <div className="text-xs text-muted-foreground">Last response 3m ago</div>
                </div>
              </div>

              <div className="text-xs text-muted-foreground mb-2">Recent responses</div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm py-1 border-b">
                  <div>New York, US</div>
                  <div className="text-xs text-muted-foreground">2m ago</div>
                </div>
                <div className="flex justify-between items-center text-sm py-1 border-b">
                  <div>London, UK</div>
                  <div className="text-xs text-muted-foreground">5m ago</div>
                </div>
                <div className="flex justify-between items-center text-sm py-1 border-b">
                  <div>Toronto, CA</div>
                  <div className="text-xs text-muted-foreground">7m ago</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 1 && (
            <div className="animate-fadeIn">
              <div className="flex justify-between items-center mb-3">
                <div className="font-medium text-sm">Current activity</div>
                <Badge variant="outline" className="bg-green-500/10 text-green-500 text-xs">
                  12 users online
                </Badge>
              </div>

              <div className="space-y-3">
                <div className="bg-background rounded-md border p-2">
                  <div className="flex justify-between items-center mb-1">
                    <div className="text-sm font-medium">Completion rate</div>
                    <div className="text-xs text-green-500">+2.4%</div>
                  </div>
                  <div className="w-full bg-muted h-2 rounded-full overflow-hidden">
                    <div className="bg-primary h-full rounded-full" style={{ width: "73%" }}></div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">73% completion rate</div>
                </div>

                <div className="bg-background rounded-md border p-2">
                  <div className="flex justify-between items-center mb-1">
                    <div className="text-sm font-medium">Average time</div>
                    <div className="text-xs text-green-500">-0:12</div>
                  </div>
                  <div className="text-xl font-bold">2:45</div>
                  <div className="text-xs text-muted-foreground">minutes to complete</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Advanced Analytics Feature Preview
export function AnalyticsPreview() {
  const [activeTab, setActiveTab] = useState(0);

  const tabs = useMemo(() => ["Overview", "Insights", "Trends"], []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % tabs.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [tabs.length]);

  return (
    <Card className="w-full h-[320px] overflow-hidden shadow-lg border-primary/20 hover:border-primary/50 transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-sm font-medium">Analytics Dashboard</div>
          <div className="flex gap-1">
            {tabs.map((tab, i) => (
              <Button
                key={i}
                variant={activeTab === i ? "default" : "ghost"}
                size="sm"
                className={`text-xs px-2 py-1 h-7 ${activeTab === i ? 'bg-primary text-primary-foreground' : ''}`}
                onClick={() => setActiveTab(i)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        <div className="h-[240px] bg-muted/30 rounded-md p-3 overflow-y-auto">
          {activeTab === 0 && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="bg-background rounded-md border p-2">
                  <div className="text-xs text-muted-foreground">Total Responses</div>
                  <div className="text-xl font-bold">94</div>
                </div>
                <div className="bg-background rounded-md border p-2">
                  <div className="text-xs text-muted-foreground">Completion Rate</div>
                  <div className="text-xl font-bold">73%</div>
                </div>
              </div>

              <div className="bg-background rounded-md border p-2 mb-3">
                <div className="text-xs text-muted-foreground mb-1">Response Distribution</div>
                <div className="flex items-end h-20 gap-1">
                  {[35, 42, 65, 78, 90, 65, 45].map((height, i) => (
                    <div
                      key={i}
                      className="bg-primary/80 rounded-sm flex-1"
                      style={{ height: `${height}%` }}
                    ></div>
                  ))}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <div>Mon</div>
                  <div>Sun</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 1 && (
            <div className="animate-fadeIn">
              <div className="text-xs text-muted-foreground mb-2">AI-Generated Insights</div>

              <div className="space-y-3">
                <div className="bg-background rounded-md border p-2 flex items-start gap-2">
                  <div className="text-primary font-medium">1</div>
                  <div>
                    <div className="text-sm">76% of respondents prefer the new interface design</div>
                  </div>
                </div>

                <div className="bg-background rounded-md border p-2 flex items-start gap-2">
                  <div className="text-primary font-medium">2</div>
                  <div>
                    <div className="text-sm">Users aged 25-34 report highest satisfaction scores</div>
                  </div>
                </div>

                <div className="bg-background rounded-md border p-2 flex items-start gap-2">
                  <div className="text-primary font-medium">3</div>
                  <div>
                    <div className="text-sm">Feature X is mentioned in 80% of positive comments</div>
                  </div>
                </div>

                <div className="bg-background rounded-md border p-2 flex items-start gap-2">
                  <div className="text-primary font-medium">4</div>
                  <div>
                    <div className="text-sm">Response rate peaks on Wednesdays at 2pm</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 2 && (
            <div className="animate-fadeIn">
              <div className="flex justify-between items-center mb-3">
                <div className="text-sm font-medium">Satisfaction Trend</div>
                <div className="text-xs text-green-500">+12% this month</div>
              </div>

              <div className="bg-background rounded-md border p-3 mb-3">
                <div className="relative h-20">
                  <div className="absolute inset-0 flex items-end">
                    <svg viewBox="0 0 100 20" className="w-full h-full overflow-visible">
                      <path
                        d="M0,10 Q10,8 20,12 T40,9 T60,12 T80,8 T100,11"
                        fill="none"
                        stroke="hsl(var(--primary))"
                        strokeWidth="1.5"
                      />
                      <circle cx="100" cy="11" r="2" fill="hsl(var(--primary))" />
                    </svg>
                  </div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <div>Jan</div>
                  <div>Dec</div>
                </div>
              </div>

              <div className="text-xs text-muted-foreground mb-2">Key Metrics</div>

              <div className="grid grid-cols-2 gap-2">
                <div className="bg-background rounded-md border p-2">
                  <div className="text-xs text-muted-foreground">NPS Score</div>
                  <div className="text-xl font-bold">+42</div>
                </div>
                <div className="bg-background rounded-md border p-2">
                  <div className="text-xs text-muted-foreground">CSAT</div>
                  <div className="text-xl font-bold">4.7/5</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
