import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, User, Bot } from 'lucide-react';
import { Loader } from '@/components/ui/loader';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ReactMarkdown from 'react-markdown';

export type Message = {
  role: 'user' | 'assistant' | 'system';
  content: string;
  processed?: boolean; // Flag to mark messages that have been processed for JSON formatting
};

export type AIModel = 'gemini' | 'mistral';

export type SuggestedResponse = {
  text: string;
  action?: () => void;
};

interface ConversationInterfaceProps {
  messages: Message[];
  onSendMessage: (message: string, model?: AIModel) => void;
  isLoading?: boolean;
  placeholder?: string;
  className?: string;
  defaultModel?: AIModel;
  showModelSelector?: boolean;
  suggestedResponses?: SuggestedResponse[];
  onSuggestedResponseClick?: (response: string) => void;
}

export function ConversationInterface({
  messages,
  onSendMessage,
  isLoading = false,
  placeholder = "Tell me about your poll...",
  className = "",
  defaultModel = "gemini",
  showModelSelector = true,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  suggestedResponses = [],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onSuggestedResponseClick
}: ConversationInterfaceProps) {
  // Add CSS for typing indicator
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .typing-indicator {
        display: flex;
        align-items: center;
      }

      .typing-indicator span {
        height: 8px;
        width: 8px;
        margin: 0 2px;
        background-color: #555;
        border-radius: 50%;
        opacity: 0.4;
        display: inline-block;
        animation: typing 1.5s infinite ease-in-out;
      }

      .typing-indicator span:nth-child(1) {
        animation-delay: 0s;
      }

      .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
      }

      .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
      }

      /* Add JSON code formatting styles */
      .conversation pre {
        max-width: 100%;
        overflow-x: auto;
        background-color: #f8f9fa;
        border-radius: 6px;
      }

      .conversation pre code {
        white-space: pre-wrap !important;
        word-break: break-word !important;
        max-width: 100% !important;
        overflow-x: auto !important;
      }

      .dark .conversation pre {
        background-color: #1e293b;
      }

      .conversation code {
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.9em;
      }

      @keyframes typing {
        0% {
          transform: translateY(0px);
        }
        28% {
          transform: translateY(-5px);
          opacity: 1;
        }
        44% {
          transform: translateY(0px);
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState<AIModel>(defaultModel);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    // Clear any existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Use setTimeout to ensure DOM has updated before scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      if (endOfMessagesRef.current && messagesContainerRef.current) {
        const container = messagesContainerRef.current;

        // Check if we're near the bottom before auto-scrolling
        const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;

        // Only auto-scroll if user is near bottom or if it's the first message
        if (isNearBottom || messages.length <= 1) {
          // On mobile, use a more gentle scrolling approach
          if (window.innerWidth < 640) {
            // Use smooth scrolling with requestAnimationFrame for better mobile performance
            const scrollToBottom = () => {
              container.scrollTo({
                top: container.scrollHeight,
                behavior: 'smooth'
              });
            };
            requestAnimationFrame(scrollToBottom);
          } else {
            // Desktop: use scrollIntoView
            endOfMessagesRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'end',
              inline: 'nearest'
            });
          }
        }
      }
    }, 150); // Increased timeout for mobile

    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [messages]);

  // Focus input on mount (but not on mobile to prevent keyboard issues)
  useEffect(() => {
    if (inputRef.current && window.innerWidth >= 640) {
      inputRef.current.focus();
    }
  }, []);

  // Handle viewport changes (mobile keyboard open/close) to prevent scroll issues
  useEffect(() => {
    let resizeTimeout: NodeJS.Timeout;

    const handleResize = () => {
      // Clear any pending scroll operations when viewport changes
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Debounce resize events
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Re-scroll to bottom after viewport stabilizes (mobile keyboard)
        if (messagesContainerRef.current && window.innerWidth < 640) {
          const container = messagesContainerRef.current;
          requestAnimationFrame(() => {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          });
        }
      }, 300);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSendMessage(input, selectedModel);
      setInput('');
    }
  };

  const handleModelChange = (value: string) => {
    setSelectedModel(value as AIModel);
  };

  // Format message content to handle markdown and improve formatting
  const formatMessageContent = (content: string) => {
    // Pre-process content to enhance formatting for common patterns
    let enhancedContent = content;

    // Pre-process numbered lists (1., 2., etc.) to make sure they are properly converted to markdown
    enhancedContent = enhancedContent.replace(
      /(\d+)\.\s+(\*\*[^*]+\*\*)/g,
      (match, number, boldText) => `${number}. ${boldText}`
    );

    // Pre-process topic/audience/purpose patterns that are common in poll responses
    enhancedContent = enhancedContent.replace(
      /\*\*(Topic|Audience|Purpose|Target Audience)[^*]*\*\*/g,
      (match) => `\n\n### ${match}\n`
    );

    // Convert plain text questions to enhanced format
    if (enhancedContent.includes("follow-up questions:") ||
        enhancedContent.includes("some questions:") ||
        enhancedContent.includes("questions to consider:")) {

      // Make the "questions" heading stand out
      enhancedContent = enhancedContent.replace(
        /(follow-up questions:|some questions:|questions to consider:)/i,
        "\n\n**$1**\n\n"
      );
    }

    return (
      <ReactMarkdown
        components={{
          // Style numbered lists nicely
          ol: ({...props}) => (
            <ol className="list-decimal pl-5 space-y-2 my-3" {...props} />
          ),
          // Style bullet points nicely
          ul: ({...props}) => (
            <ul className="list-disc pl-5 space-y-2 my-3" {...props} />
          ),
          // Make headings stand out
          h1: ({...props}) => (
            <h1 className="text-lg font-bold my-3" {...props} />
          ),
          h2: ({...props}) => (
            <h2 className="text-base font-bold my-3" {...props} />
          ),
          h3: ({...props}) => (
            <h3 className="text-base font-semibold my-3" {...props} />
          ),
          // Highlight important content
          strong: ({...props}) => (
            <strong className="font-bold text-primary-600" {...props} />
          ),
          // Style paragraphs with proper spacing
          p: ({...props}) => (
            <p className="mb-3" {...props} />
          ),
          // Style links to be distinct
          a: ({...props}) => (
            <a className="text-blue-600 underline" {...props} />
          ),
          // Style blockquotes for examples or important callouts
          blockquote: ({...props}) => (
            <blockquote className="border-l-4 border-gray-300 pl-3 italic my-3" {...props} />
          ),
          // Style code blocks
          code: ({className, ...props}: React.HTMLAttributes<HTMLElement> & { inline?: boolean }) => {
            const isInline = className?.includes("inline");
            return isInline
              ? <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded" {...props} />
              : <code className="block bg-gray-100 dark:bg-gray-800 p-2 rounded my-3 overflow-x-auto" {...props} />;
          },
          // Style list items
          li: ({...props}) => (
            <li className="mb-1" {...props} />
          ),
        }}
      >
        {enhancedContent}
      </ReactMarkdown>
    );
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950"
      >
        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
          >
            <div
              className={`flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 max-w-[95%] sm:max-w-[85%] ${
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground rounded-2xl rounded-tr-sm'
                  : 'bg-muted rounded-2xl rounded-tl-sm shadow-sm'
              } p-4`}
            >
              <div className={`flex items-center space-x-2 ${message.role === 'user' ? 'justify-end sm:justify-start' : 'justify-start'}`}>
                <Avatar className="h-8 w-8 flex-shrink-0">
                  {message.role === 'user' ? (
                    <>
                      <AvatarFallback><User size={16} /></AvatarFallback>
                      <AvatarImage src="/avatars/user.png" />
                    </>
                  ) : (
                    <>
                      <AvatarFallback><Bot size={16} /></AvatarFallback>
                      <AvatarImage src="/logo.png" />
                    </>
                  )}
                </Avatar>
                <div className="text-sm font-medium sm:hidden">
                  {message.role === 'user' ? 'You' : 'PollGPT'}
                </div>
              </div>
              <div className="space-y-2 flex-1 min-w-0">
                <div className="text-sm font-medium hidden sm:block">
                  {message.role === 'user' ? 'You' : 'PollGPT'}
                </div>
                <div className="conversation text-sm prose prose-sm max-w-none dark:prose-invert">
                  {formatMessageContent(message.content)}
                </div>
              </div>
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start mb-4">
            <div className="flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 bg-muted p-4 rounded-2xl rounded-tl-sm shadow-sm max-w-[95%] sm:max-w-[85%]">
              <div className="flex items-center space-x-2 justify-start">
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarFallback><Bot size={16} /></AvatarFallback>
                  <AvatarImage src="/logo.png" />
                </Avatar>
                <div className="text-sm font-medium sm:hidden">PollGPT</div>
              </div>
              <div className="space-y-2 flex-1 min-w-0">
                <div className="text-sm font-medium hidden sm:block">PollGPT</div>
                <div className="flex items-center space-x-2">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={endOfMessagesRef} />
      </div>

      <form onSubmit={handleSubmit} className="p-4 border-t bg-white dark:bg-gray-900 shadow-sm">
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={placeholder}
            disabled={isLoading}
            className="flex-1"
          />
          <div className="flex space-x-2">
            {showModelSelector && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="w-full sm:w-36">
                      <Select value={selectedModel} onValueChange={handleModelChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select AI Model" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gemini">Gemini</SelectItem>
                          <SelectItem value="mistral">Mistral</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Select AI model for this conversation</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <Button type="submit" disabled={isLoading || !input.trim()} className="flex-shrink-0">
              {isLoading ? <Loader variant="minimal" size="xs" /> : <Send className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
