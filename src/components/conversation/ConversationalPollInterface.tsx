'use client';

import React, { useState, useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import { useChat } from '@ai-sdk/react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, User, Bot, CheckCircle, AlertCircle, Clock, Sparkles } from 'lucide-react';
import { Loader } from '@/components/ui/loader';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ReactMarkdown from 'react-markdown';
import { motion, AnimatePresence } from 'framer-motion';

export type AIModel = 'gemini' | 'mistral';

interface MessagePart {
  type: string;
  toolInvocation?: {
    toolName: string;
    args: unknown;
    toolCallId: string;
    state: string;
  };
}

interface ChatMessage {
  role: string;
  content: string;
  parts?: MessagePart[];
  id?: string;
}

interface ConversationContext {
  hasBasicInfo: boolean;
  hasAudience: boolean;
  hasPurpose: boolean;
  mentionedTopics: string[];
  stage: 'initial' | 'gathering' | 'refining' | 'finalizing';
}

export interface PollSuggestion {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'open' | 'text';
    options?: string[];
    required: boolean;
  }>;
  reasoning?: string;
}

interface ConversationalPollInterfaceProps {
  className?: string;
  defaultModel?: AIModel;
  showModelSelector?: boolean;
  onPollConfirmed?: (pollData: PollSuggestion) => void;
  initialMessage?: string;
}

export interface ConversationalPollInterfaceRef {
  appendMessage: (content: string) => Promise<void>;
  resetConversation: () => void;
}

/**
 * Conversational Poll Interface Component
 *
 * AI Provider Strategy:
 * - Chat UI: User can select between Gemini (gemini-2.0-flash-001) and Mistral (mistral-large-2411)
 * - Content Extraction: Always uses Mistral (handled separately)
 * - Simulation: Always uses Perplexity (handled separately)
 *
 * See AI-PROVIDER-STRATEGY.md for complete documentation
 */
export const ConversationalPollInterface = forwardRef<ConversationalPollInterfaceRef, ConversationalPollInterfaceProps>(({
  className = "",
  defaultModel = "gemini",
  showModelSelector = true,
  onPollConfirmed,
  initialMessage = "Hi there! I'm PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
}, ref) => {
  const [selectedModel, setSelectedModel] = useState<AIModel>(defaultModel);

  const { messages, input, handleInputChange, handleSubmit, addToolResult, isLoading, append, status, error, reload } = useChat({
    api: '/api/ai/chat-completion',
    maxSteps: 5,
    // Add streamProtocol option as fallback for compatibility
    streamProtocol: 'data', // Use 'data' protocol for proper AI SDK v4+ streaming
    body: {
      preferredModel: selectedModel,
      context: 'poll-creation',
      features: ['tool-calling']
    },
    initialMessages: [
      { role: 'assistant', content: initialMessage, id: 'initial' }
    ],
    onError: (error) => {
      console.error('Chat error:', error);
      // Try to recover from stream parsing errors or empty message parts errors
      if (error.message?.includes('Failed to parse stream string') ||
          error.message?.includes('contents.parts must not be empty') ||
          error.message?.includes('GenerateContentRequest.contents')) {
        console.log('Stream error or empty message parts detected, attempting recovery...');
        // As a fallback, try reloading the chat
        setTimeout(() => {
          reload();
        }, 1000);
      }
    },
    onFinish: (message) => {
      console.log('Chat finished:', message);

      // Handle tool results if any
      if (message.toolInvocations) {
        message.toolInvocations.forEach((tool) => {
          if (tool.toolName === 'suggestPollCreation' && tool.state === 'result') {
            console.log('Poll suggestion received:', tool);
            // Handle poll confirmation UI here if needed
          }
        });
      }
    }
  });

  // Expose append method and reset method to parent component
  useImperativeHandle(ref, () => ({
    appendMessage: async (content: string) => {
      // Validate content to prevent empty messages
      if (!content || content.trim() === '') {
        console.warn('Attempted to append empty message, ignoring');
        return;
      }

      await append({
        role: 'user',
        content
      });
    },
    resetConversation: () => {
      // Reset the chat to initial state
      window.location.reload();
    }
  }), [append]);

  // Create a ref to track if we've already processed a message in this component instance
  // This prevents duplicate processing even if the effect runs multiple times
  const processedMessageRef = useRef(false);

  // Check for pending messages in localStorage when component mounts
  useEffect(() => {
    const checkForPendingMessages = () => {
      // Skip if we've already processed a message in this component instance
      if (processedMessageRef.current) {
        return;
      }

      try {
        const pendingMessageJson = localStorage.getItem('pollgpt_pending_message');
        if (pendingMessageJson) {
          const pendingMessage = JSON.parse(pendingMessageJson);

          // Only process if it hasn't been processed already
          if (!pendingMessage.processed) {
            console.log('Found pending message to process:', pendingMessage.source);

            // Mark as being processed immediately to prevent race conditions
            pendingMessage.processed = true;
            localStorage.setItem('pollgpt_pending_message', JSON.stringify(pendingMessage));

            // Mark that we've processed a message in this component instance
            processedMessageRef.current = true;

            // Add the message to the chat
            append({
              role: 'user',
              content: pendingMessage.message
            }).then(() => {
              console.log('Successfully processed pending message');

              // IMPORTANT: Don't remove the pending message yet!
              // Keep it available for poll creation - only mark as processed
              // The poll creation logic needs access to this data
              console.log('Keeping pending message for poll creation access');
            }).catch(error => {
              console.error('Failed to process pending message:', error);
              // If processing fails, we could potentially mark it as unprocessed to try again
              // But for now, we'll leave it as processed to avoid infinite loops
            });
          }
        }
      } catch (error) {
        console.error('Error checking for pending messages:', error);
      }
    };

    // Check immediately when component mounts
    checkForPendingMessages();

    // Also check again after a short delay to ensure the chat is fully initialized
    // But only if we haven't processed anything yet
    const delayedCheck = setTimeout(() => {
      if (!processedMessageRef.current) {
        checkForPendingMessages();
      }
    }, 2000);

    return () => clearTimeout(delayedCheck);
  }, [append]);

  const endOfMessagesRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      if (endOfMessagesRef.current && messagesContainerRef.current) {
        const container = messagesContainerRef.current;
        const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;

        if (isNearBottom || messages.length <= 1) {
          endOfMessagesRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }, 100);

    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [messages]);

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && status === 'ready' && !error) {
      handleSubmit(e);
    }
  };

  const handleModelChange = (value: AIModel) => {
    setSelectedModel(value);
  };

  const handleToolResponse = (toolCallId: string, result: unknown) => {
    addToolResult({
      toolCallId,
      result
    });
  };

  const renderSuggestedReplies = (message: ChatMessage) => {
    // Only show suggestions for assistant messages
    if (message.role !== 'assistant') return null;

    const suggestions = generateContextualSuggestions(message, messages);

    if (suggestions.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 mt-3">
        {suggestions.map((suggestion, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => {
              if (status === 'ready' && !error) {
                append({
                  role: 'user',
                  content: suggestion.text
                });
              }
            }}
            className="text-xs"
            disabled={status !== 'ready' || error != null}
          >
            <Sparkles className="h-3 w-3 mr-1" />
            {suggestion.text}
          </Button>
        ))}
      </div>
    );
  };

  const generateContextualSuggestions = (message: ChatMessage, allMessages: ChatMessage[]) => {
    const suggestions: Array<{ text: string; priority: number }> = [];
    const content = message.content.toLowerCase();
    const conversationContext = analyzeConversationContext(allMessages);

    // Check if this message contains a tool call
    const hasToolCall = message.parts?.some(part => part.type === 'tool-invocation');
    const pollSuggestionCall = message.parts?.find(part =>
      part.type === 'tool-invocation' &&
      part.toolInvocation?.toolName === 'suggestPollCreation'
    );
    const requirementsCall = message.parts?.find(part =>
      part.type === 'tool-invocation' &&
      part.toolInvocation?.toolName === 'gatherPollRequirements'
    );

    // Enhanced content analysis - extract key phrases and questions
    const sentences = message.content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const questions = sentences.filter(s => s.trim().endsWith('?') || s.includes('what') || s.includes('how') || s.includes('which') || s.includes('when') || s.includes('where') || s.includes('why'));

    // Extract specific topics mentioned in this message
    const mentionedTerms = extractMentionedTerms(content);

    // 1. Handle poll suggestion tool calls - provide specific refinement options
    if (pollSuggestionCall) {
      const suggestion = pollSuggestionCall.toolInvocation?.args as PollSuggestion;
      if (suggestion) {
        // Suggest refinements based on the actual poll structure
        if (suggestion.questions?.length > 7) {
          suggestions.push({ text: "Make it shorter with fewer questions", priority: 10 });
        }
        if (suggestion.questions?.length < 4) {
          suggestions.push({ text: "Add a few more questions for better insights", priority: 10 });
        }

        const hasOpenQuestions = suggestion.questions?.some((q) => q.type === 'open' || q.type === 'text');
        if (!hasOpenQuestions) {
          suggestions.push({ text: "Add some open-ended questions", priority: 9 });
        }

        const hasRatings = suggestion.questions?.some((q) => q.type === 'single' && q.options?.some(opt => opt.includes('5')));
        if (!hasRatings && mentionedTerms.includes('satisfaction')) {
          suggestions.push({ text: "Add rating scale questions", priority: 8 });
        }

        suggestions.push({ text: "Change the question order", priority: 6 });
        suggestions.push({ text: "Adjust the wording", priority: 5 });
      }

      // Don't show other suggestions when there's a poll suggestion - let user focus on the poll
      return suggestions
        .sort((a, b) => b.priority - a.priority)
        .slice(0, 3)
        .map(s => ({ text: s.text }));
    }

    // 2. Handle requirements gathering - respond to specific questions asked
    if (requirementsCall) {
      const args = requirementsCall.toolInvocation?.args as { clarificationQuestions?: string[]; missingInfo?: string[] };

      if (args.clarificationQuestions) {
        args.clarificationQuestions.forEach((question: string, index: number) => {
          const q = question.toLowerCase();

          // Generate specific answers to specific questions
          if (q.includes('how many questions') || q.includes('how many items')) {
            suggestions.push(
              { text: "5-7 questions would be good", priority: 10 - index },
              { text: "About 10 questions", priority: 9 - index },
              { text: "Keep it under 5 minutes", priority: 8 - index }
            );
          } else if (q.includes('how long') || q.includes('duration')) {
            suggestions.push(
              { text: "2-3 minutes maximum", priority: 10 - index },
              { text: "5 minutes is fine", priority: 9 - index },
              { text: "As quick as possible", priority: 8 - index }
            );
          } else if (q.includes('anonymous') || q.includes('identify')) {
            suggestions.push(
              { text: "Yes, keep it anonymous", priority: 10 - index },
              { text: "Collect emails for follow-up", priority: 9 - index },
              { text: "Optional name field", priority: 8 - index }
            );
          } else if (q.includes('demographic') || q.includes('background')) {
            suggestions.push(
              { text: "Include age and location", priority: 10 - index },
              { text: "Just basic demographics", priority: 9 - index },
              { text: "Skip demographics", priority: 8 - index }
            );
          } else if (q.includes('scale') || q.includes('rating')) {
            suggestions.push(
              { text: "1-5 scale works well", priority: 10 - index },
              { text: "1-10 for more precision", priority: 9 - index },
              { text: "Simple Yes/No is better", priority: 8 - index }
            );
          } else {
            // Generic responses for other questions
            suggestions.push(
              { text: "Let me think about that...", priority: 7 - index },
              { text: "Good question, I'm not sure", priority: 6 - index }
            );
          }
        });
      }

      if (args.missingInfo) {
        args.missingInfo.forEach((info: string, index: number) => {
          const inf = info.toLowerCase();
          if (inf.includes('audience') || inf.includes('target')) {
            suggestions.push({ text: "Our customers", priority: 9 - index });
            suggestions.push({ text: "General public", priority: 8 - index });
          } else if (inf.includes('purpose') || inf.includes('goal')) {
            suggestions.push({ text: "To improve our service", priority: 9 - index });
            suggestions.push({ text: "For market research", priority: 8 - index });
          }
        });
      }

      return suggestions
        .sort((a, b) => b.priority - a.priority)
        .slice(0, 4)
        .map(s => ({ text: s.text }));
    }

    // 3. Analyze specific questions in the AI's response
    questions.forEach((question, index) => {
      const q = question.toLowerCase();

      if (q.includes('what kind') || q.includes('what type') || q.includes('what topic')) {
        // AI is asking about poll type/topic
        if (mentionedTerms.includes('business') || conversationContext.mentionedTopics.includes('business')) {
          suggestions.push({ text: "Customer satisfaction survey", priority: 10 - index });
          suggestions.push({ text: "Employee feedback poll", priority: 9 - index });
        } else {
          suggestions.push({ text: "I want to survey customer satisfaction", priority: 10 - index });
          suggestions.push({ text: "I need feedback on a new idea", priority: 9 - index });
          suggestions.push({ text: "I want to understand preferences", priority: 8 - index });
        }
      } else if (q.includes('who') || q.includes('audience') || q.includes('target')) {
        // AI is asking about target audience
        if (mentionedTerms.includes('customer')) {
          suggestions.push({ text: "Our existing customers", priority: 10 - index });
          suggestions.push({ text: "Potential customers", priority: 9 - index });
        } else if (mentionedTerms.includes('employee')) {
          suggestions.push({ text: "All employees", priority: 10 - index });
          suggestions.push({ text: "Specific departments", priority: 9 - index });
        } else {
          suggestions.push({ text: "General public", priority: 8 - index });
          suggestions.push({ text: "Specific demographic", priority: 7 - index });
        }
      } else if (q.includes('why') || q.includes('purpose') || q.includes('goal')) {
        // AI is asking about purpose
        suggestions.push({ text: "To make better decisions", priority: 10 - index });
        suggestions.push({ text: "To improve our services", priority: 9 - index });
        suggestions.push({ text: "To understand what people want", priority: 8 - index });
      } else if (q.includes('how many') || q.includes('how long')) {
        // AI is asking about survey length
        suggestions.push({ text: "Keep it short (5 questions)", priority: 10 - index });
        suggestions.push({ text: "Medium length (10 questions)", priority: 9 - index });
        suggestions.push({ text: "Whatever works best", priority: 8 - index });
      }
    });

    // 4. Handle specific content patterns with dynamic responses
    if (content.includes('understand') || content.includes('got it') || content.includes('clear')) {
      // AI is confirming understanding
      suggestions.push(
        { text: "Yes, that's exactly right!", priority: 10 },
        { text: "Almost, but let me clarify...", priority: 9 },
        { text: "Actually, I need to add something", priority: 8 }
      );
    } else if (content.includes('suggest') && content.includes('poll') && !hasToolCall) {
      // AI is offering to suggest a poll
      suggestions.push(
        { text: "Yes, show me your suggestion", priority: 10 },
        { text: "I need to provide more details first", priority: 9 },
        { text: "Can you ask me specific questions?", priority: 8 }
      );
    } else if (content.includes('create') && !hasToolCall) {
      // AI is offering to create something
      suggestions.push(
        { text: "Yes, let's do it", priority: 10 },
        { text: "Show me a preview first", priority: 9 },
        { text: "I want to refine it more", priority: 8 }
      );
    } else if (content.includes('help') && (content.includes('understand') || content.includes('know'))) {
      // AI is asking for help understanding something
      suggestions.push(
        { text: "Let me explain in more detail", priority: 10 },
        { text: "Can you be more specific?", priority: 9 },
        { text: "I'm not sure myself", priority: 7 }
      );
    } else if (content.includes('sounds like') || content.includes('seems like')) {
      // AI is making an assumption
      suggestions.push(
        { text: "Exactly! You understand perfectly", priority: 10 },
        { text: "Close, but not quite", priority: 9 },
        { text: "That's one aspect, but also...", priority: 8 }
      );
    }

    // 5. Dynamic suggestions based on conversation tone and AI response style
    if (content.includes('great') || content.includes('excellent') || content.includes('perfect')) {
      // AI is being positive/encouraging
      suggestions.push(
        { text: "Thanks! Let's keep going", priority: 8 },
        { text: "What's the next step?", priority: 7 }
      );
    }

    if (content.includes('might') || content.includes('could') || content.includes('perhaps')) {
      // AI is being tentative/suggesting options
      suggestions.push(
        { text: "Yes, that sounds good", priority: 9 },
        { text: "I prefer a different approach", priority: 8 },
        { text: "What are the other options?", priority: 7 }
      );
    }

    // 6. Conversational flow suggestions
    if (conversationContext.stage === 'initial' && !conversationContext.hasBasicInfo) {
      suggestions.push(
        { text: "I need help choosing a topic", priority: 6 },
        { text: "Can you guide me through this?", priority: 5 }
      );
    }

    // 7. Topic-specific suggestions based on detected context
    if (mentionedTerms.includes('satisfaction') || mentionedTerms.includes('feedback')) {
      suggestions.push(
        { text: "Include Net Promoter Score", priority: 7 },
        { text: "Ask about specific experiences", priority: 6 }
      );
    }

    if (mentionedTerms.includes('product') && !suggestions.some(s => s.text.includes('feature'))) {
      suggestions.push(
        { text: "Focus on feature usability", priority: 6 },
        { text: "Ask about missing features", priority: 5 }
      );
    }

    // 8. Fallback suggestions if we don't have many specific ones
    if (suggestions.length < 2) {
      suggestions.push(
        { text: "Tell me more", priority: 4 },
        { text: "That makes sense", priority: 3 },
        { text: "What do you recommend?", priority: 2 }
      );
    }

    // Sort by priority and return top 4 suggestions
    return suggestions
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 4)
      .map(s => ({ text: s.text }));
  };

  // Helper function to extract mentioned terms from content
  const extractMentionedTerms = (content: string): string[] => {
    const terms: string[] = [];
    const keywords = [
      'customer', 'employee', 'satisfaction', 'feedback', 'product', 'service',
      'business', 'market', 'research', 'opinion', 'preference', 'experience',
      'quality', 'improvement', 'decision', 'analysis', 'survey', 'poll'
    ];

    keywords.forEach(keyword => {
      if (content.includes(keyword)) {
        terms.push(keyword);
      }
    });

    return terms;
  };

  const analyzeConversationContext = (messages: ChatMessage[]): ConversationContext => {
    const context: ConversationContext = {
      hasBasicInfo: false,
      hasAudience: false,
      hasPurpose: false,
      mentionedTopics: [],
      stage: 'initial'
    };

    // Analyze user messages for context
    const userMessages = messages.filter(m => m.role === 'user').map(m => m.content.toLowerCase());
    const assistantMessages = messages.filter(m => m.role === 'assistant').map(m => m.content.toLowerCase());
    const allContent = [...userMessages, ...assistantMessages].join(' ');

    // Enhanced topic detection
    const topics: string[] = [];
    const topicPatterns = [
      { keywords: ['customer', 'satisfaction', 'service', 'support'], topic: 'customer satisfaction' },
      { keywords: ['product', 'feature', 'usability', 'design'], topic: 'product feedback' },
      { keywords: ['employee', 'work', 'workplace', 'team', 'staff'], topic: 'employee survey' },
      { keywords: ['market', 'research', 'trend', 'analysis'], topic: 'market research' },
      { keywords: ['brand', 'awareness', 'perception', 'reputation'], topic: 'brand research' },
      { keywords: ['event', 'conference', 'meeting', 'workshop'], topic: 'event feedback' },
      { keywords: ['training', 'education', 'course', 'learning'], topic: 'training feedback' },
      { keywords: ['price', 'pricing', 'cost', 'value'], topic: 'pricing research' }
    ];

    topicPatterns.forEach(pattern => {
      if (pattern.keywords.some(keyword => allContent.includes(keyword))) {
        topics.push(pattern.topic);
      }
    });
    context.mentionedTopics = topics;

    // Enhanced information detection
    context.hasBasicInfo = userMessages.some(msg =>
      msg.includes('poll') || msg.includes('survey') || msg.includes('questionnaire') ||
      msg.includes('feedback') || msg.includes('opinion') || msg.includes('ask people')
    );

    // More sophisticated audience detection
    context.hasAudience = allContent.includes('customer') || allContent.includes('employee') ||
                         allContent.includes('student') || allContent.includes('user') ||
                         allContent.includes('audience') || allContent.includes('people') ||
                         allContent.includes('respondent') || allContent.includes('participant') ||
                         allContent.includes('target') || allContent.includes('demographic');

    // Enhanced purpose detection
    context.hasPurpose = allContent.includes('satisfaction') || allContent.includes('feedback') ||
                        allContent.includes('opinion') || allContent.includes('research') ||
                        allContent.includes('improve') || allContent.includes('understand') ||
                        allContent.includes('measure') || allContent.includes('analyze') ||
                        allContent.includes('decision') || allContent.includes('insight');

    // Smarter stage detection based on AI responses too
    const hasRequirementsGathering = messages.some(m =>
      m.parts?.some(p => p.type === 'tool-invocation' &&
      p.toolInvocation?.toolName === 'gatherPollRequirements')
    );
    const hasPollSuggestion = messages.some(m =>
      m.parts?.some(p => p.type === 'tool-invocation' &&
      p.toolInvocation?.toolName === 'suggestPollCreation')
    );

    if (hasPollSuggestion) {
      context.stage = 'finalizing';
    } else if (hasRequirementsGathering || (context.hasBasicInfo && (!context.hasAudience || !context.hasPurpose))) {
      context.stage = 'gathering';
    } else if (context.hasBasicInfo && context.hasAudience && context.hasPurpose) {
      context.stage = 'refining';
    } else {
      context.stage = 'initial';
    }

    return context;
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header with model selector */}
      <div className="flex justify-between items-center mb-4 px-6 pt-4">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-primary" />
          <h3 className="font-semibold">PollGPT Assistant</h3>
        </div>
        {showModelSelector && (
          <Select value={selectedModel} onValueChange={handleModelChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gemini">Gemini</SelectItem>
              <SelectItem value="mistral">Mistral</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto mb-4 space-y-4 conversation px-6"
        style={{ maxHeight: 'calc(100% - 140px)' }}
      >
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={message.id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.role === 'assistant' && (
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-blue-500 text-white">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              )}

              <div className={`max-w-[80%] ${message.role === 'user' ? 'order-first' : ''}`}>
                <div className={`rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground ml-auto'
                    : 'bg-muted'
                }`}>
                  {/* Render message content */}
                  <div className="text-sm">
                    <ReactMarkdown>{message.content}</ReactMarkdown>
                  </div>                  {/* Render tool calls - AI SDK v5 */}
                  {message.parts?.map((part, partIndex) => {
                    // Handle ToolInvocationUIPart
                    if (part.type === 'tool-invocation') {
                      const toolInvocationPart = part as { toolInvocation: { toolName: string; toolCallId: string; args: unknown; state: string } };
                      const toolInvocation = toolInvocationPart.toolInvocation;

                      if (toolInvocation) {
                        switch (toolInvocation.toolName) {
                          case 'suggestPollCreation':
                            // Handle both 'call' and 'result' states for poll suggestions
                            if (toolInvocation.state === 'call' || toolInvocation.state === 'result') {
                              return (
                                <div key={partIndex} className="mt-3">
                                  <PollSuggestionCard
                                    suggestion={toolInvocation.args as PollSuggestion}
                                    onConfirm={() => {
                                      handleToolResponse(toolInvocation.toolCallId, 'Yes, create this poll now');
                                      if (onPollConfirmed) {
                                        onPollConfirmed(toolInvocation.args as PollSuggestion);
                                      }
                                    }}
                                    onReject={() => {
                                      handleToolResponse(toolInvocation.toolCallId, 'No, let me refine the requirements');
                                    }}
                                  />
                                </div>
                              );
                            }
                            return null;

                          case 'gatherPollRequirements':
                            // Handle both 'call' and 'result' states for requirements gathering
                            if (toolInvocation.state === 'call' || toolInvocation.state === 'result') {
                              return (
                                <div key={partIndex} className="mt-3">
                                  <RequirementsCard
                                    requirements={toolInvocation.args as {
                                      missingInfo?: string[];
                                      clarificationQuestions?: string[];
                                      currentUnderstanding?: string;
                                    }}
                                    onContinue={() => {
                                      // Complete the tool invocation with a progression response
                                      // This tells the AI that we're ready to move forward in the conversation
                                      handleToolResponse(
                                        toolInvocation.toolCallId,
                                        'I understand the requirements you\'ve shared so far. Based on our conversation, please proceed with suggesting a poll structure that matches what we\'ve discussed. If you need any specific clarification, feel free to ask, but I\'m ready to see your poll suggestion.'
                                      );
                                    }}
                                  />
                                </div>
                              );
                            }
                            return null;

                          case 'analyzeConversationContext':
                            // This tool auto-executes, so we don't need to render anything special
                            return null;

                          default:
                            // Generic tool call display for debugging - show for both states
                            return (
                              <div key={partIndex} className="bg-muted p-4 rounded-lg mt-3">
                                <p className="text-sm font-medium">Tool: {toolInvocation.toolName}</p>
                                <p className="text-sm text-muted-foreground">
                                  State: {toolInvocation.state}
                                </p>
                                <p className="text-sm text-muted-foreground mt-2">
                                  Args: {JSON.stringify(toolInvocation.args, null, 2)}
                                </p>
                              </div>
                            );
                        }
                      }
                    }
                    return null;
                  })}
                </div>

                {/* Render suggested replies for assistant messages */}
                {message.role === 'assistant' && renderSuggestedReplies(message)}
              </div>

              {message.role === 'user' && (
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-green-500 text-white">
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Loading indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex gap-3 justify-start"
          >
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-blue-500 text-white">
                <Bot className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div className="bg-muted rounded-lg p-3">
              <div className="flex items-center gap-2">
                <Loader className="h-4 w-4" />
                <span className="text-sm text-muted-foreground">Thinking...</span>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={endOfMessagesRef} />
      </div>

      {/* Error State */}
      {error && (
        <div className="mx-6 mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <span className="text-sm font-medium text-red-800">An error occurred</span>
          </div>
          <p className="text-sm text-red-700 mt-1">{error.message}</p>
          <Button
            onClick={() => reload()}
            variant="outline"
            size="sm"
            className="mt-2"
            disabled={status !== 'ready' && status !== 'error'}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleFormSubmit} className="flex gap-2 px-6 pb-4">
        <Input
          ref={inputRef}
          value={input}
          onChange={handleInputChange}
          placeholder="Describe your poll idea..."
          disabled={status !== 'ready' || error != null}
          className="flex-1"
        />
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="submit"
                disabled={status !== 'ready' || !input.trim() || error != null}
                size="icon"
              >
                <Send className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Send message (Enter)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </form>
    </div>
  );
});

ConversationalPollInterface.displayName = 'ConversationalPollInterface';

// Component for rendering poll suggestions
function PollSuggestionCard({
  suggestion,
  onConfirm,
  onReject
}: {
  suggestion: PollSuggestion;
  onConfirm: () => void;
  onReject: () => void;
}) {
  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">Poll Suggestion</CardTitle>
        </div>
        <CardDescription>
          I think I understand what you&apos;re looking for! Here&apos;s a poll suggestion:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-semibold text-primary">📊 {suggestion.title}</h4>
          <p className="text-sm text-muted-foreground mt-1">{suggestion.description}</p>
        </div>

        <div>
          <h4 className="font-semibold text-sm mb-2">Questions ({suggestion.questions.length}):</h4>
          <div className="space-y-2">
            {suggestion.questions.map((question, index) => (
              <div key={index} className="bg-background/50 rounded p-2">
                <p className="text-sm font-medium">{question.text}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {question.type}
                  </Badge>
                  {question.options && (
                    <span className="text-xs text-muted-foreground">
                      {question.options.length} options
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {suggestion.reasoning && (
          <div className="bg-background/50 rounded p-3">
            <p className="text-sm">
              <strong>Why this structure:</strong> {suggestion.reasoning}
            </p>
          </div>
        )}

        <div className="flex gap-2 pt-2">
          <Button onClick={onConfirm} className="flex-1">
            <CheckCircle className="h-4 w-4 mr-2" />
            Create Poll Now
          </Button>
          <Button onClick={onReject} variant="outline" className="flex-1">
            <AlertCircle className="h-4 w-4 mr-2" />
            Let me refine this
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Component for rendering requirements gathering
function RequirementsCard({
  requirements,
  onContinue
}: {
  requirements: {
    missingInfo?: string[];
    clarificationQuestions?: string[];
    currentUnderstanding?: string;
  };
  onContinue: () => void;
}) {
  // Track if user has started typing in the input field
  const [userStartedTyping, setUserStartedTyping] = useState(false);

  // Set up a global listener for input field focus and typing
  useEffect(() => {
    const handleInputFocus = () => {
      setUserStartedTyping(true);
    };

    // Find the input field
    const inputField = document.querySelector('input[placeholder*="poll"]');

    if (inputField) {
      inputField.addEventListener('focus', handleInputFocus);
    }

    return () => {
      if (inputField) {
        inputField.removeEventListener('focus', handleInputFocus);
      }
    };
  }, []);

  return (
    <Card className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-lg">Need More Information</CardTitle>
        </div>
        <CardDescription>
          I need a bit more information to create the perfect poll for you.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {requirements.missingInfo && requirements.missingInfo.length > 0 && (
          <div>
            <h4 className="font-semibold text-sm mb-2">Missing Information:</h4>
            <ul className="list-disc list-inside text-sm space-y-1">
              {requirements.missingInfo.map((info: string, index: number) => (
                <li key={index} className="text-muted-foreground">{info}</li>
              ))}
            </ul>
          </div>
        )}

        {requirements.clarificationQuestions && requirements.clarificationQuestions.length > 0 && (
          <div>
            <h4 className="font-semibold text-sm mb-2">Questions for you:</h4>
            <ul className="list-disc list-inside text-sm space-y-1">
              {requirements.clarificationQuestions.map((question: string, index: number) => (
                <li key={index} className="text-muted-foreground">{question}</li>
              ))}
            </ul>
          </div>
        )}

        {requirements.currentUnderstanding && (
          <div className="bg-background/50 rounded p-3">
            <p className="text-sm">
              <strong>What I understand so far:</strong> {requirements.currentUnderstanding}
            </p>
          </div>
        )}

        {/* Show continue button and handle auto-focusing */}
        {!userStartedTyping && (
          <Button onClick={onContinue} className="w-full">
            Continue conversation
          </Button>
        )}

        <div className="text-sm text-center text-muted-foreground">
          <em>💡 Click &quot;Continue conversation&quot; or type below to provide more details</em>
        </div>
      </CardContent>
    </Card>
  );
}
