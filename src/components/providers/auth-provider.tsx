'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import { getSiteUrl } from '@/lib/utils/redirect-utils'

// We'll use the standard Supabase client for all operations

type AuthContextType = {
  user: User | null
  session: Session | null
  loading: boolean
  isNewUser: boolean
  setIsNewUser: (value: boolean) => void
  signUp: (email: string, password: string, name: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  signIn: (email: string, password: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  signInWithGoogle: () => Promise<{
    error: Error | null
    success: boolean
    data?: { url?: string }
  }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  completeOnboarding: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Add this useAuth hook export
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Use Broadcast Channel for cross-tab communication
let authChannel: BroadcastChannel | null = null;

if (typeof window !== 'undefined') {
  authChannel = new BroadcastChannel('supabase-auth');
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isNewUser, setIsNewUser] = useState(false)
  const [envVarsAvailable, setEnvVarsAvailable] = useState(true)
  const router = useRouter()

  // Define getInitialSession outside of useEffect
  const getInitialSession = async () => {
    try {
      console.log('[AuthProvider] Getting initial session...');

      // First try to get session from localStorage directly (much faster)
      let hasStoredSession = false;
      try {
        const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
        const storedAuth = localStorage.getItem(authKey);
        hasStoredSession = !!storedAuth;
        console.log('[AuthProvider] Found stored session:', hasStoredSession);
      } catch (e) {
        console.warn('[AuthProvider] Error checking localStorage:', e);
      }

      // If no stored session, skip the slow getSession call
      if (!hasStoredSession) {
        console.log('[AuthProvider] No stored session found, proceeding without auth');
        setSession(null);
        setUser(null);
        setLoading(false);
        return;
      }

      // Try to parse session from localStorage as backup plan
      let localSession: Session | null = null;
      try {
        const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
        const storedAuth = localStorage.getItem(authKey);
        if (storedAuth) {
          const parsed = JSON.parse(storedAuth);
          if (parsed?.currentSession?.user) {
            localSession = parsed.currentSession as Session;
          }
        }
      } catch (e) {
        console.warn('[AuthProvider] Error parsing localStorage session:', e);
      }

      let session: Session | null = null;
      let error: Error | null = null;

      // Try to get session from Supabase with timeout
      try {
        // Use Promise.race with a timeout (5 seconds)
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session retrieval timeout')), 15000) // Increased from 5000ms to 15000ms
        );

        const result = await Promise.race([
          sessionPromise,
          timeoutPromise
        ]);

        const sessionData = result as { data: { session: Session | null }, error: Error | null };
        session = sessionData.data.session;
        error = sessionData.error;
      } catch (sessionError) {
        console.warn('[AuthProvider] Session retrieval error:', sessionError);
        error = sessionError as Error;
      }

      // Handle session results with fallback to localSession
      if (error) {
        console.warn('[AuthProvider] Using local session fallback due to error:', error);
        if (localSession?.user) {
          // Check if the local session hasn't expired
          const now = Math.floor(Date.now() / 1000);
          const sessionExpiry = localSession.expires_at;

          if (sessionExpiry && sessionExpiry > now) {
            console.log('[AuthProvider] Using valid local session as fallback');
            setSession(localSession);
            setUser(localSession.user);

            // Trigger a background refresh after using the local session
            setTimeout(() => {
              supabase.auth.refreshSession().catch(e =>
                console.warn('[AuthProvider] Background session refresh failed:', e)
              );
            }, 2000);
          } else {
            console.log('[AuthProvider] Local session is expired, signing out');
            setSession(null);
            setUser(null);
            // Clear expired session data in background
            setTimeout(() => supabase.auth.signOut(), 100);
          }
        } else {
          setSession(null);
          setUser(null);
        }
      } else if (session) {
        console.log('[AuthProvider] Session retrieved successfully');

        // Quick expiration check
        const now = Math.floor(Date.now() / 1000);
        if (session.expires_at && session.expires_at < now) {
          console.log('[AuthProvider] Session expired, clearing');
          setSession(null);
          setUser(null);
          // Don't await this - let it happen in background
          supabase.auth.signOut().catch(console.warn);
        } else {
          setSession(session);
          setUser(session.user);
        }
      } else {
        setSession(null);
        setUser(null);
      }
    } catch (error) {
      console.warn('[AuthProvider] Session retrieval failed:', error);
      // On any error, just proceed without session
      setSession(null);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if environment variables are available
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn('Supabase URL or Anon Key is missing. Check your environment variables.')
      setEnvVarsAvailable(false)
      setLoading(false)
      return
    }

    // Verify local storage is working
    try {
      localStorage.setItem('auth_test', 'test');
      const testValue = localStorage.getItem('auth_test');
      localStorage.removeItem('auth_test');

      if (testValue !== 'test') {
        console.warn('LocalStorage is not working properly, which may affect authentication');
      }
    } catch (e) {
      console.error('LocalStorage access error:', e);
      setEnvVarsAvailable(false);
      setLoading(false);
      return;
    }

    // Call the initial session getter on component mount
    getInitialSession();
  }, []);  // Add empty dependency array to prevent infinite updates

  // Add an effect to proactively refresh aging sessions
  useEffect(() => {
    // If no session, nothing to do
    if (!session) return;

    const supabaseInstance = supabase; // Capture for dependency

    // Check session age - refresh if more than 45 minutes old
    const sessionAgeCheck = () => {
      try {
        if (!session?.expires_at) return;

        const expiresAt = new Date(session.expires_at * 1000);
        const now = new Date();
        const timeLeft = expiresAt.getTime() - now.getTime();

        // If less than 15 minutes until expiry, refresh in background
        if (timeLeft < 15 * 60 * 1000) {
          console.log('[AuthProvider] Session nearing expiry, refreshing in background');
          supabaseInstance.auth.refreshSession().catch(e =>
            console.warn('[AuthProvider] Background session refresh failed:', e)
          );
        }
      } catch (err) {
        console.error('[AuthProvider] Error in session age check:', err);
      }
    };

    // Run session age check on mount and every 5 minutes
    sessionAgeCheck();
    const intervalId = setInterval(sessionAgeCheck, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [session]); // The session dependency is fine here since we're checking its expiration

  // Listen for auth changes in a separate effect
  useEffect(() => {
    // Only set up listener if env vars are available
    if (!envVarsAvailable) return;

    // Store reference to supabase for cleanup
    const supabaseInstance = supabase;

    const { data: { subscription } } = supabaseInstance.auth.onAuthStateChange(async (event, newSession) => {
      // INFINITE LOOP FIX: Only update state if it actually changed
      setSession(currentSession => {
        if (currentSession?.access_token !== newSession?.access_token ||
            currentSession?.user?.id !== newSession?.user?.id) {
          return newSession;
        }
        return currentSession;
      });

      setUser(currentUser => {
        const newUser = newSession?.user ?? null;
        if (currentUser?.id !== newUser?.id) {
          return newUser;
        }
        return currentUser;
      });

      // Broadcast auth state change to other tabs
      if (authChannel) {
        authChannel.postMessage({ type: 'auth_state_change', event, session: newSession });
      }

        // Force refresh server-side rendering data
        if (event === 'SIGNED_IN') {
          router.refresh()

          // Check if this is a new user by looking at their metadata
          if (newSession?.user) {
            try {
              // Start profile fetch immediately with a timeout
              const profilePromise = supabase
                .from('profiles')
                .select('*')
                .eq('id', newSession.user.id)
                .maybeSingle();

              // Set a timeout to navigate early if profile fetch is taking too long
              const timeoutPromise = new Promise<null>((resolve) => {
                setTimeout(() => {
                  // Navigate to dashboard anyway after timeout
                  if (window.location.pathname === '/login') {
                    // Check for redirect parameter
                    const urlParams = new URLSearchParams(window.location.search);
                    const redirectTo = urlParams.get('redirect');

                    if (redirectTo) {
                      try {
                        const decodedRedirect = decodeURIComponent(redirectTo);

                        // Enhanced safety checks to prevent redirect loops
                        if (
                          decodedRedirect.includes('login') ||
                          decodedRedirect.includes('register') ||
                          decodedRedirect.includes('auth/callback') ||
                          decodedRedirect === window.location.pathname
                        ) {
                          console.warn('Prevented redirect loop in timeout, redirecting to dashboard');
                          router.push('/dashboard');
                        } else {
                          router.push(decodedRedirect);
                        }
                      } catch (error) {
                        console.error('Error decoding redirect URL:', error);
                        router.push('/dashboard');
                      }
                    } else {
                      router.push('/dashboard');
                    }
                  }
                  resolve(null);
                }, 500); // Navigate after 500ms if profile fetch is slow
              });

              // Race between profile fetch and timeout
              const { data } = await Promise.race([
                profilePromise,
                timeoutPromise.then(() => profilePromise) // Still keep the profile promise running
              ]);

              // Check if user is new or existing
              if (data) {
                const createdAt = new Date(data.created_at);
                const now = new Date();
                const isRecentlyCreated = (now.getTime() - createdAt.getTime()) < 60000; // 1 minute
                setIsNewUser(isRecentlyCreated);

                // Navigate if we haven't navigated already from the timeout
                if (window.location.pathname === '/login') {
                  // Check for redirect parameter
                  const urlParams = new URLSearchParams(window.location.search);
                  const redirectTo = urlParams.get('redirect');

                  if (redirectTo) {
                    try {
                      const decodedRedirect = decodeURIComponent(redirectTo);

                      // Enhanced safety checks to prevent redirect loops
                      if (
                        decodedRedirect.includes('login') ||
                        decodedRedirect.includes('register') ||
                        decodedRedirect.includes('auth/callback') ||
                        decodedRedirect === window.location.pathname
                      ) {
                        console.warn('Prevented redirect loop in AuthProvider, redirecting to dashboard');
                        if (isRecentlyCreated) {
                          router.push('/dashboard?onboarding=true');
                        } else {
                          router.push('/dashboard');
                        }
                      } else {
                        if (isRecentlyCreated) {
                          // For new users, append onboarding parameter if not already a simulate page
                          const targetUrl = decodedRedirect.includes('/simulate')
                            ? decodedRedirect
                            : '/dashboard?onboarding=true';
                          router.push(targetUrl);
                        } else {
                          router.push(decodedRedirect);
                        }
                      }
                    } catch (error) {
                      console.error('Error decoding redirect URL:', error);
                      if (isRecentlyCreated) {
                        router.push('/dashboard?onboarding=true');
                      } else {
                        router.push('/dashboard');
                      }
                    }
                  } else {
                    if (isRecentlyCreated) {
                      router.push('/dashboard?onboarding=true');
                    } else {
                      router.push('/dashboard');
                    }
                  }
                }
              } else {
                // No profile found, treat as a new user
                setIsNewUser(true);

                // Profile will be created by the trigger or API call
                // No need to create it here since it's handled in signUp

                // Navigate if we haven't navigated already from the timeout
                if (window.location.pathname === '/login') {
                  // Check for redirect parameter
                  const urlParams = new URLSearchParams(window.location.search);
                  const redirectTo = urlParams.get('redirect');

                  if (redirectTo) {
                    try {
                      const decodedRedirect = decodeURIComponent(redirectTo);

                      // Enhanced safety checks to prevent redirect loops
                      if (
                        decodedRedirect.includes('login') ||
                        decodedRedirect.includes('register') ||
                        decodedRedirect.includes('auth/callback') ||
                        decodedRedirect === window.location.pathname
                      ) {
                        console.warn('Prevented redirect loop in new user flow, redirecting to dashboard');
                        router.push('/dashboard?onboarding=true');
                      } else {
                        router.push(decodedRedirect);
                      }
                    } catch (error) {
                      console.error('Error decoding redirect URL:', error);
                      router.push('/dashboard?onboarding=true');
                    }
                  } else {
                    router.push('/dashboard?onboarding=true');
                  }
                }
              }
            } catch (error) {
              console.error('Error during profile check:', error);
              // Still navigate to dashboard in case of error
              if (window.location.pathname === '/login') {
                router.push('/dashboard');
              }
            }
          }
        }

        setLoading(false);

        // Auth state changed, no additional actions needed
      });

      // Listen for auth state changes from other tabs
      if (authChannel) {
        authChannel.onmessage = (event) => {
          if (event.data.type === 'auth_state_change') {
            console.log('[Website AuthProvider] Received auth state from other tab:', event.data.event);
            // Update session and user state based on the broadcasted data
            setSession(event.data.session);
            setUser(event.data.session?.user ?? null);
          }
        };
      }

      return () => {
        subscription.unsubscribe();
        if (authChannel) {
          authChannel.onmessage = null; // Clean up listener
        }
      };

    return () => subscription?.unsubscribe();
  }, [router, envVarsAvailable]);

  // Minimal visibility change listener - only for essential coordination
  useEffect(() => {
    let lastVisibilityCheck = 0;

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        // Very aggressive rate limiting - only check once per minute
        const now = Date.now();
        if (now - lastVisibilityCheck < 60000) { // 60 second minimum between checks
          console.log('[AuthProvider] Skipping visibility check - too recent');
          return;
        }
        lastVisibilityCheck = now;

        // Skip session check for most pages to prevent slowdowns
        // In production, we're more lenient to avoid logout issues
        if (window.location.pathname.includes('/simulate') ||
            window.location.pathname.includes('/login') ||
            window.location.pathname.includes('/register') ||
            window.location.pathname.includes('/dashboard') ||
            window.location.pathname.includes('/polls') ||
            window.location.hostname.endsWith('pollgpt.com')) { // More lenient in production
          console.log('[AuthProvider] Skipping session check for current page');
          return;
        }

        console.log('[AuthProvider] Tab became visible, minimal session check...');

        try {
          // Let's only check localStorage instead of making a network request
          // This is much more efficient and less likely to cause session timeouts
          let isValid = false;
          try {
            const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
            const storedAuth = localStorage.getItem(authKey);
            if (storedAuth) {
              const parsed = JSON.parse(storedAuth);
              if (parsed?.currentSession?.expires_at) {
                const expiresAt = parsed.currentSession.expires_at;
                const now = Math.floor(Date.now() / 1000);
                isValid = expiresAt > now;
              }
            }
          } catch (e) {
            console.warn('[AuthProvider] Error checking localStorage session:', e);
          }

          // Just broadcast the status - no network request needed
          if (authChannel) {
            authChannel.postMessage({
              type: 'session_validation_complete',
              sessionValid: isValid,
              timestamp: Date.now()
            });
          }
        } catch (err) {
          console.warn('[AuthProvider] Error in visibility check:', err);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [session]); // Only depend on session, not router

  // Add proactive session refresh for active sessions
  useEffect(() => {
    if (!session) return;

    // Function to check session expiry and refresh if needed
    const checkAndRefreshSession = async () => {
      try {
        if (!session?.expires_at) return;

        const expiresAt = new Date(session.expires_at * 1000);
        const now = new Date();
        const timeUntilExpiry = expiresAt.getTime() - now.getTime();

        // If less than 15 minutes until expiry, refresh in background
        if (timeUntilExpiry < 15 * 60 * 1000) {
          console.log('[AuthProvider] Session nearing expiry, refreshing in background');
          await supabase.auth.refreshSession()
            .catch(e => console.warn('[AuthProvider] Background session refresh failed:', e));
        }
      } catch (err) {
        console.error('[AuthProvider] Error in session refresh check:', err);
      }
    };

    // Run check immediately and set up interval
    checkAndRefreshSession();
    const intervalId = setInterval(checkAndRefreshSession, 5 * 60 * 1000); // Check every 5 minutes

    return () => clearInterval(intervalId);
  }, [session]);

  const signUp = async (email: string, password: string, name: string) => {
    try {
      // Get the site URL from utility function
      const siteUrl = getSiteUrl();

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
          emailRedirectTo: `${siteUrl}/auth/callback`,
        },
      });

      if (error) {
        console.error('Error during sign up:', error);
        return { error, success: false };
      }

      // If user is created, create profile via API endpoint
      if (data?.user) {
        try {
          const profileResponse = await fetch('/api/auth/create-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: data.user.id,
              username: name,
              fullName: name,
              email: email,
            }),
          });

          if (!profileResponse.ok) {
            const errorData = await profileResponse.json();
            console.error('Profile creation error:', errorData);
            // Don't fail the signup if profile creation fails
            // The user can still sign in and the profile will be created later
          }
        } catch (profileError) {
          console.error('Error creating profile:', profileError);
          // Don't fail the signup if profile creation fails
        }
      }

      return { error: null, success: true };
    } catch (error) {
      console.error('Error during sign up:', error);
      return { error: error as Error, success: false };
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      return { error: null, success: true }
    } catch (error) {
      console.error('Error during sign in:', error)
      return { error: error as Error, success: false }
    }
  }

  const signInWithGoogle = async () => {
    try {
      // Determine the current origin for proper redirect handling
      const currentOrigin = typeof window !== 'undefined' ? window.location.origin : 
                           (process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000');
      
      // Explicitly construct the callback URL using the current origin
      const redirectTo = `${currentOrigin}/auth/callback`;
      
      console.log('[AuthProvider] Using redirect URL:', redirectTo);

      // Set up the OAuth options with explicit scopes and redirect
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
          scopes: 'email profile',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('Error during Google sign in:', error);
        return { error, success: false };
      }

      return { error: null, success: true, data };
    } catch (error) {
      console.error('Error during Google sign in:', error);
      return { error: error as Error, success: false };
    }
  }

  const signOut = async () => {
    try {
      console.log('[AuthProvider] Signing out user...');

      // First clear session and user state in our context
      setSession(null);
      setUser(null);

      // Clear any additional auth-related data from localStorage
      if (typeof window !== 'undefined') {
        try {
          // Clear Supabase-specific items from localStorage
          const storageKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
          localStorage.removeItem(storageKey);

          // Clear any other auth-related items
          localStorage.removeItem('supabase.auth.token');
          localStorage.removeItem('supabase.auth.expires_at');
          localStorage.removeItem('supabase.auth.refresh_token');

          // Clear cookies by setting expired date
          document.cookie.split(';').forEach(cookie => {
            const [name] = cookie.trim().split('=');
            if (name.includes('supabase') || name.includes('auth')) {
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            }
          });
        } catch (e) {
          console.error('[AuthProvider] Error clearing local storage during logout:', e);
        }
      }

      // Broadcast logout to other tabs
      if (authChannel) {
        authChannel.postMessage({ type: 'auth_state_change', event: 'SIGNED_OUT', session: null });
      }

      // Call Supabase signOut AFTER clearing local state
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[AuthProvider] Error during sign out:', error);
        // Continue with redirect even if there's an error
      }

      console.log('[AuthProvider] User signed out successfully');

      // Use window.location for a full page redirect instead of Next.js router
      // This avoids RSC/client component navigation issues
      window.location.href = '/login';
    } catch (error) {
      console.error('[AuthProvider] Unexpected error during sign out:', error);
      // Still try to redirect even if there was an error
      window.location.href = '/login';
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) throw error

      return { error: null, success: true }
    } catch (error) {
      console.error('Error during password reset:', error)
      return { error: error as Error, success: false }
    }
  }

  const completeOnboarding = () => {
    setIsNewUser(false);
  };

  const value = {
    user,
    session,
    loading,
    isNewUser,
    setIsNewUser,
    signUp,
    signIn,
    signOut,
    resetPassword,
    completeOnboarding,
    signInWithGoogle,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}