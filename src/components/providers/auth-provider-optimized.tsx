'use client'

import React, { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react'
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { supabase, restoreSession } from '@/lib/supabase'
import type { Session, User } from '@supabase/supabase-js'

// Auth query keys
const authKeys = {
  session: () => ['auth', 'session'] as const,
  user: () => ['auth', 'user'] as const,
  profile: () => ['auth', 'profile'] as const,
}

// Global user data store
let globalUserData: { id: string | null; email: string | null } = { id: null, email: null }

const setUserData = (id: string, email: string | null) => {
  globalUserData = { id, email }
}

const clearUserData = () => {
  globalUserData = { id: null, email: null }
}

// Session restoration state
let sessionRestorationPromise: Promise<boolean> | null = null
let sessionRestorationComplete = false

// Session validity checker
function isSessionValid(session: Session | null): boolean {
  if (!session) return false
  const now = Date.now() / 1000
  return session.expires_at ? session.expires_at > now : true
}

// Strategic session fetcher with restoration
async function getSession(): Promise<Session | null> {
  try {
    // Ensure session restoration is complete before proceeding
    if (!sessionRestorationComplete) {
      if (!sessionRestorationPromise) {
        sessionRestorationPromise = restoreSession()
      }
      await sessionRestorationPromise
      sessionRestorationComplete = true
    }

    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Session fetch error:', error)
      return null
    }

    return data.session
  } catch (error) {
    console.error('Session fetch exception:', error)
    return null
  }
}

// Strategic user fetcher (secure, for sensitive operations)
async function getUser(): Promise<User | null> {
  try {
    const { data, error } = await supabase.auth.getUser()

    if (error) {
      console.error('User fetch error:', error)
      return null
    }

    return data.user
  } catch (error) {
    console.error('User fetch exception:', error)
    return null
  }
}

// User profile fetcher
async function getUserProfile(userId: string): Promise<Record<string, unknown> | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()

    if (error) {
      console.error('Profile fetch error:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Profile fetch exception:', error)
    return null
  }
}

interface AuthContextType {
  // Core auth state
  session: Session | null
  user: User | null
  profile: Record<string, unknown> | null
  isAuthenticated: boolean

  // Instant access (sync)
  userId: string | null
  userEmail: string | null

  // Loading states
  isLoading: boolean
  isLoadingProfile: boolean
  isRestoringSession: boolean

  // Error states
  error: Error | null
  profileError: Error | null

  // Strategic data access
  getAuthData: (operation?: 'read' | 'write' | 'sensitive') => {
    user: User | null
    isLoading: boolean
    error: Error | null
  }

  // Utility functions
  refetch: () => void
  refetchProfile: () => void
  signOut: () => Promise<void>
  refreshSession: () => Promise<Session | null>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
  enablePerformanceOptimization?: boolean
}

export function AuthProvider({
  children,
  enablePerformanceOptimization = true
}: AuthProviderProps) {
  const queryClient = useQueryClient()
  const [isRestoringSession, setIsRestoringSession] = useState(!sessionRestorationComplete)
  const authStateChangeHandled = useRef(false)

  // Restore session on mount if not already done
  useEffect(() => {
    if (!sessionRestorationComplete && !sessionRestorationPromise) {
      setIsRestoringSession(true)
      sessionRestorationPromise = restoreSession()
      sessionRestorationPromise?.finally(() => {
        sessionRestorationComplete = true
        setIsRestoringSession(false)
      })
    } else if (sessionRestorationComplete) {
      setIsRestoringSession(false)
    }
  }, [])

  // Main session query (fast, for general use)
  const { data: session, isLoading, error } = useQuery({
    queryKey: authKeys.session(),
    queryFn: getSession,
    enabled: sessionRestorationComplete, // Only run after session restoration
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  })

  // User query for sensitive operations
  const { data: secureUser, isLoading: isSecureUserLoading, error: secureUserError } = useQuery({
    queryKey: authKeys.user(),
    queryFn: getUser,
    enabled: !!session && sessionRestorationComplete,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  })

  // User profile query - only runs when we have a session
  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError
  } = useQuery({
    queryKey: authKeys.profile(),
    queryFn: () => getUserProfile(session?.user?.id || ''),
    enabled: !!session?.user?.id && sessionRestorationComplete,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  })

  // Update global user data when session changes
  useEffect(() => {
    if (session?.user) {
      setUserData(session.user.id, session.user.email || null)
    } else {
      clearUserData()
    }
  }, [session?.user])

  // Strategic data access function
  const getAuthData = (operation: 'read' | 'write' | 'sensitive' = 'read') => {
    if (operation === 'sensitive') {
      return {
        user: secureUser || null,
        isLoading: isSecureUserLoading,
        error: secureUserError,
      }
    }

    return {
      user: session?.user || null,
      isLoading: isLoading,
      error: error,
    }
  }

  // Sign out mutation with cleanup
  const signOutMutation = useMutation({
    mutationFn: async () => {
      clearUserData()
      sessionRestorationComplete = false
      sessionRestorationPromise = null
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.clear()
    },
  })

  // Refresh session mutation
  const refreshSessionMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.auth.refreshSession()
      if (error) throw error
      return data.session
    },
    onSuccess: (newSession) => {
      queryClient.setQueryData(authKeys.session(), newSession)
      if (newSession?.user) {
        setUserData(newSession.user.id, newSession.user.email || null)
      }
    },
  })

  // Auth state change listener with proper session restoration handling
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Only handle auth state changes after initial session restoration
        if (!sessionRestorationComplete) {
          console.log('Auth state change ignored - session restoration not complete:', event)
          return
        }

        console.log('Auth state changed:', event, session?.user?.id)

        // Update the session cache
        queryClient.setQueryData(authKeys.session(), session)

        // Update global user data
        if (session?.user) {
          setUserData(session.user.id, session.user.email || null)
        } else {
          clearUserData()
        }

        // Clear profile cache on sign out
        if (event === 'SIGNED_OUT') {
          queryClient.removeQueries({ queryKey: authKeys.profile() })
        }

        // Invalidate profile and user cache on sign in
        if (event === 'SIGNED_IN' && session?.user?.id) {
          queryClient.invalidateQueries({ queryKey: authKeys.profile() })
          queryClient.invalidateQueries({ queryKey: authKeys.user() })
        }

        authStateChangeHandled.current = true
      }
    )

    return () => subscription.unsubscribe()
  }, [queryClient])

  // Background session refresh with strategic validation
  useEffect(() => {
    if (!session || !sessionRestorationComplete) return

    const interval = setInterval(async () => {
      if (session && isSessionValid(session)) {
        await refreshSessionMutation.mutateAsync()
      }
    }, 30 * 60 * 1000) // Refresh every 30 minutes

    return () => clearInterval(interval)
  }, [session, refreshSessionMutation])

  // Enhanced performance monitoring
  useEffect(() => {
    if (!enablePerformanceOptimization) return

    const interval = setInterval(() => {
      const memoryUsage = (performance as { memory?: { usedJSHeapSize?: number } })?.memory?.usedJSHeapSize || 0
      const memoryMB = memoryUsage / (1024 * 1024)

      if (memoryUsage > 150 * 1024 * 1024) {
        console.warn(`🚨 High memory usage detected: ${memoryMB.toFixed(1)}MB in AuthProvider`)
      }
    }, 5 * 60000) // Check every 5 minutes

    return () => clearInterval(interval)
  }, [enablePerformanceOptimization])

  // Context value
  const contextValue: AuthContextType = {
    // Core auth state
    session: session || null,
    user: session?.user || null,
    profile: profile || null,
    isAuthenticated: !!session?.user && sessionRestorationComplete,

    // Instant access
    userId: globalUserData.id,
    userEmail: globalUserData.email,

    // Loading states
    isLoading: isLoading || isRestoringSession,
    isLoadingProfile,
    isRestoringSession,

    // Error states
    error,
    profileError,

    // Strategic data access
    getAuthData,

    // Utility functions
    refetch: () => queryClient.invalidateQueries({ queryKey: authKeys.session() }),
    refetchProfile: () => queryClient.invalidateQueries({ queryKey: authKeys.profile() }),
    signOut: signOutMutation.mutateAsync,
    refreshSession: refreshSessionMutation.mutateAsync,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Enhanced hooks for different use cases
export function useAuth() {
  const context = useAuthContext()
  return {
    session: context.session,
    user: context.user,
    isAuthenticated: context.isAuthenticated,
    isLoading: context.isLoading,
    signOut: context.signOut,
  }
}

export function useAuthWithProfile() {
  const context = useAuthContext()
  return {
    session: context.session,
    user: context.user,
    profile: context.profile,
    isAuthenticated: context.isAuthenticated,
    isLoading: context.isLoading || context.isLoadingProfile,
    isLoadingProfile: context.isLoadingProfile,
    profileError: context.profileError,
    refetchProfile: context.refetchProfile,
    signOut: context.signOut,
  }
}

export function useAuthSecure() {
  const context = useAuthContext()
  const authData = context.getAuthData('sensitive')

  return {
    user: authData.user,
    isLoading: authData.isLoading,
    error: authData.error,
    isAuthenticated: !!authData.user,
  }
}

// Export context and provider
export { AuthContext }
export default AuthProvider
