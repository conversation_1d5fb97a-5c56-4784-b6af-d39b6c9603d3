"use client";

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

export function AuthSuccessHandler() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const onboarding = searchParams.get('onboarding');
    const welcome = searchParams.get('welcome');
    const existingUser = searchParams.get('existing_user');

    if (welcome === 'true') {
      toast.success('Welcome to PollGPT! Your Google account has been connected successfully.', {
        duration: 4000,
      });
    } else if (onboarding === 'true') {
      toast.success('Successfully signed in with Google!', {
        duration: 3000,
      });
    } else if (existingUser === 'true') {
      toast.info('Welcome back! You already have an account with this Google email.', {
        duration: 3000,
      });
    }

    // Clean up URL parameters
    if (onboarding || welcome || existingUser) {
      const url = new URL(window.location.href);
      url.searchParams.delete('onboarding');
      url.searchParams.delete('welcome');
      url.searchParams.delete('existing_user');
      window.history.replaceState({}, '', url.pathname);
    }
  }, [searchParams]);

  return null; // This component doesn't render anything
}
