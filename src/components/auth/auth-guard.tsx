"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider-optimized";
import { clearCorruptedSession } from "@/lib/utils/auth-refresh";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";

interface AuthGuardProps {
  redirectUrl?: string;
  children: React.ReactNode;
  pollId?: string;
}

export default function AuthGuard({ redirectUrl = '/login', children, pollId }: AuthGuardProps) {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [loadingTime, setLoadingTime] = useState(0);
  const [showCleanupOption, setShowCleanupOption] = useState(false);

  // Track loading time with optimized approach
  useEffect(() => {
    if (isLoading) {
      const startTime = Date.now();

      // Update loading time more frequently for better responsiveness
      const updateTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        setLoadingTime(elapsed);
      }, 500); // Reduced from 1000ms to 500ms for more responsive updates

      // Show cleanup option sooner (after 4 seconds instead of 6)
      const cleanupTimer = setTimeout(() => {
        setShowCleanupOption(true);
      }, 4000);

      return () => {
        clearInterval(updateTimer);
        clearTimeout(cleanupTimer);
      };
    } else {
      // Reset state immediately when not loading
      setLoadingTime(0);
      setShowCleanupOption(false);
    }
  }, [isLoading]);

  useEffect(() => {
    // Skip if still loading
    if (isLoading) return;

    // Check if user is authenticated
    if (!user) {
      // Use a memoized check to prevent infinite redirect loops
      const isLoginPage = typeof window !== 'undefined' &&
        window.location.pathname.includes('login');

      if (!isLoginPage) {
        // Build the redirect URL with the return path - preserve search parameters
        const returnPath = pollId
          ? `/dashboard/polls/${pollId}/simulate`
          : typeof window !== 'undefined' ? window.location.pathname + window.location.search : '';

        const returnUrl = encodeURIComponent(returnPath);

        // Use replace instead of push for faster navigation without adding to history
        router.replace(`${redirectUrl}?redirect=${returnUrl}`);
      }
    }
  }, [user, isLoading, router, redirectUrl, pollId]);

  const handleSessionCleanup = async () => {
    try {
      await clearCorruptedSession();
      // Redirect to login after cleanup
      window.location.href = '/login';
    } catch (error) {
      console.error('Error during session cleanup:', error);
      // Still redirect to login
      window.location.href = '/login';
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="text-center space-y-6">
          <Loader
            variant="spinner"
            size="xl"
            centered={true}
            text={loadingTime > 2 ? `Loading... (${loadingTime}s)` : "Loading"}
          />

          {loadingTime > 3 && (
            <p className="text-sm text-muted-foreground">
              Just a moment while we prepare your dashboard...
            </p>
          )}

          {showCleanupOption && (
            <div className="space-y-2 pt-4 border-t border-border mt-4">
              <p className="text-xs text-muted-foreground">
                Having trouble? Try resetting your session data.
              </p>
              <Button
                onClick={handleSessionCleanup}
                variant="secondary"
                size="sm"
                className="w-full mt-1"
              >
                Reset Session Data
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50 ">
        <div className=" rounded-lg p-6 shadow-lg max-w-sm w-full mx-auto ">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className="w-3 h-3 bg-primary rounded-full animate-bounce"
                  style={{ animationDelay: `${i * 0.15}s`, animationDuration: '0.6s' }}
                />
              ))}
            </div>
            <p className="text-primary font-medium">Redirecting to login...</p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
