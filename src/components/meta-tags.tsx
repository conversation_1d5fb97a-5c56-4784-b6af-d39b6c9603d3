// src/components/meta-tags.tsx
// Updated to use React components for inline meta tags instead of Head
// for compatibility with Next.js App Router

interface CanonicalProps {
  url?: string;
}

/**
 * CanonicalTag component adds canonical URL tag to your pages
 * This helps search engines understand the preferred version of your page
 * Note: In App Router, canonical URLs should be defined in layout.tsx or page.tsx metadata
 * This component is for client components that need to add canonical tags
 */
export function CanonicalTag({ url }: CanonicalProps) {
  const canonicalUrl = url || 'https://pollgpt.com';

  return (
    <>
      {/* These will be applied client-side only */}
      <link rel="canonical" href={canonicalUrl} key="canonical" />
      {/* Additional alternates for SEO to associate "poll gpt" with your site */}
      <link rel="alternate" href={`${canonicalUrl}?ref=poll-gpt`} hrefLang="en" />
    </>
  );
}

/**
 * AdditionalMeta adds extra meta tags to help with SEO for variations of your name
 */
export function AdditionalMeta() {
  return (
    <>
      <meta name="keywords" content="pollgpt, poll gpt, ai polling, survey tools, poll creator, ai survey, poll generator" />
      <meta name="news_keywords" content="pollgpt, poll gpt, ai polling, gpt for polls" />
    </>
  );
}
