"use client";

import * as React from 'react';
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useQuery } from '@tanstack/react-query';
import { Tip, Goal, Achievement, ResourceCategory } from '@/lib/services/tips-resources';
import { getPersonalizedTips, getCurrentGoals, getRecentAchievements, getResourceCategories } from '@/lib/services/tips-resources';

export function EnhancedTipsResources() {
  // Use React Query for tips
  const { 
    data: tips = [], 
    isLoading: tipsLoading 
  } = useQuery<Tip[], Error>({
    queryKey: ['tips'],
    queryFn: getPersonalizedTips,
    staleTime: 1000 * 60 * 5, // 5 minutes
    placeholderData: [],
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Use React Query for goals
  const { 
    data: goals = [], 
    isLoading: goalsLoading 
  } = useQuery<Goal[], Error>({
    queryKey: ['goals'],
    queryFn: getCurrentGoals,
    staleTime: 1000 * 60 * 5, // 5 minutes
    placeholderData: [],
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Use React Query for achievements
  const { 
    data: achievements = [], 
    isLoading: achievementsLoading 
  } = useQuery<Achievement[], Error>({
    queryKey: ['achievements'],
    queryFn: getRecentAchievements,
    staleTime: 1000 * 60 * 5, // 5 minutes
    placeholderData: [],
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Use React Query for resources
  const { 
    data: resources = [], 
    isLoading: resourcesLoading 
  } = useQuery<ResourceCategory[], Error>({
    queryKey: ['resources'],
    queryFn: getResourceCategories,
    staleTime: 1000 * 60 * 15, // 15 minutes
    placeholderData: [],
    retry: 2,
    refetchOnWindowFocus: false,
  });

  const isLoading = tipsLoading || goalsLoading || achievementsLoading || resourcesLoading;

  if (isLoading) {
    return (
      <Card className="p-6 md:p-8">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-primary rounded-full animate-pulse"></div>
            <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 md:p-8 hover:shadow-md transition-shadow duration-300 border-muted/50">
      <div className="flex items-center justify-between mb-6 pb-2 border-b">
        <h3 className="text-xl font-medium flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
            <circle cx="12" cy="12" r="10" />
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
            <path d="M12 17h.01" />
          </svg>
          <span>Tips & Resources</span>
        </h3>
      </div>
      <Tabs defaultValue="tips" className="w-full">
        <TabsList className="flex w-full bg-muted/30 p-1 rounded-lg overflow-x-auto">
          <TabsTrigger value="tips" className="flex-1 text-xs font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm whitespace-nowrap">
            💡 Tips
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex-1 text-xs font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm whitespace-nowrap">
            🎯 Goals
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex-1 text-xs font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm whitespace-nowrap">
            🏆 Achievements
          </TabsTrigger>
          <TabsTrigger value="resources" className="flex-1 text-xs font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm whitespace-nowrap">
            📚 Resources
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tips" className="space-y-3 mt-6">
          {tips.filter(tip => tip.isPersonalized).length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Personalized For You</span>
              </div>
              {tips.filter(tip => tip.isPersonalized).map((tip, index) => (
                <motion.div key={tip.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }}>
                  <Card className={`p-4 rounded-lg border bg-gradient-to-br ${tip.gradient} text-white hover:shadow-lg transition-shadow`}>
                    <div className="flex items-start gap-3">
                      <div className="text-2xl opacity-80">{tip.icon}</div>
                      <div className="flex-1">
                        <h4 className="text-sm font-semibold">{tip.title}</h4>
                        <p className="text-xs opacity-90 mt-1">{tip.description}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
          {tips.filter(tip => !tip.isPersonalized).length > 0 && (
            <div className="space-y-3 pt-4">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>General Tips</span>
              </div>
              {tips.filter(tip => !tip.isPersonalized).map((tip, index) => (
                <motion.div key={tip.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }}>
                  <Card className="p-3 rounded-md border bg-background/50 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="text-xl text-muted-foreground">{tip.icon}</div>
                      <div>
                        <h4 className="text-sm font-medium">{tip.title}</h4>
                        <p className="text-xs text-muted-foreground mt-1">{tip.description}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="goals" className="space-y-4 mt-6">
          {goals.filter(goal => goal.current < goal.target).length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>Active Goals</span>
              </div>
              {goals.filter(goal => goal.current < goal.target).map((goal, index) => (
                <motion.div key={goal.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }}>
                  <Card className="p-4 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{goal.icon}</div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="text-sm font-medium">{goal.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{goal.description}</p>
                          </div>
                          <Badge variant="outline" className="text-xs capitalize">{goal.period}</Badge>
                        </div>
                        <div className="mt-3 flex items-center gap-3">
                          <Progress value={(goal.current / goal.target) * 100} className="flex-1 h-2" />
                          <span className="text-xs font-medium text-muted-foreground">
                            {goal.current} / {goal.target} {goal.unit}s
                          </span>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
          {goals.filter(goal => goal.current >= goal.target).length > 0 && (
            <div className="space-y-3 pt-4">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Completed Goals</span>
              </div>
              {goals.filter(goal => goal.current >= goal.target).map((goal, index) => (
                <motion.div key={goal.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }}>
                  <Card className="p-4 rounded-lg border bg-muted/30 opacity-70">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">✅</div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-muted-foreground line-through">{goal.title}</h4>
                        <p className="text-xs text-muted-foreground mt-1">Completed!</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="achievements" className="space-y-4 mt-6">
          {achievements.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="font-medium">Recent Achievements</span>
              </div>
              {achievements.sort((a, b) => new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime()).map((achievement, index) => (
                <motion.div key={achievement.id} initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} transition={{ delay: index * 0.1 }} className={`p-4 rounded-lg border bg-gradient-to-br overflow-hidden relative hover:shadow-sm transition-shadow ${achievement.rarity === 'legendary' ? 'from-yellow-50 to-orange-50 border-yellow-200' : achievement.rarity === 'epic' ? 'from-purple-50 to-pink-50 border-purple-200' : achievement.rarity === 'rare' ? 'from-blue-50 to-indigo-50 border-blue-200' : 'from-green-50 to-emerald-50 border-green-200'}`}>
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium">{achievement.title}</h4>
                        <Badge variant="outline" className={`text-xs ${achievement.rarity === 'legendary' ? 'bg-yellow-100 border-yellow-300 text-yellow-700' : achievement.rarity === 'epic' ? 'bg-purple-100 border-purple-300 text-purple-700' : achievement.rarity === 'rare' ? 'bg-blue-100 border-blue-300 text-blue-700' : 'bg-green-100 border-green-300 text-green-700'}`}>
                          {achievement.rarity}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{achievement.description}</p>
                      <p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                        <span>🗓️</span>
                        Unlocked {new Date(achievement.unlockedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
          {achievements.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-2xl mb-2">🏆</div>
              <p className="text-sm">No achievements yet</p>
              <p className="text-xs mt-1">Start creating polls to unlock achievements!</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="resources" className="space-y-5 mt-6">
          {resources.map((category, categoryIndex) => (
            <motion.div key={category.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: categoryIndex * 0.1 }} className="space-y-3">
              <div className="flex items-center gap-2 mb-4 pb-2 border-b border-muted/30">
                <div className="text-lg">{category.icon}</div>
                <div>
                  <h4 className="text-sm font-medium">{category.title}</h4>
                  <p className="text-xs text-muted-foreground">{category.description}</p>
                </div>
                <div className="ml-auto">
                  <Badge variant="outline" className="text-xs">
                    {category.items.length} {category.items.length === 1 ? 'item' : 'items'}
                  </Badge>
                </div>
              </div>
              <div className="grid gap-2">
                {category.items.map((item, itemIndex) => (
                  <motion.div key={item.id} initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: (categoryIndex * 0.1) + (itemIndex * 0.05) }} className="p-3 rounded-md border bg-background/50 hover:bg-muted/50 transition-colors group cursor-pointer" onClick={() => item.url && window.open(item.url, '_blank')}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1 flex-wrap">
                          <h5 className="text-xs font-medium group-hover:text-primary transition-colors">
                            {item.title}
                          </h5>
                          <Badge variant="outline" className="text-xs">
                            {item.type}
                          </Badge>
                          {item.difficulty && (
                            <Badge variant="secondary" className={`text-xs ${item.difficulty === 'advanced' ? 'bg-red-100 text-red-700' : item.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-700' : 'bg-green-100 text-green-700'}`}>
                              {item.difficulty}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground leading-relaxed mb-2">
                          {item.description}
                        </p>
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                          {item.estimatedTime && (
                            <span className="flex items-center gap-1">
                              ⏱️ {item.estimatedTime}
                            </span>
                          )}
                          {item.tags.length > 0 && (
                            <div className="flex items-center gap-1">
                              <span>🏷️</span>
                              <span>{item.tags.slice(0, 2).join(', ')}{item.tags.length > 2 ? '...' : ''}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {item.url && (
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0 mt-0.5">
                          <path d="M7 17L17 7"></path>
                          <path d="M7 7h10v10"></path>
                        </svg>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </TabsContent>
      </Tabs>

      <div className="mt-6 pt-4 border-t">
        <Button variant="ghost" className="w-full justify-center text-muted-foreground hover:text-foreground gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
            <path d="M13.73 21a2 2 0 0 1-3.46 0" />
          </svg>
          Explore All Resources
        </Button>
      </div>
    </Card>
  );
}
