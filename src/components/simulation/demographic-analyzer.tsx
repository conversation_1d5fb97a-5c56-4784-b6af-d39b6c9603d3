import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Users, Brain, Target, TrendingUp, MapPin, Briefcase, RefreshCw } from 'lucide-react';
import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { toast } from 'sonner';

interface DemographicAnalyzerProps {
  pollQuestion: string;
  pollOptions: string[];
  onSimulationComplete?: (result: SimulationResponse | BatchSimulationResult) => void;
}

interface AnalysisResult {
  recommendedDemographics: Array<{
    id: string;
    name: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    expectedEngagement: number;
    keyCharacteristics: string[];
    predictedResponse: {
      primaryChoice: string;
      confidence: number;
      reasoning: string;
    };
    sampleSizeRecommendation: number;
  }>;
  demographicInsights: {
    targetAudienceMatch: number;
    coverageCompleteness: number;
    diversityScore: number;
    representativenessScore: number;
  };
  analysis: {
    potentialBiases: string[];
    samplingStrategy: string;
    expectedChallenges: string[];
  };
  strategicRecommendations: {
    minimalViableSet?: Array<{
      id: string;
      name: string;
      description: string;
      priority: string;
    }>;
    comprehensiveSet?: Array<{
      id: string;
      name: string;
      description: string;
      priority: string;
    }>;
    budgetOptimized?: Array<{
      id: string;
      name: string;
      description: string;
      priority: string;
    }>;
    [key: string]: Array<{
      id: string;
      name: string;
      description: string;
      priority: string;
    }> | undefined;
  };
}

export function DemographicAnalyzer({
  pollQuestion,
  pollOptions,
  onSimulationComplete
}: DemographicAnalyzerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isRunningSimulation, setIsRunningSimulation] = useState(false);
  const [analysisScope, setAnalysisScope] = useState('comprehensive');
  const [targetAudience, setTargetAudience] = useState('');
  const [geographicFocus, setGeographicFocus] = useState('');

  const handleDemographicAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/ai/analyze-demographics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollQuestion,
          pollOptions,
          targetAudience,
          analysisScope,
          geographicFocus,
          pollContext: `Poll with ${pollOptions.length} options targeting ${targetAudience || 'general audience'}`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisResult(data.analysis);
        toast.success('Demographic analysis completed');
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to analyze demographics');
      }
    } catch (error) {
      console.error('Error analyzing demographics:', error);
      toast.error('Error during demographic analysis');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleRunOptimalSimulation = async (demographicSet: 'minimal' | 'comprehensive' | 'budget') => {
    if (!analysisResult) return;

    setIsRunningSimulation(true);
    try {
      const selectedDemographics = analysisResult.strategicRecommendations[`${demographicSet}ViableSet`] ||
                                  analysisResult.strategicRecommendations[`${demographicSet}Set`] ||
                                  analysisResult.strategicRecommendations[`${demographicSet}Optimized`];

      const demographicRequests = selectedDemographics?.map((demObj) => {
        const demographic = analysisResult.recommendedDemographics.find((d) => d.id === demObj.id);
        return {
          group: demographic?.name || demObj.name,
          size: demographic?.sampleSizeRecommendation || 100
        };
      }) || [];

      const response = await fetch('/api/simulate-poll-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollQuestion,
          pollOptions,
          demographics: demographicRequests,
          responseFormat: 'distribution'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onSimulationComplete?.(result);
        toast.success(`${demographicSet} simulation completed successfully`);
      } else {
        throw new Error('Simulation failed');
      }
    } catch (error) {
      console.error('Error running simulation:', error);
      toast.error('Failed to run simulation');
    } finally {
      setIsRunningSimulation(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Demographic Analysis</h3>
          <Badge variant="outline" className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-none">
            AI-Powered
          </Badge>
        </div>
        <p className="text-muted-foreground">
          Intelligent demographic analysis to identify the most valuable audience segments for your poll.
        </p>
      </div>

      {/* Analysis Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Analysis Configuration
          </CardTitle>
          <CardDescription>
            Configure the demographic analysis parameters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Analysis Scope</label>
              <Select value={analysisScope} onValueChange={setAnalysisScope}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic Analysis</SelectItem>
                  <SelectItem value="standard">Standard Analysis</SelectItem>
                  <SelectItem value="comprehensive">Comprehensive Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Target Audience</label>
              <Select value={targetAudience} onValueChange={setTargetAudience}>
                <SelectTrigger>
                  <SelectValue placeholder="Select audience" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general_public">General Public</SelectItem>
                  <SelectItem value="professionals">Working Professionals</SelectItem>
                  <SelectItem value="students">Students</SelectItem>
                  <SelectItem value="parents">Parents</SelectItem>
                  <SelectItem value="seniors">Seniors (65+)</SelectItem>
                  <SelectItem value="millennials">Millennials</SelectItem>
                  <SelectItem value="gen_z">Gen Z</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Geographic Focus</label>
              <Select value={geographicFocus} onValueChange={setGeographicFocus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="global">Global</SelectItem>
                  <SelectItem value="north_america">North America</SelectItem>
                  <SelectItem value="europe">Europe</SelectItem>
                  <SelectItem value="asia_pacific">Asia Pacific</SelectItem>
                  <SelectItem value="latin_america">Latin America</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button
            onClick={handleDemographicAnalysis}
            disabled={isAnalyzing}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing Demographics...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                Analyze Demographics
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysisResult && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Target Match</span>
                </div>
                <div className="mt-2">
                  <Progress
                    value={analysisResult.demographicInsights.targetAudienceMatch * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(analysisResult.demographicInsights.targetAudienceMatch * 100)}% match
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Coverage</span>
                </div>
                <div className="mt-2">
                  <Progress
                    value={analysisResult.demographicInsights.coverageCompleteness * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(analysisResult.demographicInsights.coverageCompleteness * 100)}% complete
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">Diversity</span>
                </div>
                <div className="mt-2">
                  <Progress
                    value={analysisResult.demographicInsights.diversityScore * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(analysisResult.demographicInsights.diversityScore * 100)}% diverse
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium">Representation</span>
                </div>
                <div className="mt-2">
                  <Progress
                    value={analysisResult.demographicInsights.representativenessScore * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(analysisResult.demographicInsights.representativenessScore * 100)}% representative
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recommended Demographics */}
          <Card>
            <CardHeader>
              <CardTitle>Recommended Demographics</CardTitle>
              <CardDescription>
                AI-identified demographic groups with highest value for your poll
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysisResult.recommendedDemographics.slice(0, 6).map((demographic) => (
                  <div key={demographic.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge className={`${getPriorityColor(demographic.priority)} text-white`}>
                          {demographic.priority}
                        </Badge>
                        <h4 className="font-medium">{demographic.name}</h4>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">Sample: {demographic.sampleSizeRecommendation}</p>
                        <p className="text-xs text-muted-foreground">
                          Engagement: {Math.round(demographic.expectedEngagement * 100)}%
                        </p>
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">
                      {demographic.description}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs font-medium text-muted-foreground mb-1">Key Characteristics</p>
                        <div className="flex flex-wrap gap-1">
                          {demographic.keyCharacteristics.slice(0, 3).map((char: string, i: number) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {char}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <p className="text-xs font-medium text-muted-foreground mb-1">Predicted Response</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            {demographic.predictedResponse.primaryChoice}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {Math.round(demographic.predictedResponse.confidence * 100)}% confidence
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Strategic Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Simulation Strategies</CardTitle>
              <CardDescription>
                Optimized demographic combinations for different objectives
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Briefcase className="h-4 w-4 text-blue-600" />
                    <h4 className="font-medium">Minimal Viable</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Quick insights with essential demographics
                  </p>
                  <p className="text-xs text-muted-foreground mb-3">
                    {analysisResult.strategicRecommendations.minimalViableSet?.length || 0} demographics
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRunOptimalSimulation('minimal')}
                    disabled={isRunningSimulation}
                    className="w-full"
                  >
                    Run Minimal Simulation
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <h4 className="font-medium">Comprehensive</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Complete analysis across all key demographics
                  </p>
                  <p className="text-xs text-muted-foreground mb-3">
                    {analysisResult.strategicRecommendations.comprehensiveSet?.length || 0} demographics
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRunOptimalSimulation('comprehensive')}
                    disabled={isRunningSimulation}
                    className="w-full"
                  >
                    Run Full Simulation
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Target className="h-4 w-4 text-purple-600" />
                    <h4 className="font-medium">Budget Optimized</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Best value demographics for cost-effective insights
                  </p>
                  <p className="text-xs text-muted-foreground mb-3">
                    {analysisResult.strategicRecommendations.budgetOptimized?.length || 0} demographics
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRunOptimalSimulation('budget')}
                    disabled={isRunningSimulation}
                    className="w-full"
                  >
                    Run Budget Simulation
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
