"use client";

import React, { useState } from 'react';
import {
  BatchSimulationRequest,
  BatchSimulationResult,
  BatchSimulationProgress
} from '@/lib/types/simulation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Brain, Users, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { ComparisonView } from './comparison-view';

interface BatchSimulationProps {
  pollQuestion: string;
  pollOptions: string[];
  pollId?: string;
  onComplete?: (result: BatchSimulationResult) => void;
}

// Demographic categories for better organization
const DEMOGRAPHIC_CATEGORIES = [
  { id: 'age_groups', label: 'Age Groups' },
  { id: 'education', label: 'Education' },
  { id: 'occupation', label: 'Occupation' },
  { id: 'lifestyle', label: 'Lifestyle' },
  { id: 'location', label: 'Location' }
];

// Enhanced demographics with categories
const AVAILABLE_DEMOGRAPHICS = [
  // Age Groups
  { id: 'gen_z', label: 'Gen Z (18-24)', icon: '📱', category: 'age_groups' },
  { id: 'millennials', label: 'Millennials (25-40)', icon: '🌟', category: 'age_groups' },
  { id: 'gen_x', label: 'Gen X (41-56)', icon: '🎸', category: 'age_groups' },
  { id: 'baby_boomers', label: 'Baby Boomers (57-75)', icon: '🏠', category: 'age_groups' },
  { id: 'seniors', label: 'Seniors (76+)', icon: '👴', category: 'age_groups' },
  
  // Education
  { id: 'high_school_students', label: 'High School Students', icon: '📚', category: 'education' },
  { id: 'college_students', label: 'College Students', icon: '🎓', category: 'education' },
  { id: 'graduate_students', label: 'Graduate Students', icon: '🧠', category: 'education' },
  { id: 'phd_academics', label: 'PhD/Academics', icon: '🔬', category: 'education' },
  
  // Occupation
  { id: 'working_professionals', label: 'Working Professionals', icon: '💼', category: 'occupation' },
  { id: 'healthcare_workers', label: 'Healthcare Workers', icon: '⚕️', category: 'occupation' },
  { id: 'tech_workers', label: 'Tech Workers', icon: '💻', category: 'occupation' },
  { id: 'educators', label: 'Educators', icon: '📝', category: 'occupation' },
  { id: 'service_industry', label: 'Service Industry', icon: '🍽️', category: 'occupation' },
  
  // Lifestyle
  { id: 'parents', label: 'Parents', icon: '👨‍👩‍👧‍👦', category: 'lifestyle' },
  { id: 'remote_workers', label: 'Remote Workers', icon: '🏡', category: 'lifestyle' },
  { id: 'urban_dwellers', label: 'Urban Dwellers', icon: '🏙️', category: 'lifestyle' },
  { id: 'suburban_residents', label: 'Suburban Residents', icon: '🏘️', category: 'lifestyle' },
  { id: 'rural_residents', label: 'Rural Residents', icon: '🌾', category: 'lifestyle' },
  
  // Location
  { id: 'west_coast', label: 'West Coast', icon: '🌊', category: 'location' },
  { id: 'east_coast', label: 'East Coast', icon: '🗽', category: 'location' },
  { id: 'midwest', label: 'Midwest', icon: '🌽', category: 'location' },
  { id: 'southern_us', label: 'Southern US', icon: '🤠', category: 'location' },
  { id: 'international', label: 'International', icon: '🌍', category: 'location' }
];

export function BatchSimulation({
  pollQuestion,
  pollOptions,
  pollId,
  onComplete
}: BatchSimulationProps) {
  const [selectedDemographics, setSelectedDemographics] = useState<string[]>([]);
  const [sampleSize, setSampleSize] = useState(1000);
  const [isRunning, setIsRunning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<BatchSimulationResult | null>(null);
  const [progress, setProgress] = useState<BatchSimulationProgress | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>(DEMOGRAPHIC_CATEGORIES[0].id);
  const [cacheStatus, setCacheStatus] = useState<{
    isCached: boolean;
    cachedDemographics: string[];
    estimatedSavings: number;
  }>({ isCached: false, cachedDemographics: [], estimatedSavings: 0 });

  // Calculate estimated completion time based on progress
  const calculateEstimatedCompletion = (progress: BatchSimulationProgress): string => {
    if (!progress.startedAt || progress.completedDemographics === 0) {
      return 'Calculating...';
    }
    
    const startTime = new Date(progress.startedAt).getTime();
    const currentTime = new Date().getTime();
    const elapsedMs = currentTime - startTime;
    const msPerDemographic = elapsedMs / progress.completedDemographics;
    const remainingDemographics = progress.totalDemographics - progress.completedDemographics;
    const estimatedRemainingMs = msPerDemographic * remainingDemographics;
    
    // Account for cached results if available
    const cachedCount = cacheStatus.cachedDemographics.length;
    const adjustedRemainingMs = cachedCount > 0 
      ? estimatedRemainingMs * (1 - (cachedCount / progress.totalDemographics) * 0.8)
      : estimatedRemainingMs;
    
    const estimatedCompletionTime = new Date(currentTime + adjustedRemainingMs);
    return estimatedCompletionTime.toLocaleTimeString();
  };

  // Filter demographics by active category and search query
  const filteredDemographics = AVAILABLE_DEMOGRAPHICS.filter(demographic => 
    (activeCategory === 'all' || demographic.category === activeCategory) &&
    (searchQuery === "" || 
      demographic.label.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleDemographicToggle = (demographicId: string) => {
    setSelectedDemographics(prev => {
      // If already selected, remove it
      if (prev.includes(demographicId)) {
        return prev.filter(id => id !== demographicId);
      }
      
      // If adding would exceed the limit of 5, show error and don't add
      if (prev.length >= 5) {
        toast.error("Maximum 5 demographic groups allowed");
        return prev;
      }
      
      // Otherwise add it
      return [...prev, demographicId];
    });
  };
  
  // We're implementing these functions directly in the UI buttons
  // so we don't need separate function declarations

  const handleRunBatchSimulation = async () => {
    if (selectedDemographics.length === 0) {
      toast.error('Please select at least one demographic group');
      return;
    }

    if (selectedDemographics.length > 5) {
      toast.error('Maximum 5 demographic groups allowed');
      return;
    }

    setIsRunning(true);
    setError(null);
    setResult(null);
    setProgress(null);

    try {
      const request: BatchSimulationRequest = {
        pollQuestion,
        pollOptions,
        demographics: selectedDemographics.map(id => {
          // Find the demographic details from the available demographics
          const demographic = AVAILABLE_DEMOGRAPHICS.find(d => d.id === id);
          return {
            group: demographic?.label || id.replace('_', ' '),
            size: sampleSize
          };
        }),
        responseFormat: 'distribution',
        batchId: `batch_${Date.now()}`
      };

      // Setup event source for progress updates if supported by the browser
      let eventSource: EventSource | null = null;
      const supportsEventSource = 'EventSource' in window;
      
      if (supportsEventSource) {
        // Create a unique progress ID for this simulation
        const progressId = `progress_${Date.now()}`;
        
        // Setup server-sent events for progress updates
        eventSource = new EventSource(`/api/simulate-poll-batch/progress?id=${progressId}`);
        
        eventSource.onmessage = (event) => {
          try {
            const progressData: BatchSimulationProgress = JSON.parse(event.data);
            setProgress(progressData);
            
            // If completed, close the connection
            if (progressData.status === 'completed' || progressData.status === 'failed') {
              eventSource?.close();
            }
          } catch (e) {
            console.error('Error parsing progress data:', e);
          }
        };
        
        eventSource.onerror = () => {
          console.error('Progress event source error');
          eventSource?.close();
        };
        
        // Add progress ID to the request
        request.batchId = progressId;
      }
      
      // First check cache status to provide immediate feedback
      const cacheCheckResponse = await fetch('/api/simulate-poll-batch/cache-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pollQuestion,
          pollOptions,
          demographics: request.demographics
        })
      });
      
      if (cacheCheckResponse.ok) {
        const cacheInfo = await cacheCheckResponse.json();
        setCacheStatus({
          isCached: cacheInfo.fullyCached || cacheInfo.partiallyCached,
          cachedDemographics: cacheInfo.cachedDemographics || [],
          estimatedSavings: cacheInfo.estimatedTimeSavings || 0
        });
        
        if (cacheInfo.fullyCached) {
          toast.success('Using cached results for all demographics - instant results!');
        } else if (cacheInfo.partiallyCached) {
          toast.success(`Using cached results for ${cacheInfo.cachedDemographics.length} demographics - faster results!`);
        }
      }
      
      // Make the API request
      const response = await fetch('/api/simulate-poll-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(pollId && { 'X-Poll-ID': pollId })
        },
        body: JSON.stringify(request)
      });

      // Clean up event source if it exists
      if (eventSource) {
        eventSource.close();
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Batch simulation failed');
      }

      const batchResult: BatchSimulationResult = await response.json();
      setResult(batchResult);
      onComplete?.(batchResult);
      toast.success(`Batch simulation completed! Analyzed ${batchResult.metadata.totalDemographics} demographics.`);

    } catch (error) {
      console.error('Batch simulation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to run batch simulation';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsRunning(false);
      setProgress(null);
    }
  };

  const resetSimulation = () => {
    setResult(null);
    setError(null);
    setProgress(null);
    setSelectedDemographics([]);
  };

  if (result) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Batch Simulation Results</h3>
            <p className="text-sm text-muted-foreground">
              {result.metadata.totalDemographics} demographics • {result.metadata.totalResponses} total responses
            </p>
          </div>
          <Button onClick={resetSimulation} variant="outline" size="sm">
            Run New Simulation
          </Button>
        </div>

        <ComparisonView
          batchResult={result}
          className="mt-4"
        />
      </div>
    );
  }

  if (isRunning) {
    // Calculate progress percentage
    const progressPercentage = progress ? 
      Math.round((progress.completedDemographics / progress.totalDemographics) * 100) : 0;
    
    // Estimate remaining time based on progress
    const getEstimatedTimeRemaining = () => {
      if (!progress || progress.completedDemographics === 0) return 'Calculating...';
      
      const startTime = new Date(progress.startedAt).getTime();
      const currentTime = new Date().getTime();
      const elapsedMs = currentTime - startTime;
      const msPerDemographic = elapsedMs / progress.completedDemographics;
      const remainingDemographics = progress.totalDemographics - progress.completedDemographics;
      const remainingMs = msPerDemographic * remainingDemographics;
      
      if (remainingMs < 60000) return 'Less than a minute';
      return `About ${Math.ceil(remainingMs / 60000)} minutes`;
    };

    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="mb-4">
                <RefreshCw className="h-10 w-10 animate-spin text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Running Batch Simulation</h3>
              
              {progress ? (
                <>
                  <p className="text-muted-foreground mb-4">
                    Analyzing {progress.currentDemographic || 'demographics'}...
                    <br />
                    <span className="text-sm">
                      {progress.completedDemographics} of {progress.totalDemographics} demographics completed
                    </span>
                  </p>
                  <div className="w-full max-w-md space-y-2">
                    <Progress value={progressPercentage} className="w-full" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{progressPercentage}% complete</span>
                      <span>Time remaining: {getEstimatedTimeRemaining()}</span>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-muted-foreground mb-4">
                    Analyzing responses across {selectedDemographics.length} demographic groups...
                  </p>
                  <Progress value={30} className="w-full max-w-md" />
                  <p className="text-xs text-muted-foreground mt-2">Initializing simulation...</p>
                </>
              )}
              
              {/* Tips while waiting */}
              <div className="mt-8 text-sm text-muted-foreground bg-muted p-4 rounded-md max-w-md">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" /> Simulation in Progress
                </h4>
                <p className="text-xs">
                  Our AI is analyzing demographic patterns and generating realistic responses. 
                  This typically takes 15-30 seconds per demographic group.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Multi-Demographic Simulation</h3>
        </div>
        <p className="text-sm text-muted-foreground">
          Run simulations across multiple demographic groups to compare response patterns and identify insights.
        </p>
      </div>

      {/* Demographic Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Select Demographics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Category Tabs */}
          <div className="flex overflow-x-auto pb-2 mb-2 border-b">
            {DEMOGRAPHIC_CATEGORIES.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-3 py-1.5 mr-2 text-sm rounded-md whitespace-nowrap ${activeCategory === category.id 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted hover:bg-muted/80'}`}
              >
                {category.label}
              </button>
            ))}
            <button
              onClick={() => setActiveCategory('all')}
              className={`px-3 py-1.5 mr-2 text-sm rounded-md whitespace-nowrap ${activeCategory === 'all' 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted hover:bg-muted/80'}`}
            >
              All Categories
            </button>
          </div>
          
          {/* Search Input */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search demographics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border rounded-md text-sm"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                ✕
              </button>
            )}
          </div>
          
          {/* Selection Actions */}
          <div className="flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              {selectedDemographics.length} of 5 selected
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedDemographics([])}
                disabled={selectedDemographics.length === 0}
              >
                Clear
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  const categoryDemographics = filteredDemographics.map(d => d.id);
                  const selection = categoryDemographics.slice(0, 5);
                  setSelectedDemographics(selection);
                  
                  if (categoryDemographics.length > 5) {
                    toast.warning("Only the first 5 demographics were selected due to the limit");
                  }
                }}
                disabled={filteredDemographics.length === 0}
              >
                Select All
              </Button>
            </div>
          </div>

          {/* Demographics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
            {filteredDemographics.length > 0 ? (
              filteredDemographics.map((demographic) => (
                <div key={demographic.id} className="flex items-center space-x-3 p-2 border rounded-md hover:bg-muted/50">
                  <Checkbox
                    id={demographic.id}
                    checked={selectedDemographics.includes(demographic.id)}
                    onCheckedChange={() => handleDemographicToggle(demographic.id)}
                    disabled={!selectedDemographics.includes(demographic.id) && selectedDemographics.length >= 5}
                  />
                  <Label
                    htmlFor={demographic.id}
                    className="flex items-center gap-2 cursor-pointer w-full"
                  >
                    <span className="text-lg">{demographic.icon}</span>
                    <span className="flex-1">{demographic.label}</span>
                  </Label>
                </div>
              ))
            ) : (
              <div className="col-span-2 py-8 text-center text-muted-foreground">
                No demographics found matching your search
              </div>
            )}
          </div>

          {/* Selected Demographics */}
          {selectedDemographics.length > 0 && (
            <div className="pt-3 border-t">
              <h4 className="text-sm font-medium mb-2">Selected Demographics:</h4>
              <div className="flex flex-wrap gap-2">
                {selectedDemographics.map(id => {
                  const demographic = AVAILABLE_DEMOGRAPHICS.find(d => d.id === id);
                  return demographic ? (
                    <Badge 
                      key={id} 
                      variant="secondary" 
                      className="text-xs flex items-center gap-1 pr-1"
                    >
                      <span>{demographic.icon}</span>
                      <span>{demographic.label}</span>
                      <button 
                        onClick={() => handleDemographicToggle(id)}
                        className="ml-1 hover:bg-muted rounded-full h-4 w-4 inline-flex items-center justify-center"
                      >
                        ✕
                      </button>
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sample Size Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Sample Size per Demographic</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={sampleSize.toString()} onValueChange={(value) => setSampleSize(parseInt(value))}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose sample size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="50">50 responses per demographic</SelectItem>
              <SelectItem value="100">100 responses per demographic</SelectItem>
              <SelectItem value="250">250 responses per demographic</SelectItem>
              <SelectItem value="500">500 responses per demographic</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-2">
            Total responses: {selectedDemographics.length * sampleSize}
          </p>
        </CardContent>
      </Card>

      {/* Progress Display */}
      {progress && (
        <Card>
          <CardContent className="pt-6">
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  Processing {progress.completedDemographics} of {progress.totalDemographics} demographics
                </span>
                <span className="text-xs text-muted-foreground">
                  {progress.currentDemographic && `Currently: ${progress.currentDemographic}`}
                </span>
              </div>
              
              <Progress value={(progress.completedDemographics / progress.totalDemographics) * 100} />
              
              {/* Cache status indicator */}
              {cacheStatus.isCached && (
                <div className="flex items-center gap-1 text-xs text-emerald-600">
                  <CheckCircle className="h-3.5 w-3.5" />
                  {cacheStatus.cachedDemographics.length === progress.totalDemographics ? (
                    <span>Using cached results for all demographics (saving ~{cacheStatus.estimatedSavings}s)</span>
                  ) : (
                    <span>Using cached results for {cacheStatus.cachedDemographics.length} demographics (saving ~{cacheStatus.estimatedSavings}s)</span>
                  )}
                </div>
              )}
              
              {/* Estimated time remaining */}
              {progress.startedAt && (
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    Started: {new Date(progress.startedAt).toLocaleTimeString()}
                  </span>
                  <span>
                    {progress.status === 'running' && (
                      <>Est. completion: {calculateEstimatedCompletion(progress)}</>
                    )}
                    {progress.status === 'completed' && (
                      <>Completed at: {new Date().toLocaleTimeString()}</>
                    )}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Button */}
      <Button
        onClick={handleRunBatchSimulation}
        disabled={isRunning || selectedDemographics.length === 0}
        className="w-full"
        size="lg"
      >
        {isRunning ? (
          <>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Running Batch Simulation...
          </>
        ) : (
          <>
            <Brain className="h-4 w-4 mr-2" />
            Run Batch Simulation ({selectedDemographics.length} demographics)
          </>
        )}
      </Button>

      {selectedDemographics.length > 0 && (
        <div className="text-xs text-muted-foreground text-center">
          Estimated time: {Math.ceil(selectedDemographics.length * 0.5)} minutes •
          Estimated cost: ${(selectedDemographics.length * 0.03).toFixed(2)}
        </div>
      )}
    </div>
  );
}
