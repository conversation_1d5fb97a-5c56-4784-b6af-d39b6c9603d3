import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Brain, BarChart3, Users, TrendingUp, Settings, Zap } from 'lucide-react';
import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { StreamingInsights } from './streaming-insights';
import { DemographicAnalyzer } from './demographic-analyzer';
import { SimulationComparison } from './simulation-comparison';
import { ConfigurationGenerator } from './configuration-generator';

interface EnhancedSimulationDashboardProps {
  pollQuestion: string;
  pollOptions: string[];
  pollId?: string;
  onSimulationComplete?: (result: SimulationResponse | BatchSimulationResult) => void;
}

export function EnhancedSimulationDashboard({
  pollQuestion,
  pollOptions,
  onSimulationComplete
}: EnhancedSimulationDashboardProps) {
  const [activeTab, setActiveTab] = useState<string>('configure');
  const [simulationResults, setSimulationResults] = useState<(SimulationResponse | BatchSimulationResult)[]>([]);
  const [isGeneratingConfig, setIsGeneratingConfig] = useState(false);

  const handleConfigGeneration = async () => {
    setIsGeneratingConfig(true);
    try {
      const response = await fetch('/api/ai/generate-simulation-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollQuestion,
          pollOptions,
          objectives: ['maximize_accuracy', 'optimize_cost'],
          pollContext: `Poll with ${pollOptions.length} options`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Configuration generated:', data.config);
        setActiveTab('demographics');
      } else {
        console.error('Failed to generate configuration');
      }
    } catch (error) {
      console.error('Error generating configuration:', error);
    } finally {
      setIsGeneratingConfig(false);
    }
  };

  const handleSimulationComplete = (result: SimulationResponse | BatchSimulationResult) => {
    setSimulationResults(prev => [...prev, result]);
    onSimulationComplete?.(result);
    if (simulationResults.length === 0) {
      setActiveTab('insights');
    }
  };

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Brain className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Enhanced AI Simulation</h2>
          <Badge variant="outline" className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none">
            Phase 3
          </Badge>
        </div>
        <p className="text-muted-foreground">
          Advanced AI-powered poll simulation with streaming insights, demographic analysis, and intelligent configuration.
        </p>
      </div>

      {/* Quick Stats */}
      {simulationResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Simulations</span>
              </div>
              <p className="text-2xl font-bold">{simulationResults.length}</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Total Responses</span>
              </div>
              <p className="text-2xl font-bold">
                {simulationResults.reduce((sum, result) => {
                  if ('batchId' in result) {
                    return sum + result.metadata.totalResponses;
                  } else {
                    return sum + result.metadata.sampleSize;
                  }
                }, 0)}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Avg Confidence</span>
              </div>
              <p className="text-2xl font-bold">
                {simulationResults.length > 0 ? (
                  Math.round(
                    simulationResults.reduce((sum, result) => {
                      if ('batchId' in result) {
                        // This is a BatchSimulationResult
                        const batchResult = result as BatchSimulationResult;
                        return sum + (batchResult.metadata.averageConfidence || 0);
                      } else {
                        // This is a SimulationResponse
                        const simResult = result as SimulationResponse;
                        return sum + (simResult.metadata.confidence || 0);
                      }
                    }, 0) / simulationResults.length * 100
                  )
                ) : 0}%
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">AI Features</span>
              </div>
              <p className="text-2xl font-bold">8</p>
              <p className="text-xs text-muted-foreground">Active</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Tabs Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="demographics" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Demographics
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Insights
          </TabsTrigger>
          <TabsTrigger value="compare" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Compare
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Advanced
          </TabsTrigger>
        </TabsList>

        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-4">
          <ConfigurationGenerator
            pollQuestion={pollQuestion}
            pollOptions={pollOptions}
            onConfigGenerated={(config) => console.log('Config generated:', config)}
            isGenerating={isGeneratingConfig}
            onGenerate={handleConfigGeneration}
          />
        </TabsContent>

        {/* Demographics Analysis Tab */}
        <TabsContent value="demographics" className="space-y-4">
          <DemographicAnalyzer
            pollQuestion={pollQuestion}
            pollOptions={pollOptions}
            onSimulationComplete={handleSimulationComplete}
          />
        </TabsContent>

        {/* Streaming Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <StreamingInsights
            simulationResults={simulationResults}
            pollQuestion={pollQuestion}
            pollOptions={pollOptions}
          />
        </TabsContent>

        {/* Comparison Tab */}
        <TabsContent value="compare" className="space-y-4">
          <SimulationComparison
            simulations={simulationResults}
            pollQuestion={pollQuestion}
            pollOptions={pollOptions}
          />
        </TabsContent>

        {/* Advanced Features Tab */}
        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Advanced Analytics
              </CardTitle>
              <CardDescription>
                Advanced features for power users and researchers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="h-auto p-4 flex flex-col items-start space-y-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    <span className="font-medium">Export Analysis</span>
                  </div>
                  <span className="text-sm text-muted-foreground text-left">
                    Export simulation results and insights to CSV, JSON, or PDF formats
                  </span>
                </Button>

                <Button variant="outline" className="h-auto p-4 flex flex-col items-start space-y-2">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    <span className="font-medium">A/B Test Setup</span>
                  </div>
                  <span className="text-sm text-muted-foreground text-left">
                    Set up A/B tests using different simulation parameters
                  </span>
                </Button>

                <Button variant="outline" className="h-auto p-4 flex flex-col items-start space-y-2">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    <span className="font-medium">Custom Demographics</span>
                  </div>
                  <span className="text-sm text-muted-foreground text-left">
                    Define custom demographic groups for specialized analysis
                  </span>
                </Button>

                <Button variant="outline" className="h-auto p-4 flex flex-col items-start space-y-2">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    <span className="font-medium">Real-time Monitoring</span>
                  </div>
                  <span className="text-sm text-muted-foreground text-left">
                    Monitor simulation accuracy and performance metrics
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
