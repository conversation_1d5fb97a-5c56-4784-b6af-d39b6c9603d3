"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings,
  Wand2,
  Loader2,
  CheckCircle,
  AlertCircle,
  Copy,
  Download,
  Lightbulb,
  Target,
  Users,
  Clock,
  DollarSign
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConfigurationGeneratorProps {
  pollQuestion: string;
  pollOptions: string[];
  onConfigGenerated?: (config: SimulationConfig) => void;
  isGenerating?: boolean;
  onGenerate?: () => void;
  className?: string;
}

interface SimulationConfig {
  demographics: string[];
  sampleSizes: { [key: string]: number };
  confidence: {
    target: number;
    tolerance: number;
  };
  costOptimization: {
    maxBudget?: number;
    priority: 'speed' | 'accuracy' | 'cost';
  };
  specialInstructions: string[];
  polling: {
    methodology: string;
    biasCorrection: boolean;
    crossValidation: boolean;
  };
  recommendations: {
    primaryDemographics: string[];
    secondaryDemographics: string[];
    riskFactors: string[];
    expectedAccuracy: number;
    estimatedCost: number;
    estimatedTime: number;
  };
}

const DEFAULT_DEMOGRAPHICS = [
  'College Students (18-24)',
  'Young Professionals (25-34)',
  'Mid-Career (35-49)',
  'Experienced Professionals (50+)',
  'Urban Residents',
  'Suburban Residents',
  'Rural Residents',
  'High School Educated',
  'College Educated',
  'Graduate Degree',
  'Low Income (<$50k)',
  'Middle Income ($50k-$100k)',
  'High Income (>$100k)',
  'Tech Workers',
  'Healthcare Workers',
  'Educators'
];

export function ConfigurationGenerator({
  pollQuestion,
  pollOptions,
  onConfigGenerated,
  isGenerating = false,
  onGenerate,
  className
}: ConfigurationGeneratorProps) {
  const [activeTab, setActiveTab] = useState<string>('wizard');
  const [generatedConfig, setGeneratedConfig] = useState<SimulationConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Manual configuration state
  const [selectedDemographics, setSelectedDemographics] = useState<string[]>([]);
  const [sampleSize, setSampleSize] = useState<number>(100);
  const [targetConfidence, setTargetConfidence] = useState<number>(85);
  const [priority, setPriority] = useState<'speed' | 'accuracy' | 'cost'>('accuracy');
  const [maxBudget, setMaxBudget] = useState<number>(50);
  const [specialInstructions, setSpecialInstructions] = useState<string>('');
  const [methodology, setMethodology] = useState<string>('standard');
  const [biasCorrection, setBiasCorrection] = useState<boolean>(true);
  const [crossValidation, setCrossValidation] = useState<boolean>(false);

  const generateAIConfig = async () => {
    setIsLoadingConfig(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/generate-simulation-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollQuestion,
          pollOptions,
          objectives: [
            priority === 'accuracy' ? 'maximize_accuracy' : priority === 'speed' ? 'minimize_time' : 'optimize_cost'
          ],
          constraints: {
            maxBudget: maxBudget || undefined,
            targetConfidence: targetConfidence / 100,
            preferredDemographics: selectedDemographics.length > 0 ? selectedDemographics : undefined
          },
          pollContext: `Poll with ${pollOptions.length} options: ${pollQuestion.substring(0, 100)}...`,
          requirements: specialInstructions || undefined
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setGeneratedConfig(data.config);
        onConfigGenerated?.(data.config);
        setActiveTab('results');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to generate configuration');
      }
    } catch (error) {
      console.error('Configuration generation error:', error);
      setError('An error occurred while generating the configuration');
    } finally {
      setIsLoadingConfig(false);
    }
  };

  const createManualConfig = () => {
    const manualConfig: SimulationConfig = {
      demographics: selectedDemographics,
      sampleSizes: selectedDemographics.reduce((acc, demo) => {
        acc[demo] = sampleSize;
        return acc;
      }, {} as { [key: string]: number }),
      confidence: {
        target: targetConfidence / 100,
        tolerance: 0.05
      },
      costOptimization: {
        maxBudget: maxBudget > 0 ? maxBudget : undefined,
        priority
      },
      specialInstructions: specialInstructions ? [specialInstructions] : [],
      polling: {
        methodology,
        biasCorrection,
        crossValidation
      },
      recommendations: {
        primaryDemographics: selectedDemographics.slice(0, 3),
        secondaryDemographics: selectedDemographics.slice(3),
        riskFactors: [],
        expectedAccuracy: targetConfidence / 100,
        estimatedCost: calculateEstimatedCost(),
        estimatedTime: calculateEstimatedTime()
      }
    };

    setGeneratedConfig(manualConfig);
    onConfigGenerated?.(manualConfig);
    setActiveTab('results');
  };

  const calculateEstimatedCost = (): number => {
    const baseCost = 0.02; // $0.02 per response
    const totalResponses = selectedDemographics.length * sampleSize;
    const methodologyMultiplier = methodology === 'advanced' ? 1.5 : 1;
    const biasMultiplier = biasCorrection ? 1.2 : 1;
    const crossValidationMultiplier = crossValidation ? 1.3 : 1;

    return totalResponses * baseCost * methodologyMultiplier * biasMultiplier * crossValidationMultiplier;
  };

  const calculateEstimatedTime = (): number => {
    const baseTime = 30; // 30 seconds per demographic
    const complexityMultiplier = pollOptions.length > 5 ? 1.3 : 1;
    const sampleMultiplier = sampleSize > 200 ? 1.2 : 1;

    return selectedDemographics.length * baseTime * complexityMultiplier * sampleMultiplier;
  };

  const copyConfig = () => {
    if (generatedConfig) {
      navigator.clipboard.writeText(JSON.stringify(generatedConfig, null, 2));
    }
  };

  const exportConfig = () => {
    if (generatedConfig) {
      const blob = new Blob([JSON.stringify(generatedConfig, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `simulation-config-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const getDemographicRecommendations = () => {
    // Simple recommendation logic based on poll question content
    const question = pollQuestion.toLowerCase();
    const recommendations: string[] = [];

    if (question.includes('work') || question.includes('job') || question.includes('career')) {
      recommendations.push('Young Professionals (25-34)', 'Mid-Career (35-49)', 'Tech Workers');
    }
    if (question.includes('education') || question.includes('school') || question.includes('learning')) {
      recommendations.push('College Students (18-24)', 'Educators', 'College Educated');
    }
    if (question.includes('health') || question.includes('medical')) {
      recommendations.push('Healthcare Workers', 'Experienced Professionals (50+)');
    }

    return recommendations.length > 0 ? recommendations : DEFAULT_DEMOGRAPHICS.slice(0, 5);
  };

  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration Generator
          </h3>
          <p className="text-sm text-muted-foreground">
            Create optimal simulation configurations using AI or manual setup
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="wizard" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            AI Wizard
          </TabsTrigger>
          <TabsTrigger value="manual" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Manual Setup
          </TabsTrigger>
          <TabsTrigger value="results" disabled={!generatedConfig} className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Results
          </TabsTrigger>
        </TabsList>

        {/* AI Wizard Tab */}
        <TabsContent value="wizard" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Wand2 className="h-5 w-5 text-purple-600" />
                AI-Powered Configuration
              </CardTitle>
              <CardDescription>
                Let AI analyze your poll and generate optimal simulation parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={priority} onValueChange={(value: 'speed' | 'accuracy' | 'cost') => setPriority(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="accuracy">
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Maximum Accuracy
                        </div>
                      </SelectItem>
                      <SelectItem value="speed">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Fastest Results
                        </div>
                      </SelectItem>
                      <SelectItem value="cost">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          Cost Optimized
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confidence">Target Confidence (%)</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[targetConfidence]}
                      onValueChange={([value]) => setTargetConfidence(value)}
                      max={95}
                      min={70}
                      step={5}
                      className="flex-1"
                    />
                    <div className="text-center text-sm text-muted-foreground">
                      {targetConfidence}%
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">Max Budget ($)</Label>
                  <Input
                    type="number"
                    value={maxBudget}
                    onChange={(e) => setMaxBudget(Number(e.target.value))}
                    placeholder="50"
                    min="0"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructions">Special Requirements (Optional)</Label>
                <Textarea
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  placeholder="Any specific requirements or constraints for the simulation..."
                  rows={3}
                />
              </div>

              <div className="space-y-3">
                <Label>Recommended Demographics</Label>
                <div className="flex flex-wrap gap-2">
                  {getDemographicRecommendations().map((demo, idx) => (
                    <Badge key={idx} variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                      <Lightbulb className="h-3 w-3 mr-1" />
                      {demo}
                    </Badge>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  AI will automatically select the most relevant demographics based on your poll content.
                </p>
              </div>

              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              <Button
                onClick={generateAIConfig}
                disabled={isLoadingConfig || isGenerating}
                className="w-full"
              >
                {isLoadingConfig || isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Configuration...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Generate AI Configuration
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manual Setup Tab */}
        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Settings className="h-5 w-5 text-green-600" />
                Manual Configuration
              </CardTitle>
              <CardDescription>
                Customize every aspect of your simulation parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <Label>Select Demographics</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto p-3 border rounded-md">
                  {DEFAULT_DEMOGRAPHICS.map((demo) => (
                    <div key={demo} className="flex items-center space-x-2">
                      <Checkbox
                        id={demo}
                        checked={selectedDemographics.includes(demo)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedDemographics([...selectedDemographics, demo]);
                          } else {
                            setSelectedDemographics(selectedDemographics.filter(d => d !== demo));
                          }
                        }}
                      />
                      <Label htmlFor={demo} className="text-sm cursor-pointer">
                        {demo}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  Selected: {selectedDemographics.length} demographics
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sampleSize">Sample Size per Demographic</Label>
                  <Input
                    type="number"
                    value={sampleSize}
                    onChange={(e) => setSampleSize(Number(e.target.value))}
                    placeholder="100"
                    min="10"
                    max="1000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="methodology">Methodology</Label>
                  <Select value={methodology} onValueChange={setMethodology}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="enhanced">Enhanced</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Advanced Options</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="bias-correction"
                      checked={biasCorrection}
                      onCheckedChange={(checked) => setBiasCorrection(checked === true)}
                    />
                    <Label htmlFor="bias-correction" className="text-sm">
                      Enable bias correction
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="cross-validation"
                      checked={crossValidation}
                      onCheckedChange={(checked) => setCrossValidation(checked === true)}
                    />
                    <Label htmlFor="cross-validation" className="text-sm">
                      Enable cross-validation
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="special-instructions">Special Instructions</Label>
                <Textarea
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  placeholder="Enter any special instructions for the simulation..."
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-md border border-blue-200">
                <div>
                  <p className="text-sm font-medium">Estimated Cost</p>
                  <p className="text-lg font-bold text-blue-600">${calculateEstimatedCost().toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Estimated Time</p>
                  <p className="text-lg font-bold text-blue-600">{Math.round(calculateEstimatedTime() / 60)} min</p>
                </div>
              </div>

              <Button
                onClick={createManualConfig}
                disabled={selectedDemographics.length === 0}
                className="w-full"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Create Configuration
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-4">
          {generatedConfig ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold">Generated Configuration</h4>
                <div className="flex items-center gap-2">
                  <Button onClick={copyConfig} variant="outline" size="sm">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button onClick={exportConfig} variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Demographics</span>
                    </div>
                    <p className="text-2xl font-bold">{generatedConfig.demographics.length}</p>
                    <p className="text-xs text-muted-foreground">Selected groups</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Expected Accuracy</span>
                    </div>
                    <p className="text-2xl font-bold">
                      {(generatedConfig.recommendations.expectedAccuracy * 100).toFixed(0)}%
                    </p>
                    <p className="text-xs text-muted-foreground">Confidence level</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-orange-600" />
                      <span className="text-sm font-medium">Estimated Cost</span>
                    </div>
                    <p className="text-2xl font-bold">
                      ${generatedConfig.recommendations.estimatedCost.toFixed(2)}
                    </p>
                    <p className="text-xs text-muted-foreground">Total estimated</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Primary Demographics</CardTitle>
                    <CardDescription>Most relevant demographic groups</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {generatedConfig.recommendations.primaryDemographics.map((demo, idx) => (
                        <Badge key={idx} variant="outline" className="text-green-600 border-green-200 bg-green-50">
                          {demo}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Configuration Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Target Confidence:</span>
                      <span className="font-medium">{(generatedConfig.confidence.target * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Methodology:</span>
                      <span className="font-medium capitalize">{generatedConfig.polling.methodology}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Bias Correction:</span>
                      <span className="font-medium">{generatedConfig.polling.biasCorrection ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Priority:</span>
                      <span className="font-medium capitalize">{generatedConfig.costOptimization.priority}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {generatedConfig.recommendations.riskFactors.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                      Risk Factors
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {generatedConfig.recommendations.riskFactors.map((risk, idx) => (
                        <li key={idx} className="text-sm p-2 bg-orange-50 rounded border border-orange-200">
                          {risk}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Ready to Start Simulation?</CardTitle>
                  <CardDescription>
                    Your configuration is ready. You can now run the simulation with these parameters.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button onClick={onGenerate} className="w-full">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Start Simulation with This Configuration
                  </Button>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Configuration Generated</h3>
                <p className="text-muted-foreground text-center">
                  Use the AI Wizard or Manual Setup to create a simulation configuration.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
