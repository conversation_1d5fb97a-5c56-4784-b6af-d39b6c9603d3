import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, Zap, TrendingUp, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { toast } from 'sonner';

interface StreamingInsightsProps {
  simulationResults: (SimulationResponse | BatchSimulationResult)[];
  pollQuestion: string;
  pollOptions: string[];
}

export function StreamingInsights({
  simulationResults,
  pollQuestion,
  pollOptions
}: StreamingInsightsProps) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedContent, setStreamedContent] = useState<string>('');
  const [analysisType, setAnalysisType] = useState<string>('comprehensive');
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('live');

  const availableFocusAreas = [
    'demographic_patterns',
    'confidence_analysis',
    'consensus_areas',
    'polarization_points',
    'strategic_implications',
    'risk_assessment',
    'opportunity_identification'
  ];

  const handleStreamInsights = async () => {
    if (simulationResults.length === 0) {
      toast.error('No simulation results available for analysis');
      return;
    }

    setIsStreaming(true);
    setStreamedContent('');

    try {
      const response = await fetch('/api/ai/stream-simulation-insights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          simulationData: simulationResults.length === 1 ? simulationResults[0] : simulationResults,
          analysisType,
          focusAreas,
          pollQuestion,
          pollOptions
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start streaming insights');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          // Update the streamed content
          setStreamedContent(prev => prev + decoder.decode(value, { stream: true }));

          // Auto-scroll to bottom
          setTimeout(() => {
            const element = document.getElementById('streaming-content');
            if (element) {
              element.scrollTop = element.scrollHeight;
            }
          }, 100);
        }
      }

      toast.success('Streaming insights completed');
    } catch (error) {
      console.error('Error streaming insights:', error);
      toast.error('Failed to stream insights');
    } finally {
      setIsStreaming(false);
    }
  };

  const toggleFocusArea = (area: string) => {
    setFocusAreas(prev =>
      prev.includes(area)
        ? prev.filter(a => a !== area)
        : [...prev, area]
    );
  };

  const formatFocusAreaLabel = (area: string) => {
    return area.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Real-time AI Insights</h3>
            <Badge variant="outline" className="bg-gradient-to-r from-green-500 to-blue-600 text-white border-none">
              Streaming
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {simulationResults.length > 0 && (
              <Badge variant="secondary">
                {simulationResults.length} simulation{simulationResults.length > 1 ? 's' : ''} loaded
              </Badge>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Analysis Type</label>
            <Select value={analysisType} onValueChange={setAnalysisType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="comprehensive">Comprehensive Analysis</SelectItem>
                <SelectItem value="focused">Focused Analysis</SelectItem>
                <SelectItem value="summary">Quick Summary</SelectItem>
                <SelectItem value="strategic">Strategic Insights</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Focus Areas ({focusAreas.length})</label>
            <div className="flex flex-wrap gap-1">
              {availableFocusAreas.slice(0, 3).map(area => (
                <Badge
                  key={area}
                  variant={focusAreas.includes(area) ? "default" : "outline"}
                  className="text-xs cursor-pointer"
                  onClick={() => toggleFocusArea(area)}
                >
                  {formatFocusAreaLabel(area)}
                </Badge>
              ))}
              {availableFocusAreas.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{availableFocusAreas.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-end">
            <Button
              onClick={handleStreamInsights}
              disabled={isStreaming || simulationResults.length === 0}
              className="w-full"
            >
              {isStreaming ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Streaming...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Stream Insights
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* No Data State */}
      {simulationResults.length === 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">No Simulation Data</h3>
                <p className="text-muted-foreground">
                  Run some simulations first to generate AI-powered insights
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Streaming Interface */}
      {simulationResults.length > 0 && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="live">Live Stream</TabsTrigger>
            <TabsTrigger value="history">Analysis History</TabsTrigger>
            <TabsTrigger value="export">Export Options</TabsTrigger>
          </TabsList>

          <TabsContent value="live" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Analysis Stream
                  {isStreaming && (
                    <Badge className="bg-green-500 animate-pulse">
                      Live
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  Real-time AI insights streaming as they&apos;re generated
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="streaming-content"
                  className="min-h-[400px] max-h-[600px] overflow-y-auto p-4 bg-muted/30 rounded-lg border"
                >
                  {streamedContent ? (
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {streamedContent}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-32 text-muted-foreground">
                      {isStreaming ? (
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Waiting for AI insights...
                        </div>
                      ) : (
                        <div className="text-center space-y-2">
                          <Brain className="h-8 w-8 mx-auto text-muted-foreground" />
                          <p>Click &quot;Stream Insights&quot; to start real-time analysis</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Streaming Status */}
                {isStreaming && (
                  <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="h-3 w-3 animate-spin" />
                    AI is analyzing your simulation data...
                  </div>
                )}

                {streamedContent && !isStreaming && (
                  <div className="mt-4 flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    Analysis completed
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Analysis History</CardTitle>
                <CardDescription>
                  Previous AI analysis sessions and their results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center py-8 text-muted-foreground">
                    <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                    <p>Analysis history will appear here</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Export Analysis</CardTitle>
                <CardDescription>
                  Export your insights and analysis results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" disabled={!streamedContent}>
                    Export as PDF
                  </Button>
                  <Button variant="outline" disabled={!streamedContent}>
                    Export as CSV
                  </Button>
                  <Button variant="outline" disabled={!streamedContent}>
                    Share Analysis
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
