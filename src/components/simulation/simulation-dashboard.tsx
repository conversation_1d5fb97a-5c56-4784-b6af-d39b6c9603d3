"use client";

import { useState, useEffect, useMemo } from "react";
import { Database } from "@/lib/database.types";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  BarChart3,
  Brain,
  ChevronRight,
  Download,
  ListChecks,
  MessageSquareText,
  Share2,
  Users
} from "lucide-react";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";
import { SimulationSetup } from "./simulation-setup";
import { SimulationResults } from "./simulation-results";
import { useSimulation } from "@/lib/hooks/use-simulation";

type Poll = Database["public"]["Tables"]["polls"]["Row"] & {
  questions: Database["public"]["Tables"]["questions"]["Row"][]
};

interface SimulationDashboardProps {
  poll: Poll;
}

export default function SimulationDashboard({ poll }: SimulationDashboardProps) {
  // Generate a unique key for this poll's simulation state
  const stateKey = `simulation_state_${poll.id}`;

  // Initialize state from localStorage if available
  const [activeQuestion, setActiveQuestion] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(stateKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.activeQuestion || 0;
        } catch (e) {
          console.error('Error parsing saved simulation state:', e);
        }
      }
    }
    return 0;
  });

  const [activeTab, setActiveTab] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(stateKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.activeTab || 'setup';
        } catch (e) {
          console.error('Error parsing saved simulation state:', e);
        }
      }
    }
    return 'setup';
  });

  const currentQuestion = poll.questions[activeQuestion];

  // Memoize currentQuestionId to prevent unnecessary re-renders and reduce GoTrueClient calls
  const currentQuestionId = useMemo(() => {
    return currentQuestion?.id;
  }, [currentQuestion?.id]);

  const {
    simulation,
    isLoading,
    error,
    demographic,
    setDemographic,
    sampleSize,
    setSampleSize,
    specialInstructions,
    setSpecialInstructions,
    startSimulation,
    resetSimulation
  } = useSimulation(poll.id, currentQuestionId);

  // Save state to localStorage whenever it changes (with debouncing to prevent excessive writes)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const timeoutId = setTimeout(() => {
        const stateToSave = {
          activeQuestion,
          activeTab,
          timestamp: Date.now()
        };
        try {
          localStorage.setItem(stateKey, JSON.stringify(stateToSave));
        } catch (error) {
          console.warn('Failed to save simulation state to localStorage:', error);
        }
      }, 100); // Debounce localStorage writes

      return () => clearTimeout(timeoutId);
    }
  }, [activeQuestion, activeTab, stateKey]);

  // Handle tab state when simulation changes (INFINITE LOOP FIX)
  useEffect(() => {
    // Only switch tabs based on simulation state, not activeQuestion
    // This prevents the infinite loop with GoTrueClient
    const newTab = simulation ? 'results' : 'setup';

    // Only update if the tab actually needs to change
    if (activeTab !== newTab) {
      setActiveTab(newTab);
    }
  }, [simulation, activeTab]); // Removed activeQuestion to break the infinite loop

  const hasNextQuestion = activeQuestion < poll.questions.length - 1;
  const hasPrevQuestion = activeQuestion > 0;
  const isTextQuestion = currentQuestion?.question_type === "text";

  const handleNextQuestion = () => {
    if (hasNextQuestion) {
      setActiveQuestion(prev => prev + 1);
      // With React Query, the simulation state will persist and be retrieved when switching questions
    }
  };

  const handlePrevQuestion = () => {
    if (hasPrevQuestion) {
      setActiveQuestion(prev => prev - 1);
      // With React Query, the simulation state will persist and be retrieved when switching questions
    }
  };

  const handleStartSimulation = async () => {
    const questionOptions =
      Array.isArray(currentQuestion.options)
        ? currentQuestion.options.map(opt => {
            if (typeof opt === "string") return opt;
            if (opt && typeof opt === "object" && "text" in opt && typeof opt.text === "string") return opt.text;
            return "";
          })
        : [];
    // Create a summary of the poll for context
    const pollContext = `Poll Title: ${poll.title}\nPoll Description: ${poll.description || 'No description'}\nTotal Questions: ${poll.questions.length}\nCurrent Question: ${activeQuestion + 1} of ${poll.questions.length}`;

    // Pass an onComplete callback that will switch to results tab only after simulation completes
    await startSimulation({
      pollId: poll.id,
      questionId: currentQuestion.id,
      pollQuestion: currentQuestion.question_text,
      pollOptions: questionOptions || [],
      demographic: {
        group: demographic,
        size: sampleSize,
        context: "", // Keep this empty as we'll use dedicated fields
      },
      responseFormat: isTextQuestion ? "individual" : "distribution",
      questionType: isTextQuestion ? "open_ended" : "multiple_choice",
      specialInstructions: specialInstructions,
      pollContext: pollContext
    },
    () => {
      // Only switch to results tab after simulation completes successfully
      setActiveTab("results");
    });

    // No longer immediately switch tabs - wait for callback
  };

  return (
    <div className="space-y-6">
      {/* Header - Mobile Responsive */}
      <div className="flex flex-col space-y-4">
        {/* Buttons row - visible at the top on all screen sizes */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
          <div className="flex items-center">
            <Link href={`/dashboard/polls/${poll.id}`} passHref>
              <Button variant="ghost" size="sm" className="gap-1 h-9">
                <ArrowLeft className="h-4 w-4" />
                <span className="text-sm">Back to Poll</span>
              </Button>
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1 h-9 min-w-[44px]">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline text-sm">Export</span>
            </Button>
            <Button variant="outline" size="sm" className="gap-1 h-9 min-w-[44px]">
              <Share2 className="h-4 w-4" />
              <span className="hidden sm:inline text-sm">Share</span>
            </Button>
          </div>
        </div>

        {/* Title row - full width on its own row */}
        <div className="w-full">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold leading-tight">{poll.title}</h1>
          <p className="text-muted-foreground text-sm sm:text-base mt-1 sm:mt-2">
            {poll.description || "No description provided."}
          </p>
        </div>
      </div>

      <Separator />

      {/* Questions Navigation - Mobile Responsive */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
        <h2 className="text-lg sm:text-xl font-semibold flex items-center gap-2 flex-wrap">
          <ListChecks className="h-4 w-4 sm:h-5 sm:w-5" />
          <span className="text-base sm:text-xl">Question {activeQuestion + 1} of {poll.questions.length}</span>
          {simulation && (
            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
              Simulated
            </span>
          )}
        </h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevQuestion}
            disabled={!hasPrevQuestion}
            className="h-9 min-w-[44px]"
          >
            <span className="hidden sm:inline">Previous</span>
            <span className="sm:hidden">Prev</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextQuestion}
            disabled={!hasNextQuestion}
            className="h-9 min-w-[44px]"
          >
            <span className="hidden sm:inline">Next</span>
            <span className="sm:hidden">Next</span>
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>

      {/* Current Question - Mobile Responsive */}
      <div className="bg-muted/40 p-3 sm:p-4 rounded-lg border">
        <h3 className="font-medium text-base sm:text-lg mb-2 leading-relaxed">
          {currentQuestion?.question_text || "No question selected"}
        </h3>
        {!isTextQuestion ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-3">
            {(
              (currentQuestion?.options as {id: string, text: string, value: string}[] | string[] | null) || []
            ).map((option, index) => (
              <div
                key={typeof option === 'string' ? index : option.id || index}
                className="bg-background flex items-center border rounded-md px-3 py-2 text-sm"
              >
                {typeof option === "string" ? option : option.text}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center mt-2 gap-2 text-sm text-muted-foreground">
            <MessageSquareText className="h-4 w-4" />
            <span>Open-ended question (text responses)</span>
          </div>
        )}
      </div>

      {/* Tabs - Mobile Responsive */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-6">
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabsTrigger value="setup" className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Simulation Setup</span>
            <span className="sm:hidden">Setup</span>
          </TabsTrigger>
          <TabsTrigger
            value="results"
            disabled={!simulation}
            className="flex items-center gap-2 text-sm"
          >
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Results</span>
            <span className="sm:hidden">Results</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="mt-6">
          <SimulationSetup
            isLoading={isLoading}
            demographic={demographic}
            setDemographic={setDemographic}
            sampleSize={sampleSize}
            setSampleSize={setSampleSize}
            specialInstructions={specialInstructions}
            setSpecialInstructions={setSpecialInstructions}
            onSimulate={handleStartSimulation}
            error={error}
            questionType={isTextQuestion ? "open_ended" : "multiple_choice"}
          />
        </TabsContent>

        <TabsContent value="results" className="mt-6">
          {simulation ? (
            <SimulationResults
              results={simulation}
              enhancedUI={true}
              options={!isTextQuestion ?
                (Array.isArray(currentQuestion?.options) ?
                  currentQuestion?.options.map(opt => {
                    if (typeof opt === 'string') return opt;
                    if (opt && typeof opt === 'object' && 'text' in opt) return String(opt.text);
                    return String(opt);
                  }) :
                  []) :
                undefined}
              pollQuestion={currentQuestion?.question_text || ''}
            />
          ) : (
            <div className="text-center py-12">
              <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-medium mb-2">No simulation results yet</h3>
              <p className="text-muted-foreground mb-6">
                Configure your simulation parameters and run the simulation to see results
              </p>
              <Button onClick={() => setActiveTab("setup")}>
                Configure Simulation
              </Button>
            </div>
          )}

          {simulation && (
            <div className="flex items-center justify-end gap-3 mt-8">
              <Button
                variant="outline"
                onClick={resetSimulation}
              >
                New Simulation
              </Button>
              <Button
                onClick={() => {
                  handleNextQuestion();
                  // The tab reset is already handled in handleNextQuestion
                }}
                disabled={!hasNextQuestion}
              >
                Simulate Next Question
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
