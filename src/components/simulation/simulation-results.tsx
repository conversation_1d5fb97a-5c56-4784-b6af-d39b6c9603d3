import { SimulationResponse } from "@/lib/types/simulation";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, Users, TrendingUp, ExternalLink } from "lucide-react";
import { useState } from 'react';

interface SimulationResultsProps {
  results: SimulationResponse;
  className?: string;
  options?: string[];
  enhancedUI?: boolean;
  pollQuestion?: string;
}

// Helper function to extract URL from citation text or return as-is if it's already a URL
function extractUrlFromCitation(citation: string): { url: string; title: string } {
  // If it's already a URL, return it
  if (citation.startsWith('http://') || citation.startsWith('https://')) {
    return { url: citation, title: citation };
  }

  // Try to extract URL from citation text using various patterns
  const urlPatterns = [
    /https?:\/\/[^\s\)]+/g,  // Standard URL pattern
    /\(https?:\/\/[^\)]+\)/g, // URL in parentheses
    /\[https?:\/\/[^\]]+\]/g, // URL in brackets
  ];

  for (const pattern of urlPatterns) {
    const matches = citation.match(pattern);
    if (matches && matches.length > 0) {
      let url = matches[0];
      // Clean up URL by removing brackets/parentheses
      url = url.replace(/[\[\]()]/g, '');
      return { url, title: citation };
    }
  }

  // If no URL found, return the citation as title with no link
  return { url: '', title: citation };
}

// Helper function to open URL in new tab
function openUrl(url: string) {
  if (url) {
    window.open(url, '_blank', 'noopener,noreferrer');
  }
}

interface SimulationResultsProps {
  results: SimulationResponse;
  className?: string;
  options?: string[];
  enhancedUI?: boolean;
  pollQuestion?: string;
}

export function SimulationResults({
  results,
  className,
  options,
  enhancedUI,
  pollQuestion
}: SimulationResultsProps) {
  const [showAnalysis, setShowAnalysis] = useState(true);
  const [showSources, setShowSources] = useState(false);

  // Add safety checks for undefined properties
  if (!results || !results.results) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Error: Simulation results not available</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Unable to display simulation results due to invalid data format.</p>
          <pre className="mt-2 text-xs bg-muted p-2 rounded">
            {JSON.stringify(results, null, 2)}
          </pre>
        </CardContent>
      </Card>
    );
  }

  // Add context information for the current question
  const showContext = pollQuestion && options && options.length > 0;

  // Add a header with question context if available
  const contextHeader = showContext ? (
    <div className="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
      <h3 className="font-medium text-blue-800 text-sm mb-1">
        <span className="inline-block bg-blue-600 text-white px-1.5 py-0.5 rounded mr-1 text-xs">Current Question</span>
        {pollQuestion}
      </h3>
      {options && options.length > 0 && (
        <div className="text-xs text-blue-600 flex flex-wrap gap-1 mt-2">
          <span className="font-medium">Available Options:</span>
          {options.map((opt, i) => (
            <span key={i} className="bg-blue-100 px-1.5 py-0.5 rounded-sm">
              {opt}
            </span>
          ))}
        </div>
      )}
    </div>
  ) : null;

  // Ensure distribution exists and is an object
  const distribution = results.results.distribution || {};

  // Improved option mapping to handle various AI response formats
  const sortedResults = Object.entries(distribution)
    .map(([key, value]) => {
      let displayKey = key;
      let matchFound = false;

      // Check if this is a numbered option (option1, option2, etc.)
      if (options && /^option\d+$/i.test(key)) {
        const optionIdx = parseInt(key.replace(/[^\d]/g, ''), 10) - 1;
        if (options[optionIdx]) {
          displayKey = options[optionIdx];
          matchFound = true;
        }
      }
      // Try fuzzy matching for non-standard option keys with the provided options
      else if (options) {
        // Look for exact matches first
        const exactMatch = options.find(opt =>
          opt.toLowerCase() === key.toLowerCase()
        );
        if (exactMatch) {
          displayKey = exactMatch;
          matchFound = true;
        }
        // If no exact match, try substring match
        else {
          const substringMatch = options.find(opt =>
            key.toLowerCase().includes(opt.toLowerCase()) ||
            opt.toLowerCase().includes(key.toLowerCase())
          );
          if (substringMatch) {
            displayKey = substringMatch;
            matchFound = true;
          }
        }
      }

      return [displayKey, value, matchFound] as [string, number, boolean];
    })
    .sort(([, a], [, b]) => b - a);

  // Check if we have options that didn't match the current question
  const hasUnmatchedOptions = options && options.length > 0 &&
    sortedResults.some(([, , matched]) => !matched);


  // Enhanced UI/UX
  if (enhancedUI) {
    return (
      <div className={`space-y-6 animate-fade-in ${className || ''}`}>
        {/* Context Header */}
        {contextHeader}

        {/* Warning for mismatched options */}
        {hasUnmatchedOptions && (
          <div className="mb-4 p-2 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-xs text-amber-700">
              <strong>Note:</strong> Some simulation results may not perfectly match the current question options.
              This can happen when the AI generates results in a different format. Try running a new simulation.
            </p>
          </div>
        )}

        {/* Sticky Sub-header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-muted pb-2 mb-2 border-b flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <span className="font-semibold text-lg flex items-center gap-2"><Brain className="h-5 w-5 text-primary" /> AI Simulation Results</span>
            <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded font-medium">{results.metadata?.sampleSize || 'N/A'} responses</span>
          </div>
          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
            <span><Users className="inline h-3 w-3 mr-1" />{results.metadata?.demographic || 'N/A'}</span>
            <span className="font-semibold text-green-700 dark:text-green-400">Confidence: {results.metadata?.confidence ? (results.metadata.confidence * 100).toFixed(0) + '%' : 'N/A'}</span>
          </div>
        </div>

        {/* Response Distribution */}
        <div className="space-y-4">
          <h4 className="font-semibold flex items-center gap-2 text-base sticky top-10 bg-white dark:bg-muted z-10"><TrendingUp className="h-4 w-4" /> Response Distribution</h4>
          <div className="space-y-4">
            {sortedResults.length > 0 ? (
              sortedResults.map(([option, percentage, matched]) => (
                <div key={option} className="space-y-2">
                  <div className="flex items-center justify-between text-base font-medium">
                    <span className={`truncate ${!matched && "text-amber-600"}`}>
                      {option}
                      {!matched && options && options.length > 0 && (
                        <span className="ml-1 text-xs text-amber-500">(option mismatch)</span>
                      )}
                    </span>
                    <span className="text-muted-foreground font-semibold">
                      {Number(percentage).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-300 ${matched ? "bg-primary" : "bg-amber-400"}`}
                      style={{ width: `${Number(percentage)}%` }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">No distribution data available</div>
            )}
          </div>
        </div>

        {/* Collapsible AI Analysis */}
        {results.results.analysis && (
          <div className="pt-2 border-t">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-primary hover:underline mb-2"
              onClick={() => setShowAnalysis((v) => !v)}
              type="button"
            >
              {showAnalysis ? '▼' : '►'} AI Analysis
            </button>
            {showAnalysis && (
              <p className="text-sm text-muted-foreground leading-relaxed animate-fade-in">
                {results.results.analysis}
              </p>
            )}
          </div>
        )}

        {/* Collapsible Sources */}
        {((results.metadata?.citations && Array.isArray(results.metadata.citations) && results.metadata.citations.length > 0) ||
          (results.metadata?.searchResults && Array.isArray(results.metadata.searchResults) && results.metadata.searchResults.length > 0)) && (
          <div className="pt-2 border-t">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-primary hover:underline mb-2"
              onClick={() => setShowSources((v) => !v)}
              type="button"
            >
              {showSources ? '▼' : '►'} Sources
            </button>
            {showSources && (
              <div className="text-xs text-muted-foreground space-y-2 animate-fade-in">
                {/* Prioritize search_results over citations for better UX */}
                {results.metadata?.searchResults && results.metadata.searchResults.length > 0 ? (
                  results.metadata.searchResults.slice(0, 5).map((searchResult, index) => (
                    <div key={index} className="flex items-start gap-2 group hover:bg-muted/30 rounded-md p-2 -mx-2 transition-colors">
                      <span className="text-muted-foreground mt-0.5">{index + 1}.</span>
                      <div className="flex-1 min-w-0">
                        <button
                          onClick={() => openUrl(searchResult.url)}
                          className="text-left hover:text-primary hover:underline transition-all duration-200 group-hover:text-primary flex items-center gap-1.5 break-words text-sm"
                          title={`Visit: ${searchResult.url}`}
                        >
                          <span className="truncate">{searchResult.title}</span>
                          <ExternalLink className="h-3 w-3 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-0.5" />
                        </button>
                        {searchResult.date && (
                          <div className="text-xs text-muted-foreground/70 mt-1">
                            {searchResult.date}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground/50 mt-1 truncate">
                          {searchResult.url}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  // Fallback to citations if search_results not available
                  results.metadata.citations.slice(0, 5).map((citation, index) => {
                    const { url, title } = extractUrlFromCitation(citation);
                    return (
                      <div key={index} className="flex items-start gap-2 group hover:bg-muted/30 rounded-md p-2 -mx-2 transition-colors">
                        <span className="text-muted-foreground mt-0.5">{index + 1}.</span>
                        <div className="flex-1 min-w-0">
                          {url ? (
                            <button
                              onClick={() => openUrl(url)}
                              className="text-left hover:text-primary hover:underline transition-all duration-200 group-hover:text-primary flex items-center gap-1.5 break-words text-sm"
                              title={`Visit: ${url}`}
                            >
                              <span className="truncate">{title}</span>
                              <ExternalLink className="h-3 w-3 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-0.5" />
                            </button>
                          ) : (
                            <span className="truncate text-sm">{title}</span>
                          )}
                          {url && (
                            <div className="text-xs text-muted-foreground/50 mt-1 truncate">
                              {url}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Simulation Results
          </CardTitle>
          <Badge variant="secondary" className="gap-1">
            <Users className="h-3 w-3" />
            {results.metadata?.sampleSize || 'N/A'} responses
          </Badge>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Demographic: {results.metadata?.demographic || 'N/A'}</span>
          <span>Confidence: {results.metadata?.confidence ?
            (results.metadata.confidence * 100).toFixed(0) + '%' :
            'N/A'}</span>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 max-h-[60vh] overflow-y-auto">
        {/* Response Distribution */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Response Distribution
          </h4>
          <div className="space-y-3">
            {sortedResults.length > 0 ? (
              sortedResults.map(([option, percentage, matched]) => (
                <div key={option} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className={`font-medium truncate ${!matched && "text-amber-600"}`}>
                      {option}
                      {!matched && options && options.length > 0 && (
                        <span className="ml-1 text-xs text-amber-500">(option mismatch)</span>
                      )}
                    </span>
                    <span className="text-muted-foreground font-medium">
                      {Number(percentage).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${matched ? "bg-primary" : "bg-amber-400"}`}
                      style={{ width: `${Number(percentage)}%` }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">No distribution data available</div>
            )}
          </div>
        </div>

        {/* Analysis */}
        {results.results.analysis && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">AI Analysis</h4>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {results.results.analysis}
            </p>
          </div>
        )}

        {/* Citations */}
        {((results.metadata?.citations && Array.isArray(results.metadata.citations) && results.metadata.citations.length > 0) ||
          (results.metadata?.searchResults && Array.isArray(results.metadata.searchResults) && results.metadata.searchResults.length > 0)) && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">Sources</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              {/* Prioritize search_results for better UX */}
              {results.metadata?.searchResults && results.metadata.searchResults.length > 0 ? (
                results.metadata.searchResults.slice(0, 3).map((searchResult, index) => (
                  <div key={index} className="group hover:bg-muted/30 rounded-md p-1 -mx-1 transition-colors">
                    <button
                      onClick={() => openUrl(searchResult.url)}
                      className="text-left hover:text-primary hover:underline transition-all duration-200 group-hover:text-primary flex items-center gap-1.5 truncate w-full text-sm"
                      title={`Visit: ${searchResult.url}`}
                    >
                      <span className="truncate">{index + 1}. {searchResult.title}</span>
                      <ExternalLink className="h-3 w-3 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-0.5" />
                    </button>
                    <div className="text-xs text-muted-foreground/50 mt-1 truncate pl-4">
                      {searchResult.url}
                    </div>
                  </div>
                ))
              ) : (
                results.metadata.citations.slice(0, 3).map((citation, index) => {
                  const { url, title } = extractUrlFromCitation(citation);
                  return (
                    <div key={index} className="group hover:bg-muted/30 rounded-md p-1 -mx-1 transition-colors">
                      {url ? (
                        <>
                          <button
                            onClick={() => openUrl(url)}
                            className="text-left hover:text-primary hover:underline transition-all duration-200 group-hover:text-primary flex items-center gap-1.5 truncate w-full text-sm"
                            title={`Visit: ${url}`}
                          >
                            <span className="truncate">{index + 1}. {title}</span>
                            <ExternalLink className="h-3 w-3 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-0.5" />
                          </button>
                          <div className="text-xs text-muted-foreground/50 mt-1 truncate pl-4">
                            {url}
                          </div>
                        </>
                      ) : (
                        <div className="truncate text-sm">
                          {index + 1}. {title}
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
