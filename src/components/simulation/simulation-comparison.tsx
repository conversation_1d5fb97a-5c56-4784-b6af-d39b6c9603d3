"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart3, TrendingUp, AlertCircle, Loader2, Download, RefreshCw } from 'lucide-react';
import { SimulationResponse, BatchSimulationResult } from '@/lib/types/simulation';
import { cn } from '@/lib/utils';

interface SimulationComparisonProps {
  simulations: (SimulationResponse | BatchSimulationResult)[];
  pollQuestion: string;
  pollOptions: string[];
  className?: string;
}

interface ComparisonData {
  comparisonSummary: {
    totalSimulations: number;
    comparisonType: string;
    overallSimilarity: number;
    significantDifferences: string[];
    keyTrends: string[];
  };
  statisticalAnalysis: {
    varianceAnalysis: {
      lowVarianceOptions: string[];
      highVarianceOptions: string[];
    };
    correlationAnalysis: {
      strongCorrelations: Array<{
        simulation1: string;
        simulation2: string;
        correlation: number;
      }>;
      weakCorrelations: Array<{
        simulation1: string;
        simulation2: string;
        correlation: number;
      }>;
    };
    confidenceAnalysis: {
      averageConfidence: number;
      confidenceRange: { min: number; max: number };
      reliabilityScore: number;
    };
  };
  demographicInsights: {
    consensusDemographics: string[];
    polarizedDemographics: string[];
    demographicPatterns: string[];
  };
  actionableInsights: {
    strategicImplications: string[];
    targetingRecommendations: string[];
    messagingInsights: string[];
    riskAssessments: string[];
  };
  comparisonMetrics: {
    stabilityScore: number;
    predictionAccuracy?: number;
    demographicCoverage: number;
    methodologyRating: number;
  };
}

export function SimulationComparison({
  simulations,
  pollQuestion,
  pollOptions,
  className
}: SimulationComparisonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [comparisonType, setComparisonType] = useState<string>('cross_demographic');
  const [analysisDepth, setAnalysisDepth] = useState<string>('comprehensive');
  const [activeTab, setActiveTab] = useState<string>('overview');

  const runComparison = useCallback(async () => {
    if (simulations.length < 2) {
      setError('At least 2 simulations are required for comparison');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/compare-simulations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          simulations,
          comparisonType,
          analysisDepth,
          pollQuestion,
          pollOptions,
          comparisonContext: `Comparing ${simulations.length} simulation results for poll analysis`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setComparisonData(data.analysis);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to compare simulations');
      }
    } catch (error) {
      console.error('Comparison error:', error);
      setError('An error occurred while comparing simulations');
    } finally {
      setIsLoading(false);
    }
  }, [simulations, comparisonType, analysisDepth, pollQuestion, pollOptions]);

  const exportComparison = () => {
    if (!comparisonData) return;

    const exportData = {
      pollQuestion,
      pollOptions,
      comparisonType,
      analysisDepth,
      totalSimulations: simulations.length,
      timestamp: new Date().toISOString(),
      ...comparisonData
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `simulation-comparison-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.8) return 'text-green-600 bg-green-50 border-green-200';
    if (similarity >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getStabilityColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  useEffect(() => {
    if (simulations.length >= 2) {
      runComparison();
    }
  }, [simulations.length, comparisonType, analysisDepth, runComparison]);

  if (simulations.length < 2) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Comparisons Available</h3>
          <p className="text-muted-foreground text-center max-w-md">
            Run at least 2 simulations to enable comparison analysis.
            Comparisons help identify patterns and validate simulation reliability.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h3 className="text-xl font-semibold">Simulation Comparison</h3>
          <p className="text-sm text-muted-foreground">
            Comparing {simulations.length} simulation results
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={comparisonType} onValueChange={setComparisonType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cross_demographic">Cross-Demographic</SelectItem>
              <SelectItem value="temporal">Temporal Analysis</SelectItem>
              <SelectItem value="methodology">Methodology Comparison</SelectItem>
              <SelectItem value="confidence">Confidence Analysis</SelectItem>
            </SelectContent>
          </Select>
          <Select value={analysisDepth} onValueChange={setAnalysisDepth}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="basic">Basic</SelectItem>
              <SelectItem value="detailed">Detailed</SelectItem>
              <SelectItem value="comprehensive">Comprehensive</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={runComparison} disabled={isLoading} size="sm">
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          {comparisonData && (
            <Button onClick={exportComparison} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="flex items-center gap-3 pt-6">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <div>
              <p className="text-sm font-medium text-red-800">Comparison Failed</p>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-lg font-medium mb-2">Analyzing Simulations</h3>
            <p className="text-muted-foreground text-center">
              Running {analysisDepth} comparison analysis across {simulations.length} simulations...
            </p>
          </CardContent>
        </Card>
      ) : comparisonData ? (
        /* Comparison Results */
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="statistical">Statistical</TabsTrigger>
            <TabsTrigger value="demographics">Demographics</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Overall Similarity</span>
                    <Badge className={getSimilarityColor(comparisonData.comparisonSummary.overallSimilarity)}>
                      {(comparisonData.comparisonSummary.overallSimilarity * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <Progress
                    value={comparisonData.comparisonSummary.overallSimilarity * 100}
                    className="h-2"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Stability Score</span>
                    <span className={cn("text-sm font-bold", getStabilityColor(comparisonData.comparisonMetrics.stabilityScore))}>
                      {(comparisonData.comparisonMetrics.stabilityScore * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={comparisonData.comparisonMetrics.stabilityScore * 100}
                    className="h-2"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Coverage</span>
                    <span className="text-sm font-bold text-blue-600">
                      {(comparisonData.comparisonMetrics.demographicCoverage * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={comparisonData.comparisonMetrics.demographicCoverage * 100}
                    className="h-2"
                  />
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Key Findings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {comparisonData.comparisonSummary.keyTrends.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Key Trends</h4>
                    <ul className="space-y-2">
                      {comparisonData.comparisonSummary.keyTrends.map((trend, idx) => (
                        <li key={idx} className="text-sm p-3 bg-blue-50 rounded-md border border-blue-200">
                          <TrendingUp className="h-4 w-4 inline mr-2 text-blue-600" />
                          {trend}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {comparisonData.comparisonSummary.significantDifferences.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Significant Differences</h4>
                    <ul className="space-y-2">
                      {comparisonData.comparisonSummary.significantDifferences.map((diff, idx) => (
                        <li key={idx} className="text-sm p-3 bg-yellow-50 rounded-md border border-yellow-200">
                          <AlertCircle className="h-4 w-4 inline mr-2 text-yellow-600" />
                          {diff}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Statistical Analysis Tab */}
          <TabsContent value="statistical" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Variance Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {comparisonData.statisticalAnalysis.varianceAnalysis.lowVarianceOptions.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Low Variance (Consistent)</h4>
                        <div className="flex flex-wrap gap-1">
                          {comparisonData.statisticalAnalysis.varianceAnalysis.lowVarianceOptions.map((option, idx) => (
                            <Badge key={idx} variant="outline" className="text-green-600 border-green-200 bg-green-50">
                              {option}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {comparisonData.statisticalAnalysis.varianceAnalysis.highVarianceOptions.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">High Variance (Inconsistent)</h4>
                        <div className="flex flex-wrap gap-1">
                          {comparisonData.statisticalAnalysis.varianceAnalysis.highVarianceOptions.map((option, idx) => (
                            <Badge key={idx} variant="outline" className="text-red-600 border-red-200 bg-red-50">
                              {option}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Confidence Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Average Confidence</span>
                        <span className="font-medium">
                          {(comparisonData.statisticalAnalysis.confidenceAnalysis.averageConfidence * 100).toFixed(1)}%
                        </span>
                      </div>
                      <Progress
                        value={comparisonData.statisticalAnalysis.confidenceAnalysis.averageConfidence * 100}
                        className="h-2"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Min Confidence</span>
                        <p className="font-medium">
                          {(comparisonData.statisticalAnalysis.confidenceAnalysis.confidenceRange.min * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Max Confidence</span>
                        <p className="font-medium">
                          {(comparisonData.statisticalAnalysis.confidenceAnalysis.confidenceRange.max * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-muted-foreground">Reliability Score</span>
                      <div className="flex items-center gap-2 mt-1">
                        <Progress
                          value={comparisonData.statisticalAnalysis.confidenceAnalysis.reliabilityScore * 100}
                          className="h-2 flex-1"
                        />
                        <span className="text-sm font-medium">
                          {(comparisonData.statisticalAnalysis.confidenceAnalysis.reliabilityScore * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {comparisonData.statisticalAnalysis.correlationAnalysis.strongCorrelations.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Correlation Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {comparisonData.statisticalAnalysis.correlationAnalysis.strongCorrelations.map((corr, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-green-50 rounded-md border border-green-200">
                        <span className="text-sm">
                          Strong correlation between simulations {corr.simulation1} and {corr.simulation2}
                        </span>
                        <Badge className="bg-green-100 text-green-800">
                          {(corr.correlation * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Demographics Tab */}
          <TabsContent value="demographics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Consensus Demographics</CardTitle>
                  <CardDescription>Demographics showing consistent patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  {comparisonData.demographicInsights.consensusDemographics.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {comparisonData.demographicInsights.consensusDemographics.map((demo, idx) => (
                        <Badge key={idx} variant="outline" className="text-green-600 border-green-200 bg-green-50">
                          {demo}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No consistent demographic patterns identified</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Polarized Demographics</CardTitle>
                  <CardDescription>Demographics with significant variation</CardDescription>
                </CardHeader>
                <CardContent>
                  {comparisonData.demographicInsights.polarizedDemographics.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {comparisonData.demographicInsights.polarizedDemographics.map((demo, idx) => (
                        <Badge key={idx} variant="outline" className="text-red-600 border-red-200 bg-red-50">
                          {demo}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No polarized demographic patterns identified</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {comparisonData.demographicInsights.demographicPatterns.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Demographic Patterns</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {comparisonData.demographicInsights.demographicPatterns.map((pattern, idx) => (
                      <li key={idx} className="text-sm p-3 bg-blue-50 rounded-md border border-blue-200">
                        {pattern}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Actionable Insights Tab */}
          <TabsContent value="insights" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Strategic Implications</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {comparisonData.actionableInsights.strategicImplications.map((insight, idx) => (
                      <li key={idx} className="text-sm p-3 bg-purple-50 rounded-md border border-purple-200">
                        {insight}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Targeting Recommendations</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {comparisonData.actionableInsights.targetingRecommendations.map((rec, idx) => (
                      <li key={idx} className="text-sm p-3 bg-orange-50 rounded-md border border-orange-200">
                        {rec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Messaging Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {comparisonData.actionableInsights.messagingInsights.map((insight, idx) => (
                      <li key={idx} className="text-sm p-3 bg-teal-50 rounded-md border border-teal-200">
                        {insight}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Risk Assessments</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {comparisonData.actionableInsights.riskAssessments.map((risk, idx) => (
                      <li key={idx} className="text-sm p-3 bg-red-50 rounded-md border border-red-200">
                        <AlertCircle className="h-4 w-4 inline mr-2 text-red-600" />
                        {risk}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Metrics Tab */}
          <TabsContent value="metrics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span>Stability Score</span>
                      <span className={cn("font-bold", getStabilityColor(comparisonData.comparisonMetrics.stabilityScore))}>
                        {(comparisonData.comparisonMetrics.stabilityScore * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={comparisonData.comparisonMetrics.stabilityScore * 100} className="h-2" />
                  </div>

                  <div>
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span>Demographic Coverage</span>
                      <span className="font-bold text-blue-600">
                        {(comparisonData.comparisonMetrics.demographicCoverage * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={comparisonData.comparisonMetrics.demographicCoverage * 100} className="h-2" />
                  </div>

                  <div>
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span>Methodology Rating</span>
                      <span className="font-bold text-purple-600">
                        {(comparisonData.comparisonMetrics.methodologyRating * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={comparisonData.comparisonMetrics.methodologyRating * 100} className="h-2" />
                  </div>

                  {comparisonData.comparisonMetrics.predictionAccuracy && (
                    <div>
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span>Prediction Accuracy</span>
                        <span className="font-bold text-green-600">
                          {(comparisonData.comparisonMetrics.predictionAccuracy * 100).toFixed(1)}%
                        </span>
                      </div>
                      <Progress value={comparisonData.comparisonMetrics.predictionAccuracy * 100} className="h-2" />
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Comparison Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Simulations</span>
                      <p className="text-lg font-bold">{comparisonData.comparisonSummary.totalSimulations}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Comparison Type</span>
                      <p className="text-sm font-medium capitalize">{comparisonData.comparisonSummary.comparisonType.replace('_', ' ')}</p>
                    </div>
                  </div>

                  <div className="pt-3 border-t">
                    <span className="text-sm text-muted-foreground">Analysis Completed</span>
                    <p className="text-sm font-medium">{new Date().toLocaleString()}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      ) : null}
    </div>
  );
}
