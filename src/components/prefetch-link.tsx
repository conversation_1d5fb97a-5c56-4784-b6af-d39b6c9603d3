import React, { useState } from 'react';
import Link from 'next/link';
import { useQueryClient } from '@tanstack/react-query';

// Define pollKeys locally to avoid circular dependencies
const pollKeys = {
  detail: (id: string) => ['polls', 'detail', id]
};

interface PrefetchLinkProps {
  href: string;
  pollId?: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Enhanced Link component that prefetches poll data when hovered
 * This improves perceived performance by loading data before the user clicks
 */
export function PrefetchLink({ href, pollId, children, className }: PrefetchLinkProps) {
  const queryClient = useQueryClient();
  const [hasPrefetched, setHasPrefetched] = useState(false);

  // Handle mouse enter - prefetch data if it's a poll link and hasn't been prefetched yet
  const handleMouseEnter = () => {
    if (pollId && !hasPrefetched) {
      // Prefetch the poll detail data
      queryClient.prefetchQuery({
        queryKey: pollKeys.detail(pollId),
        // We don't need to provide a queryFn as it will use the one registered with this key
        // This assumes you have a useQuery hook for poll details with this key
      });
      
      setHasPrefetched(true);
    }
  };

  return (
    <Link 
      href={href} 
      className={className}
      onMouseEnter={handleMouseEnter}
    >
      {children}
    </Link>
  );
}
