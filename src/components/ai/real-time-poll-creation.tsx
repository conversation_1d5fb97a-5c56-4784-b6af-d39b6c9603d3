"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import {
  Wand2,
  Sparkles,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';
import { useAIPollGeneration, useAIPollInsights } from '@/hooks/use-ai-poll-generation';
import { cn } from '@/lib/utils';
import type { PollCreationInput } from '@/lib/ai/schemas';

interface RealTimePollCreationProps {
  onPollCreated?: (poll: {
    id?: string;
    title: string;
    description?: string;
    questions?: Array<{
      text: string;
      type: string;
      options?: Array<{ text: string; value: string }>;
    }>;
  }) => void;
  className?: string;
}

export function RealTimePollCreation({ onPollCreated, className }: RealTimePollCreationProps) {
  const [formData, setFormData] = useState<PollCreationInput>({
    title: '',
    topic: '',
    audience: '',
    additionalInfo: '',
    content: '',
  });
  const [activeTab, setActiveTab] = useState('basic');
  const [creationStage, setCreationStage] = useState<'input' | 'generating' | 'reviewing' | 'completed'>('input');

  const {
    generatedPoll,
    analysisText,
    isGeneratingPoll,
    isAnalyzing,
    isStreaming,
    pollGenerationError,
    generatePollWithAnalysis,
    saveGeneratedPoll,
    isSavingPoll,
    pollSaved,
    resetGeneration,
  } = useAIPollGeneration();

  const {
    insights,
    currentInsight,
    isGeneratingInsight,
    getInsights,
    clearInsights,
  } = useAIPollInsights();

  // Update creation stage based on generation state
  useEffect(() => {
    if (isGeneratingPoll || isAnalyzing) {
      setCreationStage('generating');
    } else if (generatedPoll && !pollSaved) {
      setCreationStage('reviewing');
    } else if (pollSaved) {
      setCreationStage('completed');
    } else {
      setCreationStage('input');
    }
  }, [isGeneratingPoll, isAnalyzing, generatedPoll, pollSaved]);

  const handleInputChange = useCallback((field: keyof PollCreationInput, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleGenerate = useCallback(async () => {
    if (!formData.topic || !formData.audience) {
      return;
    }

    try {
      await generatePollWithAnalysis(formData);
      // Get insights about the generated poll
      if (generatedPoll) {
        getInsights(generatedPoll);
      }
    } catch (error) {
      console.error('Poll generation failed:', error);
    }
  }, [formData, generatePollWithAnalysis, generatedPoll, getInsights]);

  const handleSave = useCallback(async () => {
    try {
      const savedPoll = await saveGeneratedPoll();
      onPollCreated?.(savedPoll);
    } catch (error) {
      console.error('Failed to save poll:', error);
    }
  }, [saveGeneratedPoll, onPollCreated]);

  const handleReset = useCallback(() => {
    resetGeneration();
    clearInsights();
    setFormData({
      title: '',
      topic: '',
      audience: '',
      additionalInfo: '',
      content: '',
    });
    setCreationStage('input');
    setActiveTab('basic');
  }, [resetGeneration, clearInsights]);

  const getProgressValue = () => {
    switch (creationStage) {
      case 'input': return 0;
      case 'generating': return 50;
      case 'reviewing': return 80;
      case 'completed': return 100;
      default: return 0;
    }
  };

  const isFormValid = formData.topic.trim() && formData.audience.trim();

  return (
    <div className={cn("w-full max-w-4xl mx-auto space-y-6", className)}>
      {/* Progress Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="w-5 h-5 text-primary" />
                AI Poll Creation Studio
              </CardTitle>
              <CardDescription>
                Create intelligent polls with real-time AI assistance
              </CardDescription>
            </div>
            <Badge variant={creationStage === 'completed' ? 'default' : 'secondary'}>
              {creationStage === 'input' && 'Ready'}
              {creationStage === 'generating' && 'Generating...'}
              {creationStage === 'reviewing' && 'Review'}
              {creationStage === 'completed' && 'Complete'}
            </Badge>
          </div>
          <Progress value={getProgressValue()} className="w-full" />
        </CardHeader>
      </Card>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Creation Form */}
        <div className="lg:col-span-2 space-y-6">
          {creationStage === 'input' && (
            <Card>
              <CardHeader>
                <CardTitle>Poll Configuration</CardTitle>
                <CardDescription>
                  Provide details about your poll and let AI do the rest
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    <TabsTrigger value="content">Content</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Poll Title</Label>
                      <Input
                        id="title"
                        placeholder="Enter poll title (optional - AI will generate if empty)"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="topic">Topic *</Label>
                      <Input
                        id="topic"
                        placeholder="e.g., Remote work preferences"
                        value={formData.topic}
                        onChange={(e) => handleInputChange('topic', e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="audience">Target Audience *</Label>
                      <Input
                        id="audience"
                        placeholder="e.g., Software developers, Ages 25-40"
                        value={formData.audience}
                        onChange={(e) => handleInputChange('audience', e.target.value)}
                        required
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="additionalInfo">Additional Context</Label>
                      <Textarea
                        id="additionalInfo"
                        placeholder="Any specific requirements, constraints, or objectives for this poll..."
                        value={formData.additionalInfo}
                        onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                        rows={4}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="content" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="content">Source Content</Label>
                      <Textarea
                        id="content"
                        placeholder="Paste any relevant content (articles, documents, etc.) to analyze..."
                        value={formData.content}
                        onChange={(e) => handleInputChange('content', e.target.value)}
                        rows={6}
                      />
                      <p className="text-sm text-muted-foreground">
                        AI will analyze this content to generate relevant poll questions
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex gap-3 mt-6">
                  <Button
                    onClick={handleGenerate}
                    disabled={!isFormValid || isGeneratingPoll}
                    className="flex-1"
                  >
                    {isGeneratingPoll ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Generating Poll...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generate AI Poll
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Generation Progress */}
          {creationStage === 'generating' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 animate-spin" />
                  Creating Your Poll
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      isAnalyzing ? "bg-primary animate-pulse" : "bg-green-500"
                    )} />
                    <span className="text-sm">
                      Analyzing content and requirements
                    </span>
                  </div>

                  <div className={cn(
                    "flex items-center gap-3",
                    !isAnalyzing ? "opacity-100" : "opacity-50"
                  )}>
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      isGeneratingPoll && !isAnalyzing ? "bg-primary animate-pulse" :
                      !isGeneratingPoll && generatedPoll ? "bg-green-500" : "bg-muted"
                    )} />
                    <span className="text-sm">
                      Generating optimized questions
                    </span>
                  </div>
                </div>

                {(analysisText || isStreaming) && (
                  <div className="bg-muted/30 rounded-lg p-4">
                    <h4 className="text-sm font-medium mb-2">AI Analysis:</h4>
                    <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {analysisText}
                      {isStreaming && <span className="animate-pulse">▊</span>}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Poll Review */}
          {creationStage === 'reviewing' && generatedPoll && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  Review Your Generated Poll
                </CardTitle>
                <CardDescription>
                  Review and customize your AI-generated poll before saving
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{generatedPoll.title}</h3>
                  <p className="text-muted-foreground">{generatedPoll.description}</p>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Questions:</h4>
                  {generatedPoll.questions?.map((question: {
                    text: string;
                    type: string;
                    options?: Array<{ text: string; value: string }>;
                  }, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="font-medium">{question.text}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Type: {question.type}
                      </div>
                      {question.options && question.options.length > 0 && (
                        <div className="mt-2">
                          <div className="text-sm font-medium">Options:</div>
                          <ul className="text-sm text-muted-foreground ml-4">
                            {question.options.map((option: { text: string; value: string }, optIndex: number) => (
                              <li key={optIndex}>• {option.text}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="flex gap-3">
                  <Button onClick={handleSave} disabled={isSavingPoll} className="flex-1">
                    {isSavingPoll ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Save Poll
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={handleReset}>
                    Start Over
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completion */}
          {creationStage === 'completed' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  Poll Created Successfully!
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Your AI-generated poll has been saved and is ready to use.
                </p>
                <div className="flex gap-3">
                  <Button onClick={handleReset} variant="outline">
                    Create Another Poll
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Real-time Insights Sidebar */}
        <div className="space-y-6">
          {/* AI Insights */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <TrendingUp className="w-4 h-4" />
                AI Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {isGeneratingInsight && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="w-3 h-3 animate-spin" />
                  Analyzing poll...
                </div>
              )}

              {currentInsight && (
                <div className="bg-blue-50 dark:bg-blue-950/30 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                  <div className="text-sm">{currentInsight}</div>
                </div>
              )}

              {insights.length > 0 && (
                <div className="space-y-2">
                  {insights.slice(0, 3).map((insight, index) => (
                    <div key={index} className="bg-muted/50 rounded p-2 text-sm">
                      {insight}
                    </div>
                  ))}
                </div>
              )}

              {!isGeneratingInsight && insights.length === 0 && !currentInsight && (
                <div className="text-sm text-muted-foreground text-center py-4">
                  Generate a poll to see AI insights
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Creation Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Progress</span>
                <span className="font-medium">{getProgressValue()}%</span>
              </div>

              {generatedPoll && (
                <>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Questions</span>
                    <span className="font-medium">{generatedPoll.questions?.length || 0}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Target</span>
                    <span className="font-medium">{formData.audience}</span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Error Display */}
          {pollGenerationError && (
            <Card className="border-destructive">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="w-4 h-4" />
                  Generation Error
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {pollGenerationError.message || 'Failed to generate poll'}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGenerate}
                  className="mt-2"
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

export default RealTimePollCreation;
