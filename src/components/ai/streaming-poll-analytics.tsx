"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  Users,
  Clock,
  Activity,
  BarChart3,
  MessageSquare,
  Target,
  Zap,
  Eye,
  Pause,
  Play,
  RefreshCw
} from 'lucide-react';
import {
  useStreamingAnalytics,
  useRealTimeResponseAnalysis,
  useLivePollMonitoring
} from '@/hooks/use-streaming-analytics';
import { cn } from '@/lib/utils';

interface StreamingPollAnalyticsProps {
  pollId: string;
  pollData?: {
    id?: string;
    title?: string;
    description?: string;
    questions?: Array<{
      text: string;
      type: string;
      options?: Array<{ text: string; value: string }>;
    }>;
  };
  className?: string;
}

export function StreamingPollAnalytics({ pollId, pollData, className }: StreamingPollAnalyticsProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLiveMode, setIsLiveMode] = useState(true);

  const {
    currentAnalysis,
    analyticsHistory,
    isConnected,
    isAnalyzing,
    analysisError,
    runAnalysis,
    stopAnalysis,
    analyzePerformance,
    analyzeDemographics,
    analyzeTrends,
    analyzeSentiment,
    clearHistory,
  } = useStreamingAnalytics(pollData);

  const {
    insights,
    totalResponses,
    newResponsesCount,
    isAnalyzingResponse,
    refetchResponses,
    clearInsights,
  } = useRealTimeResponseAnalysis(pollId);

  const {
    metrics,
    performanceInsight,
  } = useLivePollMonitoring(pollId);

  const toggleLiveMode = () => {
    if (isLiveMode) {
      stopAnalysis();
    } else if (pollData) {
      runAnalysis(pollData, 'resume');
    }
    setIsLiveMode(!isLiveMode);
  };

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Header Controls */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className={cn("w-5 h-5", isConnected ? "text-green-500" : "text-muted-foreground")} />
                Live Poll Analytics
              </CardTitle>
              <CardDescription>
                Real-time AI-powered insights and analysis
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isConnected ? "default" : "secondary"}>
                {isConnected ? "Live" : "Paused"}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleLiveMode}
                className="gap-2"
              >
                {isLiveMode ? (
                  <>
                    <Pause className="w-4 h-4" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    Resume
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid gap-6 lg:grid-cols-4">
        {/* Main Analytics Panel */}
        <div className="lg:col-span-3 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="responses">Responses</TabsTrigger>
              <TabsTrigger value="demographics">Demographics</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Responses</p>
                        <p className="text-2xl font-bold">{totalResponses}</p>
                      </div>
                      <Users className="w-8 h-8 text-muted-foreground" />
                    </div>
                    {newResponsesCount > 0 && (
                      <Badge variant="secondary" className="mt-2">
                        +{newResponsesCount} new
                      </Badge>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Completion Rate</p>
                        <p className="text-2xl font-bold">{Math.round(metrics.completionRate)}%</p>
                      </div>
                      <Target className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <Progress value={metrics.completionRate} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Avg. Time</p>
                        <p className="text-2xl font-bold">{Math.round(metrics.averageTime)}s</p>
                      </div>
                      <Clock className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Live Analysis Stream */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Zap className="w-5 h-5" />
                    Live AI Analysis
                    {isAnalyzing && <Clock className="w-4 h-4 animate-spin" />}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Current Analysis */}
                    {currentAnalysis && (
                      <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 animate-pulse" />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-primary mb-1">
                              Live Analysis
                            </div>
                            <div className="text-sm whitespace-pre-wrap">
                              {currentAnalysis}
                              {isAnalyzing && <span className="animate-pulse">▊</span>}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Performance Insights */}
                    {performanceInsight && (
                      <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <BarChart3 className="w-4 h-4 text-blue-600 mt-1" />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-blue-600 mb-1">
                              Performance Insight
                            </div>
                            <div className="text-sm text-blue-800 dark:text-blue-200">
                              {performanceInsight}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Quick Actions */}
                    <div className="flex gap-2 flex-wrap">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => pollData && analyzePerformance(pollData)}
                        disabled={isAnalyzing || !pollData}
                      >
                        <BarChart3 className="w-4 h-4 mr-1" />
                        Analyze Performance
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => pollData && analyzeDemographics(pollData)}
                        disabled={isAnalyzing || !pollData}
                      >
                        <Users className="w-4 h-4 mr-1" />
                        Demographics
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => pollData && analyzeSentiment(pollData)}
                        disabled={isAnalyzing || !pollData}
                      >
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Sentiment
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Responses Tab */}
            <TabsContent value="responses" className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle>Recent Response Activity</CardTitle>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refetchResponses()}
                    >
                      <RefreshCw className="w-4 h-4 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {isAnalyzingResponse && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                      <Clock className="w-4 h-4 animate-spin" />
                      Analyzing new responses...
                    </div>
                  )}

                  <div className="space-y-3">
                    {insights.slice(0, 5).map((insight) => (
                      <div
                        key={insight.id}
                        className="flex items-start gap-3 p-3 rounded-lg border"
                      >
                        <div className={cn(
                          "w-2 h-2 rounded-full mt-2",
                          insight.type === 'trend' ? "bg-green-500" :
                          insight.type === 'anomaly' ? "bg-yellow-500" :
                          insight.type === 'pattern' ? "bg-blue-500" :
                          "bg-gray-500"
                        )} />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <Badge variant="outline" className="text-xs">
                              {insight.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {insight.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm">{insight.message}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="text-xs text-muted-foreground">
                              Confidence: {Math.round(insight.confidence * 100)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {insights.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Eye className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p>Waiting for responses to analyze...</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Demographics Tab */}
            <TabsContent value="demographics">
              <Card>
                <CardHeader>
                  <CardTitle>Demographic Analysis</CardTitle>
                  <CardDescription>
                    AI-powered demographic insights from response patterns
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => pollData && analyzeDemographics(pollData)}
                    disabled={isAnalyzing || !pollData}
                    className="mb-4"
                  >
                    {isAnalyzing ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Users className="w-4 h-4 mr-2" />
                        Analyze Demographics
                      </>
                    )}
                  </Button>

                  {/* Demographics insights will be displayed here */}
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Click &quot;Analyze Demographics&quot; to see insights</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Trends Tab */}
            <TabsContent value="trends">
              <Card>
                <CardHeader>
                  <CardTitle>Trend Analysis</CardTitle>
                  <CardDescription>
                    Real-time trend detection and pattern recognition
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => pollData && analyzeTrends(pollData)}
                    disabled={isAnalyzing || !pollData}
                    className="mb-4"
                  >
                    {isAnalyzing ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <TrendingUp className="w-4 h-4 mr-2" />
                        Analyze Trends
                      </>
                    )}
                  </Button>

                  {/* Trends insights will be displayed here */}
                  <div className="text-center py-8 text-muted-foreground">
                    <TrendingUp className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Click &quot;Analyze Trends&quot; to see patterns</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Connection Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Analytics Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Connection</span>
                <Badge variant={isConnected ? "default" : "secondary"}>
                  {isConnected ? "Live" : "Offline"}
                </Badge>
              </div>

              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Analysis</span>
                <Badge variant={isAnalyzing ? "default" : "outline"}>
                  {isAnalyzing ? "Running" : "Idle"}
                </Badge>
              </div>

              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Insights</span>
                <span className="font-medium">{insights.length}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={clearHistory}
              >
                Clear History
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={clearInsights}
              >
                Clear Insights
              </Button>
            </CardContent>
          </Card>

          {/* Analysis History */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Recent Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {analyticsHistory.slice(0, 3).map((analysis, index) => (
                  <div key={index} className="text-xs p-2 bg-muted/50 rounded">
                    {analysis.substring(0, 100)}...
                  </div>
                ))}

                {analyticsHistory.length === 0 && (
                  <div className="text-center py-4 text-xs text-muted-foreground">
                    No analysis history yet
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {analysisError && (
            <Card className="border-destructive">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-destructive">
                  Analysis Error
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">
                  {analysisError.message}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

export default StreamingPollAnalytics;
