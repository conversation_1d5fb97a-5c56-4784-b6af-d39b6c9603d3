'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAdvancedSimulation } from '@/lib/hooks/use-enhanced-simulation';
import { SimulationRequest } from '@/lib/types/simulation';
import { Sparkles, Brain, Users, BarChart3, Zap, AlertCircle } from 'lucide-react';

interface EnhancedSimulationDemoProps {
  pollId?: string;
  pollData?: {
    title: string;
    questions: Array<{
      id: string;
      question_text: string;
      options?: Array<{ text: string; value: string }>;
    }>;
  };
}

export default function EnhancedSimulationDemo({ pollId, pollData }: EnhancedSimulationDemoProps) {
  const {
    runWithInsights,
    runBatchSimulation,
    isRunning,
    isBatchRunning,
    simulation,
    batchResult,
    insights,
    isStreamingInsights,
    generateConfig,
    generatedConfig,
    isGeneratingConfig,
    addToComparison,
    compareAll,
    comparisonAnalysis,
    comparisonCount,
    features,
    simulationError,
    batchError,
    progress
  } = useAdvancedSimulation(pollId);

  // Demo configuration
  const [demoConfig, setDemoConfig] = useState({
    pollQuestion: pollData?.questions[0]?.question_text || 'What is your preferred method of transportation?',
    pollOptions: pollData?.questions[0]?.options?.map(opt => opt.text) || [
      'Public Transportation',
      'Personal Vehicle',
      'Walking/Biking',
      'Ride-sharing',
      'Other'
    ],
    demographic: 'College students aged 18-24',
    sampleSize: 100,
    specialInstructions: '',
    useEnhancedEngine: true
  });

  const [batchDemographics, setBatchDemographics] = useState([
    'College students aged 18-24',
    'Working professionals aged 25-40',
    'Parents with school-age children',
    'Seniors aged 65+'
  ]);

  const handleSingleSimulation = async () => {
    const request: SimulationRequest = {
      pollQuestion: demoConfig.pollQuestion,
      pollOptions: demoConfig.pollOptions,
      demographic: {
        group: demoConfig.demographic,
        size: demoConfig.sampleSize,
        context: ''
      },
      responseFormat: 'distribution',
      specialInstructions: demoConfig.specialInstructions || undefined
    };

    try {
      await runWithInsights(request);
    } catch (error) {
      console.error('Simulation failed:', error);
    }
  };

  const handleBatchSimulation = async () => {
    try {
      await runBatchSimulation({
        pollQuestion: demoConfig.pollQuestion,
        pollOptions: demoConfig.pollOptions,
        demographics: batchDemographics
      });
    } catch (error) {
      console.error('Batch simulation failed:', error);
    }
  };

  const handleGenerateConfig = () => {
    generateConfig({
      pollDescription: demoConfig.pollQuestion,
      targetAudience: demoConfig.demographic,
      goals: ['demographic_analysis', 'preference_insights']
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            AI SDK Enhanced Simulation Demo
            <Badge variant="secondary" className="ml-2">Phase 3</Badge>
          </CardTitle>
          <CardDescription>
            Experience the power of AI SDK structured generation for poll simulations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {Object.entries(features).map(([feature, enabled]) => (
              <Badge
                key={feature}
                variant={enabled ? "default" : "secondary"}
                className={enabled ? "bg-green-500" : ""}
              >
                {feature}: {enabled ? 'ON' : 'OFF'}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Configuration */}
      <Tabs defaultValue="single" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Single Simulation
          </TabsTrigger>
          <TabsTrigger value="batch" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Batch Analysis
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Real-time Insights
          </TabsTrigger>
        </TabsList>

        {/* Single Simulation Tab */}
        <TabsContent value="single" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Single Simulation Configuration</CardTitle>
              <CardDescription>
                Configure and run an enhanced AI SDK simulation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="pollQuestion">Poll Question</Label>
                <Textarea
                  id="pollQuestion"
                  value={demoConfig.pollQuestion}
                  onChange={(e) => setDemoConfig(prev => ({ ...prev, pollQuestion: e.target.value }))}
                  placeholder="Enter your poll question..."
                />
              </div>

              <div>
                <Label htmlFor="pollOptions">Poll Options (one per line)</Label>
                <Textarea
                  id="pollOptions"
                  value={demoConfig.pollOptions.join('\n')}
                  onChange={(e) => setDemoConfig(prev => ({
                    ...prev,
                    pollOptions: e.target.value.split('\n').filter(opt => opt.trim())
                  }))}
                  placeholder="Option 1&#10;Option 2&#10;Option 3"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="demographic">Target Demographic</Label>
                  <Input
                    id="demographic"
                    value={demoConfig.demographic}
                    onChange={(e) => setDemoConfig(prev => ({ ...prev, demographic: e.target.value }))}
                    placeholder="e.g., College students aged 18-24"
                  />
                </div>

                <div>
                  <Label htmlFor="sampleSize">Sample Size</Label>
                  <Input
                    id="sampleSize"
                    type="number"
                    value={demoConfig.sampleSize}
                    onChange={(e) => setDemoConfig(prev => ({ ...prev, sampleSize: parseInt(e.target.value) }))}
                    min="10"
                    max="1000"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="specialInstructions">Special Instructions (optional)</Label>
                <Textarea
                  id="specialInstructions"
                  value={demoConfig.specialInstructions}
                  onChange={(e) => setDemoConfig(prev => ({ ...prev, specialInstructions: e.target.value }))}
                  placeholder="Any special considerations or context..."
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    onClick={handleSingleSimulation}
                    disabled={isRunning || !features.enhanced}
                    className="flex items-center gap-2"
                  >
                    {isRunning ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                        Running...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4" />
                        Run Enhanced Simulation
                      </>
                    )}
                  </Button>

                  {features.structured && (
                    <Button
                      variant="outline"
                      onClick={handleGenerateConfig}
                      disabled={isGeneratingConfig}
                    >
                      {isGeneratingConfig ? 'Generating...' : 'Auto-Generate Config'}
                    </Button>
                  )}
                </div>

                {simulation && (
                  <Button
                    variant="secondary"
                    onClick={() => addToComparison(simulation)}
                  >
                    Add to Comparison ({comparisonCount})
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Batch Simulation Tab */}
        <TabsContent value="batch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Batch Demographic Analysis</CardTitle>
              <CardDescription>
                Run simulations across multiple demographics simultaneously
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="batchDemographics">Demographics (one per line)</Label>
                <Textarea
                  id="batchDemographics"
                  value={batchDemographics.join('\n')}
                  onChange={(e) => setBatchDemographics(e.target.value.split('\n').filter(d => d.trim()))}
                  placeholder="College students aged 18-24&#10;Working professionals aged 25-40&#10;Parents with school-age children"
                />
              </div>

              {isBatchRunning && (
                <div className="space-y-2">
                  <Label>Batch Processing Progress</Label>
                  <Progress value={progress} className="w-full" />
                  <p className="text-sm text-muted-foreground">
                    Processing {batchDemographics.length} demographics...
                  </p>
                </div>
              )}

              <Button
                onClick={handleBatchSimulation}
                disabled={isBatchRunning || !features.batch || batchDemographics.length === 0}
                className="w-full"
              >
                {isBatchRunning ? 'Processing Batch...' : `Run Batch Simulation (${batchDemographics.length} demographics)`}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Real-time AI Insights</CardTitle>
              <CardDescription>
                Streaming analysis and insights generation
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isStreamingInsights && (
                <div className="flex items-center gap-2 mb-4">
                  <div className="animate-pulse h-2 w-2 bg-blue-500 rounded-full" />
                  <span className="text-sm text-muted-foreground">Generating insights...</span>
                </div>
              )}

              {insights ? (
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-medium mb-2">AI-Generated Insights</h4>
                  <div className="whitespace-pre-wrap text-sm">
                    {insights}
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  Run a simulation to see real-time AI insights
                </div>
              )}

              {comparisonCount >= 2 && (
                <div className="mt-4">
                  <Button onClick={compareAll} variant="outline" className="w-full">
                    Generate Comparison Analysis ({comparisonCount} simulations)
                  </Button>

                  {comparisonAnalysis && (
                    <div className="mt-4 bg-muted p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Comparison Analysis</h4>
                      <div className="whitespace-pre-wrap text-sm">
                        {comparisonAnalysis}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Results Display */}
      {(simulation || batchResult) && (
        <Card>
          <CardHeader>
            <CardTitle>Simulation Results</CardTitle>
            <CardDescription>
              {simulation ? 'Single simulation results' : 'Batch simulation results'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {simulation && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Confidence Score</Label>
                    <div className="text-2xl font-bold text-green-600">
                      {(simulation.metadata.confidence * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <Label>Sample Size</Label>
                    <div className="text-2xl font-bold">
                      {simulation.metadata.sampleSize}
                    </div>
                  </div>
                </div>

                <div>
                  <Label>Response Distribution</Label>
                  <div className="space-y-2 mt-2">
                    {Object.entries(simulation.results.distribution).map(([option, percentage]) => (
                      <div key={option} className="flex items-center justify-between">
                        <span className="text-sm">{option}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={percentage * 100} className="w-32" />
                          <span className="text-sm font-medium">
                            {(percentage * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Demographic Group</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {simulation.metadata.demographic}
                  </p>
                </div>
              </div>
            )}

            {batchResult && (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>Total Sample Size</Label>
                    <div className="text-2xl font-bold">
                      {batchResult.metadata.totalSampleSize}
                    </div>
                  </div>
                  <div>
                    <Label>Average Confidence</Label>
                    <div className="text-2xl font-bold text-green-600">
                      {(batchResult.metadata.averageConfidence * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <Label>Processing Time</Label>
                    <div className="text-2xl font-bold">
                      {(batchResult.metadata.processingTime / 1000).toFixed(1)}s
                    </div>
                  </div>
                </div>

                <div>
                  <Label>Demographic Comparison</Label>
                  <div className="space-y-3 mt-2">
                    {batchResult.aggregatedResults.demographicComparison.map((demo: { demographic: string; distribution: Record<string, number> }, index: number) => (
                      <div key={index} className="border rounded-lg p-3">
                        <h4 className="font-medium text-sm mb-2">{demo.demographic}</h4>
                        <div className="space-y-1">
                          {Object.entries(demo.distribution).map(([option, percentage]: [string, number]) => (
                            <div key={option} className="flex items-center justify-between text-xs">
                              <span>{option}</span>
                              <span className="font-medium">{(percentage * 100).toFixed(1)}%</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Errors */}
      {(simulationError || batchError) && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {simulationError?.message || batchError?.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Generated Config */}
      {generatedConfig && (
        <Card>
          <CardHeader>
            <CardTitle>AI-Generated Configuration</CardTitle>
            <CardDescription>
              Structured configuration generated by AI SDK
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
              {JSON.stringify(generatedConfig, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
