/**
 * Create a sample PDF file for testing Mistral OCR
 * Run with: node src/create-sample-pdf.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

// Get current file directory with ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createSamplePDF() {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();
  
  // Add a page to the document
  const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
  
  // Get the standard font
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  
  // Draw text on the page
  const fontSize = 12;
  page.drawText('Sample PDF Document for Mistral OCR Testing', {
    x: 50,
    y: 800,
    size: 16,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('This is a sample PDF document created for testing the Mistral OCR functionality.', {
    x: 50,
    y: 750,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('The document contains multiple paragraphs of text to verify that the OCR', {
    x: 50,
    y: 730,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('extraction works correctly with the Mistral OCR API.', {
    x: 50,
    y: 710,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('This text should be extracted and processed by the Mistral OCR API', {
    x: 50,
    y: 670,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('and returned as markdown text in the response.', {
    x: 50,
    y: 650,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  // Add some formatting examples
  page.drawText('Testing different text styles:', {
    x: 50,
    y: 600,
    size: 14,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('1. Bullet point item one', {
    x: 70,
    y: 570,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('2. Bullet point item two', {
    x: 70,
    y: 550,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  page.drawText('3. Bullet point item three', {
    x: 70,
    y: 530,
    size: fontSize,
    font,
    color: rgb(0, 0, 0),
  });
  
  // Save the PDF
  const pdfBytes = await pdfDoc.save();
  
  // Write the PDF to a file
  const outputPath = path.join(__dirname, '../sample.pdf');
  fs.writeFileSync(outputPath, pdfBytes);
  
  console.log(`Sample PDF created at: ${outputPath}`);
}

// Run the function
createSamplePDF().catch(console.error);
