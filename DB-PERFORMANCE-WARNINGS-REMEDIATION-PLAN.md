# Database Performance Warnings Remediation Plan

## Summary of Issues

Based on analysis of `db-performance-warnings.json`, we have identified three main categories of performance issues:

### 1. Auth RLS InitPlan Issues (Most Critical)
- **Issue**: RLS policies using `auth.<function>()` directly causing unnecessary re-evaluation for each row
- **Impact**: Severely impacts query performance at scale
- **Solution**: Replace `auth.<function>()` with `(select auth.<function>())`
- **Tables Affected**: profiles, polls, questions, responses, answers, survey_distributions, mturk_hits, worker_assignments

### 2. Multiple Permissive Policies
- **Issue**: Multiple permissive RLS policies for the same table/role/action
- **Impact**: Redundant policy evaluations
- **Solution**: Consolidate policies into single policies where possible
- **Tables Affected**: answers (multiple roles: anon, authenticated, authenticator, dashboard_user)

### 3. Duplicate Indexes
- **Issue**: Multiple indexes covering the same columns
- **Impact**: Increased storage usage and slower write operations
- **Solution**: Drop redundant indexes
- **Tables Affected**: poll_simulations, polls, questions, responses

## Detailed Remediation Steps

### Phase 1: Fix Auth RLS InitPlan Issues (Critical Priority)

#### 1.1 Profiles Table RLS Policies
- Policy: "Users can delete their own profile"
- Policy: "Users can insert their own profile"
- Policy: "Users can update their own profile"
- Policy: "Users can view their own profile"

#### 1.2 Polls Table RLS Policies
- Policy: "Users can delete their own polls"
- Policy: "Users can insert their own polls"
- Policy: "Users can update their own polls"
- Policy: "Users can view their own polls"

#### 1.3 Questions Table RLS Policies
- Policy: "Users can delete their own questions"
- Policy: "Users can insert their own questions"
- Policy: "Users can update their own questions"
- Policy: "Users can view their own questions"
- Policy: "Users can view questions from public polls"
- Policy: "Anon users can view questions from public polls"

#### 1.4 Responses Table RLS Policies
- Policy: "Users can delete their own responses"
- Policy: "Users can insert their own responses"
- Policy: "Users can view their own responses"

#### 1.5 Answers Table RLS Policies
- Policy: "Users can delete their own answers"
- Policy: "Users can insert their own answers"
- Policy: "Users can view their own answers"

#### 1.6 Survey Distributions Table RLS Policies
- Policy: "Users can delete their own survey distributions"
- Policy: "Users can insert their own survey distributions"
- Policy: "Users can view their own survey distributions"

#### 1.7 MTurk HITs Table RLS Policies
- Policy: "Users can view their own mturk hits"

#### 1.8 Worker Assignments Table RLS Policies
- Policy: "Users can view their own worker assignments"

### Phase 2: Consolidate Multiple Permissive Policies (Medium Priority)

#### 2.1 Answers Table Multiple Policies
The answers table has multiple permissive policies for different roles (anon, authenticated, authenticator, dashboard_user) for both INSERT and SELECT operations. These should be consolidated into unified policies.

### Phase 3: Remove Duplicate Indexes (Low Priority)

#### 3.1 Poll Simulations Table
- Remove duplicate: `idx_poll_simulations_poll_id` vs `poll_simulations_poll_id_idx`

#### 3.2 Polls Table
- Remove duplicate: `idx_polls_user_id` vs `polls_user_id_idx`

#### 3.3 Questions Table
- Remove duplicate: `idx_questions_poll_id` vs `questions_poll_id_idx`

#### 3.4 Responses Table
- Remove duplicate: `idx_responses_poll_id` vs `responses_poll_id_idx`

## Migration Strategy

### Step 1: Create RLS Policy Optimization Migration
- Fix all auth RLS initplan issues by updating policy expressions
- This is the most critical performance improvement

### Step 2: Create Policy Consolidation Migration
- Consolidate multiple permissive policies where appropriate
- Ensure no security gaps are introduced

### Step 3: Create Index Cleanup Migration
- Drop duplicate indexes
- Verify no queries are negatively impacted

## Implementation Timeline

1. **Immediate**: Fix RLS InitPlan issues (highest impact)
2. **Next**: Consolidate multiple permissive policies
3. **Final**: Clean up duplicate indexes

## Monitoring & Validation

After each migration:
1. Run database linter to verify issues are resolved
2. Monitor query performance metrics
3. Test critical application workflows
4. Validate no regressions in security policies

## Risk Assessment

- **Low Risk**: Index cleanup (can be easily rolled back)
- **Medium Risk**: Policy consolidation (requires careful testing)
- **High Impact**: RLS optimization (critical for performance but needs validation)

## Success Metrics

- [ ] Zero auth_rls_initplan warnings
- [ ] Zero multiple_permissive_policies warnings
- [ ] Zero duplicate_index warnings
- [ ] Improved query performance (measurable via monitoring)
- [ ] No security policy regressions
