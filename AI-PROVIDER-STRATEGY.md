# AI Provider Strategy Documentation

## Overview
This document outlines the AI provider/model strategy for PollGPT, ensuring each feature uses the optimal AI provider for its specific requirements.

## 🎯 AI Provider Strategy

### 1. **Chat UI** (User Selectable)
- **Gemini**: `gemini-2.0-flash-001` (latest) or `gemini-1.5-pro` (stable)
- **Mistral**: `mistral-large-2411` (latest stable, replaces mistral-large-latest)
- **Usage**: Conversational poll creation interface
- **Location**: `src/components/conversation/ConversationalPollInterface.tsx`
- **API Route**: `/api/ai/chat-completion`

### 2. **PDF/Website/URL Extraction** (Fixed Provider)
- **Provider**: **Mistral ONLY**
- **Model**: `mistral-large-2411` (latest stable)
- **Usage**: Document processing, content extraction, URL analysis
- **Location**: `src/lib/ai/providers.ts` → `contentExtraction`
- **API Routes**: `/api/ai/extract-content`, `/api/ai/process-document`

### 3. **Simulation** (Fixed Provider)
- **Provider**: **Perplexity ONLY**
- **Model**: `sonar` (current, with search capabilities)
- **Usage**: Poll demographic simulation, response prediction
- **Location**: `src/lib/services/perplexity-ai.ts` → `simulatePoll`
- **API Routes**: `/api/perplexity`, simulation endpoints

## 🔧 Implementation Details

### Environment Variables Required
```env
# For Gemini (Chat UI)
GOOGLE_API_KEY=your-google-api-key

# For Mistral (Chat UI + Content Extraction)
MISTRAL_API_KEY=your-mistral-api-key

# For Perplexity (Simulation)
PERPLEXITY_API_KEY=your-perplexity-api-key
```

### Provider Configuration
```typescript
// src/lib/ai/providers.ts
export const aiProviders = {
  // Content extraction - ALWAYS Mistral
  contentExtraction: mistral('mistral-large-2411'),

  // Chat UI - User selectable (Gemini OR Mistral)
  chatGemini: google('gemini-2.0-flash-001'),
  chatMistral: mistral('mistral-large-2411'),

  // Simulation - ALWAYS Perplexity (via our service wrapper)
  // Note: Perplexity not supported by AI SDK yet, uses service wrapper
  // Model: llama-3.1-sonar-large-128k-online

  // Poll generation - Mistral (used by chat completion)
  pollGeneration: mistral('mistral-large-2411'),
}
```

## 📁 File Structure

### Key Files
- `src/lib/ai/providers.ts` - AI provider configuration
- `src/lib/services/perplexity-ai.ts` - Perplexity service wrapper
- `src/components/conversation/ConversationalPollInterface.tsx` - Chat UI with model selection
- `src/app/api/ai/chat-completion/route.ts` - Chat API with model routing

### API Routes
- `/api/ai/chat-completion` - Chat with Gemini/Mistral selection
- `/api/ai/extract-content` - Content extraction (Mistral only)
- `/api/perplexity` - Simulation (Perplexity only)

## 🎨 User Experience

### Chat Interface
1. User sees dropdown to select between "Gemini" and "Mistral"
2. Selection affects only the conversational AI model
3. All other features (extraction, simulation) use fixed providers

### Content Extraction
1. Automatically uses Mistral for all document/URL processing
2. No user selection required
3. Optimized for content understanding and extraction

### Simulation
1. Automatically uses Perplexity for all poll simulations
2. No user selection required
3. Optimized for demographic modeling and response prediction

## 🚀 Benefits

### Performance
- **Gemini**: Fast, efficient for conversational interactions
- **Mistral**: Excellent for content extraction and analysis
- **Perplexity**: Best-in-class for research and simulation with real-time data

### Cost Optimization
- Use appropriate model tiers for each task
- Avoid over-powered models for simple tasks
- Leverage strengths of each provider

### Reliability
- Fallback mechanisms for each provider
- Independent failure domains
- Graceful degradation

## 🔍 Testing Strategy

### Unit Tests
- Test each provider independently
- Mock API responses for consistent testing
- Validate model selection logic

### Integration Tests
- Test full chat flow with both Gemini and Mistral
- Test content extraction with various document types
- Test simulation with different demographic inputs

### Manual Testing
- Verify UI shows correct model selection
- Test feature isolation (extraction uses Mistral, simulation uses Perplexity)
- Confirm environment variable handling

## 📊 Monitoring

### Key Metrics
- API response times by provider
- Error rates by model
- User model selection patterns
- Feature usage distribution

### Alerts
- Provider API failures
- High error rates
- Performance degradation
- Cost threshold breaches

## 🔄 Future Considerations

### Potential Enhancements
- Add Claude for specialized tasks
- Implement dynamic model selection based on task complexity
- Add A/B testing for model performance
- Implement cost-based routing

### Migration Strategy
- Gradual rollout of new providers
- Feature flags for model selection
- Backward compatibility maintenance
- Performance comparison tracking

## 📊 Current Model Usage Summary

### ✅ **Verified Current Models (January 2025)**

| Feature | Provider | Model | Status |
|---------|----------|--------|--------|
| **Chat UI** | Gemini | `gemini-2.0-flash-001` | ✅ Latest flagship |
| **Chat UI** | Mistral | `mistral-large-2411` | ✅ Latest stable |
| **PDF/URL Extraction** | Mistral | `mistral-large-2411` | ✅ Latest stable |
| **Simulation** | Perplexity | `llama-3.1-sonar-large-128k-online` | ✅ Current with search |

### 🔄 **Model Updates Applied**

- **Mistral**: Updated from `mistral-large-latest` → `mistral-large-2411`
- **Gemini**: Confirmed `gemini-2.0-flash-001` as current flagship
- **Perplexity**: Confirmed `llama-3.1-sonar-large-128k-online` as current

### 🗑️ **Deprecated Models Removed**
- Removed `aiProviders.simulation` key (now uses Perplexity service directly)
- All OpenAI references removed from codebase

---

**Last Updated**: January 2025
**Version**: 1.1
**Status**: ✅ **COMPLETED - All models verified and updated**
