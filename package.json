{"name": "pollgpt", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:sitemap": "node scripts/test-sitemap.js", "test:schema": "node scripts/test-schema.js", "test:seo": "npm run test:sitemap && npm run test:schema", "test:ai-sdk": "node scripts/validate-ai-sdk-phase1.mjs", "mcp:start": "node scripts/mcp-server.js", "perf:start": "tsx scripts/performance-optimization.ts start", "perf:health": "tsx scripts/performance-optimization.ts health", "perf:benchmark": "tsx scripts/performance-optimization.ts benchmark", "perf:warm-cache": "tsx scripts/performance-optimization.ts warm-cache", "perf:stats": "tsx scripts/performance-optimization.ts stats", "jobs:start": "tsx scripts/job-worker.ts", "dev:full": "concurrently \"npm run perf:start\" \"npm run dev\"", "start:production": "concurrently \"npm run perf:start\" \"npm run start\""}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@mistralai/mistralai": "^1.6.0", "@modelcontextprotocol/sdk": "^1.11.2", "@modelcontextprotocol/server-brave-search": "^0.6.2", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/mcp-server-supabase": "^0.4.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.80.6", "@types/lodash": "^4.17.17", "@types/lodash-es": "^4.17.12", "@types/redis": "^4.0.10", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@upstash/context7-mcp": "^1.0.13", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "ai": "^4.3.17", "bullmq": "^5.53.2", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "echarts": "^5.6.0", "framer-motion": "^12.10.5", "html-to-text": "^9.0.5", "ioredis": "^5.6.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "^0.508.0", "mammoth": "^1.9.1", "mcp-smart-crawler": "^1.0.10", "natural": "^8.1.0", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "openai": "^5.2.0", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "puppeteer-mcp-server": "^0.7.2", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "redis": "^5.1.1", "schema-dts": "^1.1.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "vercel": "^41.7.6", "ws": "^8.18.2", "xml2js": "^0.6.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@modelcontextprotocol/server-postgres": "^0.6.2", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.56", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "dotenv": "^16.6.1", "eslint": "^9", "eslint-config-next": "15.3.2", "next": "^15.3.3", "next-sitemap": "^4.2.3", "nodemailer": "^7.0.3", "playwright": "^1.52.0", "puppeteer": "^24.10.0", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "optionalDependencies": {"webworker-threads": "^0.7.17"}}