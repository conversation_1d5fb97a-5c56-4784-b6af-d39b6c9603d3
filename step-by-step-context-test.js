// STEP-BY-STEP Context Workflow Test
// Run this in the browser console while on the conversational poll creation page

console.log('🚀 COMPREHENSIVE CONTEXT WORKFLOW TEST');
console.log('=====================================\n');

// Step 1: Test URL extraction API
async function step1_testUrlExtraction() {
  console.log('🔄 STEP 1: Testing URL Extraction API...');
  const testUrl = 'https://www.feedbackgpt.com';

  try {
    const response = await fetch('/api/extract-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: testUrl })
    });

    if (!response.ok) {
      throw new Error(`URL extraction failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ URL extraction successful');
    console.log(`   - Content length: ${data.content?.length || 0} characters`);
    console.log(`   - First 100 chars: ${data.content?.substring(0, 100)}...`);

    return { success: true, data };
  } catch (error) {
    console.error('❌ URL extraction failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Step 2: Test summarization API
async function step2_testSummarization(content) {
  console.log('\n🔄 STEP 2: Testing Content Summarization...');

  try {
    const response = await fetch('/api/ai/summarize-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: content,
        sourceType: 'url',
        maxLength: 150
      })
    });

    if (!response.ok) {
      throw new Error(`Summarization failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Content summarization successful');
    console.log(`   - Summary: ${data.summary}`);

    return { success: true, data };
  } catch (error) {
    console.error('❌ Summarization failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Step 3: Set up context message in localStorage (exactly as the real workflow does)
function step3_setupContextMessage(url, extractedContent) {
  console.log('\n🔄 STEP 3: Setting up context message in localStorage...');

  const contextMessage = {
    source: 'url',
    resourceName: url,
    extractedContent: extractedContent,
    message: `Please create a poll about this website: ${url}`,
    processed: false  // Critical: must be false initially
  };

  localStorage.setItem('pollgpt_pending_message', JSON.stringify(contextMessage));
  console.log('✅ Context message saved to localStorage');
  console.log('   - Source:', contextMessage.source);
  console.log('   - URL:', contextMessage.resourceName);
  console.log('   - Content length:', contextMessage.extractedContent?.length || 0);
  console.log('   - Processed:', contextMessage.processed);

  return contextMessage;
}

// Step 4: Simulate ConversationalPollInterface processing
function step4_simulateConversationalProcessing() {
  console.log('\n🔄 STEP 4: Simulating ConversationalPollInterface processing...');

  const pendingMessage = localStorage.getItem('pollgpt_pending_message');
  if (!pendingMessage) {
    console.error('❌ No pending message found in localStorage');
    return { success: false, error: 'No pending message' };
  }

  const parsedMessage = JSON.parse(pendingMessage);
  console.log('✅ ConversationalPollInterface would process this message:');
  console.log('   - Source:', parsedMessage.source);
  console.log('   - Resource:', parsedMessage.resourceName);
  console.log('   - Processed:', parsedMessage.processed);

  // Simulate the processing (mark as processed but keep data)
  if (!parsedMessage.processed) {
    parsedMessage.processed = true;
    localStorage.setItem('pollgpt_pending_message', JSON.stringify(parsedMessage));
    console.log('✅ Marked as processed but kept data available');
  }

  return { success: true, data: parsedMessage };
}

// Step 5: Simulate handleCreatePoll context extraction logic
function step5_simulateHandleCreatePoll() {
  console.log('\n🔄 STEP 5: Simulating handleCreatePoll context extraction...');

  let contextData = null;

  try {
    const pendingMessage = localStorage.getItem('pollgpt_pending_message');
    console.log('Raw pending message from localStorage:', pendingMessage ? 'Found' : 'Not found');

    if (pendingMessage) {
      const parsedMessage = JSON.parse(pendingMessage);
      console.log('Parsed pending message:', {
        source: parsedMessage.source,
        resourceName: parsedMessage.resourceName,
        hasExtractedContent: !!parsedMessage.extractedContent
      });

      // This is the exact logic from handleCreatePoll
      if (parsedMessage.source === 'url' && parsedMessage.resourceName) {
        contextData = {
          url: parsedMessage.resourceName,
          extractedContent: parsedMessage.extractedContent || '',
          source: 'url'
        };
        console.log('✅ URL context data extracted:', {
          url: contextData.url,
          contentLength: contextData.extractedContent?.length || 0,
          source: contextData.source
        });
      } else {
        console.log('❌ No recognized context type in parsed message');
        return { success: false, error: 'Unrecognized context type' };
      }
    } else {
      console.log('❌ No pending message found in localStorage');
      return { success: false, error: 'No pending message' };
    }
  } catch (err) {
    console.error('❌ Error parsing context data from localStorage:', err);
    return { success: false, error: err.message };
  }

  return { success: true, contextData };
}

// Step 6: Simulate poll data creation with context
async function step6_simulatePollDataCreation(contextData, summaryData) {
  console.log('\n🔄 STEP 6: Simulating poll data creation with context...');

  if (!contextData) {
    console.error('❌ No context data available');
    return { success: false, error: 'No context data' };
  }

  // Simulate the exact logic from handleCreatePoll
  const pollDataToCreate = {
    title: 'Test Poll from URL',
    description: 'A test poll created from website content',
    questions: [
      {
        id: 'q-test-1',
        text: 'How would you rate this website?',
        type: 'single',
        options: [
          { id: 'o-1', text: 'Excellent', value: 'excellent' },
          { id: 'o-2', text: 'Good', value: 'good' },
          { id: 'o-3', text: 'Fair', value: 'fair' },
          { id: 'o-4', text: 'Poor', value: 'poor' }
        ],
        required: true,
        order: 1
      }
    ],
    userId: '', // Would be filled by server
    status: 'active',
    is_public: true,
    show_source: true
  };

  // Add context data - this is the critical part
  console.log('Setting context data on poll:', contextData);
  if (contextData.source === 'url' && contextData.url) {
    console.log('Setting URL context data:', contextData.url);

    // Simulate the content summarization logic
    const contentToSummarize = contextData.extractedContent || contextData.url;
    let contextSummary;

    if (contentToSummarize.length > 100) {
      // Use our pre-generated summary
      contextSummary = summaryData.summary;
      console.log('✅ Using generated summary as context');
    } else {
      contextSummary = contentToSummarize;
      console.log('✅ Using content directly as context');
    }

    pollDataToCreate.context = contextSummary;
    pollDataToCreate.source_url = contextData.url;
    pollDataToCreate.source_type = 'url';
    pollDataToCreate.source_filename = null;

    console.log('✅ Poll data prepared with context:');
    console.log('   - Context:', pollDataToCreate.context.substring(0, 50) + '...');
    console.log('   - Source URL:', pollDataToCreate.source_url);
    console.log('   - Source Type:', pollDataToCreate.source_type);
    console.log('   - Source Filename:', pollDataToCreate.source_filename);

    return { success: true, pollData: pollDataToCreate };
  } else {
    console.error('❌ Context data not in expected format');
    return { success: false, error: 'Invalid context format' };
  }
}

// Step 7: Final verification
function step7_finalVerification(pollData) {
  console.log('\n🔄 STEP 7: Final verification of poll data...');

  const requiredFields = ['context', 'source_url', 'source_type'];
  const missingFields = [];

  for (const field of requiredFields) {
    if (!pollData[field]) {
      missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    console.error('❌ Missing required context fields:', missingFields);
    return { success: false, missingFields };
  }

  console.log('✅ All context fields present:');
  console.log('   ✓ context:', pollData.context ? 'Present' : 'Missing');
  console.log('   ✓ source_url:', pollData.source_url ? 'Present' : 'Missing');
  console.log('   ✓ source_type:', pollData.source_type ? 'Present' : 'Missing');

  return { success: true };
}

// Run the complete test
async function runCompleteTest() {
  console.log('Starting complete context workflow test...\n');

  try {
    // Step 1: Test URL extraction
    const extractResult = await step1_testUrlExtraction();
    if (!extractResult.success) {
      throw new Error(`Step 1 failed: ${extractResult.error}`);
    }

    // Step 2: Test summarization
    const summaryResult = await step2_testSummarization(extractResult.data.content);
    if (!summaryResult.success) {
      throw new Error(`Step 2 failed: ${summaryResult.error}`);
    }

    // Step 3: Set up context message
    const contextMessage = step3_setupContextMessage('https://www.feedbackgpt.com', extractResult.data.content);

    // Step 4: Simulate conversational processing
    const processResult = step4_simulateConversationalProcessing();
    if (!processResult.success) {
      throw new Error(`Step 4 failed: ${processResult.error}`);
    }

    // Step 5: Simulate handleCreatePoll context extraction
    const extractionResult = step5_simulateHandleCreatePoll();
    if (!extractionResult.success) {
      throw new Error(`Step 5 failed: ${extractionResult.error}`);
    }

    // Step 6: Simulate poll data creation
    const pollDataResult = await step6_simulatePollDataCreation(extractionResult.contextData, summaryResult.data);
    if (!pollDataResult.success) {
      throw new Error(`Step 6 failed: ${pollDataResult.error}`);
    }

    // Step 7: Final verification
    const verificationResult = step7_finalVerification(pollDataResult.pollData);
    if (!verificationResult.success) {
      throw new Error(`Step 7 failed: Missing fields ${verificationResult.missingFields.join(', ')}`);
    }

    console.log('\n🎉 SUCCESS! Complete context workflow test passed!');
    console.log('=====================================');
    console.log('All steps completed successfully:');
    console.log('✅ URL extraction');
    console.log('✅ Content summarization');
    console.log('✅ Context message setup');
    console.log('✅ ConversationalPollInterface processing');
    console.log('✅ handleCreatePoll context extraction');
    console.log('✅ Poll data creation with context');
    console.log('✅ Final verification');

    console.log('\nFinal poll data would be sent to createPoll with:');
    console.log('- Context:', pollDataResult.pollData.context.substring(0, 100) + '...');
    console.log('- Source URL:', pollDataResult.pollData.source_url);
    console.log('- Source Type:', pollDataResult.pollData.source_type);

    return {
      success: true,
      pollData: pollDataResult.pollData,
      extractData: extractResult.data,
      summaryData: summaryResult.data
    };

  } catch (error) {
    console.error('\n❌ COMPLETE TEST FAILED:', error.message);
    console.log('=====================================');
    return { success: false, error: error.message };
  }
}

// Run the test
runCompleteTest();
