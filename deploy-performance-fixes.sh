#!/bin/bash

# Database Performance Optimization Deployment Script
# This script deploys all performance fix migrations in the correct order

set -e

echo "🚀 Starting Database Performance Optimization Deployment..."

# Define migration files in order
MIGRATIONS=(
    "20250710000003_minimal_indexes.sql"
    "20250710000004_fix_rls_initplan_issues.sql"
    "20250710000006_simple_index_cleanup.sql"
    "20250710000007_comprehensive_performance_fix.sql"
    "20250710000008_final_performance_optimization.sql"
)

# Check if we're in the right directory
if [ ! -d "supabase/migrations" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Function to run a migration
run_migration() {
    local migration_file=$1
    local migration_path="supabase/migrations/$migration_file"

    if [ ! -f "$migration_path" ]; then
        echo "❌ Error: Migration file $migration_path not found"
        exit 1
    fi

    echo "📝 Running migration: $migration_file"

    # Use Supabase CLI to run the migration
    if command -v supabase &> /dev/null; then
        supabase db reset --linked
        echo "✅ Migration $migration_file completed successfully"
    else
        echo "⚠️  Supabase CLI not found. Please run migrations manually or install Supabase CLI"
        echo "   Migration file: $migration_path"
    fi
}

# Pre-migration checks
echo "🔍 Running pre-migration checks..."

# Check current policy count
echo "📊 Current RLS policy count:"
if command -v supabase &> /dev/null; then
    supabase db diff --schema public --local
fi

# Run migrations in order
echo "🔄 Deploying migrations..."
for migration in "${MIGRATIONS[@]}"; do
    run_migration "$migration"
    echo "⏳ Waiting 2 seconds between migrations..."
    sleep 2
done

# Post-migration verification
echo "🔍 Running post-migration verification..."

# Create verification SQL
cat > temp_verification.sql << 'EOF'
-- Check RLS policy optimization status
SELECT
  tablename,
  COUNT(*) as policy_count,
  bool_or(qual LIKE '%auth.uid()%' OR with_check LIKE '%auth.uid()%') as uses_auth_uid_directly
FROM pg_policies
WHERE schemaname = 'public'
  AND tablename IN ('answers', 'responses', 'questions', 'polls', 'poll_simulations')
GROUP BY tablename
ORDER BY tablename;

-- Check for tables needing maintenance
SELECT relname, n_dead_tup
FROM pg_stat_user_tables
WHERE schemaname = 'public' AND n_dead_tup > 50;

-- Check index count
SELECT
  schemaname,
  tablename,
  COUNT(*) as index_count
FROM pg_indexes
WHERE schemaname = 'public'
  AND tablename IN ('answers', 'responses', 'questions', 'polls', 'poll_simulations')
GROUP BY schemaname, tablename
ORDER BY tablename;
EOF

if command -v supabase &> /dev/null; then
    echo "📊 Running verification queries..."
    supabase db diff --schema public --local
    supabase db shell < temp_verification.sql
fi

# Clean up
rm -f temp_verification.sql

echo "✅ Database Performance Optimization Deployment Complete!"
echo ""
echo "📈 Expected Performance Improvements:"
echo "   - Reduced RLS policy evaluation time"
echo "   - Improved query planning with optimized auth.uid() usage"
echo "   - Reduced storage bloat through VACUUM operations"
echo "   - Better index utilization"
echo ""
echo "🔧 Next Steps:"
echo "   1. Monitor query performance in production"
echo "   2. Run Supabase linter to verify all warnings are resolved"
echo "   3. Schedule regular maintenance with the created functions"
echo ""
echo "🎯 Performance Metrics to Monitor:"
echo "   - RLS policy execution time"
echo "   - Index usage statistics"
echo "   - Table bloat levels"
echo "   - Query performance on authenticated operations"
