#!/bin/bash

# Database Security and Performance Fix Deployment Script
# Run this script to apply all the database fixes

echo "🔧 Database Security and Performance Remediation"
echo "================================================"
echo ""

echo "📋 Issues to be fixed:"
echo "  🔴 RLS Performance Issues (16 policies)"
echo "  🟡 Function Search Path Security (17 functions)"
echo "  🟠 Extension Security (pg_trgm location)"
echo "  🟢 Materialized View Permissions Review"
echo ""

echo "⚠️  IMPORTANT: This script will modify your database schema."
echo "   Make sure you have a backup before proceeding."
echo ""

# Check if we have psql available
if ! command -v psql &> /dev/null; then
    echo "❌ psql not found. Please install PostgreSQL client or use Supabase SQL Editor."
    echo ""
    echo "🔧 Alternative options:"
    echo "  1. Copy the contents of fix-db-security-performance.sql"
    echo "  2. Paste into Supabase Dashboard > SQL Editor"
    echo "  3. Run the script there"
    echo ""
    echo "  OR"
    echo ""
    echo "  1. Install Supabase CLI: npm install -g supabase"
    echo "  2. Run: supabase db reset --linked"
    echo ""
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL environment variable not set."
    echo ""
    echo "🔧 To set it:"
    echo "  export DATABASE_URL='postgresql://user:password@host:port/database'"
    echo ""
    echo "  Or get it from Supabase Dashboard > Settings > Database > Connection string"
    echo ""
    exit 1
fi

echo "🔍 Testing database connection..."
if ! psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Cannot connect to database. Please check your DATABASE_URL."
    exit 1
fi

echo "✅ Database connection successful."
echo ""

read -p "🚀 Proceed with applying fixes? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cancelled by user."
    exit 0
fi

echo "📥 Applying database fixes..."
echo ""

# Apply the fixes
if psql "$DATABASE_URL" -f fix-db-security-performance.sql; then
    echo ""
    echo "✅ Database fixes applied successfully!"
    echo ""

    echo "🔍 Running verification checks..."

    # Run verification queries
    echo "Checking RLS policies..."
    psql "$DATABASE_URL" -c "
    SELECT 'RLS Policies Check' as check_type,
           COUNT(*) as policy_count
    FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename IN ('answers', 'responses', 'questions', 'poll_simulations', 'polls');
    "

    echo ""
    echo "Checking function security..."
    psql "$DATABASE_URL" -c "
    SELECT 'Function Search Path Check' as check_type,
           p.proname as function_name,
           CASE
             WHEN array_to_string(p.proconfig, ',') LIKE '%search_path%'
             THEN 'SECURE'
             ELSE 'NEEDS_REVIEW'
           END as status
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
      AND p.proname IN (
        'handle_new_user', 'refresh_poll_stats', 'cleanup_expired_simulation_cache',
        'get_simulation_statistics', 'update_poll_timestamp_on_simulation',
        'get_poll_with_counts', 'get_policies_for_table', 'check_user_poll_access',
        'test_poll_policies', 'check_multiple_policies', 'get_polls_with_counts',
        'find_duplicate_indexes', 'get_index_usage_stats', 'performance_diagnostics',
        'query_performance_check', 'validate_security_policies', 'database_health_check'
      )
    ORDER BY status, function_name;
    "

    echo ""
    echo "Checking extension location..."
    psql "$DATABASE_URL" -c "
    SELECT 'Extension Location Check' as check_type,
           e.extname as extension_name,
           n.nspname as schema_name,
           CASE
             WHEN n.nspname = 'extensions' THEN 'SECURE'
             ELSE 'NEEDS_REVIEW'
           END as status
    FROM pg_extension e
    JOIN pg_namespace n ON e.extnamespace = n.oid
    WHERE e.extname = 'pg_trgm';
    "

    echo ""
    echo "🎉 Database remediation completed!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. ✅ Database schema fixes applied"
    echo "  2. ⏳ Configure Auth settings in Supabase Dashboard:"
    echo "     - Reduce OTP expiry to 30 minutes"
    echo "     - Enable leaked password protection"
    echo "  3. ⏳ Monitor performance improvements"
    echo "  4. ⏳ Re-run Supabase linter to verify fixes"
    echo ""
    echo "📖 See DB-SECURITY-PERFORMANCE-REMEDIATION-GUIDE.md for complete details."

else
    echo ""
    echo "❌ Error applying database fixes. Please check the output above."
    echo ""
    echo "🔧 Manual application:"
    echo "  1. Open Supabase Dashboard > SQL Editor"
    echo "  2. Copy contents of fix-db-security-performance.sql"
    echo "  3. Paste and run in SQL Editor"
    exit 1
fi
