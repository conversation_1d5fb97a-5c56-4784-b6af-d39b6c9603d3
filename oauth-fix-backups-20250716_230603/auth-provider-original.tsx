/**
 * Optimized AuthProvider using React Query with Strategic Auth Methods
 *
 * Self-contained auth provider with strategic getUser/getSession usage
 * Implements single source of truth for user authentication
 */

'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Session, User } from '@supabase/supabase-js';
import { setUserData, clearUserData, getUserId, getAuthStrategy } from '@/lib/utils/user-id-manager';

// Query keys for consistent caching
const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
};

// Session validation helper
function isSessionValid(session: Session | null): boolean {
  if (!session) return false;

  const now = Math.floor(Date.now() / 1000);
  const expiresAt = session.expires_at;

  // Session is valid if it expires more than 5 minutes from now
  return expiresAt ? expiresAt > now + 300 : false;
}

// Strategic session fetcher (fast, for general use)
async function getSession(): Promise<Session | null> {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Session fetch error:', error);
      return null;
    }

    return data.session;
  } catch (error) {
    console.error('Session fetch exception:', error);
    return null;
  }
}

// Strategic user fetcher (secure, for sensitive operations)
async function getUser(): Promise<User | null> {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      console.error('User fetch error:', error);
      return null;
    }

    return data.user;
  } catch (error) {
    console.error('User fetch exception:', error);
    return null;
  }
}

// User profile fetcher
async function getUserProfile(userId: string): Promise<Record<string, unknown> | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      console.error('Profile fetch error:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Profile fetch exception:', error);
    return null;
  }
}

interface AuthContextType {
  // Core auth state
  session: Session | null;
  user: User | null;
  profile: Record<string, unknown> | null;
  isAuthenticated: boolean;

  // Instant access (sync)
  userId: string | null;
  userEmail: string | null;

  // Loading states
  isLoading: boolean;
  isLoadingProfile: boolean;

  // Error states
  error: Error | null;
  profileError: Error | null;

  // Strategic data access
  getAuthData: (operation?: 'read' | 'write' | 'sensitive') => {
    user: User | null;
    isLoading: boolean;
    error: Error | null;
  };

  // Utility functions
  refetch: () => void;
  refetchProfile: () => void;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<Session | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
  enablePerformanceOptimization?: boolean;
}

export function AuthProvider({
  children,
  enablePerformanceOptimization = true
}: AuthProviderProps) {
  const queryClient = useQueryClient();

  // Note: Performance optimization moved to app-level to avoid circular dependency
  // useAdvancedPerformanceOptimization will be called from layout.tsx instead

  // Main session query (fast, for general use)
  const { data: session, isLoading, error } = useQuery({
    queryKey: authKeys.session(),
    queryFn: getSession,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // User query for sensitive operations
  const { data: secureUser, isLoading: isSecureUserLoading, error: secureUserError } = useQuery({
    queryKey: authKeys.user(),
    queryFn: getUser,
    enabled: !!session, // Only fetch if we have a session
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // User profile query - only runs when we have a session
  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError
  } = useQuery({
    queryKey: authKeys.profile(),
    queryFn: () => getUserProfile(session?.user?.id || ''),
    enabled: !!session?.user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Update global user data when session changes
  useEffect(() => {
    if (session?.user) {
      setUserData(session.user.id, session.user.email || null);
    } else {
      clearUserData();
    }
  }, [session?.user]);

  // Strategic data access function
  const getAuthData = (operation: 'read' | 'write' | 'sensitive' = 'read') => {
    const strategy = getAuthStrategy(operation);

    if (strategy === 'getUser') {
      return {
        user: secureUser || null,
        isLoading: isSecureUserLoading,
        error: secureUserError,
      };
    }

    return {
      user: session?.user || null,
      isLoading: isLoading,
      error: error,
    };
  };

  // Sign out mutation with cleanup
  const signOutMutation = useMutation({
    mutationFn: async () => {
      clearUserData();
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.clear();
    },
  });

  // Refresh session mutation
  const refreshSessionMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      return data.session;
    },
    onSuccess: (newSession) => {
      queryClient.setQueryData(authKeys.session(), newSession);
      if (newSession?.user) {
        setUserData(newSession.user.id, newSession.user.email || null);
      }
    },
  });

  // Background session refresh with strategic validation
  useEffect(() => {
    if (!session) return;

    const interval = setInterval(async () => {
      if (session && isSessionValid(session)) {
        await refreshSessionMutation.mutateAsync();
      }
    }, 30 * 60 * 1000); // Refresh every 30 minutes

    return () => clearInterval(interval);
  }, [session, refreshSessionMutation]);

  // Auth state change listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        // Update the session cache
        queryClient.setQueryData(authKeys.session(), session);

        // Update global user data
        if (session?.user) {
          setUserData(session.user.id, session.user.email || null);
        } else {
          clearUserData();
        }

        // Clear profile cache on sign out
        if (event === 'SIGNED_OUT') {
          queryClient.removeQueries({ queryKey: authKeys.profile() });
        }

        // Invalidate profile and user cache on sign in
        if (event === 'SIGNED_IN' && session?.user?.id) {
          queryClient.invalidateQueries({ queryKey: authKeys.profile() });
          queryClient.invalidateQueries({ queryKey: authKeys.user() });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [queryClient]);

  // Enhanced performance monitoring
  // Performance monitoring (simplified without circular dependency)
  useEffect(() => {
    if (!enablePerformanceOptimization) return;

    const interval = setInterval(() => {
      // Basic memory monitoring without advanced performance optimization
      const memoryUsage = (performance as { memory?: { usedJSHeapSize?: number } })?.memory?.usedJSHeapSize || 0;
      const memoryMB = memoryUsage / (1024 * 1024);

      // Only warn if memory usage is unusually high (150MB threshold for modern apps)
      if (memoryUsage > 150 * 1024 * 1024) {
        console.warn(`🚨 High memory usage detected: ${memoryMB.toFixed(1)}MB in AuthProvider`);
      } else if (memoryUsage > 0) {
        // Debug info for development - less frequent
        console.debug(`📊 Memory usage: ${memoryMB.toFixed(1)}MB`);
      }
    }, 5 * 60000); // Check every 5 minutes instead of every minute

    return () => clearInterval(interval);
  }, [enablePerformanceOptimization]);

  // Instant user ID access (only on client-side)
  const userId = typeof window !== 'undefined' ? getUserId() : null;
  const userEmail = session?.user?.email || null;

  const value: AuthContextType = {
    user: session?.user || null,
    session: session || null,
    profile: profile || null,
    isAuthenticated: !!session?.user,
    userId,
    userEmail,
    isLoading,
    isLoadingProfile,
    error: error || profileError,
    profileError,
    getAuthData,
    signOut: () => signOutMutation.mutateAsync(),
    refreshSession: () => refreshSessionMutation.mutateAsync(),
    refetch: () => queryClient.refetchQueries({ queryKey: authKeys.session() }),
    refetchProfile: () => queryClient.refetchQueries({ queryKey: authKeys.profile() }),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Legacy compatibility - export the old useAuth function name
export const useAuth = useAuthContext;
