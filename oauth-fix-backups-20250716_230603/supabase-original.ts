import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/database.types'

// This helper ensures we don't get runtime errors during SSR or static generation
let supabaseInstance: ReturnType<typeof createRealClient> | ReturnType<typeof createDummyClient> | null = null;

// Function to create a real Supabase client
function createRealClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn('Supabase URL or Anon Key is missing. Using dummy client instead.')
    return createDummyClient()
  }

  return createClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        storageKey: 'sb-sumruaeyfidjlssrmfrm-auth-token',
        flowType: 'pkce',  // More secure flow type
        debug: false, // Disable debug logging to reduce console noise
        storage: {
          getItem: (key) => {
            try {
              // Check if localStorage is available (not in SSR)
              if (typeof window !== 'undefined' && window.localStorage) {
                const itemStr = window.localStorage.getItem(key);
                console.log(`Storage getItem for key ${key}: ${itemStr ? 'found' : 'not found'}`);
                return itemStr;
              }
              return null;
            } catch (error) {
              console.error(`Error in storage.getItem for key ${key}:`, error);
              return null;
            }
          },
          setItem: (key, value) => {
            try {
              // Check if localStorage is available (not in SSR)
              if (typeof window !== 'undefined' && window.localStorage) {
                console.log(`Storage setItem for key ${key}`);
                window.localStorage.setItem(key, value);
              }
            } catch (error) {
              console.error(`Error in storage.setItem for key ${key}:`, error);
            }
          },
          removeItem: (key) => {
            try {
              // Check if localStorage is available (not in SSR)
              if (typeof window !== 'undefined' && window.localStorage) {
                console.log(`Storage removeItem for key ${key}`);
                window.localStorage.removeItem(key);
              }
            } catch (error) {
              console.error(`Error in storage.removeItem for key ${key}:`, error);
            }
          }
        }
      }
    }
  )
}

// Function to create a dummy client for SSR/static generation or missing env vars
function createDummyClient() {
  return {
    from: () => ({
      select: () => ({ data: null, error: null }),
      insert: () => ({ data: null, error: null }),
      update: () => ({ data: null, error: null }),
      delete: () => ({ data: null, error: null }),
      eq: () => ({ data: null, error: null }),
      order: () => ({ data: null, error: null }),
      limit: () => ({ data: null, error: null }),
      single: () => ({ data: null, error: null }),
      maybeSingle: () => Promise.resolve({ data: null, error: null }),
    }),
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      signOut: () => Promise.resolve({ error: null }),
      signInWithPassword: () => Promise.resolve({ data: { user: null, session: null }, error: null }),
      signUp: () => Promise.resolve({ data: { user: null, session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
      refreshSession: () => Promise.resolve({ data: { session: null }, error: null }),
      resetPasswordForEmail: () => Promise.resolve({ data: null, error: null }),
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any // We need to use 'any' here because we're creating a mock client
}

// Get the Supabase client instance
export function getSupabase() {
  // For browser environments, always create a real client
  if (typeof window !== 'undefined') {
    return createRealClient()
  }

  // For server environments, use a singleton pattern
  if (!supabaseInstance) {
    // During build/SSR, use a dummy client to avoid errors
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'phase-production-build') {
      supabaseInstance = createDummyClient()
    } else {
      // For normal server runtime, use a real client
      supabaseInstance = createRealClient()
    }
  }

  return supabaseInstance
}

// Create and export the Supabase client
export const supabase = getSupabase()

// Helper function to refresh the session
export const refreshSession = async () => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return false;

    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error("Error refreshing session:", error);
      return false;
    }

    if (!data.session) {
      console.warn("No session returned when refreshing");
      return false;
    }

    return true;
  } catch (err) {
    console.error("Exception during session refresh:", err);
    return false;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error("Error checking authentication:", error);
      return false;
    }

    return !!data.session;
  } catch (err) {
    console.error("Exception during auth check:", err);
    return false;
  }
};

export type { Database }
export * from '@/lib/database.types'
