# Database Security and Performance Remediation Guide

## Overview
This document addresses critical security and performance warnings identified by the Supabase database linter. The issues have been prioritized based on their impact on security and performance.

## Issues Identified

### 🔴 Critical Performance Issues

#### 1. Auth RLS Initialization Plan (16 warnings)
**Problem**: RLS policies using `auth.role()` and `auth.uid()` directly cause these functions to be re-evaluated for each row, leading to severe performance degradation at scale.

**Tables Affected**:
- `answers` (2 policies)
- `responses` (2 policies)
- `questions` (4 policies)
- `poll_simulations` (4 policies)
- `polls` (4 policies)

**Solution**: Wrap auth function calls with `(select auth.function())` to cache the result per query instead of evaluating per row.

**Impact**: This fix can improve query performance by 10-100x on large datasets.

### 🟡 High Priority Security Issues

#### 2. Function Search Path Mutable (17 warnings)
**Problem**: Database functions don't have `search_path` set, which creates a potential security vulnerability where malicious users could create functions in schemas that get evaluated before the intended functions.

**Functions Affected**:
- `handle_new_user`
- `refresh_poll_stats`
- `cleanup_expired_simulation_cache`
- `get_simulation_statistics`
- `update_poll_timestamp_on_simulation`
- `get_poll_with_counts`
- `get_policies_for_table`
- `check_user_poll_access`
- `test_poll_policies`
- `check_multiple_policies`
- `get_polls_with_counts`
- `find_duplicate_indexes`
- `get_index_usage_stats`
- `performance_diagnostics`
- `query_performance_check`
- `validate_security_policies`
- `database_health_check`

**Solution**: Add `SET search_path = public` to all functions.

#### 3. Extension in Public Schema
**Problem**: `pg_trgm` extension is installed in the public schema, which is a security risk.

**Solution**: Move extension to a dedicated `extensions` schema.

#### 4. Materialized View in API
**Problem**: `poll_stats` materialized view is accessible via API to anon/authenticated users.

**Solution**: Review if this exposure is intentional. Consider restricting access if not needed.

### 🟠 Medium Priority Auth Issues

#### 5. Auth OTP Long Expiry
**Problem**: OTP (One-Time Password) expiry is set to more than one hour.

**Recommendation**: Configure OTP expiry to less than one hour in Supabase Auth settings.

#### 6. Leaked Password Protection Disabled
**Problem**: Password protection against known compromised passwords is disabled.

**Recommendation**: Enable leaked password protection in Supabase Auth settings.

## Implementation Plan

### Phase 1: Database Schema Fixes (Automated)
Run the `fix-db-security-performance.sql` script to address:
- ✅ RLS policy performance optimization
- ✅ Function search_path security fixes
- ✅ Extension schema relocation
- ✅ Materialized view permissions review

### Phase 2: Auth Configuration (Manual)
Configure in Supabase Dashboard:
1. **Reduce OTP Expiry**:
   - Go to Authentication > Settings > Auth
   - Set OTP expiry to 30 minutes (1800 seconds)

2. **Enable Leaked Password Protection**:
   - Go to Authentication > Settings > Security
   - Enable "Prevent use of leaked passwords"

## Risk Assessment

### Before Fixes
- **Performance**: 🔴 Critical - RLS policies cause O(n) auth evaluations
- **Security**: 🟡 High - Functions vulnerable to search path attacks
- **Auth**: 🟠 Medium - Weak auth configurations

### After Fixes
- **Performance**: 🟢 Optimized - RLS policies use cached auth evaluations
- **Security**: 🟢 Secured - Functions protected with explicit search paths
- **Auth**: 🟢 Hardened - Industry standard auth configurations

## Verification

After running the fixes, verify with these queries:

```sql
-- Check RLS policies are optimized
SELECT COUNT(*) as optimized_policies
FROM pg_policies
WHERE schemaname = 'public'
  AND (qual LIKE '%(select auth.%' OR with_check LIKE '%(select auth.%');

-- Check function security
SELECT proname,
       CASE WHEN proconfig::text LIKE '%search_path%'
            THEN 'SECURE' ELSE 'VULNERABLE' END as status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public';

-- Check extension location
SELECT extname, nspname
FROM pg_extension e
JOIN pg_namespace n ON e.extnamespace = n.oid
WHERE extname = 'pg_trgm';
```

## Performance Impact

### Expected Improvements
- **RLS Queries**: 10-100x faster on large datasets
- **Function Calls**: Eliminated search path overhead
- **Security**: Hardened against injection attacks

### Monitoring
Monitor these metrics after deployment:
- Query execution times for tables with RLS
- Function execution performance
- Auth-related query patterns

## Rollback Plan

If issues arise, rollback steps:

1. **RLS Policies**: Restore from backup or manually revert to original policies
2. **Functions**: Remove search_path settings with `ALTER FUNCTION ... RESET search_path`
3. **Extension**: Move back to public schema if needed
4. **Auth Settings**: Revert in Supabase Dashboard

## Next Steps

1. ✅ Review and understand the fixes
2. ⏳ Test in development environment
3. ⏳ Deploy to staging for validation
4. ⏳ Apply auth configuration changes
5. ⏳ Deploy to production during maintenance window
6. ⏳ Monitor performance metrics

## Support

For questions or issues:
- Check Supabase documentation: https://supabase.com/docs/guides/database/database-linter
- Review RLS best practices: https://supabase.com/docs/guides/database/postgres/row-level-security
- Monitor database performance: https://supabase.com/docs/guides/platform/metrics
