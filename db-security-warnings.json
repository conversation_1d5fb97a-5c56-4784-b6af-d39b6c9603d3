[{"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.handle_new_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "handle_new_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_handle_new_user_3e33ae456feb268422a35d6d99706dcf"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.refresh_poll_stats\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "refresh_poll_stats", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_refresh_poll_stats_2930ddbecfb5ecfb1ca58891f8e94c6c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.cleanup_expired_simulation_cache\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "cleanup_expired_simulation_cache", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_cleanup_expired_simulation_cache_af18418d2ce771e9cb699b47064336ab"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_simulation_statistics\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_simulation_statistics", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_simulation_statistics_6f7c62442292de1141c0bbdc99e3dd96"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_poll_timestamp_on_simulation\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_poll_timestamp_on_simulation", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_poll_timestamp_on_simulation_4d97db11f086f0a9fc369471adbd3d7a"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_poll_with_counts\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_poll_with_counts", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_poll_with_counts_226f9828cebdc25c31384bfd3b2fea1f"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_policies_for_table\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_policies_for_table", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_policies_for_table_b447e065425d84c2496fe64f1e80c7cb"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_user_poll_access\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_user_poll_access", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_user_poll_access_92d318ffb84388986dbefb30b6025425"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.test_poll_policies\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "test_poll_policies", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_test_poll_policies_737cf1128b606ec0082baad11d9055eb"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_multiple_policies\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_multiple_policies", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_multiple_policies_88a061eeac69c54e4d4b04f2f2eea316"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_polls_with_counts\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_polls_with_counts", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_polls_with_counts_daa6e3fb26b6bbe64befb501487f56f0"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.find_duplicate_indexes\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "find_duplicate_indexes", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_find_duplicate_indexes_17879408c94e718715eccc2e0bff3f12"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_index_usage_stats\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_index_usage_stats", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_index_usage_stats_401fd599ca36c36f35ccce9f20eb9edf"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.performance_diagnostics\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "performance_diagnostics", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_performance_diagnostics_037d050d158c7dca336140fde12dce62"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.query_performance_check\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "query_performance_check", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_query_performance_check_9fd1d82b47872cefa74a6f8ecfdbe7b6"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.validate_security_policies\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "validate_security_policies", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_validate_security_policies_e778620f23f7904342ef768b524c8015"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.database_health_check\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "database_health_check", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_database_health_check_87d33441a577a49b9fa9b637c10de0cb"}, {"name": "extension_in_public", "title": "Extension in Public", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects extensions installed in the \\`public\\` schema.", "detail": "Extension \\`pg_trgm\\` is installed in the public schema. Move it to another schema.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0014_extension_in_public", "metadata": {"name": "pg_trgm", "type": "extension", "schema": "public"}, "cache_key": "extension_in_public_pg_trgm"}, {"name": "materialized_view_in_api", "title": "Materialized View in API", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects materialized views that are accessible over the Data APIs.", "detail": "Materialized view \\`public.poll_stats\\` is selectable by anon or authenticated roles", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0016_materialized_view_in_api", "metadata": {"name": "poll_stats", "type": "materialized view", "schema": "public"}, "cache_key": "materialized_view_in_api_public_poll_stats"}, {"name": "auth_otp_long_expiry", "title": "Auth OTP long expiry", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "OTP expiry exceeds recommended threshold", "detail": "We have detected that you have enabled the email provider with the OTP expiry set to more than an hour. It is recommended to set this value to less than an hour.", "cache_key": "auth_otp_long_expiry", "remediation": "https://supabase.com/docs/guides/platform/going-into-prod#security", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}, {"name": "auth_leaked_password_protection", "title": "Leaked Password Protection Disabled", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Leaked password protection is currently disabled.", "detail": "Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security.", "cache_key": "auth_leaked_password_protection", "remediation": "https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}]