-- VACUUM_MAINTENANCE.sql - INSTRUCTIONS ONLY
--
-- IMPORTANT: VACUUM CANNOT RUN INSIDE TRANSACTION BLOCKS
--
-- To run the VACUUM commands successfully:
--
-- OPTION 1: Run each VACUUM command in a separate query execution:
--   1. Open a new SQL query window in Supabase SQL Editor
--   2. Enter only ONE of the following commands:
--      <PERSON>CUUM ANALYZE questions;
--   3. Execute the command (run query)
--   4. Open a NEW SQL query window
--   5. Enter the next command:
--      VACUUM ANALYZE polls;
--   6. Repeat for all tables (profiles, answers, poll_simulations, responses)
--
-- OPTION 2: Use the Supabase CLI (preferred method):
--   1. We've created separate files for each VACUUM command:
--      - vacuum_questions.sql
--      - vacuum_polls.sql
--      - vacuum_profiles.sql
--      - vacuum_answers.sql
--      - vacuum_poll_simulations.sql
--      - vacuum_responses.sql
--   2. Run each file individually using the CLI:
--      supabase db execute --file vacuum_questions.sql
--      supabase db execute --file vacuum_polls.sql
--      ...and so on
--
--   3. Or run the bash script that executes all files:
--      ./vacuum_maintenance.sh
--
-- The key is that each VACUUM command must be executed SEPARATELY,
-- never as part of a batch or within a transaction block.
--
-- After completing all VACUUM commands, you can run:
SELECT 'VACUUM maintenance verification' as operation,
       COALESCE(SUM(n_dead_tup), 0) as remaining_dead_tuples
FROM pg_stat_user_tables
WHERE schemaname = 'public';
