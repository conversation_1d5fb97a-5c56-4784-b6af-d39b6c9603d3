/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: 'https://pollgpt.com',
  generateRobotsTxt: false, // We have a custom robots.ts
  generateIndexSitemap: false,
  outDir: 'public',
  exclude: ['/dashboard/*', '/api/*'],
  changefreq: 'weekly',
  priority: 0.7,
  sitemapSize: 7000,
  // Make sure XML is properly formatted
  transform: async (config, path) => {
    // Return object which will be transformed into XML
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: new Date().toISOString(),
      alternateRefs: config.alternateRefs ?? [],
    }
  }
}
