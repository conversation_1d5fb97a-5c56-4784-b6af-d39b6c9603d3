{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref", "${env.SUPABASE_MCP_PROJECT_ID}"], "env": {"SUPABASE_ACCESS_TOKEN": "${env.SUPABASE_MCP_KEY}"}}, "postgresql-pollgpt": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "--connection-string", "${env.DATABASE_URL}", "--pool-size", "10", "--idle-timeout", "30000"], "env": {"DATABASE_URL": "${env.DATABASE_URL}", "NODE_ENV": "${env.NODE_ENV}"}}}}