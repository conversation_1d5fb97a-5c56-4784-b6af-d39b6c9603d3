-- Update get_poll_with_counts function to include context fields
-- First drop the existing function since we're changing the return type
DROP FUNCTION IF EXISTS public.get_poll_with_counts(uuid, uuid);

-- Now create the function with the updated return type
CREATE FUNCTION public.get_poll_with_counts(poll_id_param uuid, user_id_param uuid)
 RETURNS TABLE(
   id uuid,
   title text,
   description text,
   created_at timestamp with time zone,
   updated_at timestamp with time zone,
   user_id uuid,
   status text,
   is_public boolean,
   slug text,
   expires_at timestamp with time zone,
   access_code text,
   response_count bigint,
   view_count bigint,
   questions jsonb,
   questions_count bigint,
   source_url text,
   source_type text,
   source_filename text,
   show_source boolean,
   context text
 )
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  RETURN QUERY
  WITH poll_responses AS (
    -- Count responses for this poll
    SELECT COUNT(r.id)::BIGINT AS num_responses
    FROM responses r
    WHERE r.poll_id = poll_id_param
  ),
  poll_questions AS (
    -- Get questions for this poll
    SELECT
      jsonb_agg(
        jsonb_build_object(
          'id', q.id,
          'poll_id', q.poll_id,
          'question_text', q.question_text,
          'question_type', q.question_type,
          'options', q.options,
          'required', q.required,
          'order', q.order
        ) ORDER BY q.order
      ) AS aggregated_questions,
      COUNT(q.id)::BIGINT AS question_count
    FROM questions q
    WHERE q.poll_id = poll_id_param
    GROUP BY q.poll_id
  )
  SELECT
    p.id,
    p.title,
    p.description,
    p.created_at,
    p.updated_at,
    p.user_id,
    p.status,
    p.is_public,
    p.slug,
    p.expires_at,
    p.access_code,
    COALESCE((SELECT num_responses FROM poll_responses), 0) AS response_count,
    COALESCE(p.views, 0)::BIGINT AS view_count,
    COALESCE((SELECT aggregated_questions FROM poll_questions), '[]'::jsonb) AS questions,
    COALESCE((SELECT question_count FROM poll_questions), 0) AS questions_count,
    p.source_url,
    p.source_type,
    p.source_filename,
    COALESCE(p.show_source, true) AS show_source,
    p.context
  FROM
    polls p
  WHERE
    p.id = poll_id_param
    AND (p.user_id = user_id_param OR p.is_public = true);
END;
$function$;
