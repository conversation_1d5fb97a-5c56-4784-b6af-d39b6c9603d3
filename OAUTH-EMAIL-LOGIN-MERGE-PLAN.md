# <PERSON><PERSON><PERSON> and <PERSON><PERSON> Login Merge Implementation Plan

## Objective
Create a unified authentication system that combines the best of both branches:
- **<PERSON>ail login**: Working correctly (preserve from both branches)
- **Google OAuth**: Fix PKCE issues while maintaining race condition improvements

## Implementation Strategy

### Step 1: Create Hybrid Branch
Start with main branch as base and selectively incorporate oauth-enhancements improvements.

### Step 2: Core Authentication System

#### A. Simplified Auth Hook (Based on Main + OAuth-Enhancements Insights)
```typescript
// src/hooks/use-auth-unified.ts
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

export const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
};

// Main auth hook - simplified but robust
export function useAuth() {
  const queryClient = useQueryClient();

  const { data: session, isLoading, error } = useQuery({
    queryKey: authKeys.session(),
    queryFn: async () => {
      const { data, error } = await supabase.auth.getSession();
      if (error) throw error;
      return data.session;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const refreshAuth = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: authKeys.all });
  }, [queryClient]);

  return {
    user: session?.user || null,
    session,
    isLoading,
    error,
    isAuthenticated: !!session?.user,
    refreshAuth,
  };
}

// Email sign in (preserve existing working implementation)
export function useSignIn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

// Simplified Google OAuth (fix PKCE issues)
export function useGoogleAuth() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      // CRITICAL: Use consistent domain/port for PKCE
      const redirectTo = `${window.location.origin}/auth/callback`;

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
          // Remove skipBrowserRedirect to let Supabase handle PKCE properly
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Set up monitoring for OAuth completion
      setupOAuthCompletionMonitoring(queryClient);
    },
  });
}
```

#### B. OAuth Completion Monitoring (Simplified from oauth-enhancements)
```typescript
// src/lib/utils/oauth-completion-monitor.ts
import { QueryClient } from '@tanstack/react-query';
import { authKeys } from '@/hooks/use-auth-unified';

export function setupOAuthCompletionMonitoring(queryClient: QueryClient) {
  if (typeof window === 'undefined') return;

  // Monitor for OAuth redirect completion
  const checkOAuthCompletion = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const hasOAuthParams = urlParams.has('code') || urlParams.has('error') ||
                          urlParams.has('oauth_completed');

    if (hasOAuthParams) {
      console.log('OAuth redirect detected, refreshing auth state');

      // Immediate refresh
      queryClient.invalidateQueries({ queryKey: authKeys.all });

      // Delayed refresh to ensure Supabase has processed the session
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: authKeys.all });
      }, 1500);

      // Clean up URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  };

  // Check on load and setup listeners
  checkOAuthCompletion();
  window.addEventListener('focus', checkOAuthCompletion);

  return () => {
    window.removeEventListener('focus', checkOAuthCompletion);
  };
}
```

#### C. Simplified Auth Callback Route
```typescript
// src/app/auth/callback/route.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const error = requestUrl.searchParams.get('error');

  if (error) {
    console.error('OAuth error:', error);
    return NextResponse.redirect(`${requestUrl.origin}/login?error=oauth_error`);
  }

  if (!code) {
    console.error('No authorization code');
    return NextResponse.redirect(`${requestUrl.origin}/login?error=missing_code`);
  }

  const cookieStore = await cookies();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => cookieStore.getAll(),
        setAll: (cookiesToSet) => {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );

  try {
    // Exchange code for session (Supabase handles PKCE internally)
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.error('Code exchange error:', error);
      return NextResponse.redirect(
        `${requestUrl.origin}/login?error=exchange_failed`
      );
    }

    if (!data.session || !data.user) {
      return NextResponse.redirect(
        `${requestUrl.origin}/login?error=no_session`
      );
    }

    // Handle profile creation for new users
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();

    if (!profile) {
      // Create profile for new user
      await supabase.from('profiles').insert({
        id: data.user.id,
        email: data.user.email,
        name: data.user.user_metadata?.full_name ||
              data.user.user_metadata?.name ||
              data.user.email?.split('@')[0] ||
              'User',
        avatar_url: data.user.user_metadata?.avatar_url || null,
      });
    }

    // Set completion flag and redirect
    const response = NextResponse.redirect(`${requestUrl.origin}/dashboard`);
    response.cookies.set('oauth_completed', 'true', {
      maxAge: 60,
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    return response;
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.redirect(
      `${requestUrl.origin}/login?error=unexpected_error`
    );
  }
}
```

#### D. Updated Login Page
```tsx
// src/app/(auth)/login/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth, useSignIn, useGoogleAuth } from "@/hooks/use-auth-unified";
import { setupOAuthCompletionMonitoring } from "@/lib/utils/oauth-completion-monitor";
import { useQueryClient } from "@tanstack/react-query";

export default function LoginPage() {
  const { user, isLoading } = useAuth();
  const signInMutation = useSignIn();
  const googleAuthMutation = useGoogleAuth();
  const router = useRouter();
  const queryClient = useQueryClient();

  // Set up OAuth monitoring on mount
  useEffect(() => {
    const cleanup = setupOAuthCompletionMonitoring(queryClient);
    return cleanup;
  }, [queryClient]);

  // Redirect if authenticated
  useEffect(() => {
    if (!isLoading && user) {
      router.replace("/dashboard/polls");
    }
  }, [user, isLoading, router]);

  const handleGoogleSignIn = async () => {
    try {
      const result = await googleAuthMutation.mutateAsync();
      if (result.url) {
        window.location.href = result.url;
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      toast.error('Failed to sign in with Google');
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Email login form (preserve existing) */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Email and password fields */}
          <Button type="submit" disabled={isSubmitting}>
            Sign in
          </Button>
        </form>
      </Form>

      {/* Google OAuth button */}
      <Button
        variant="outline"
        onClick={handleGoogleSignIn}
        disabled={googleAuthMutation.isPending}
        className="w-full"
      >
        {googleAuthMutation.isPending ? (
          <>Loading...</>
        ) : (
          <>
            <GoogleIcon />
            Continue with Google
          </>
        )}
      </Button>
    </div>
  );
}
```

### Step 3: Key Implementation Points

#### A. PKCE Fix Strategy
1. **Single Domain/Port**: Ensure entire OAuth flow uses same origin
2. **No Custom Storage**: Remove complex storage systems that interfere with Supabase PKCE
3. **Trust Supabase**: Let Supabase handle PKCE code verifier storage/retrieval
4. **Consistent Redirects**: Use `window.location.origin` for redirect URLs

#### B. Race Condition Fix Strategy
1. **OAuth Completion Monitoring**: Detect OAuth redirects and refresh auth state
2. **Multiple Refresh Attempts**: Immediate + delayed cache invalidation
3. **URL Cleanup**: Remove OAuth parameters after processing
4. **Focus-based Refresh**: Refresh auth state when window gains focus

#### C. Error Handling Strategy
1. **Specific Error Types**: Distinguish between different OAuth error scenarios
2. **User-Friendly Messages**: Provide clear feedback for each error type
3. **Debug Mode**: Include debug features for development environment
4. **Graceful Degradation**: Ensure email login continues working if OAuth fails

### Step 4: Testing Plan

#### A. Google OAuth Testing
1. **Fresh Browser Session**: Test with cleared storage
2. **Consistent Port**: Always use port 3000 for development
3. **Profile Creation**: Verify new user profile creation
4. **Session Persistence**: Test page refresh after login

#### B. Email Login Testing
1. **No Regressions**: Ensure email login still works perfectly
2. **Password Reset**: Verify password reset flows
3. **Email Verification**: Test email verification process

#### C. Integration Testing
1. **Mixed Authentication**: Test switching between email and OAuth
2. **Concurrent Sessions**: Test multiple browser tabs
3. **Network Issues**: Test with poor connectivity

### Step 5: Implementation Order

1. **Create unified auth hook** (based on main branch simplicity)
2. **Implement OAuth completion monitoring** (simplified from oauth-enhancements)
3. **Update auth callback route** (simplified SSR handling)
4. **Update login page** (single component approach)
5. **Add development debug features** (optional, dev-only)
6. **Test thoroughly** on consistent port/domain
7. **Deploy and verify** in production environment

### Step 6: Configuration Requirements

#### A. Development Environment
- **Consistent Port**: Always use port 3000 for OAuth flows
- **Consistent Domain**: Use localhost:3000 or same domain throughout
- **Clean Storage**: Clear browser storage when testing OAuth

#### B. Production Environment
- **SSL Required**: HTTPS for production OAuth flows
- **Consistent Domains**: Same domain for app and OAuth callback
- **Cookie Settings**: Proper secure/sameSite settings for production

### Step 7: Monitoring and Debugging

#### A. Development Debug Features
```typescript
// Debug utilities (development only)
if (process.env.NODE_ENV === 'development') {
  // OAuth debug panel
  // Session state inspector
  // Storage viewer
  // Cache invalidation controls
}
```

#### B. Production Monitoring
- **OAuth Success/Failure Rates**
- **Session Creation Metrics**
- **Error Type Tracking**
- **Performance Monitoring**

This implementation plan addresses both the PKCE issues in oauth-enhancements and the race condition issues in main, while preserving the working email login functionality from both branches.
