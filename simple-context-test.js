// Simple test for context workflow
console.log('🧪 Testing Context Workflow with FeedbackGPT...\n');

// First, let's test the URL extraction API
async function testUrlExtraction() {
  console.log('📡 Testing URL extraction for https://www.feedbackgpt.com');

  try {
    const response = await fetch('http://localhost:3001/api/ai/extract-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: 'https://www.feedbackgpt.com',
        extractionType: 'url',
        analysisType: 'basic'
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ URL extraction successful!');
      console.log('📝 Extracted content length:', data.extractedText?.length || 0);
      console.log('📝 Content preview:', data.extractedText?.substring(0, 200) + '...');
      return data.extractedText;
    } else {
      console.log('❌ URL extraction failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ URL extraction error:', error.message);
    return null;
  }
}

// Test context summarization
async function testContextSummarization(content) {
  console.log('\n🤖 Testing context summarization...');

  try {
    const response = await fetch('http://localhost:3001/api/ai/summarize-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: content,
        sourceType: 'url',
        maxLength: 150
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Context summarization successful!');
      console.log('📝 Summary:', data.summary);
      return data.summary;
    } else {
      console.log('❌ Context summarization failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Context summarization error:', error.message);
    return null;
  }
}

// Run the tests
async function runTests() {
  // Test URL extraction
  const extractedContent = await testUrlExtraction();

  if (extractedContent) {
    // Test context summarization
    const summary = await testContextSummarization(extractedContent);

    if (summary) {
      console.log('\n🎉 All API tests passed!');
      console.log('✅ URL extraction works');
      console.log('✅ Context summarization works');
      console.log('\nNext step: Test the full poll creation workflow');
    }
  }
}

// Run if in browser
if (typeof window !== 'undefined') {
  runTests().catch(console.error);
} else {
  console.log('This test should be run in a browser environment');
}
