/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false, // Remove X-Powered-By header for security
  compress: true, // Enable gzip compression for better performance
  images: {
    domains: ['pollgpt.com'], // Add your domain for optimized images
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap',
        // This ensures the sitemap is always dynamically generated with proper headers
      },
    ];
  },
  async headers() {
    return [
      {
        // This adds proper XML content type for any XML files
        source: '/:path*.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml',
          },
        ],
      },
      {
        // Ensure sitemap specifically gets correct headers
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, stale-while-revalidate=86400',
          },
        ],
      },
    ];
  },
  webpack: (config, { isServer }) => {
    // For pdf-parse and other Node.js modules that use built-in modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        http: false,
        https: false,
        url: false,
        path: false,
        stream: false,
        crypto: false,
        zlib: false,
        // Fix for natural library's webworker-threads
        'webworker-threads': false,
      };
    }

    // Ignore specific modules that cause issues
    config.resolve.alias = {
      ...config.resolve.alias,
      'webworker-threads': false,
    };

    // Skip parsing for problematic modules
    config.module.noParse = /webworker-threads/;

    // Handle tesseract.js workers with better configuration
    if (!isServer) {
      config.module.rules.push({
        test: /\.worker\.js$/,
        use: {
          loader: 'worker-loader',
          options: {
            name: 'static/workers/[hash].worker.js',
            publicPath: '/_next/',
          }
        }
      });

      // Improve tesseract.js handling
      config.module.rules.push({
        test: /tesseract\.js/,
        use: {
          loader: 'file-loader',
          options: {
            publicPath: '/_next/static/workers/',
            outputPath: 'static/workers/',
            name: '[name].[hash].[ext]'
          }
        }
      });

      // Add buffer polyfill for browser compatibility
      config.resolve.fallback = {
        ...config.resolve.fallback,
        buffer: require.resolve('buffer/'),
      };
    }

    // Suppress PDF.js and font warnings during build
    config.infrastructureLogging = {
      level: 'warn',
      appendOnly: true,
    };

    // Add externals to prevent bundling issues
    if (isServer) {
      config.externals = [...(config.externals || []), 'canvas', 'jsdom'];
    }

    return config;
  },
};

export default nextConfig;
