-- Function to get polls with counts in a single query
-- This optimizes the poll loading by reducing multiple queries to one
CREATE OR REPLACE FUNCTION get_polls_with_counts(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  is_published BOOLEAN,
  is_public BOOLEAN,
  slug TEXT,
  response_count BIGINT,
  view_count BIGINT,
  questions JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.description,
    p.created_at,
    p.updated_at,
    p.user_id,
    p.is_published,
    p.is_public,
    p.slug,
    -- Count responses for each poll
    COALESCE((SELECT COUNT(*) FROM responses r WHERE r.poll_id = p.id), 0) AS response_count,
    -- View count (if you have a views table, otherwise return 0)
    0 AS view_count,
    -- Aggregate questions into a JSON array
    COALESCE(
      (SELECT 
        jsonb_agg(
          jsonb_build_object(
            'id', q.id,
            'poll_id', q.poll_id,
            'question_text', q.question_text,
            'question_type', q.question_type,
            'order', q.order
          )
        )
       FROM questions q
       WHERE q.poll_id = p.id
      ),
      '[]'::jsonb
    ) AS questions
  FROM 
    polls p
  WHERE 
    p.user_id = user_id_param
  ORDER BY 
    p.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_polls_with_counts(UUID) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION get_polls_with_counts(UUID) IS 'Gets all polls for a user with response counts and questions in a single optimized query';
