# Supabase Database Configuration for Optimal Performance

## Settings That Can Be Applied via SQL (Session-Level)
These settings are included in the migration and will be applied automatically:

```sql
-- Enable parallel query execution (session-level)
SET max_parallel_workers_per_gather = 4;

-- Optimize work memory for better sorting/hashing (session-level)
SET work_mem = '64MB';
```

## Settings That Require Server Configuration (Managed by Supabase)
These settings cannot be changed via SQL and must be configured through Supabase's infrastructure:

### 1. Shared Buffers
- **What it does**: Controls how much memory PostgreSQL uses for caching
- **Recommended**: 25% of available RAM
- **Supabase**: This is automatically optimized by Supabase based on your plan

### 2. Max Parallel Workers
- **What it does**: Controls maximum parallel workers across all sessions
- **Recommended**: Number of CPU cores
- **Supabase**: Automatically configured based on your compute resources

### 3. Effective Cache Size
- **What it does**: Tells PostgreSQL how much memory is available for caching
- **Recommended**: 75% of available RAM
- **Supabase**: Automatically optimized

## How to Monitor Performance Settings in Supabase

### Via SQL Dashboard
```sql
-- Check current settings
SELECT name, setting, unit, context
FROM pg_settings
WHERE name IN (
    'shared_buffers',
    'effective_cache_size',
    'work_mem',
    'max_parallel_workers_per_gather',
    'max_parallel_workers'
);

-- Check memory usage
SELECT
    pg_size_pretty(pg_total_relation_size('polls')) as polls_size,
    pg_size_pretty(pg_total_relation_size('questions')) as questions_size,
    pg_size_pretty(pg_total_relation_size('responses')) as responses_size;
```

### Via Supabase Dashboard
1. Go to **Settings** → **Database**
2. Check **Compute Resources** section
3. Monitor **Database Statistics**

## Performance Optimization Strategy

### 1. Application-Level Optimizations (Immediate)
- ✅ Index optimization (handled by migration)
- ✅ RLS policy optimization (handled by migration)
- ✅ Query optimization (handled by migration)

### 2. Connection-Level Optimizations (Automatic)
- Connection pooling (managed by Supabase)
- Session-level settings (applied in migration)

### 3. Server-Level Optimizations (Managed by Supabase)
- Memory allocation (shared_buffers, etc.)
- CPU utilization (parallel workers)
- Storage optimization (automatically managed)

## Monitoring Performance Improvements

### Key Metrics to Watch
1. **Query Performance**: Use `EXPLAIN ANALYZE` on key queries
2. **Index Usage**: Monitor `pg_stat_user_indexes`
3. **Cache Hit Ratio**: Check `pg_stat_database`
4. **Connection Stats**: Monitor `pg_stat_activity`

### SQL Monitoring Queries
```sql
-- Check cache hit ratio (should be > 95%)
SELECT
    schemaname,
    relname,
    heap_blks_read,
    heap_blks_hit,
    CASE
        WHEN heap_blks_hit + heap_blks_read = 0 THEN 0
        ELSE ROUND(heap_blks_hit * 100.0 / (heap_blks_hit + heap_blks_read), 2)
    END AS cache_hit_ratio
FROM pg_statio_user_tables
WHERE schemaname = 'public';

-- Check index usage
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

## Next Steps
1. ✅ Run the fixed migration (server-level settings removed)
2. ✅ Monitor performance improvements from indexes and RLS optimization
3. ✅ Use the monitoring queries to validate improvements
4. ✅ Consider upgrading Supabase plan if more resources are needed

## Supabase Plan Considerations
- **Free Tier**: Basic optimization, limited resources
- **Pro Tier**: Better performance, more CPU/memory
- **Team/Enterprise**: Full optimization, dedicated resources

The migration will still provide significant performance improvements through:
- Optimized indexes
- Better RLS policies
- Materialized views
- Query optimization
