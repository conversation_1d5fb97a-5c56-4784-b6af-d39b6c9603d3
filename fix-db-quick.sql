-- ================================================================
-- Database Security and Performance Quick Fix Script
-- ================================================================
-- This script addresses the most critical issues with safe error handling
-- ================================================================

-- ================================================================
-- PART 1: Fix RLS Performance Issues (Critical Performance Impact)
-- ================================================================
-- Issue: auth.<function>() calls in RLS policies cause re-evaluation per row
-- Solution: Wrap auth calls with (select auth.<function>()) for caching

-- Drop and recreate answers policies
DROP POLICY IF EXISTS "answers_insert_policy" ON public.answers;
CREATE POLICY "answers_insert_policy" ON public.answers
FOR INSERT WITH CHECK (
  (((select auth.role()) = 'authenticated'::text) AND (response_id IN ( SELECT responses.id
   FROM responses
  WHERE (responses.user_id = ( SELECT auth.uid() AS uid))))) OR
  (((select auth.role()) = 'anon'::text) AND (response_id IN ( SELECT r.id
   FROM (responses r
     JOIN polls p ON ((r.poll_id = p.id)))
  WHERE ((p.is_public = true) AND (p.status = 'active'::text))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "answers_select_policy" ON public.answers;
CREATE POLICY "answers_select_policy" ON public.answers
FOR SELECT USING (
  (((select auth.role()) = 'authenticated'::text) AND (response_id IN ( SELECT responses.id
   FROM responses
  WHERE (responses.user_id = ( SELECT auth.uid() AS uid))))) OR
  (((select auth.role()) = 'authenticated'::text) AND (response_id IN ( SELECT r.id
   FROM (responses r
     JOIN polls p ON ((r.poll_id = p.id)))
  WHERE (p.user_id = ( SELECT auth.uid() AS uid))))) OR
  (((select auth.role()) = 'anon'::text) AND (response_id IN ( SELECT r.id
   FROM (responses r
     JOIN polls p ON ((r.poll_id = p.id)))
  WHERE ((p.is_public = true) AND (p.status = 'active'::text))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

-- Drop and recreate responses policies
DROP POLICY IF EXISTS "responses_insert_policy" ON public.responses;
CREATE POLICY "responses_insert_policy" ON public.responses
FOR INSERT WITH CHECK (
  (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = responses.poll_id) AND (polls.status = 'active'::text)))) AND
  ((((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
   (((select auth.role()) = 'anon'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = responses.poll_id) AND (polls.is_public = true))))) OR
   ((select auth.role()) = 'dashboard_user'::text))
);

DROP POLICY IF EXISTS "responses_select_policy" ON public.responses;
CREATE POLICY "responses_select_policy" ON public.responses
FOR SELECT USING (
  (((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = responses.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

-- Drop and recreate questions policies
DROP POLICY IF EXISTS "questions_select_policy" ON public.questions;
CREATE POLICY "questions_select_policy" ON public.questions
FOR SELECT USING (
  (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = questions.poll_id) AND (polls.is_public = true)))) OR
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = questions.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "questions_insert_policy" ON public.questions;
CREATE POLICY "questions_insert_policy" ON public.questions
FOR INSERT WITH CHECK (
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = questions.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "questions_update_policy" ON public.questions;
CREATE POLICY "questions_update_policy" ON public.questions
FOR UPDATE USING (
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = questions.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "questions_delete_policy" ON public.questions;
CREATE POLICY "questions_delete_policy" ON public.questions
FOR DELETE USING (
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = questions.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

-- Drop and recreate poll_simulations policies
DROP POLICY IF EXISTS "poll_simulations_select_policy" ON public.poll_simulations;
CREATE POLICY "poll_simulations_select_policy" ON public.poll_simulations
FOR SELECT USING (
  (((select auth.role()) = 'authenticated'::text) AND (created_by = ( SELECT auth.uid() AS uid))) OR
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = poll_simulations.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  (((select auth.role()) = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = poll_simulations.poll_id) AND (polls.is_public = true))))) OR
  ((select auth.role()) = ANY (ARRAY['service_role'::text, 'dashboard_user'::text]))
);

DROP POLICY IF EXISTS "poll_simulations_insert_policy" ON public.poll_simulations;
CREATE POLICY "poll_simulations_insert_policy" ON public.poll_simulations
FOR INSERT WITH CHECK (
  (((select auth.role()) = 'authenticated'::text) AND (created_by = ( SELECT auth.uid() AS uid)) AND (EXISTS ( SELECT 1
   FROM polls
  WHERE ((polls.id = poll_simulations.poll_id) AND (polls.user_id = ( SELECT auth.uid() AS uid)))))) OR
  ((select auth.role()) = ANY (ARRAY['service_role'::text, 'dashboard_user'::text]))
);

DROP POLICY IF EXISTS "poll_simulations_update_policy" ON public.poll_simulations;
CREATE POLICY "poll_simulations_update_policy" ON public.poll_simulations
FOR UPDATE USING (
  (((select auth.role()) = 'authenticated'::text) AND (created_by = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = ANY (ARRAY['service_role'::text, 'dashboard_user'::text]))
);

DROP POLICY IF EXISTS "poll_simulations_delete_policy" ON public.poll_simulations;
CREATE POLICY "poll_simulations_delete_policy" ON public.poll_simulations
FOR DELETE USING (
  (((select auth.role()) = 'authenticated'::text) AND (created_by = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = ANY (ARRAY['service_role'::text, 'dashboard_user'::text]))
);

-- Drop and recreate polls policies
DROP POLICY IF EXISTS "polls_delete_policy" ON public.polls;
CREATE POLICY "polls_delete_policy" ON public.polls
FOR DELETE USING (
  (((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "polls_insert_policy" ON public.polls;
CREATE POLICY "polls_insert_policy" ON public.polls
FOR INSERT WITH CHECK (
  (((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "polls_select_policy" ON public.polls;
CREATE POLICY "polls_select_policy" ON public.polls
FOR SELECT USING (
  (is_public = true) OR
  (((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

DROP POLICY IF EXISTS "polls_update_policy" ON public.polls;
CREATE POLICY "polls_update_policy" ON public.polls
FOR UPDATE USING (
  (((select auth.role()) = 'authenticated'::text) AND (user_id = ( SELECT auth.uid() AS uid))) OR
  ((select auth.role()) = 'dashboard_user'::text)
);

-- ================================================================
-- PART 2: Fix Function Search Path Security Issues
-- ================================================================

-- Fix functions that exist with no parameters
DO $$
BEGIN
    -- handle_new_user
    BEGIN
        ALTER FUNCTION public.handle_new_user() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function handle_new_user() not found, skipping...';
    END;

    -- refresh_poll_stats
    BEGIN
        ALTER FUNCTION public.refresh_poll_stats() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function refresh_poll_stats() not found, skipping...';
    END;

    -- cleanup_expired_simulation_cache
    BEGIN
        ALTER FUNCTION public.cleanup_expired_simulation_cache() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function cleanup_expired_simulation_cache() not found, skipping...';
    END;

    -- update_poll_timestamp_on_simulation
    BEGIN
        ALTER FUNCTION public.update_poll_timestamp_on_simulation() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function update_poll_timestamp_on_simulation() not found, skipping...';
    END;

    -- test_poll_policies
    BEGIN
        ALTER FUNCTION public.test_poll_policies() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function test_poll_policies() not found, skipping...';
    END;

    -- check_multiple_policies
    BEGIN
        ALTER FUNCTION public.check_multiple_policies() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function check_multiple_policies() not found, skipping...';
    END;

    -- find_duplicate_indexes
    BEGIN
        ALTER FUNCTION public.find_duplicate_indexes() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function find_duplicate_indexes() not found, skipping...';
    END;

    -- get_index_usage_stats
    BEGIN
        ALTER FUNCTION public.get_index_usage_stats() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function get_index_usage_stats() not found, skipping...';
    END;

    -- performance_diagnostics
    BEGIN
        ALTER FUNCTION public.performance_diagnostics() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function performance_diagnostics() not found, skipping...';
    END;

    -- query_performance_check
    BEGIN
        ALTER FUNCTION public.query_performance_check() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function query_performance_check() not found, skipping...';
    END;

    -- validate_security_policies
    BEGIN
        ALTER FUNCTION public.validate_security_policies() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function validate_security_policies() not found, skipping...';
    END;

    -- database_health_check
    BEGIN
        ALTER FUNCTION public.database_health_check() SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function database_health_check() not found, skipping...';
    END;
END $$;

-- Fix functions with parameters
DO $$
BEGIN
    -- check_user_poll_access(uuid)
    BEGIN
        ALTER FUNCTION public.check_user_poll_access(uuid) SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function check_user_poll_access(uuid) not found, skipping...';
    END;

    -- get_policies_for_table(text)
    BEGIN
        ALTER FUNCTION public.get_policies_for_table(text) SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function get_policies_for_table(text) not found, skipping...';
    END;

    -- get_poll_with_counts(uuid, uuid)
    BEGIN
        ALTER FUNCTION public.get_poll_with_counts(uuid, uuid) SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function get_poll_with_counts(uuid, uuid) not found, skipping...';
    END;

    -- get_polls_with_counts with all parameters
    BEGIN
        ALTER FUNCTION public.get_polls_with_counts(uuid, integer, integer, boolean, text, text) SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function get_polls_with_counts(...) not found, skipping...';
    END;

    -- get_simulation_statistics(uuid)
    BEGIN
        ALTER FUNCTION public.get_simulation_statistics(uuid) SET search_path = public;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'Function get_simulation_statistics(uuid) not found, skipping...';
    END;
END $$;

-- ================================================================
-- SUCCESS MESSAGE
-- ================================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Database security and performance fixes applied successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 Next steps:';
    RAISE NOTICE '1. Run verification queries to confirm fixes';
    RAISE NOTICE '2. Configure Auth settings in Supabase Dashboard';
    RAISE NOTICE '3. Monitor query performance improvements';
    RAISE NOTICE '';
END $$;
