-- RLS policies for questions table
-- Drop any existing policies for questions table
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN 
    SELECT policyname FROM pg_policies WHERE tablename = 'questions'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON questions', policy_record.policyname);
  END LOOP;
END;
$$;

-- Create proper RLS policies for questions table
-- Allow users to view questions for their own polls
CREATE POLICY "Users can view questions for their polls"
ON questions FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow users to insert questions for their own polls
CREATE POLICY "Users can insert questions for their polls"
ON questions FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow users to update questions for their own polls
CREATE POLICY "Users can update questions for their polls"
ON questions FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.user_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow users to delete questions for their own polls
CREATE POLICY "Users can delete questions for their polls"
ON questions FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow questions for public polls to be viewed by everyone
CREATE POLICY "Questions for public polls are viewable by everyone"
ON questions FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.is_public = true
    AND polls.is_published = true
  )
);

-- Enable RLS on questions table
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

-- RLS policies for responses table
-- Drop any existing policies for responses table
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN 
    SELECT policyname FROM pg_policies WHERE tablename = 'responses'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON responses', policy_record.policyname);
  END LOOP;
END;
$$;

-- Create proper RLS policies for responses table
-- Allow poll owners to view all responses to their polls
CREATE POLICY "Users can view responses for their polls"
ON responses FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = responses.poll_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow any authenticated user to submit responses to published polls
CREATE POLICY "Users can insert responses to any poll"
ON responses FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = responses.poll_id
    AND polls.is_published = true
  )
);

-- Allow users to view their own responses
CREATE POLICY "Users can view their own responses"
ON responses FOR SELECT
USING (
  responses.user_id = auth.uid()
);

-- Enable RLS on responses table
ALTER TABLE responses ENABLE ROW LEVEL SECURITY;

-- RLS policies for answers table
-- Drop any existing policies for answers table
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN 
    SELECT policyname FROM pg_policies WHERE tablename = 'answers'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON answers', policy_record.policyname);
  END LOOP;
END;
$$;

-- Create proper RLS policies for answers table
-- Allow poll owners to view all answers to their polls
CREATE POLICY "Users can view answers for their polls"
ON answers FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM responses
    JOIN polls ON polls.id = responses.poll_id
    WHERE responses.id = answers.response_id
    AND polls.user_id = auth.uid()
  )
);

-- Allow any authenticated user to submit answers to published polls
CREATE POLICY "Users can insert answers to any poll"
ON answers FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM responses
    JOIN polls ON polls.id = responses.poll_id
    WHERE responses.id = answers.response_id
    AND polls.is_published = true
  )
);

-- Allow users to view their own answers
CREATE POLICY "Users can view their own answers"
ON answers FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM responses
    WHERE responses.id = answers.response_id
    AND responses.user_id = auth.uid()
  )
);

-- Enable RLS on answers table
ALTER TABLE answers ENABLE ROW LEVEL SECURITY;
