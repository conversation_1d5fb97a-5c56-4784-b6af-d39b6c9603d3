-- Create diagnostic functions to help with RLS policy debugging
-- Run this in the Supabase SQL Editor

-- Function to get policies for a specific table
CREATE OR REPLACE FUNCTION public.get_policies_for_table(table_name text)
RETURNS TABLE(
  schemaname text,
  tablename text,
  policyname text,
  permissive text, -- Changed from boolean to text to fix type mismatch
  roles text[],
  cmd text,
  cmd_pretty text,
  qual text,
  with_check text
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.schemaname::text,
    p.tablename::text,
    p.policyname::text,
    p.permissive::text, -- Cast to text to match the function return type
    p.roles,
    p.cmd::text,
    CASE p.cmd
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
      ELSE p.cmd::text
    END as cmd_pretty,
    p.qual::text,
    p.with_check::text
  FROM
    pg_policies p
  WHERE
    p.tablename = table_name
  ORDER BY
    p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a user's policies are working properly
CREATE OR REPLACE FUNCTION public.check_user_poll_access(user_id uuid)
RETURNS TABLE(
  policy_name text,
  cmd text,
  has_access boolean,
  poll_count bigint,
  error_message text
)
SECURITY DEFINER
AS $$
DECLARE
  result_row record;
  error_text text;
BEGIN
  -- Check SELECT policy - own polls
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE user_id = check_user_poll_access.user_id;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      TRUE,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      FALSE,
      0::bigint,
      SQLERRM;
  END;

  -- Check SELECT policy - public polls
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE is_public = true AND is_published = true;
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      TRUE,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      FALSE,
      0::bigint,
      SQLERRM;
  END;

  -- Additional policy checks can be added here
END;
$$ LANGUAGE plpgsql;

-- Test the functions
SELECT * FROM public.get_policies_for_table('polls');
