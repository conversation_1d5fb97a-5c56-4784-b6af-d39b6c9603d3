-- Add performance optimization indexes for PollGPT
-- Run this in your Supabase SQL Editor

-- First check existing indexes
SELECT
  tablename,
  indexname,
  indexdef
FROM
  pg_indexes
WHERE
  schemaname = 'public'
  AND (tablename = 'polls' OR tablename = 'questions' OR tablename = 'responses')
ORDER BY
  tablename, indexname;

-- Add index on user_id in polls table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'polls' AND indexname = 'polls_user_id_idx'
  ) THEN
    CREATE INDEX polls_user_id_idx ON public.polls (user_id);
    RAISE NOTICE 'Created index polls_user_id_idx';
  ELSE
    RAISE NOTICE 'Index polls_user_id_idx already exists';
  END IF;
END
$$;

-- Add index on poll_id in questions table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'questions' AND indexname = 'questions_poll_id_idx'
  ) THEN
    CREATE INDEX questions_poll_id_idx ON public.questions (poll_id);
    RAISE NOTICE 'Created index questions_poll_id_idx';
  ELSE
    RAISE NOTICE 'Index questions_poll_id_idx already exists';
  END IF;
END
$$;

-- Add index on poll_id in responses table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'responses' AND indexname = 'responses_poll_id_idx'
  ) THEN
    CREATE INDEX responses_poll_id_idx ON public.responses (poll_id);
    RAISE NOTICE 'Created index responses_poll_id_idx';
  ELSE
    RAISE NOTICE 'Index responses_poll_id_idx already exists';
  END IF;
END
$$;

-- Add combined index for public polls query if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'polls' AND indexname = 'polls_public_published_idx'
  ) THEN
    CREATE INDEX polls_public_published_idx ON public.polls (is_public, is_published);
    RAISE NOTICE 'Created index polls_public_published_idx';
  ELSE
    RAISE NOTICE 'Index polls_public_published_idx already exists';
  END IF;
END
$$;

-- Refresh table statistics for query optimizer
ANALYZE public.polls;
ANALYZE public.questions;
ANALYZE public.responses;

-- Verify indexes were created
SELECT
  tablename,
  indexname,
  indexdef
FROM
  pg_indexes
WHERE
  schemaname = 'public'
  AND (tablename = 'polls' OR tablename = 'questions' OR tablename = 'responses')
ORDER BY
  tablename, indexname;
