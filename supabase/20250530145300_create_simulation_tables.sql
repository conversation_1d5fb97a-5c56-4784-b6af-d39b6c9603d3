-- Create simulation tables for LLM-as-Simulator functionality
-- Migration: 20250530145300_create_simulation_tables.sql

-- Create poll_simulations table to store simulation results
CREATE TABLE IF NOT EXISTS poll_simulations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  poll_id UUID REFERENCES polls(id) ON DELETE CASCADE,
  demographic_group TEXT NOT NULL,
  sample_size INTEGER NOT NULL,
  simulation_data JSONB NOT NULL,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
  citations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),

  -- Constraints
  CONSTRAINT valid_sample_size CHECK (sample_size > 0 AND sample_size <= 1000),
  CONSTRAINT valid_demographic CHECK (length(demographic_group) >= 3)
);

-- Create simulation_cache table for performance optimization
CREATE TABLE IF NOT EXISTS simulation_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_hash TEXT NOT NULL UNIQUE,
  demographic_key TEXT NOT NULL,
  cached_result JSONB NOT NULL,
  cache_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Composite index for cache lookups
  CONSTRAINT unique_cache_entry UNIQUE (question_hash, demographic_key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_poll_simulations_poll_id ON poll_simulations(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_simulations_created_by ON poll_simulations(created_by);
CREATE INDEX IF NOT EXISTS idx_poll_simulations_demographic ON poll_simulations(demographic_group);
CREATE INDEX IF NOT EXISTS idx_poll_simulations_created_at ON poll_simulations(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_simulation_cache_hash ON simulation_cache(question_hash);
CREATE INDEX IF NOT EXISTS idx_simulation_cache_expires ON simulation_cache(cache_expires_at);
CREATE INDEX IF NOT EXISTS idx_simulation_cache_created_at ON simulation_cache(created_at DESC);

-- Add comments for documentation
COMMENT ON TABLE poll_simulations IS 'Stores LLM-generated poll simulation results';
COMMENT ON TABLE simulation_cache IS 'Cache for simulation results to optimize performance and reduce API costs';

COMMENT ON COLUMN poll_simulations.simulation_data IS 'JSON containing full simulation response including distribution, individuals, and analysis';
COMMENT ON COLUMN poll_simulations.confidence_score IS 'LLM confidence score for the simulation (0.0 to 1.0)';
COMMENT ON COLUMN poll_simulations.citations IS 'Array of academic citations used by the LLM for the simulation';

COMMENT ON COLUMN simulation_cache.question_hash IS 'SHA256 hash of question + options for cache key';
COMMENT ON COLUMN simulation_cache.demographic_key IS 'Normalized demographic identifier for cache grouping';
COMMENT ON COLUMN simulation_cache.cache_expires_at IS 'Expiration timestamp for cache invalidation';

-- Add trigger to automatically update poll updated_at when simulation is added
CREATE OR REPLACE FUNCTION update_poll_timestamp_on_simulation()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE polls
  SET updated_at = NOW()
  WHERE id = NEW.poll_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_poll_on_simulation
  AFTER INSERT ON poll_simulations
  FOR EACH ROW
  EXECUTE FUNCTION update_poll_timestamp_on_simulation();

-- Add function to clean expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_simulation_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM simulation_cache
  WHERE cache_expires_at < NOW();

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add function to get simulation statistics
CREATE OR REPLACE FUNCTION get_simulation_statistics(user_id_param UUID DEFAULT NULL)
RETURNS TABLE (
  total_simulations BIGINT,
  unique_demographics BIGINT,
  avg_confidence NUMERIC,
  recent_simulations BIGINT,
  cache_hit_potential BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_simulations,
    COUNT(DISTINCT demographic_group) as unique_demographics,
    ROUND(AVG(confidence_score), 3) as avg_confidence,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_simulations,
    (SELECT COUNT(*) FROM simulation_cache WHERE cache_expires_at > NOW()) as cache_hit_potential
  FROM poll_simulations
  WHERE (user_id_param IS NULL OR created_by = user_id_param);
END;
$$ LANGUAGE plpgsql;
