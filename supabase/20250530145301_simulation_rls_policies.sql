-- RLS policies for simulation tables
-- Migration: 20250530145301_simulation_rls_policies.sql

-- Enable RLS on simulation tables
ALTER TABLE poll_simulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_cache ENABLE ROW LEVEL SECURITY;

-- RLS Policies for poll_simulations table

-- Allow users to view simulations for polls they own or public polls
CREATE POLICY "Users can view simulations for accessible polls" ON poll_simulations
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM polls p
      WHERE p.id = poll_id
      AND (
        p.user_id = auth.uid()
        OR p.is_public = true
      )
    )
  );

-- Allow users to create simulations for polls they own
CREATE POLICY "Users can create simulations for own polls" ON poll_simulations
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM polls p
      WHERE p.id = poll_id
      AND p.user_id = auth.uid()
    )
    AND created_by = auth.uid()
  );

-- Allow users to update their own simulations
CREATE POLICY "Users can update own simulations" ON poll_simulations
  FOR UPDATE
  USING (created_by = auth.uid())
  WITH CHECK (created_by = auth.uid());

-- Allow users to delete their own simulations
CREATE POLICY "Users can delete own simulations" ON poll_simulations
  FOR DELETE
  USING (created_by = auth.uid());

-- RLS Policies for simulation_cache table

-- Allow all authenticated users to read from cache (for performance)
CREATE POLICY "Authenticated users can read simulation cache" ON simulation_cache
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow all authenticated users to insert into cache
CREATE POLICY "Authenticated users can insert simulation cache" ON simulation_cache
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Allow all authenticated users to update cache entries
CREATE POLICY "Authenticated users can update simulation cache" ON simulation_cache
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Allow cache cleanup (delete expired entries)
CREATE POLICY "Allow cache cleanup" ON simulation_cache
  FOR DELETE
  TO authenticated
  USING (cache_expires_at < NOW());

-- Admin policies for simulation management

-- Allow service role to manage all simulations (for admin functions)
CREATE POLICY "Service role can manage all simulations" ON poll_simulations
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role can manage all cache" ON simulation_cache
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Grant necessary permissions

-- Grant access to authenticated users
GRANT SELECT, INSERT ON poll_simulations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON simulation_cache TO authenticated;

-- Grant full access to service role
GRANT ALL ON poll_simulations TO service_role;
GRANT ALL ON simulation_cache TO service_role;

-- Grant access to the helper functions
GRANT EXECUTE ON FUNCTION cleanup_expired_simulation_cache() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_simulation_statistics(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION update_poll_timestamp_on_simulation() TO authenticated, service_role;

-- Create indexes for RLS policy performance
CREATE INDEX IF NOT EXISTS idx_poll_simulations_rls_lookup
  ON poll_simulations(poll_id, created_by);

-- Add policy for public poll access optimization
CREATE INDEX IF NOT EXISTS idx_polls_public_user
  ON polls(is_public, user_id)
  WHERE is_public = true OR user_id IS NOT NULL;
