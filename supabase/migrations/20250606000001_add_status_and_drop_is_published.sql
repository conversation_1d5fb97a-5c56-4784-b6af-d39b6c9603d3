-- Migration: Add 'status' column and remove 'is_published'

BEGIN;

-- 1. Add status column with default 'active'
ALTER TABLE polls
ADD COLUMN status TEXT NOT NULL DEFAULT 'active';

-- 2. Populate status based on existing is_published
UPDATE polls
SET status = CASE WHEN is_published THEN 'active' ELSE 'draft' END;

-- 3. Drop the is_published column since status replaces it
ALTER TABLE polls
DROP COLUMN is_published;

COMMIT;
