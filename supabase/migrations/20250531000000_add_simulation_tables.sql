-- Create simulation tables for the PollGPT simulations feature
-- This migration adds the necessary tables for storing simulation data and implementing a caching system

-- Table for storing simulation results
CREATE TABLE IF NOT EXISTS public.poll_simulations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  poll_id UUID NOT NULL REFERENCES public.polls(id) ON DELETE CASCADE,
  question_id UUID NOT NULL,
  demographic_group TEXT NOT NULL,
  sample_size INTEGER NOT NULL,
  simulation_data JSONB NOT NULL,
  confidence_score DECIMAL(5,2),
  citations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Add RLS policies to simulation table
ALTER TABLE public.poll_simulations ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own simulations
CREATE POLICY "Users can view their own simulations"
  ON public.poll_simulations
  FOR SELECT
  USING (auth.uid() = created_by);

-- Allow users to insert their own simulations
CREATE POLICY "Users can create their own simulations"
  ON public.poll_simulations
  FOR INSERT
  WITH CHECK (auth.uid() = created_by);

-- Add caching table for simulation results
CREATE TABLE IF NOT EXISTS public.simulation_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_hash TEXT NOT NULL,
  demographic_key TEXT NOT NULL,
  cached_result JSONB NOT NULL,
  cache_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(question_hash, demographic_key)
);

-- Add index for efficient cache lookup
CREATE INDEX IF NOT EXISTS simulation_cache_lookup_idx
  ON public.simulation_cache(question_hash, demographic_key);

-- Add index on poll_simulations for efficient poll-based lookups
CREATE INDEX IF NOT EXISTS poll_simulations_poll_id_idx
  ON public.poll_simulations(poll_id);

-- Add index for question-based lookups
CREATE INDEX IF NOT EXISTS poll_simulations_question_id_idx
  ON public.poll_simulations(question_id);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT ON public.poll_simulations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.simulation_cache TO service_role;
