-- Database Performance Optimization for PollGPT
-- This migration adds additional performance optimizations based on analysis

BEGIN;

-- 1. Add missing indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_created_at_desc
ON polls(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_user_status_updated
ON polls(user_id, status, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_poll_created
ON responses(poll_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_poll_order
ON questions(poll_id, "order" ASC);

-- 2. Optimize database settings for better performance
-- Enable parallel query execution
SET max_parallel_workers_per_gather = 4;
SET max_parallel_workers = 8;

-- Optimize work memory for better sorting/hashing
SET work_mem = '64MB';

-- Optimize shared buffers for better caching
SET shared_buffers = '256MB';

-- 3. Add partial indexes for active polls (most common queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_active_public
ON polls(user_id, updated_at DESC)
WHERE status = 'active' AND is_public = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_active_user
ON polls(user_id, updated_at DESC)
WHERE status = 'active';

-- 4. Add covering indexes to reduce I/O
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_cover_dashboard
ON polls(user_id, updated_at DESC)
INCLUDE (id, title, description, status, is_public, created_at);

-- 5. Optimize statistics collection
ALTER TABLE polls SET (n_distinct = 100);
ALTER TABLE questions SET (n_distinct = 1000);
ALTER TABLE responses SET (n_distinct = 10000);

-- 6. Add constraints for better query optimization
ALTER TABLE polls ADD CONSTRAINT check_title_length CHECK (length(title) >= 1 AND length(title) <= 500);
ALTER TABLE questions ADD CONSTRAINT check_question_text_length CHECK (length(question_text) >= 1 AND length(question_text) <= 1000);

-- 7. Create optimized materialized view for dashboard statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS poll_stats AS
SELECT
  p.user_id,
  COUNT(*) as total_polls,
  COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_polls,
  COUNT(CASE WHEN p.status = 'draft' THEN 1 END) as draft_polls,
  COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_polls,
  COALESCE(SUM(r.response_count), 0) as total_responses,
  COALESCE(SUM(p.views), 0) as total_views,
  MAX(p.updated_at) as last_poll_update
FROM polls p
LEFT JOIN (
  SELECT poll_id, COUNT(*) as response_count
  FROM responses
  GROUP BY poll_id
) r ON p.id = r.poll_id
GROUP BY p.user_id;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_poll_stats_user_id ON poll_stats(user_id);

-- 8. Add function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_poll_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY poll_stats;
END;
$$ LANGUAGE plpgsql;

-- 9. Create scheduled job to refresh stats every 5 minutes
SELECT cron.schedule(
  'refresh-poll-stats',
  '*/5 * * * *',
  'SELECT refresh_poll_stats();'
);

-- 10. Add connection pooling optimization settings
-- These should be set in your Supabase dashboard or environment
-- CONNECTION_POOL_SIZE=20
-- CONNECTION_POOL_TIMEOUT=30
-- CONNECTION_POOL_IDLE_TIMEOUT=600

COMMIT;

-- 11. Analyze tables to update statistics
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;
ANALYZE answers;

-- 12. Vacuum tables to reclaim space and update statistics
VACUUM ANALYZE polls;
VACUUM ANALYZE questions;
VACUUM ANALYZE responses;
VACUUM ANALYZE answers;
