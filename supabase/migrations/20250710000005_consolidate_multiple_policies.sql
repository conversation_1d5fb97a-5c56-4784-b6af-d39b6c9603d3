-- Consolidate Multiple Permissive Policies for Performance Optimization
-- This migration consolidates multiple permissive policies into unified policies

BEGIN;

-- 1. Consolidate Answers Table Multiple Permissive Policies
-- Remove existing multiple policies for different roles
DROP POLICY IF EXISTS "anon_insert_policy" ON answers;
DROP POLICY IF EXISTS "authenticated_insert_policy" ON answers;
DROP POLICY IF EXISTS "authenticator_insert_policy" ON answers;
DROP POLICY IF EXISTS "dashboard_user_insert_policy" ON answers;

DROP POLICY IF EXISTS "anon_select_policy" ON answers;
DROP POLICY IF EXISTS "authenticated_select_policy" ON answers;
DROP POLICY IF EXISTS "authenticator_select_policy" ON answers;
DROP POLICY IF EXISTS "dashboard_user_select_policy" ON answers;

-- Create consolidated INSERT policy for answers
CREATE POLICY "consolidated_answers_insert" ON answers
  FOR INSERT WITH CHECK (
    -- Allow authenticated users to insert answers for their own responses
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Allow anonymous users to insert answers for public polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Allow dashboard users full access
    (auth.role() = 'dashboard_user')
  );

-- Create consolidated SELECT policy for answers
CREATE POLICY "consolidated_answers_select" ON answers
  FOR SELECT USING (
    -- Allow authenticated users to view answers for their own responses
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Allow anonymous users to view answers for public polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Allow dashboard users full access
    (auth.role() = 'dashboard_user')
    OR
    -- Allow poll owners to view all answers for their polls
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.user_id = (select auth.uid())
    ))
  );

-- 2. Check for other tables with multiple permissive policies
-- (Based on the warnings, answers table seems to be the main one affected)

-- 3. Create function to help identify remaining multiple policies
CREATE OR REPLACE FUNCTION check_multiple_policies()
RETURNS TABLE(
  table_name text,
  policy_count bigint,
  operation text,
  role_name text
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    schemaname || '.' || tablename as table_name,
    COUNT(*) as policy_count,
    cmd as operation,
    COALESCE(roles::text, 'DEFAULT') as role_name
  FROM pg_policies
  WHERE schemaname = 'public'
  GROUP BY schemaname, tablename, cmd, roles
  HAVING COUNT(*) > 1
  ORDER BY policy_count DESC, table_name, operation;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- Analyze tables after policy changes
ANALYZE answers;

-- Display remaining multiple policies (if any)
SELECT * FROM check_multiple_policies();
