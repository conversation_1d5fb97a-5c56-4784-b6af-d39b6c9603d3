-- Database Performance Optimization for PollGPT (Regular Indexes)
-- This migration creates regular indexes (not concurrent) to avoid transaction block issues
-- Use this for development/staging environments or when concurrent creation is not critical

BEGIN;

-- Create regular indexes (not concurrent) inside transaction
CREATE INDEX IF NOT EXISTS idx_polls_created_at_desc
ON polls(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_polls_user_status_updated
ON polls(user_id, status, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_responses_poll_created
ON responses(poll_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_questions_poll_order
ON questions(poll_id, "order" ASC);

CREATE INDEX IF NOT EXISTS idx_polls_active_public
ON polls(user_id, updated_at DESC)
WHERE status = 'active' AND is_public = true;

CREATE INDEX IF NOT EXISTS idx_polls_active_user
ON polls(user_id, updated_at DESC)
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_polls_cover_dashboard
ON polls(user_id, updated_at DESC)
INCLUDE (id, title, description, status, is_public, created_at);

-- Add constraints for better query optimization (check if columns exist first)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'check_title_length') THEN
    ALTER TABLE polls ADD CONSTRAINT check_title_length CHECK (length(title) >= 1 AND length(title) <= 500);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'check_question_text_length') THEN
    ALTER TABLE questions ADD CONSTRAINT check_question_text_length CHECK (length(question_text) >= 1 AND length(question_text) <= 1000);
  END IF;
END $$;

-- Create optimized materialized view for dashboard statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS poll_stats AS
SELECT
  p.user_id,
  COUNT(*) as total_polls,
  COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_polls,
  COUNT(CASE WHEN p.status = 'draft' THEN 1 END) as draft_polls,
  COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_polls,
  COALESCE(SUM(r.response_count), 0) as total_responses,
  COALESCE(SUM(p.views), 0) as total_views,
  MAX(p.updated_at) as last_poll_update
FROM polls p
LEFT JOIN (
  SELECT poll_id, COUNT(*) as response_count
  FROM responses
  GROUP BY poll_id
) r ON p.id = r.poll_id
GROUP BY p.user_id;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_poll_stats_user_id ON poll_stats(user_id);

-- Add function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_poll_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY poll_stats;
END;
$$ LANGUAGE plpgsql;

-- Create scheduled job to refresh stats every 5 minutes (if cron extension is available)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
    PERFORM cron.schedule(
      'refresh-poll-stats',
      '*/5 * * * *',
      'SELECT refresh_poll_stats();'
    );
  END IF;
END $$;

COMMIT;

-- Analyze tables to update statistics (outside transaction)
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;
ANALYZE answers;

-- Vacuum tables to reclaim space and update statistics (outside transaction)
VACUUM ANALYZE polls;
VACUUM ANALYZE questions;
VACUUM ANALYZE responses;
VACUUM ANALYZE answers;
