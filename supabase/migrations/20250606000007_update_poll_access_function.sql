-- Migration: Update check_user_poll_access function to use status column instead of is_published
-- This migration updates the function that checks user poll access to use the new status column

BEGIN;

-- Drop the existing function
DROP FUNCTION IF EXISTS check_user_poll_access(uuid);

-- Recreate the function with status column instead of is_published
CREATE OR REPLACE FUNCTION check_user_poll_access(user_id uuid)
RETURNS TABLE(
  description text,
  operation text,
  success text,
  count bigint,
  error_message text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result_row record;
  error_text text;
BEGIN
  -- Check SELECT policy - own polls
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE user_id = check_user_poll_access.user_id;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      'true'::text,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      'false'::text,
      0::bigint,
      SQLERRM;
  END;

  -- Check SELECT policy - public polls (updated to use status = 'active')
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE is_public = true AND status = 'active';
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      'true'::text,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      'false'::text,
      0::bigint,
      SQLERRM;
  END;

  -- Additional policy checks can be added here
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION check_user_poll_access(uuid) TO authenticated;

-- Add comment to the function
COMMENT ON FUNCTION check_user_poll_access(uuid) IS 'Checks user poll access policies using status column instead of is_published';

COMMIT;
