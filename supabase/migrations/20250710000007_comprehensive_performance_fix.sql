-- Comprehensive Performance Fix Migration
-- This migration addresses all remaining performance warnings:
-- 1. Consolidates duplicate RLS policies
-- 2. Optimizes auth.uid() usage with (select auth.uid())
-- 3. Runs VACUUM on tables with dead tuples

-- First, drop all duplicate and inefficient policies
-- ANSWERS table cleanup
DROP POLICY IF EXISTS "Anyone can insert answers" ON answers;
DROP POLICY IF EXISTS "Users can insert answers to any poll" ON answers;
DROP POLICY IF EXISTS "Answers can be viewed by poll owner" ON answers;
DROP POLICY IF EXISTS "Users can view answers for their polls" ON answers;
DROP POLICY IF EXISTS "Users can view their own answers" ON answers;

-- RESPONSES table cleanup
DROP POLICY IF EXISTS "Anyone can insert responses" ON responses;
DROP POLICY IF EXISTS "Users can insert responses to any poll" ON responses;
DROP POLICY IF EXISTS "Responses can be viewed by poll owner" ON responses;
DROP POLICY IF EXISTS "Users can view responses for their polls" ON responses;
DROP POLICY IF EXISTS "Users can view their own responses" ON responses;

-- QUESTIONS table cleanup - remove duplicates
DROP POLICY IF EXISTS "Questions can be modified by poll owner" ON questions;
DROP POLICY IF EXISTS "Questions can be viewed by poll owner" ON questions;
DROP POLICY IF EXISTS "Questions for public polls are viewable by everyone" ON questions;
DROP POLICY IF EXISTS "Users can view questions for their polls" ON questions;

-- POLL_SIMULATIONS table cleanup
DROP POLICY IF EXISTS "Users can create simulations for own polls" ON poll_simulations;
DROP POLICY IF EXISTS "Users can view simulations for accessible polls" ON poll_simulations;
DROP POLICY IF EXISTS "Users can view their own simulations" ON poll_simulations;

-- Create optimized, consolidated policies using (select auth.uid())

-- ANSWERS table - consolidated policies
CREATE POLICY "answers_insert_policy" ON answers
  FOR INSERT TO public
  WITH CHECK (
    -- Authenticated users can insert for their own responses
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Anonymous users can insert for public active polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Dashboard users can insert anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "answers_select_policy" ON answers
  FOR SELECT TO public
  USING (
    -- Users can view their own answers
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT id FROM responses WHERE user_id = (select auth.uid())
    ))
    OR
    -- Poll owners can view answers for their polls
    (auth.role() = 'authenticated' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.user_id = (select auth.uid())
    ))
    OR
    -- Anonymous users can view answers for public polls
    (auth.role() = 'anon' AND response_id IN (
      SELECT r.id FROM responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE p.is_public = true AND p.status = 'active'
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

-- RESPONSES table - consolidated policies
CREATE POLICY "responses_insert_policy" ON responses
  FOR INSERT TO public
  WITH CHECK (
    -- Must be for active polls
    EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND status = 'active'
    )
    AND
    (
      -- Authenticated users set their own user_id
      (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
      OR
      -- Anonymous users can respond to public polls (user_id can be null)
      (auth.role() = 'anon' AND EXISTS (
        SELECT 1 FROM polls
        WHERE id = poll_id AND is_public = true
      ))
      OR
      -- Dashboard users can insert anywhere
      (auth.role() = 'dashboard_user')
    )
  );

CREATE POLICY "responses_select_policy" ON responses
  FOR SELECT TO public
  USING (
    -- Users can view their own responses
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    -- Poll owners can view responses for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

-- QUESTIONS table - consolidated policies
CREATE POLICY "questions_select_policy" ON questions
  FOR SELECT TO public
  USING (
    -- Anyone can view questions for public polls
    EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND is_public = true
    )
    OR
    -- Poll owners can view questions for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_insert_policy" ON questions
  FOR INSERT TO public
  WITH CHECK (
    -- Only poll owners can insert questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can insert anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_update_policy" ON questions
  FOR UPDATE TO public
  USING (
    -- Only poll owners can update questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can update anywhere
    (auth.role() = 'dashboard_user')
  );

CREATE POLICY "questions_delete_policy" ON questions
  FOR DELETE TO public
  USING (
    -- Only poll owners can delete questions
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Dashboard users can delete anywhere
    (auth.role() = 'dashboard_user')
  );

-- POLL_SIMULATIONS table - consolidated policies
CREATE POLICY "poll_simulations_select_policy" ON poll_simulations
  FOR SELECT TO public
  USING (
    -- Users can view simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Poll owners can view simulations for their polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND user_id = (select auth.uid())
    ))
    OR
    -- Users can view simulations for public polls
    (auth.role() = 'authenticated' AND EXISTS (
      SELECT 1 FROM polls
      WHERE id = poll_id AND is_public = true
    ))
    OR
    -- Service role and dashboard users can view all
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_insert_policy" ON poll_simulations
  FOR INSERT TO public
  WITH CHECK (
    -- Users can create simulations for their own polls
    (auth.role() = 'authenticated' AND
     created_by = (select auth.uid()) AND
     EXISTS (
       SELECT 1 FROM polls
       WHERE id = poll_id AND user_id = (select auth.uid())
     ))
    OR
    -- Service role and dashboard users can insert anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_update_policy" ON poll_simulations
  FOR UPDATE TO public
  USING (
    -- Users can update simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Service role and dashboard users can update anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

CREATE POLICY "poll_simulations_delete_policy" ON poll_simulations
  FOR DELETE TO public
  USING (
    -- Users can delete simulations they created
    (auth.role() = 'authenticated' AND created_by = (select auth.uid()))
    OR
    -- Service role and dashboard users can delete anywhere
    (auth.role() IN ('service_role', 'dashboard_user'))
  );

-- Update existing policies to use (select auth.uid()) instead of auth.uid()
-- POLLS table
DROP POLICY IF EXISTS "Users can delete their own polls" ON polls;
CREATE POLICY "polls_delete_policy" ON polls
  FOR DELETE TO public
  USING (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;
CREATE POLICY "polls_insert_policy" ON polls
  FOR INSERT TO public
  WITH CHECK (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
CREATE POLICY "polls_select_policy" ON polls
  FOR SELECT TO public
  USING (
    -- Anyone can view public polls
    is_public = true
    OR
    -- Users can view their own polls
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    -- Dashboard users can view all
    (auth.role() = 'dashboard_user')
  );

DROP POLICY IF EXISTS "Users can update their own polls" ON polls;
CREATE POLICY "polls_update_policy" ON polls
  FOR UPDATE TO public
  USING (
    (auth.role() = 'authenticated' AND user_id = (select auth.uid()))
    OR
    (auth.role() = 'dashboard_user')
  );

-- Clean up any remaining old policies
DROP POLICY IF EXISTS "Anyone can view public polls" ON polls;

-- VACUUM tables with dead tuples to reclaim space
VACUUM ANALYZE questions;
VACUUM ANALYZE polls;
VACUUM ANALYZE profiles;
VACUUM ANALYZE answers;
VACUUM ANALYZE poll_simulations;

-- Update table statistics
ANALYZE questions;
ANALYZE polls;
ANALYZE profiles;
ANALYZE answers;
ANALYZE responses;
ANALYZE poll_simulations;

-- Create a comment documenting the optimization
COMMENT ON TABLE answers IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE responses IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE questions IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE polls IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
COMMENT ON TABLE poll_simulations IS 'RLS policies optimized for performance - uses (select auth.uid()) and consolidated policies';
