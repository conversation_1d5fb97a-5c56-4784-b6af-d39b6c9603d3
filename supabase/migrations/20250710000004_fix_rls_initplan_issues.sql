-- Fix Auth RLS InitPlan Issues for Performance Optimization
-- This migration resolves the most critical performance warnings by optimizing RLS policies
-- Based on actual database schema analysis

BEGIN;

-- 1. Fix Profiles Table RLS Policies
DROP POLICY IF EXISTS "Users can delete their own profile" ON profiles;
CREATE POLICY "Users can delete their own profile" ON profiles
  FOR DELETE USING (id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT WITH CHECK (id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (id = (select auth.uid()));

-- 2. Fix Polls Table RLS Policies
DROP POLICY IF EXISTS "Users can delete their own polls" ON polls;
CREATE POLICY "Users can delete their own polls" ON polls
  FOR DELETE USING (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;
CREATE POLICY "Users can insert their own polls" ON polls
  FOR INSERT WITH CHECK (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can update their own polls" ON polls;
CREATE POLICY "Users can update their own polls" ON polls
  FOR UPDATE USING (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
CREATE POLICY "Users can view their own polls" ON polls
  FOR SELECT USING (user_id = (select auth.uid()));

-- 3. Fix Questions Table RLS Policies
DROP POLICY IF EXISTS "Questions can be modified by poll owner" ON questions;
CREATE POLICY "Questions can be modified by poll owner" ON questions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Questions can be viewed by poll owner" ON questions;
CREATE POLICY "Questions can be viewed by poll owner" ON questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can delete questions for their polls" ON questions;
CREATE POLICY "Users can delete questions for their polls" ON questions
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can insert questions for their polls" ON questions;
CREATE POLICY "Users can insert questions for their polls" ON questions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can update questions for their polls" ON questions;
CREATE POLICY "Users can update questions for their polls" ON questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can view questions for their polls" ON questions;
CREATE POLICY "Users can view questions for their polls" ON questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

-- 4. Fix Responses Table RLS Policies
DROP POLICY IF EXISTS "Responses can be viewed by poll owner" ON responses;
CREATE POLICY "Responses can be viewed by poll owner" ON responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = responses.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can view responses for their polls" ON responses;
CREATE POLICY "Users can view responses for their polls" ON responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = responses.poll_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can view their own responses" ON responses;
CREATE POLICY "Users can view their own responses" ON responses
  FOR SELECT USING (user_id = (select auth.uid()));

-- 5. Fix Answers Table RLS Policies
DROP POLICY IF EXISTS "Answers can be viewed by poll owner" ON answers;
CREATE POLICY "Answers can be viewed by poll owner" ON answers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      JOIN responses ON polls.id = responses.poll_id
      WHERE responses.id = answers.response_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can view answers for their polls" ON answers;
CREATE POLICY "Users can view answers for their polls" ON answers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM responses
      JOIN polls ON polls.id = responses.poll_id
      WHERE responses.id = answers.response_id
      AND polls.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can view their own answers" ON answers;
CREATE POLICY "Users can view their own answers" ON answers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM responses
      WHERE responses.id = answers.response_id
      AND responses.user_id = (select auth.uid())
    )
  );

-- 6. Fix Survey Distributions Table RLS Policies
DROP POLICY IF EXISTS "Users can insert their own surveys" ON survey_distributions;
CREATE POLICY "Users can insert their own surveys" ON survey_distributions
  FOR INSERT WITH CHECK (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can update their own surveys" ON survey_distributions;
CREATE POLICY "Users can update their own surveys" ON survey_distributions
  FOR UPDATE USING (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can view their own surveys" ON survey_distributions;
CREATE POLICY "Users can view their own surveys" ON survey_distributions
  FOR SELECT USING (user_id = (select auth.uid()));

-- 7. Fix MTurk HITs Table RLS Policies (linked through survey_distributions)
DROP POLICY IF EXISTS "Users can access their own HITs" ON mturk_hits;
CREATE POLICY "Users can access their own HITs" ON mturk_hits
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM survey_distributions
      WHERE survey_distributions.id = mturk_hits.survey_distribution_id
      AND survey_distributions.user_id = (select auth.uid())
    )
  );

-- 8. Fix Worker Assignments Table RLS Policies (linked through mturk_hits -> survey_distributions)
DROP POLICY IF EXISTS "Users can access their own worker assignments" ON worker_assignments;
CREATE POLICY "Users can access their own worker assignments" ON worker_assignments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM mturk_hits
      JOIN survey_distributions ON mturk_hits.survey_distribution_id = survey_distributions.id
      WHERE mturk_hits.id = worker_assignments.hit_id
      AND survey_distributions.user_id = (select auth.uid())
    )
  );

-- 9. Fix other user-related tables
DROP POLICY IF EXISTS "Users can access their own billing" ON user_billing;
CREATE POLICY "Users can access their own billing" ON user_billing
  FOR ALL USING (user_id = (select auth.uid()));

DROP POLICY IF EXISTS "Users can access their own completion codes" ON completion_codes;
CREATE POLICY "Users can access their own completion codes" ON completion_codes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM survey_distributions
      WHERE survey_distributions.id = completion_codes.survey_distribution_id
      AND survey_distributions.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can access their own assignment actions" ON mturk_assignment_actions;
CREATE POLICY "Users can access their own assignment actions" ON mturk_assignment_actions
  FOR ALL USING (performed_by = (select auth.uid()));

DROP POLICY IF EXISTS "Users can access their own targeting criteria" ON targeting_criteria;
CREATE POLICY "Users can access their own targeting criteria" ON targeting_criteria
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM survey_distributions
      WHERE survey_distributions.id = targeting_criteria.survey_distribution_id
      AND survey_distributions.user_id = (select auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can access their own quality metrics" ON quality_metrics;
CREATE POLICY "Users can access their own quality metrics" ON quality_metrics
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM worker_assignments
      JOIN mturk_hits ON worker_assignments.hit_id = mturk_hits.id
      JOIN survey_distributions ON mturk_hits.survey_distribution_id = survey_distributions.id
      WHERE quality_metrics.worker_assignment_id = worker_assignments.id
      AND survey_distributions.user_id = (select auth.uid())
    )
  );

-- 10. Fix Poll Simulations Table RLS Policies
DROP POLICY IF EXISTS "Users can create simulations for own polls" ON poll_simulations;
CREATE POLICY "Users can create simulations for own polls" ON poll_simulations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = poll_simulations.poll_id
      AND polls.user_id = (select auth.uid())
    )
    AND created_by = (select auth.uid())
  );

DROP POLICY IF EXISTS "Users can create their own simulations" ON poll_simulations;
CREATE POLICY "Users can create their own simulations" ON poll_simulations
  FOR INSERT WITH CHECK (created_by = (select auth.uid()));

DROP POLICY IF EXISTS "Users can delete own simulations" ON poll_simulations;
CREATE POLICY "Users can delete own simulations" ON poll_simulations
  FOR DELETE USING (created_by = (select auth.uid()));

DROP POLICY IF EXISTS "Users can update own simulations" ON poll_simulations;
CREATE POLICY "Users can update own simulations" ON poll_simulations
  FOR UPDATE USING (created_by = (select auth.uid()));

DROP POLICY IF EXISTS "Users can view simulations for accessible polls" ON poll_simulations;
CREATE POLICY "Users can view simulations for accessible polls" ON poll_simulations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = poll_simulations.poll_id
      AND (polls.user_id = (select auth.uid()) OR polls.is_public = true)
    )
  );

DROP POLICY IF EXISTS "Users can view their own simulations" ON poll_simulations;
CREATE POLICY "Users can view their own simulations" ON poll_simulations
  FOR SELECT USING (created_by = (select auth.uid()));

COMMIT;

-- Update table statistics after policy changes (outside transaction)
ANALYZE profiles;
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;
ANALYZE answers;
ANALYZE survey_distributions;
ANALYZE mturk_hits;
ANALYZE worker_assignments;
ANALYZE poll_simulations;
ANALYZE user_billing;
ANALYZE completion_codes;
ANALYZE mturk_assignment_actions;
ANALYZE targeting_criteria;
ANALYZE quality_metrics;
