-- Fix RLS policies for polls table to ensure users only see their own polls
-- First, check if <PERSON><PERSON> is enabled on the polls table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_tables 
        WHERE tablename = 'polls' 
        AND rowsecurity = true
    ) THEN
        RAISE NOTICE 'Enabling Row Level Security on polls table';
        ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Drop existing policies on polls table to recreate them properly
DROP POLICY IF EXISTS "Users can only view their own polls" ON polls;
DROP POLICY IF EXISTS "Users can only insert their own polls" ON polls;
DROP POLICY IF EXISTS "Users can only update their own polls" ON polls;
DROP POLICY IF EXISTS "Users can only delete their own polls" ON polls;
DROP POLICY IF EXISTS "Public polls can be viewed by anyone" ON polls;

-- Create proper RLS policies for polls table
CREATE POLICY "Users can only view their own polls" 
ON polls FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Public polls can be viewed by anyone" 
ON polls FOR SELECT 
USING (is_public = true);

CREATE POLICY "Users can only insert their own polls" 
ON polls FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can only update their own polls" 
ON polls FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can only delete their own polls" 
ON polls FOR DELETE 
USING (auth.uid() = user_id);

-- Check and fix the get_polls_with_counts function
CREATE OR REPLACE FUNCTION get_polls_with_counts(
    user_id_param UUID,
    page_number INTEGER DEFAULT 1,
    page_size INTEGER DEFAULT 10,
    fetch_all BOOLEAN DEFAULT false,
    search_query_param TEXT DEFAULT NULL,
    status_filter_param TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    title TEXT,
    description TEXT,
    user_id UUID,
    status TEXT,
    is_public BOOLEAN,
    slug TEXT,
    response_count BIGINT,
    view_count INTEGER,
    questions JSONB,
    total_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    offset_val INTEGER;
    total_count_val BIGINT;
BEGIN
    -- Calculate offset
    offset_val := (page_number - 1) * page_size;
    
    -- Get total count with the same filters for pagination
    SELECT COUNT(*)::BIGINT INTO total_count_val 
    FROM polls p
    WHERE p.user_id = user_id_param
    AND (search_query_param IS NULL OR p.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR p.status = status_filter_param);
    
    -- Return the results
    RETURN QUERY
    WITH response_counts AS (
        SELECT 
            poll_id, 
            COUNT(*)::BIGINT AS count
        FROM responses
        GROUP BY poll_id
    ),
    poll_questions AS (
        SELECT 
            poll_id,
            jsonb_agg(
                jsonb_build_object(
                    'id', id,
                    'question_text', question_text,
                    'question_type', question_type,
                    'options', options,
                    'required', required,
                    'order', "order"
                ) ORDER BY "order"
            ) AS questions
        FROM questions
        GROUP BY poll_id
    )
    SELECT 
        p.id,
        p.created_at,
        p.updated_at,
        p.title,
        p.description,
        p.user_id,
        p.status,
        p.is_public,
        p.slug,
        COALESCE(rc.count, 0::BIGINT) AS response_count,
        COALESCE(p.views, 0) AS view_count,
        COALESCE(pq.questions, '[]'::JSONB) AS questions,
        total_count_val
    FROM polls p
    LEFT JOIN response_counts rc ON p.id = rc.poll_id
    LEFT JOIN poll_questions pq ON p.id = pq.poll_id
    WHERE p.user_id = user_id_param
    AND (search_query_param IS NULL OR p.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR p.status = status_filter_param)
    ORDER BY p.updated_at DESC
    LIMIT CASE WHEN fetch_all THEN NULL ELSE page_size END
    OFFSET CASE WHEN fetch_all THEN 0 ELSE offset_val END;
END;
$$;

-- Add comment to explain the function
COMMENT ON FUNCTION get_polls_with_counts IS 'Returns polls with response counts, view counts, and questions for a specific user with pagination and filtering options';
