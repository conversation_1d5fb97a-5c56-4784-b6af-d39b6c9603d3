-- Migration: Transition from is_published to status column
-- This migration removes is_published and updates everything to use status only

BEGIN;

-- 1. First, ensure data consistency between status and is_published
UPDATE polls
SET status = CASE
  WHEN is_published = true THEN 'active'
  ELSE 'draft'
END,
updated_at = NOW()
WHERE status != CASE
  WHEN is_published = true THEN 'active'
  ELSE 'draft'
END;

-- 2. Update all RLS policies that depend on is_published column to use status instead

-- Update policy: Questions for public polls are viewable by everyone
DROP POLICY IF EXISTS "Questions for public polls are viewable by everyone" ON questions;
CREATE POLICY "Questions for public polls are viewable by everyone" ON questions
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = questions.poll_id
    AND polls.status = 'active'
  )
);

-- Update policy: Users can insert responses to any poll
DROP POLICY IF EXISTS "Users can insert responses to any poll" ON responses;
CREATE POLICY "Users can insert responses to any poll" ON responses
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM polls
    WHERE polls.id = responses.poll_id
    AND polls.status = 'active'
  )
);

-- Update policy: Users can insert answers to any poll
DROP POLICY IF EXISTS "Users can insert answers to any poll" ON answers;
CREATE POLICY "Users can insert answers to any poll" ON answers
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM responses r
    JOIN polls p ON r.poll_id = p.id
    WHERE r.id = answers.response_id
    AND p.status = 'active'
  )
);

-- Update policy: Public polls are viewable by everyone
DROP POLICY IF EXISTS "Public polls are viewable by everyone" ON polls;
CREATE POLICY "Public polls are viewable by everyone" ON polls
FOR SELECT USING (status = 'active');

-- 3. Drop the problematic constraint
ALTER TABLE polls DROP CONSTRAINT IF EXISTS check_status_published_consistency;

-- 4. Drop any indexes that reference is_published
DROP INDEX IF EXISTS idx_polls_status_published;

-- 5. Drop the is_published column entirely
ALTER TABLE polls DROP COLUMN IF EXISTS is_published;

-- 6. Create a simpler constraint that only validates the status column
ALTER TABLE polls ADD CONSTRAINT check_valid_status
CHECK (status IN ('draft', 'active', 'completed'));

-- 7. Ensure the status column has the correct default and not null constraint
ALTER TABLE polls ALTER COLUMN status SET DEFAULT 'draft';
ALTER TABLE polls ALTER COLUMN status SET NOT NULL;

COMMIT;
