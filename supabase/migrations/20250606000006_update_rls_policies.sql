-- Migration: Update RLS policies to use status column instead of is_published
-- This migration updates all RLS policies to reference the status column

BEGIN;

-- Drop existing policies that reference is_published
DROP POLICY IF EXISTS "Public polls are viewable by everyone" ON polls;
DROP POLICY IF EXISTS "Public polls can be read by anyone" ON polls;

-- Recreate the public access policy using status column
CREATE POLICY "Public polls are viewable by everyone"
  ON polls
  FOR SELECT
  USING (is_public = true AND status = 'active');

-- Update questions table policies to use status instead of is_published
DROP POLICY IF EXISTS "Questions in published polls are readable by everyone" ON questions;
CREATE POLICY "Questions in published polls are readable by everyone"
  ON questions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = questions.poll_id
      AND polls.is_public = true
      AND polls.status = 'active'
    )
  );

-- Update responses table policies
DROP POLICY IF EXISTS "Responses for public polls can be inserted by anyone" ON responses;
CREATE POLICY "Responses for public polls can be inserted by anyone"
  ON responses
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM polls
      WHERE polls.id = responses.poll_id
      AND polls.is_public = true
      AND polls.status = 'active'
    )
  );

-- Update answers table policies
DROP POLICY IF EXISTS "Answers for public polls can be inserted by anyone" ON answers;
CREATE POLICY "Answers for public polls can be inserted by anyone"
  ON answers
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM responses r
      JOIN polls p ON p.id = r.poll_id
      WHERE r.id = answers.response_id
      AND p.is_public = true
      AND p.status = 'active'
    )
  );

COMMIT;
