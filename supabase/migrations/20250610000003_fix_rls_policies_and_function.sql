-- Fix RLS policies and get_polls_with_counts function
-- This migration addresses the issue of users seeing polls they didn't create

-- 1. First, drop conflicting policies
DROP POLICY IF EXISTS "Public polls are viewable by everyone" ON polls;
DROP POLICY IF EXISTS "Authenticated users can create polls for themselves." ON polls;

-- 2. Ensure RLS is enabled on the polls table
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;

-- 3. Recreate the policies with clear naming and proper conditions
-- Policy for users to view their own polls
DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
CREATE POLICY "Users can view their own polls" ON polls
    FOR SELECT
    TO public
    USING (auth.uid() = user_id);

-- Policy for public polls (using is_public flag)
DROP POLICY IF EXISTS "Anyone can view public polls" ON polls;
CREATE POLICY "Anyone can view public polls" ON polls
    FOR SELECT
    TO public
    USING (is_public = true);

-- Policy for users to insert their own polls
DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;
CREATE POLICY "Users can insert their own polls" ON polls
    FOR INSERT
    TO public
    WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own polls
DROP POLICY IF EXISTS "Users can update their own polls" ON polls;
CREATE POLICY "Users can update their own polls" ON polls
    FOR UPDATE
    TO public
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Policy for users to delete their own polls
DROP POLICY IF EXISTS "Users can delete their own polls" ON polls;
CREATE POLICY "Users can delete their own polls" ON polls
    FOR DELETE
    TO public
    USING (auth.uid() = user_id);

-- 4. Fix the get_polls_with_counts function
-- Drop both versions of the function to avoid conflicts
DROP FUNCTION IF EXISTS get_polls_with_counts(uuid);
DROP FUNCTION IF EXISTS get_polls_with_counts(uuid, integer, integer, boolean);
DROP FUNCTION IF EXISTS get_polls_with_counts(uuid, integer, integer, boolean, text, text);

-- Create the function with proper filtering by user_id
CREATE OR REPLACE FUNCTION get_polls_with_counts(
  user_id_param UUID,
  page_number INTEGER DEFAULT 1,
  page_size INTEGER DEFAULT 10,
  fetch_all BOOLEAN DEFAULT false,
  search_query_param TEXT DEFAULT '',
  status_filter_param TEXT DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  status TEXT,
  is_public BOOLEAN,
  slug TEXT,
  response_count BIGINT,
  view_count BIGINT,
  questions JSONB,
  total_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_polls BIGINT;
  offset_val INT;
BEGIN
  -- Calculate the offset based on page number and size
  offset_val := (page_number - 1) * page_size;

  -- Get total count first for pagination info, considering filters
  SELECT COUNT(*) INTO total_polls 
  FROM polls 
  WHERE polls.user_id = user_id_param
    AND (search_query_param = '' OR polls.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR polls.status = status_filter_param);

  -- Return query with pagination and filtering
  RETURN QUERY
  WITH poll_responses AS (
    -- CTE to count responses for each poll
    SELECT r.poll_id, COUNT(r.id)::BIGINT AS num_responses
    FROM responses r
    JOIN polls p ON p.id = r.poll_id AND p.user_id = user_id_param
    GROUP BY r.poll_id
  ), poll_questions_agg AS (
    -- CTE to aggregate questions for each poll
    SELECT q.poll_id, jsonb_agg(jsonb_build_object(
      'id', q.id, 
      'poll_id', q.poll_id,
      'question_text', q.question_text, 
      'question_type', q.question_type, 
      'options', q.options, 
      'required', q.required, 
      'order', q.order
    ) ORDER BY q.order) AS aggregated_questions
    FROM questions q
    JOIN polls p ON p.id = q.poll_id AND p.user_id = user_id_param
    GROUP BY q.poll_id
  )
  SELECT
    p.id, p.title, p.description, p.created_at, p.updated_at, p.user_id, p.status, p.is_public, p.slug,
    COALESCE(pr.num_responses, 0) AS response_count,
    COALESCE(p.views, 0)::BIGINT AS view_count,
    COALESCE(pqa.aggregated_questions, '[]'::jsonb) AS questions,
    total_polls AS total_count
  FROM polls p
  LEFT JOIN poll_responses pr ON p.id = pr.poll_id
  LEFT JOIN poll_questions_agg pqa ON p.id = pqa.poll_id
  WHERE p.user_id = user_id_param
    AND (search_query_param = '' OR p.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR p.status = status_filter_param)
  ORDER BY p.updated_at DESC
  LIMIT CASE WHEN fetch_all THEN NULL ELSE page_size END
  OFFSET CASE WHEN fetch_all THEN 0 ELSE offset_val END;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_polls_with_counts(UUID, INTEGER, INTEGER, BOOLEAN, TEXT, TEXT) TO public;
