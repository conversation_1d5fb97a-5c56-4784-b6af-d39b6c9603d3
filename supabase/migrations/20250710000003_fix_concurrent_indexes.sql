-- Database Performance Optimization for PollGPT (Non-Concurrent Operations Only)
-- This migration contains only non-concurrent operations that can run in a transaction
-- NOTE: Run 20250710000003_concurrent_indexes_only.sql FIRST before this migration

-- Run the non-concurrent operations in a transaction
BEGIN;

-- Optimize database settings for better performance
-- Enable parallel query execution
SET max_parallel_workers_per_gather = 4;
SET max_parallel_workers = 8;

-- Optimize work memory for better sorting/hashing
SET work_mem = '64MB';

-- Optimize shared buffers for better caching
SET shared_buffers = '256MB';

-- Optimize statistics collection
ALTER TABLE polls SET (n_distinct = 100);
ALTER TABLE questions SET (n_distinct = 1000);
ALTER TABLE responses SET (n_distinct = 10000);

-- Add constraints for better query optimization
ALTER TABLE polls ADD CONSTRAINT check_title_length CHECK (length(title) >= 1 AND length(title) <= 500);
ALTER TABLE questions ADD CONSTRAINT check_question_text_length CHECK (length(question_text) >= 1 AND length(question_text) <= 1000);

-- <PERSON>reate optimized materialized view for dashboard statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS poll_stats AS
SELECT
  p.user_id,
  COUNT(*) as total_polls,
  COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_polls,
  COUNT(CASE WHEN p.status = 'draft' THEN 1 END) as draft_polls,
  COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_polls,
  COALESCE(SUM(r.response_count), 0) as total_responses,
  COALESCE(SUM(p.views), 0) as total_views,
  MAX(p.updated_at) as last_poll_update
FROM polls p
LEFT JOIN (
  SELECT poll_id, COUNT(*) as response_count
  FROM responses
  GROUP BY poll_id
) r ON p.id = r.poll_id
GROUP BY p.user_id;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_poll_stats_user_id ON poll_stats(user_id);

-- Add function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_poll_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY poll_stats;
END;
$$ LANGUAGE plpgsql;

-- Create scheduled job to refresh stats every 5 minutes
SELECT cron.schedule(
  'refresh-poll-stats',
  '*/5 * * * *',
  'SELECT refresh_poll_stats();'
);

COMMIT;

-- Analyze tables to update statistics (outside transaction)
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;
ANALYZE answers;

-- Vacuum tables to reclaim space and update statistics (outside transaction)
VACUUM ANALYZE polls;
VACUUM ANALYZE questions;
VACUUM ANALYZE responses;
VACUUM ANALYZE answers;
