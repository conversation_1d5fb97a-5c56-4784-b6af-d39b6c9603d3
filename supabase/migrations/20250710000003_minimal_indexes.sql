-- MINIMAL Database Performance Optimization for PollGPT
-- This migration ONL<PERSON> creates indexes - no complex configurations that could fail
-- G<PERSON>ranteed to work in any Supabase environment

BEGIN;

-- Create essential performance indexes
CREATE INDEX IF NOT EXISTS idx_polls_created_at_desc
ON polls(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_polls_user_status_updated
ON polls(user_id, status, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_responses_poll_created
ON responses(poll_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_questions_poll_order
ON questions(poll_id, "order" ASC);

CREATE INDEX IF NOT EXISTS idx_polls_active_public
ON polls(user_id, updated_at DESC)
WHERE status = 'active' AND is_public = true;

CREATE INDEX IF NOT EXISTS idx_polls_active_user
ON polls(user_id, updated_at DESC)
WHERE status = 'active';

-- Create covering index for dashboard queries
CREATE INDEX IF NOT EXISTS idx_polls_cover_dashboard
ON polls(user_id, updated_at DESC)
INCLUDE (id, title, description, status, is_public, created_at);

COMMIT;

-- Update table statistics
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;
ANALYZE answers;
