-- Clean Up Duplicate Indexes for Performance Optimization (Simple Version)
-- This migration removes actual duplicate indexes found in the database

BEGIN;

-- Remove duplicate index on questions table
-- We have both idx_questions_poll_order and idx_questions_poll_id_order covering (poll_id, order)
-- Keep the more descriptive one: idx_questions_poll_id_order
DROP INDEX IF EXISTS idx_questions_poll_order;

-- Remove any indexes mentioned in the performance warnings (if they exist)
DROP INDEX IF EXISTS poll_simulations_poll_id_idx;
DROP INDEX IF EXISTS polls_user_id_idx;
DROP INDEX IF EXISTS questions_poll_id_idx;
DROP INDEX IF EXISTS responses_poll_id_idx;

COMMIT;

-- Analyze tables after index cleanup
ANALYZE polls;
ANALY<PERSON><PERSON> questions;
ANALYZE responses;
ANALYZE poll_simulations;
