-- Database Performance Validation Script
-- This script validates all performance optimizations and provides diagnostics

BEGIN;

-- 1. Create comprehensive performance diagnostic functions
CREATE OR REPLACE FUNCTION performance_diagnostics()
RETURNS TABLE(
  category text,
  metric text,
  value text,
  status text,
  recommendation text
) AS $$
BEGIN
  -- Check RLS policy performance
  RETURN QUERY
  SELECT
    'RLS Performance'::text as category,
    'Auth InitPlan Issues'::text as metric,
    COUNT(*)::text as value,
    CASE
      WHEN COUNT(*) = 0 THEN 'GOOD'
      WHEN COUNT(*) < 5 THEN 'WARNING'
      ELSE 'CRITICAL'
    END as status,
    CASE
      WHEN COUNT(*) = 0 THEN 'No RLS initplan issues detected'
      ELSE 'Replace auth.<function>() with (select auth.<function>()) in ' || COUNT(*)::text || ' policies'
    END as recommendation
  FROM pg_policies p
  WHERE p.schemaname = 'public'
  AND (p.qual LIKE '%auth.%' OR p.with_check LIKE '%auth.%')
  AND (p.qual NOT LIKE '%(select auth.%' AND p.with_check NOT LIKE '%(select auth.%');

  -- Check duplicate indexes
  RETURN QUERY
  WITH duplicate_indexes AS (
    SELECT
      t.relname as table_name,
      COUNT(*) as duplicate_count
    FROM pg_class t
    JOIN pg_index ix ON t.oid = ix.indrelid
    JOIN pg_class i ON i.oid = ix.indexrelid
    WHERE t.relkind = 'r'
    AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    AND NOT ix.indisprimary
    GROUP BY t.relname, ix.indkey
    HAVING COUNT(*) > 1
  )
  SELECT
    'Index Optimization'::text as category,
    'Duplicate Indexes'::text as metric,
    COUNT(*)::text as value,
    CASE
      WHEN COUNT(*) = 0 THEN 'GOOD'
      WHEN COUNT(*) < 3 THEN 'WARNING'
      ELSE 'CRITICAL'
    END as status,
    CASE
      WHEN COUNT(*) = 0 THEN 'No duplicate indexes found'
      ELSE 'Remove ' || COUNT(*)::text || ' duplicate indexes'
    END as recommendation
  FROM duplicate_indexes;

  -- Check table statistics freshness
  RETURN QUERY
  SELECT
    'Statistics'::text as category,
    'Stale Statistics'::text as metric,
    COUNT(*)::text as value,
    CASE
      WHEN COUNT(*) = 0 THEN 'GOOD'
      WHEN COUNT(*) < 3 THEN 'WARNING'
      ELSE 'CRITICAL'
    END as status,
    CASE
      WHEN COUNT(*) = 0 THEN 'All table statistics are fresh'
      ELSE 'Run ANALYZE on ' || COUNT(*)::text || ' tables'
    END as recommendation
  FROM pg_stat_user_tables
  WHERE schemaname = 'public'
  AND (last_analyze IS NULL OR last_analyze < NOW() - INTERVAL '7 days');

  -- Check connection and query performance settings
  RETURN QUERY
  SELECT
    'Configuration'::text as category,
    'Work Memory'::text as metric,
    current_setting('work_mem')::text as value,
    CASE
      WHEN current_setting('work_mem')::text = '4MB' THEN 'WARNING'
      WHEN current_setting('work_mem')::text IN ('64MB', '128MB', '256MB') THEN 'GOOD'
      ELSE 'REVIEW'
    END as status,
    'Optimize work_mem for better sort/hash performance'::text as recommendation;

  RETURN QUERY
  SELECT
    'Configuration'::text as category,
    'Parallel Workers'::text as metric,
    current_setting('max_parallel_workers_per_gather')::text as value,
    CASE
      WHEN current_setting('max_parallel_workers_per_gather')::int >= 2 THEN 'GOOD'
      ELSE 'WARNING'
    END as status,
    'Enable parallel query execution for better performance'::text as recommendation;

END;
$$ LANGUAGE plpgsql;

-- 2. Create function to check query performance
CREATE OR REPLACE FUNCTION query_performance_check()
RETURNS TABLE(
  query_type text,
  avg_duration_ms numeric,
  call_count bigint,
  status text,
  recommendation text
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    'SELECT polls'::text as query_type,
    0::numeric as avg_duration_ms,
    0::bigint as call_count,
    'INFO'::text as status,
    'Monitor query performance after optimization'::text as recommendation;
END;
$$ LANGUAGE plpgsql;

-- 3. Create function to validate security policies
CREATE OR REPLACE FUNCTION validate_security_policies()
RETURNS TABLE(
  table_name text,
  policy_count bigint,
  rls_enabled boolean,
  status text,
  recommendation text
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.relname::text as table_name,
    COUNT(p.policyname)::bigint as policy_count,
    c.relrowsecurity as rls_enabled,
    CASE
      WHEN c.relrowsecurity AND COUNT(p.policyname) > 0 THEN 'GOOD'
      WHEN c.relrowsecurity AND COUNT(p.policyname) = 0 THEN 'WARNING'
      WHEN NOT c.relrowsecurity THEN 'CRITICAL'
      ELSE 'UNKNOWN'
    END as status,
    CASE
      WHEN c.relrowsecurity AND COUNT(p.policyname) > 0 THEN 'Security policies are properly configured'
      WHEN c.relrowsecurity AND COUNT(p.policyname) = 0 THEN 'RLS enabled but no policies defined'
      WHEN NOT c.relrowsecurity THEN 'RLS not enabled - potential security risk'
      ELSE 'Review security configuration'
    END as recommendation
  FROM pg_class c
  LEFT JOIN pg_policies p ON c.relname = p.tablename AND p.schemaname = 'public'
  WHERE c.relkind = 'r'
  AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  AND c.relname IN ('profiles', 'polls', 'questions', 'responses', 'answers')
  GROUP BY c.relname, c.relrowsecurity
  ORDER BY c.relname;
END;
$$ LANGUAGE plpgsql;

-- 4. Create comprehensive health check function
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
  check_category text,
  check_name text,
  result text,
  severity text,
  details text
) AS $$
BEGIN
  -- Database size check
  RETURN QUERY
  SELECT
    'Storage'::text as check_category,
    'Database Size'::text as check_name,
    pg_size_pretty(pg_database_size(current_database()))::text as result,
    'INFO'::text as severity,
    'Current database size'::text as details;

  -- Connection count check
  RETURN QUERY
  SELECT
    'Connections'::text as check_category,
    'Active Connections'::text as check_name,
    COUNT(*)::text as result,
    CASE
      WHEN COUNT(*) > 80 THEN 'CRITICAL'
      WHEN COUNT(*) > 50 THEN 'WARNING'
      ELSE 'GOOD'
    END as severity,
    'Number of active database connections'::text as details
  FROM pg_stat_activity
  WHERE state = 'active';

  -- Long running queries check
  RETURN QUERY
  SELECT
    'Performance'::text as check_category,
    'Long Running Queries'::text as check_name,
    COUNT(*)::text as result,
    CASE
      WHEN COUNT(*) > 5 THEN 'CRITICAL'
      WHEN COUNT(*) > 2 THEN 'WARNING'
      ELSE 'GOOD'
    END as severity,
    'Queries running longer than 5 minutes'::text as details
  FROM pg_stat_activity
  WHERE state = 'active'
  AND query_start < NOW() - INTERVAL '5 minutes';

  -- Bloated tables check
  RETURN QUERY
  SELECT
    'Maintenance'::text as check_category,
    'Table Bloat'::text as check_name,
    COUNT(*)::text as result,
    CASE
      WHEN COUNT(*) > 3 THEN 'WARNING'
      WHEN COUNT(*) > 0 THEN 'INFO'
      ELSE 'GOOD'
    END as severity,
    'Tables that may need VACUUM'::text as details
  FROM pg_stat_user_tables
  WHERE schemaname = 'public'
  AND (n_dead_tup > n_live_tup * 0.1 OR n_dead_tup > 1000);

END;
$$ LANGUAGE plpgsql;

COMMIT;

-- Run comprehensive diagnostics
SELECT '=== PERFORMANCE DIAGNOSTICS ===' as info;
SELECT * FROM performance_diagnostics();

SELECT '=== SECURITY POLICY VALIDATION ===' as info;
SELECT * FROM validate_security_policies();

SELECT '=== DATABASE HEALTH CHECK ===' as info;
SELECT * FROM database_health_check();

-- Cleanup diagnostic functions (optional)
-- DROP FUNCTION IF EXISTS performance_diagnostics();
-- DROP FUNCTION IF EXISTS query_performance_check();
-- DROP FUNCTION IF EXISTS validate_security_policies();
-- DROP FUNCTION IF EXISTS database_health_check();
