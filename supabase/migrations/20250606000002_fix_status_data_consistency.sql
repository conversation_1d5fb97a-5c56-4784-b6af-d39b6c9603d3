-- Fix status data consistency in polls table
-- This migration aligns the status column with the is_published boolean

-- Update all polls where is_published is true but status is 'draft' to have status 'active'
UPDATE polls
SET status = 'active', updated_at = NOW()
WHERE is_published = true AND status = 'draft';

-- Update all polls where is_published is false but status is not 'draft' to have status 'draft'
UPDATE polls
SET status = 'draft', updated_at = NOW()
WHERE (is_published = false OR is_published IS NULL) AND status != 'draft';

-- Add a check constraint to ensure data consistency going forward
-- This prevents future inconsistencies between status and is_published
ALTER TABLE polls ADD CONSTRAINT check_status_published_consistency
CHECK (
  (status = 'active' AND is_published = true) OR
  (status = 'draft' AND (is_published = false OR is_published IS NULL)) OR
  (status = 'completed')
);

-- Add an index on status column for better query performance
CREATE INDEX IF NOT EXISTS idx_polls_status ON polls(status);

-- Add a compound index on status and is_published for complex queries
CREATE INDEX IF NOT EXISTS idx_polls_status_published ON polls(status, is_published);

-- Update the updated_at timestamp for tracking when this migration was applied
UPDATE polls SET updated_at = NOW() WHERE updated_at IS NULL;
