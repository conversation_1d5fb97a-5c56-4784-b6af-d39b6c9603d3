-- Final Performance Optimization Migration
-- This addresses any remaining edge cases and ensures optimal RLS policy structure

-- Drop any remaining old consolidated policies that might conflict
DROP POLICY IF EXISTS "consolidated_answers_insert" ON answers;
DROP POLICY IF EXISTS "consolidated_answers_select" ON answers;

-- Ensure we have proper indexes for RLS performance
-- These indexes support the most common RLS policy lookups

-- Index for responses.user_id lookups (used in answers policies)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_user_id_optimized
ON responses (user_id) WHERE user_id IS NOT NULL;

-- Index for poll ownership lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_user_id_status
ON polls (user_id, status) WHERE user_id IS NOT NULL;

-- Index for public poll lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_public_status
ON polls (is_public, status) WHERE is_public = true;

-- Index for poll_simulations created_by lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_poll_simulations_created_by
ON poll_simulations (created_by) WHERE created_by IS NOT NULL;

-- Index for answers.response_id lookups (most critical for performance)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_answers_response_id_optimized
ON answers (response_id);

-- Index for responses.poll_id lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_poll_id_optimized
ON responses (poll_id);

-- Index for questions.poll_id lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_poll_id_optimized
ON questions (poll_id);

-- Composite index for poll simulations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_poll_simulations_poll_created
ON poll_simulations (poll_id, created_by);

-- Add a function to help with RLS policy performance
CREATE OR REPLACE FUNCTION auth_user_id() RETURNS uuid AS $$
BEGIN
  RETURN (SELECT auth.uid());
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Grant usage to public
GRANT EXECUTE ON FUNCTION auth_user_id() TO public;

-- Create a final verification query to ensure all policies are optimized
-- This will be used to verify the migration worked
CREATE OR REPLACE FUNCTION verify_rls_performance() RETURNS TABLE (
  table_name text,
  policy_count bigint,
  uses_auth_uid_directly boolean,
  has_duplicate_policies boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.tablename::text,
    COUNT(*)::bigint as policy_count,
    bool_or(p.qual LIKE '%auth.uid()%' OR p.with_check LIKE '%auth.uid()%') as uses_auth_uid_directly,
    (COUNT(*) >
      (SELECT COUNT(DISTINCT (cmd, COALESCE(p2.qual, 'NULL'), COALESCE(p2.with_check, 'NULL')))
       FROM pg_policies p2
       WHERE p2.tablename = p.tablename AND p2.schemaname = 'public')
    ) as has_duplicate_policies
  FROM pg_policies p
  WHERE p.schemaname = 'public'
    AND p.tablename IN ('answers', 'responses', 'questions', 'polls', 'poll_simulations')
  GROUP BY p.tablename
  ORDER BY p.tablename;
END;
$$ LANGUAGE plpgsql;

-- Create a maintenance function to run VACUUM when needed
CREATE OR REPLACE FUNCTION maintenance_vacuum_if_needed() RETURNS void AS $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec IN
    SELECT relname
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
      AND n_dead_tup > 100
  LOOP
    EXECUTE 'VACUUM ANALYZE ' || quote_ident(rec.relname);
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run final maintenance
SELECT maintenance_vacuum_if_needed();

-- Add helpful comments
COMMENT ON FUNCTION auth_user_id() IS 'Optimized function for RLS policies - returns current user ID';
COMMENT ON FUNCTION verify_rls_performance() IS 'Function to verify RLS policy optimization status';
COMMENT ON FUNCTION maintenance_vacuum_if_needed() IS 'Maintenance function to VACUUM tables with dead tuples';
