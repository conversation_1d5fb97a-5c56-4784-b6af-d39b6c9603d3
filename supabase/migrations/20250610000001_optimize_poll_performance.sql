-- Performance optimization for PollGPT database
-- This migration adds indexes to improve query performance for the polls table

-- Add index for title search using trigram for faster ILIKE queries
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX IF NOT EXISTS idx_polls_title_trgm ON polls USING gin(title gin_trgm_ops);

-- Add index for user_id + status combination which is frequently queried
CREATE INDEX IF NOT EXISTS idx_polls_user_id_status ON polls(user_id, status);

-- Add index for updated_at to improve sorting performance
CREATE INDEX IF NOT EXISTS idx_polls_updated_at ON polls(updated_at DESC);

-- Add index for poll_id on responses table to speed up response counting
CREATE INDEX IF NOT EXISTS idx_responses_poll_id ON responses(poll_id);

-- Add index for poll_id and order on questions table to speed up question retrieval
CREATE INDEX IF NOT EXISTS idx_questions_poll_id_order ON questions(poll_id, "order");

-- Add comment to explain the purpose of these indexes
COMMENT ON INDEX idx_polls_title_trgm IS 'Improves performance of title search using ILIKE in get_polls_with_counts';
COMMENT ON INDEX idx_polls_user_id_status IS 'Improves filtering by user_id and status in get_polls_with_counts';
COMMENT ON INDEX idx_polls_updated_at IS 'Improves sorting by updated_at in get_polls_with_counts';
COMMENT ON INDEX idx_responses_poll_id IS 'Improves response counting in get_polls_with_counts';
COMMENT ON INDEX idx_questions_poll_id_order IS 'Improves question aggregation in get_polls_with_counts';
