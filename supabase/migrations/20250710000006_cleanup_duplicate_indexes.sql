-- Clean Up Duplicate Indexes for Performance Optimization
-- This migration removes duplicate indexes to improve write performance and reduce storage

BEGIN;

-- 1. Remove duplicate indexes from poll_simulations table
-- Keep the more descriptive index name and drop the auto-generated one
DROP INDEX IF EXISTS poll_simulations_poll_id_idx;
-- Keep: idx_poll_simulations_poll_id

-- 2. Remove duplicate indexes from polls table
-- Keep the more descriptive index name and drop the auto-generated one
DROP INDEX IF EXISTS polls_user_id_idx;
-- Keep: idx_polls_user_id

-- 3. Remove duplicate indexes from questions table
-- Keep the more descriptive index name and drop the auto-generated one
DROP INDEX IF EXISTS questions_poll_id_idx;
-- Keep: idx_questions_poll_id

-- 4. Remove duplicate indexes from responses table
-- Keep the more descriptive index name and drop the auto-generated one
DROP INDEX IF EXISTS responses_poll_id_idx;
-- Keep: idx_responses_poll_id

-- 5. Create a function to identify any remaining duplicate indexes
CREATE OR REPLACE FUNCTION find_duplicate_indexes()
RETURNS TABLE(
  table_name text,
  index_names text[],
  columns text,
  index_count bigint
) AS $$
BEGIN
  RETURN QUERY
  WITH index_info AS (
    SELECT
      t.relname as table_name,
      i.relname as index_name,
      array_to_string(array_agg(a.attname ORDER BY a.attnum), ', ') as columns
    FROM pg_class t
    JOIN pg_index ix ON t.oid = ix.indrelid
    JOIN pg_class i ON i.oid = ix.indexrelid
    JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
    WHERE t.relkind = 'r'
    AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    AND NOT ix.indisprimary
    GROUP BY t.relname, i.relname, ix.indkey
  )
  SELECT
    ii.table_name,
    array_agg(ii.index_name) as index_names,
    ii.columns,
    COUNT(*) as index_count
  FROM index_info ii
  GROUP BY ii.table_name, ii.columns
  HAVING COUNT(*) > 1
  ORDER BY index_count DESC, ii.table_name;
END;
$$ LANGUAGE plpgsql;

-- 6. Create a function to get index usage statistics
CREATE OR REPLACE FUNCTION get_index_usage_stats()
RETURNS TABLE(
  schemaname text,
  tablename text,
  indexname text,
  num_rows bigint,
  table_size text,
  index_size text,
  unique_index boolean,
  number_of_scans bigint,
  tuples_read bigint,
  tuples_fetched bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    schemaname::text,
    tablename::text,
    indexname::text,
    pg_class.reltuples::bigint as num_rows,
    pg_size_pretty(pg_relation_size(pg_class.oid))::text as table_size,
    pg_size_pretty(pg_relation_size(indexrelid))::text as index_size,
    indisunique as unique_index,
    idx_scan as number_of_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
  FROM pg_stat_user_indexes
  JOIN pg_class ON pg_class.oid = pg_stat_user_indexes.relid
  JOIN pg_index ON pg_index.indexrelid = pg_stat_user_indexes.indexrelid
  WHERE schemaname = 'public'
  ORDER BY number_of_scans DESC, tablename, indexname;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- Analyze tables after index cleanup
ANALYZE poll_simulations;
ANALYZE polls;
ANALYZE questions;
ANALYZE responses;

-- Display any remaining duplicate indexes
SELECT * FROM find_duplicate_indexes();

-- Display index usage statistics to help identify unused indexes
SELECT * FROM get_index_usage_stats();
