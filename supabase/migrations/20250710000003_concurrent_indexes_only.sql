-- Create concurrent indexes only (no transaction block)
-- This migration ON<PERSON><PERSON> creates concurrent indexes and must be run separately

-- Create concurrent indexes outside of any transaction block
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_created_at_desc
ON polls(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_user_status_updated
ON polls(user_id, status, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_poll_created
ON responses(poll_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_poll_order
ON questions(poll_id, "order" ASC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_active_public
ON polls(user_id, updated_at DESC)
WHERE status = 'active' AND is_public = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_active_user
ON polls(user_id, updated_at DESC)
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_polls_cover_dashboard
ON polls(user_id, updated_at DESC)
INCLUDE (id, title, description, status, is_public, created_at);
