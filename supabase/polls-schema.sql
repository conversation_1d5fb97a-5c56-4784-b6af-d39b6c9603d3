-- Create polls table
CREATE TABLE IF NOT EXISTS polls (
  id UUID PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  title TEXT NOT NULL,
  description TEXT,
  user_id UUID NOT NULL,
  is_published BOOLEAN DEFAULT true,
  is_public BOOLEAN DEFAULT true,
  slug TEXT
);

-- Create questions table
CREATE TABLE IF NOT EXISTS questions (
  id UUID PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  poll_id UUID REFERENCES polls(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  question_type TEXT NOT NULL,
  options JSONB,
  required BOOLEAN DEFAULT true,
  "order" INTEGER NOT NULL
);

-- Create responses table
CREATE TABLE IF NOT EXISTS responses (
  id UUID PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  poll_id UUID REFERENCES polls(id) ON DELETE CASCADE,
  user_id UUID,
  respondent_info JSONB
);

-- Create answers table
CREATE TABLE IF NOT EXISTS answers (
  id UUID PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  response_id UUID REFERENCES responses(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  answer_value JSONB
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
CREATE INDEX IF NOT EXISTS idx_questions_poll_id ON questions(poll_id);
CREATE INDEX IF NOT EXISTS idx_responses_poll_id ON responses(poll_id);
CREATE INDEX IF NOT EXISTS idx_answers_response_id ON answers(response_id);
CREATE INDEX IF NOT EXISTS idx_answers_question_id ON answers(question_id);

-- Add RLS policies
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE answers ENABLE ROW LEVEL SECURITY;

-- Poll policies
CREATE POLICY "Users can view their own polls"
ON polls FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own polls"
ON polls FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own polls"
ON polls FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own polls"
ON polls FOR DELETE
USING (auth.uid() = user_id);

-- Public polls can be viewed by anyone if is_public is true
CREATE POLICY "Anyone can view public polls"
ON polls FOR SELECT
USING (is_public = true);

-- Questions policies
CREATE POLICY "Questions can be viewed by poll owner"
ON questions FOR SELECT
USING (EXISTS (
  SELECT 1 FROM polls
  WHERE polls.id = questions.poll_id
  AND polls.user_id = auth.uid()
));

CREATE POLICY "Questions can be modified by poll owner"
ON questions FOR ALL
USING (EXISTS (
  SELECT 1 FROM polls
  WHERE polls.id = questions.poll_id
  AND polls.user_id = auth.uid()
));

-- Public questions can be viewed by anyone for public polls
CREATE POLICY "Anyone can view questions for public polls"
ON questions FOR SELECT
USING (EXISTS (
  SELECT 1 FROM polls
  WHERE polls.id = questions.poll_id
  AND polls.is_public = true
));

-- Responses policies
CREATE POLICY "Responses can be viewed by poll owner"
ON responses FOR SELECT
USING (EXISTS (
  SELECT 1 FROM polls
  WHERE polls.id = responses.poll_id
  AND polls.user_id = auth.uid()
));

CREATE POLICY "Anyone can insert responses"
ON responses FOR INSERT
WITH CHECK (true);

-- Answers policies
CREATE POLICY "Answers can be viewed by poll owner"
ON answers FOR SELECT
USING (EXISTS (
  SELECT 1 FROM polls
  JOIN responses ON polls.id = responses.poll_id
  WHERE responses.id = answers.response_id
  AND polls.user_id = auth.uid()
));

CREATE POLICY "Anyone can insert answers"
ON answers FOR INSERT
WITH CHECK (true);