-- Create diagnostic functions to help with RLS policy debugging
-- Fixed version that handles data types correctly
-- Run this in the Supabase SQL Editor

-- Function to get policies for a specific table
CREATE OR REPLACE FUNCTION public.get_policies_for_table(table_name text)
RETURNS TABLE(
  schemaname text,
  tablename text,
  policyname text,
  permissive text,  -- Changed from boolean to text to prevent type errors
  roles name[],     -- Changed from text[] to name[] to match the actual type
  cmd text,
  cmd_pretty text,
  qual text,
  with_check text
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.schemaname::text,
    p.tablename::text,
    p.policyname::text,
    CASE WHEN p.permissive THEN 'PERMISSIVE' ELSE 'RESTRICTIVE' END::text,  -- Convert boolean to text
    p.roles,        -- Keep as name[] type
    p.cmd::text,
    CASE p.cmd::text
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
      ELSE p.cmd::text
    END as cmd_pretty,
    p.qual::text,
    p.with_check::text
  FROM
    pg_policies p
  WHERE
    p.tablename = table_name
  ORDER BY
    p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Simpler version that doesn't use the permissive field
CREATE OR REPLACE FUNCTION public.list_table_policies(table_name text)
RETURNS TABLE(
  schemaname text,
  tablename text,
  policyname text,
  roles name[],  -- Changed from text[] to name[] to match the actual type
  cmd_pretty text
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.schemaname::text,
    p.tablename::text,
    p.policyname::text,
    p.roles,  -- Keep as name[] type
    CASE p.cmd::text
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
      ELSE p.cmd::text
    END as cmd_pretty
  FROM
    pg_policies p
  WHERE
    p.tablename = table_name
  ORDER BY
    p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a user's policies are working properly
CREATE OR REPLACE FUNCTION public.check_user_poll_access(user_id uuid)
RETURNS TABLE(
  policy_name text,
  cmd text,
  has_access boolean,
  poll_count bigint,
  error_message text
)
SECURITY DEFINER
AS $$
DECLARE
  result_row record;
  error_text text;
BEGIN
  -- Check SELECT policy - own polls
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE user_id = check_user_poll_access.user_id;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      TRUE,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Users can view their own polls'::text,
      'SELECT'::text,
      FALSE,
      0::bigint,
      SQLERRM;
  END;

  -- Check SELECT policy - public polls
  BEGIN
    SELECT COUNT(*) INTO result_row FROM polls WHERE is_public = true AND is_published = true;
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      TRUE,
      result_row.count,
      NULL::text;
  EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_text = PG_EXCEPTION_DETAIL;
    RETURN QUERY SELECT
      'Public polls are viewable by everyone'::text,
      'SELECT'::text,
      FALSE,
      0::bigint,
      SQLERRM;
  END;

  -- Additional policy checks can be added here
END;
$$ LANGUAGE plpgsql;

-- Simple helper function to list all policies in the database
CREATE OR REPLACE FUNCTION public.list_all_policies()
RETURNS TABLE(
  schemaname text,
  tablename text,
  policyname text,
  cmd text
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.schemaname::text,
    p.tablename::text,
    p.policyname::text,
    CASE p.cmd::text
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
      ELSE p.cmd::text
    END as cmd
  FROM
    pg_policies p
  ORDER BY
    p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Test the functions (this should work without errors)
SELECT * FROM public.list_table_policies('polls');
