-- Super simple diagnostic functions without type issues
-- This version avoids using problematic columns altogether
-- Run this in the Supabase SQL Editor

/*
IMPORTANT: This file was created to avoid type casting issues with the original diagnostic functions.
The issue was that the pg_policies table contains columns with PostgreSQL-specific types
(like 'name[]') that don't match the expected types in our function definitions.

This simplified version avoids those problematic columns and provides essential
diagnostics for the polls table without any type errors.

Instructions:
1. Run this entire script in the Supabase SQL Editor
2. Use the functions to diagnose RLS policy issues
3. Fix any missing or incorrect policies using the commands at the end of this file
*/

-- Simple version that only includes essential information
CREATE OR REPLACE FUNCTION public.list_poll_policies()
RETURNS TABLE(
  policy_name text,
  operation text
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.policyname::text,
    CASE p.cmd::text
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
      ELSE p.cmd::text
    END as operation
  FROM
    pg_policies p
  WHERE
    p.tablename = 'polls'
  ORDER BY
    p.policyname;
END;
$$ LANGUAGE plpgsql;

-- Function to verify key RLS policies on polls table
CREATE OR REPLACE FUNCTION public.verify_poll_policies()
RETURNS TABLE(
  policy_exists boolean,
  policy_name text,
  description text
)
SECURITY DEFINER
AS $$
BEGIN
  -- Check for the policy that allows users to view their own polls
  RETURN QUERY
  SELECT
    EXISTS(SELECT 1 FROM pg_policies
           WHERE tablename = 'polls'
           AND policyname LIKE '%view%own%polls%'),
    'Users can view their own polls'::text,
    'Policy allowing users to see polls they created'::text;

  -- Check for the policy that allows users to view public polls
  RETURN QUERY
  SELECT
    EXISTS(SELECT 1 FROM pg_policies
           WHERE tablename = 'polls'
           AND policyname LIKE '%public%viewable%'),
    'Public polls are viewable by everyone'::text,
    'Policy allowing anyone to see public published polls'::text;

  -- Check for the policy that allows users to insert their own polls
  RETURN QUERY
  SELECT
    EXISTS(SELECT 1 FROM pg_policies
           WHERE tablename = 'polls'
           AND policyname LIKE '%insert%own%polls%'),
    'Users can insert their own polls'::text,
    'Policy allowing users to create new polls'::text;
END;
$$ LANGUAGE plpgsql;

-- Test function to count polls by visibility
CREATE OR REPLACE FUNCTION public.count_polls_by_visibility()
RETURNS TABLE(
  visibility text,
  poll_count bigint
)
SECURITY DEFINER
AS $$
BEGIN
  -- Count public and published polls
  RETURN QUERY
  SELECT
    'Public and published'::text,
    COUNT(*)::bigint
  FROM
    polls
  WHERE
    is_public = true AND is_published = true;

  -- Count private polls
  RETURN QUERY
  SELECT
    'Private (not public)'::text,
    COUNT(*)::bigint
  FROM
    polls
  WHERE
    is_public = false;

  -- Count unpublished polls
  RETURN QUERY
  SELECT
    'Unpublished'::text,
    COUNT(*)::bigint
  FROM
    polls
  WHERE
    is_published = false;

  -- Count total polls
  RETURN QUERY
  SELECT
    'Total'::text,
    COUNT(*)::bigint
  FROM
    polls;
END;
$$ LANGUAGE plpgsql;

-- Test the functions (these should work without errors)
SELECT * FROM list_poll_policies();
SELECT * FROM verify_poll_policies();
SELECT * FROM count_polls_by_visibility();

-- If you need to recreate the policies, run these commands:
/*
-- 1. First verify if RLS is enabled
SELECT
  schemaname,
  tablename,
  hasrowsecurity AS "rls_enabled"
FROM
  pg_tables
WHERE
  tablename = 'polls';

-- 2. If needed, enable RLS
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;

-- 3. Drop existing policies (if needed)
DROP POLICY IF EXISTS "Users can view their own polls" ON polls;
DROP POLICY IF EXISTS "Public polls are viewable by everyone" ON polls;
DROP POLICY IF EXISTS "Users can insert their own polls" ON polls;

-- 4. Create the required policies
CREATE POLICY "Users can view their own polls"
  ON polls
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Public polls are viewable by everyone"
  ON polls
  FOR SELECT
  USING (is_public = true AND is_published = true);

CREATE POLICY "Users can insert their own polls"
  ON polls
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);
*/
